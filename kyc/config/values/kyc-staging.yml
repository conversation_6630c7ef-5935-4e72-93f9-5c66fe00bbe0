Application:
  Environment: "staging"
  Name: "kyc"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8090
    GrpcSecurePort: 9512
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "kyc"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Publishers:
  QueueCKYCSearch:
    QueueName: "staging-kyc-ckyc-search-queue"
  QueueCKYCDownload:
    QueueName: "staging-kyc-ckyc-download-queue"
  QueueVKYCUpdate:
    QueueName: "staging-vkyc-update-queue"
  EKYCSuccess:
    QueueName: "staging-kyc-ekyc-success-queue"

Subscribers:
  QueueCKYCSearch:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "staging-kyc-ckyc-search-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 1
        TimeUnit: "Second"

  QueueCKYCDownload:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "staging-kyc-ckyc-download-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 1
        TimeUnit: "Second"

  QueueAccountStateUpdate:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "staging-kyc-savings-account-state-update-event-consumer-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 1
        MaxAttempts: 1
        TimeUnit: "Second"

QueueCKYCSearchPublisher:
  QueueName: "staging-kyc-ckyc-search-queue"
QueueCKYCDownloadPublisher:
  QueueName: "staging-kyc-ckyc-download-queue"
QueueVKYCUpdatePublisher:
  QueueName: "staging-vkyc-update-queue"
EKYCSuccessPublisher:
  QueueName: "staging-kyc-ekyc-success-queue"

QueueCKYCSearchSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-ckyc-search-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 2
      TimeUnit: "Second"

QueueCKYCDownloadSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-ckyc-download-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 2
      TimeUnit: "Second"

QueueAccountStateUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-savings-account-state-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 1
      TimeUnit: "Second"

RefreshCallStatusPublisher:
  QueueName: "staging-kyc-vkyc-refresh-call-status-queue"

VkycCallCompletedEventPublisher:
  TopicName: "staging-vkyc-call-completed-event-topic"

RefreshCallStatusDelay: 3600s

RefreshCallStatusSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-vkyc-refresh-call-status-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

FederalVKYCUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-federal-vkyc-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

KarzaVKYCCallEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-karza-vkyc-call-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

KarzaVKYCAgentCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-karza-vkyc-agent-response-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

KarzaVKYCAuditorCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-karza-vkyc-auditor-response-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

VKYCUserCommsDelayPublisher:
  QueueName: "staging-kyc-vkyc-user-comms-queue"

VKYCUserCommsPublisher:
  QueueName: "staging-kyc-vkyc-user-comms-queue"

VKYCUserCommsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-vkyc-user-comms-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

PANValidationRateLimitUpdatePublisher:
  QueueName: "staging-kyc-pan-validation-rate-limit-update-queue"

Aws:
  Endpoint: "localhost:4576"
  Region: "ap-south-1"
  Sqs:
    QueueNames:
      QueueCKYCDownload: "staging-kyc-ckyc-download-queue"
      QueueCKYCSearch: "staging-kyc-ckyc-search-queue"
  S3:
    BucketNames:
      BucketLiveness: "epifi-staging-liveness"
      BucketUser: "epifi-dev-users"

Secrets:
  Ids:
    EKYCSecurityToken: "staging/kyc/securitytoken"
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    KycDbUserCredentials: "staging/rds/postgres/kyc_dev_user"

Flags:
  EnableCKYCDataValidation: false
  TrimDebugMessageFromStatus: false

Values:
  CKYCDataValidity: "168h" # 7 days.
  EKYCDataValidity: "2160h" # 90 days.
  EKYCDataValidityForLSO: "168h" # 7 days
  EKYCDataValidityForOnboardingCKYCTypeO: "2160h" # 90 days.

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

SecureLogging:
  EnablePartnerLog: false
  PartnerLogPath: "/var/log/kyc/partner/partner.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

VkycAgentUpdatePublisher:
  QueueName: "staging-vkyc-agent-update-queue"

VKYCAgentUpdateDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vkyc-agent-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

ProcessVKYCOnboardingCompleteEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-vkyc-onboarding-completion-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessVKYCTransactionEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vkyc-order-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

#karza runs a job(15min interval as of now) to reassign agent-id when RM goes on leave
#1 hour delay is to avoid missing event due to delay in change or update at karza's side
Delay:
  BookSlot: 5m
  HolidayEvent: 1h

VKYC:
  VkycStartHour: 0
  VkycEndHour: 24
  PerformEkycAfter: "72h"
  MaxVKYCPriorityListSize: 10
  VKYCPriorityListElementTTL: "10m"
  EnableVKYCPriortyFlow: false
  VKYCPriorityUsersList:
    OTypeUser: true
    SalariedUser: true
    LastNDaysUser: true
  NRVKYCStartHour: 0
  NRVKYCEndHour: 24
  VKYCUserAllowedDateConstraint: #(start timestamp-end timestamp)yyyy-mm-dd HH:MM:SS/yyyy-mm-dd HH:MM:SS
    "2022-10-10 13:15:00/2022-10-10 15:15:00": 30
  VKYCUserAllowedDailyTimeIntervalConstraint: #HH:MM-HH:MM(start time-end time)
    "10:30-15:40": 100
  InstructionPageSkipOptionTime: 0 # in seconds
  LandingPageSkipOptionTime: 0 # in seconds
  UnsupportedCustomerDeviceModels: [ ]
  AgentWaitingOverlay:
    Disable: true
  EnableConfirmDetailsState:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableEpanInstructionScreen:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableCallQualityCheckStage:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false

QuestSdk:
  Disable: false

RateLimit:
  PANValidationRetryLimit: 300
  PANValidationRedisExpiryMins: 60

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on staging we are using common redis cluster with a different db
    DB: 2

UserRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: fe-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

KycDb:
  AppName: "kyc"
  StatementTimeout: 1s
  Name: "kyc"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

BKYCUpdateEventPublisher:
  TopicName: "staging-bkyc-update-event-topic"

NrBucketName: "epifi-staging-nrusers"
