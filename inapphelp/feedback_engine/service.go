package feedback_engine

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"crypto/rand"
	"fmt"
	"math"
	"math/big"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/analytics"
	"github.com/epifi/gamma/api/inapphelp/entity_mapping"
	feedbackEnginePb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	"github.com/epifi/gamma/inapphelp/config/genconf"
	dao2 "github.com/epifi/gamma/inapphelp/entity_mapping/dao"
	"github.com/epifi/gamma/inapphelp/feedback_engine/custom_eligibility_evaluation"
	"github.com/epifi/gamma/inapphelp/feedback_engine/dao"
	"github.com/epifi/gamma/inapphelp/feedback_engine/question_response_subscription"
	inAppHelpTypes "github.com/epifi/gamma/inapphelp/wire/types"
)

type FeedbackEngineService struct {
	genConf                             *genconf.Config
	feedbackQuestionsDao                dao.IFeedbackQuestionsDao
	feedbackSurveysDao                  dao.IFeedbackSurveysDao
	feedbackQuestionResponsesDao        dao.IFeedbackQuestionResponsesDao
	feedbackSurveyAttemptsDao           dao.IFeedbackSurveyAttemptsDao
	inAppHelpEntityMappingDao           dao2.IInAppHelpEntityMappingDao
	inAppHelpClientConfigMappingDao     dao2.IInAppHelpAppClientConfigMappingDao
	feedbackSurveysCoolOffsDao          dao.IFeedbackSurveyCoolOffsDao
	feedbackSurveyMappingsDao           dao.IFeedbackSurveyMappingsDao
	customEligibilityEvaluationFactory  custom_eligibility_evaluation.ICustomEligibilityEvaluationFactory
	questionResponseSubscriptionFactory question_response_subscription.IFeedbackSubscriptionFactory
	feedbackInfoEventSnsPublisher       inAppHelpTypes.FeedbackInfoEventSnsPublisher
}

func NewFeedbackEngineService(genConf *genconf.Config, feedbackQuestionsDao dao.IFeedbackQuestionsDao,
	feedbackSurveysDao dao.IFeedbackSurveysDao, feedbackQuestionResponsesDao dao.IFeedbackQuestionResponsesDao,
	feedbackSurveyAttemptsDao dao.IFeedbackSurveyAttemptsDao, inAppHelpEntityMappingDao dao2.IInAppHelpEntityMappingDao,
	inAppHelpClientConfigMappingDao dao2.IInAppHelpAppClientConfigMappingDao, feedbackSurveysCoolOffsDao dao.IFeedbackSurveyCoolOffsDao,
	feedbackSurveyMappingsDao dao.IFeedbackSurveyMappingsDao, customEligibilityEvaluationFactory custom_eligibility_evaluation.ICustomEligibilityEvaluationFactory,
	questionResponseSubscriptionFactory question_response_subscription.IFeedbackSubscriptionFactory, feedbackInfoEventSnsPublisher inAppHelpTypes.FeedbackInfoEventSnsPublisher) *FeedbackEngineService {
	return &FeedbackEngineService{
		genConf:                             genConf,
		feedbackQuestionsDao:                feedbackQuestionsDao,
		feedbackSurveysDao:                  feedbackSurveysDao,
		feedbackQuestionResponsesDao:        feedbackQuestionResponsesDao,
		feedbackSurveyAttemptsDao:           feedbackSurveyAttemptsDao,
		inAppHelpEntityMappingDao:           inAppHelpEntityMappingDao,
		inAppHelpClientConfigMappingDao:     inAppHelpClientConfigMappingDao,
		feedbackSurveysCoolOffsDao:          feedbackSurveysCoolOffsDao,
		feedbackSurveyMappingsDao:           feedbackSurveyMappingsDao,
		customEligibilityEvaluationFactory:  customEligibilityEvaluationFactory,
		questionResponseSubscriptionFactory: questionResponseSubscriptionFactory,
		feedbackInfoEventSnsPublisher:       feedbackInfoEventSnsPublisher,
	}
}

var _ feedbackEnginePb.FeedbackEngineServer = &FeedbackEngineService{}
var surveyIdToSurveyDetailsMap = make(map[string]*feedbackEnginePb.FeedbackSurvey)

const (
	defaultCoolOffDuration = "720h"
	noCoolOffDuration      = "0h"
)

func validateGetFirstFeedbackQuestionRequest(ctx context.Context, req *feedbackEnginePb.GetFirstFeedbackQuestionRequest) error {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	switch {
	case req.GetScreenId() == analytics.AnalyticsScreenName_ANALYTICS_SCREEN_NAME_UNSPECIFIED:
		return errors.New("analytics screen name is mandatory to get first feedback question")
	case req.GetFlowId() == nil:
		return errors.New("flow id is mandatory to get first feedback question")
	case len(req.GetActorId()) == 0:
		return errors.New("actor id is mandatory to get first feedback question")
	case appPlatform == commontypes.Platform_PLATFORM_UNSPECIFIED:
		return errors.New("app platform is mandatory to get first feedback question")
	case appVersion == 0:
		return errors.New("app version is mandatory to get first feedback question")
	default:
		return nil
	}
}

func (f *FeedbackEngineService) PopulateSurveyIdToSurveyDetailsMap(ctx context.Context, mappings []*feedbackEnginePb.FeedbackSurveyMapping) []string {
	var surveysWithFetchedDetails []string
	for _, mapping := range mappings {
		surveyDetails, surveyDetailsErr := f.feedbackSurveysDao.GetSurveyById(ctx, mapping.GetSurveyId())
		if surveyDetailsErr != nil {
			logger.Error(ctx, "skipping survey as error while fetching survey details", zap.String(logger.SURVEY_ID, mapping.GetSurveyId()), zap.Error(surveyDetailsErr))
		} else {
			// update the map with the fetched details for the survey id
			surveyIdToSurveyDetailsMap[mapping.GetSurveyId()] = surveyDetails
			surveysWithFetchedDetails = append(surveysWithFetchedDetails, surveyDetails.GetId())
		}
	}
	return surveysWithFetchedDetails
}

// GetCoolOffDuration concludes on a cool off to be used between two survey types
// if a correctly defined value for previous and current survey type is available in config, we utilize it
// if it's not available, then we utilize an alternate cool off
func (f *FeedbackEngineService) GetCoolOffDuration(ctx context.Context, previousSurvey *feedbackEnginePb.FeedbackSurvey, currentSurvey *feedbackEnginePb.FeedbackSurvey) time.Duration {
	noCoolOff, _ := time.ParseDuration(noCoolOffDuration)
	if lo.Contains(f.genConf.FeedbackEngineConfig().SurveyIdsWithNoCoolOff().ToStringArray(), currentSurvey.GetId()) {
		logger.Info(ctx, "current survey id found in SurveyIdsWithNoCoolOffList, so skipping cool off check", zap.String(logger.SURVEY_ID, currentSurvey.GetId()))
		return noCoolOff
	}
	fetchedCoolOffDetails, fetchedCoolOffDetailsErr := f.feedbackSurveysCoolOffsDao.GetCoolOff(ctx, previousSurvey.GetId(), currentSurvey.GetId())
	defaultCoolOff, defaultCoolOffErr := time.ParseDuration(defaultCoolOffDuration)
	if defaultCoolOffErr != nil {
		logger.Error(ctx, "error while parsing default cool off", zap.Error(defaultCoolOffErr))
	}
	globalCoolOff, globalCoolOffErr := time.ParseDuration(f.genConf.FeedbackEngineConfig().GlobalCoolOffDurationForAllSurveys())
	// alternateCoolOff contains the value of cool off to be used
	// in case the cool off value between the previous and current survey type
	// is not available in db
	var alternateCoolOff time.Duration
	if globalCoolOffErr != nil {
		logger.Error(ctx, "global cool off not defined in config, using default cool off", zap.Error(globalCoolOffErr), zap.String("defaultCoolOff", defaultCoolOff.String()))
		alternateCoolOff = defaultCoolOff
	} else {
		alternateCoolOff = globalCoolOff
	}
	// if there is an error while fetching cool off between current and previous survey type
	// then we will fall back to alternate cool off
	if fetchedCoolOffDetailsErr != nil {
		logger.Error(ctx, "error while fetching cool off", zap.Error(fetchedCoolOffDetailsErr), zap.String("previousSurveyId", previousSurvey.GetId()), zap.String("previousSurveyType", previousSurvey.GetSurveyType().String()), zap.String("currentSurveyId", currentSurvey.GetId()), zap.String("currentSurveyType", currentSurvey.GetSurveyType().String()))
		return alternateCoolOff
	}
	coolOff, coolOffParseErr := time.ParseDuration(fetchedCoolOffDetails.GetCoolOffDuration())
	// if there is an error while parsing the cool off fetched from db
	// then we will utilize the available alternate cool off
	if coolOffParseErr != nil {
		logger.Error(ctx, "error while cool off defined in config for current and previous survey type", zap.String("currentSurveyId", currentSurvey.GetId()), zap.String("previousSurveyId", previousSurvey.GetId()), zap.Error(coolOffParseErr))
		return alternateCoolOff
	}
	// cool off found in db and parsed correctly
	return coolOff
}

func getFlowIdentifierAsString(ctx context.Context, flowId *feedbackEnginePb.FlowIdentifier) string {
	switch flowId.GetFlowIdentifier().(type) {
	case *feedbackEnginePb.FlowIdentifier_FeedbackAppFlowIdentifier:
		return flowId.GetFeedbackAppFlowIdentifier().String()
	case *feedbackEnginePb.FlowIdentifier_FeedbackDropOffFlowIdentifier:
		return flowId.GetFeedbackDropOffFlowIdentifier().String()
	case *feedbackEnginePb.FlowIdentifier_FeedbackCtaFlowIdentifier:
		return flowId.GetFeedbackCtaFlowIdentifier().String()
	default:
		logger.Info(ctx, "unknown flow details received", zap.String(logger.FLOW_ID, flowId.String()))
		return ""
	}
}

func (f *FeedbackEngineService) getAttemptCountByActorId(ctx context.Context, surveyId string, actorId string) int64 {
	fetchedAttemptCount, attemptCountErr := f.feedbackSurveyAttemptsDao.GetAttemptCountForSurvey(ctx, surveyId, dao.WithActorId(actorId))
	if attemptCountErr != nil {
		logger.Error(ctx, "error while fetching attempt count for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.SURVEY_ID, surveyId), zap.Error(attemptCountErr))
		// we are returning max int64 value here to negate the current survey id from getting selected
		// as we faced error while fetching attempt count for current survey id
		return math.MaxInt64
	}
	return fetchedAttemptCount
}

// startAttempt function performs the miscellaneous tasks required to initiate a survey attempt
// based on the provided attemptDetails
func (f *FeedbackEngineService) shortListSurveyAndStartAttempt(ctx context.Context, surveyIdList []string, attemptDetails *feedbackEnginePb.FeedbackSurveyAttempt) (*feedbackEnginePb.GetFirstFeedbackQuestionResponse, error) {
	// check if a survey has been successfully shortlisted
	// if no surveys are eligible we return record not found status
	if len(surveyIdList) == 0 {
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	if len(surveyIdList) > 1 {
		return nil, errors.New("no survey shortlisted yet")
		// n := big.NewInt(int64(len(surveyIdList)))
		// idxBig, err := rand.Int(rand.Reader, n)
		// if err != nil {
		// 	// fallback to 0 if there's an error
		// 	idxBig = big.NewInt(0)
		// }
		// idx := int(idxBig.Int64())
		// surveyIdList = []string{surveyIdList[idx]}
	}
	// a survey has been shortlisted to start the attempt
	attemptDetails.SurveyId = surveyIdList[0]
	// get the details of the shortlisted survey
	fetchedSurveyDetails, isSurveyDetailsPresent := surveyIdToSurveyDetailsMap[attemptDetails.GetSurveyId()]
	if !isSurveyDetailsPresent {
		logger.Error(ctx, "survey details not present in map", zap.String(logger.SURVEY_ID, attemptDetails.GetSurveyId()))
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	// check if the survey is eligible to be asked as per rollout percentage
	randomNumber, _ := rand.Int(rand.Reader, big.NewInt(100))
	if int32(randomNumber.Int64()) >= surveyIdToSurveyDetailsMap[attemptDetails.GetSurveyId()].GetRolloutPercentage() {
		logger.Info(ctx, "survey is ineligible to be asked as per rollout percentage", zap.String(logger.ACTOR_ID_V2, attemptDetails.GetActorId()), zap.String(logger.SURVEY_ID, attemptDetails.GetSurveyId()), zap.String(logger.SURVEY_TYPE, fetchedSurveyDetails.GetSurveyType().String()), zap.String(logger.SURVEY_NAME, fetchedSurveyDetails.GetSurveyName()))
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	// add the attempt details in feedback_survey_attempts table
	attemptDetails, attemptIdErr := f.feedbackSurveyAttemptsDao.Create(ctx, attemptDetails)
	if attemptIdErr != nil {
		logger.Error(ctx, "error while marking attempt as started", zap.Error(attemptIdErr), zap.String(logger.ACTOR_ID_V2, attemptDetails.GetActorId()), zap.String(logger.SURVEY_ID, attemptDetails.GetSurveyId()))
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// get details of the first feedback question to be asked
	firstQuestionDetails, firstQuestionDetailsErr := f.feedbackQuestionsDao.GetQuestionById(ctx, fetchedSurveyDetails.GetFirstQuestionId())
	if firstQuestionDetailsErr != nil {
		logger.Error(ctx, "error while fetching details of first question", zap.Error(firstQuestionDetailsErr), zap.String(logger.QUESTION_ID, fetchedSurveyDetails.GetFirstQuestionId()), zap.String(logger.SURVEY_ID, fetchedSurveyDetails.GetId()))
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// as all details are successfully fetched, return the attempt id and details of first question to be asked
	return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
		Status:    rpcPb.StatusOk(),
		AttemptId: attemptDetails.GetId(),
		FirstFeedbackQuestion: &feedbackEnginePb.FeedbackQuestion{
			QuestionId:          firstQuestionDetails.GetId(),
			FeedbackContentType: getFeedbackContentType(firstQuestionDetails.GetAnswerType()),
			QuestionContent:     firstQuestionDetails.GetQuestionContent(),
			FeedbackAnswerType:  firstQuestionDetails.GetAnswerType(),
			AnswerOptions:       firstQuestionDetails.GetAnswerOptions(),
			IsOptional:          true,
		},
	}, nil
}

func isActorAttemptingSurveyFromCTA(flowId *feedbackEnginePb.FlowIdentifier) bool {
	// using a switch as checking using if block does not allow checking the type
	switch flowId.GetFlowIdentifier().(type) {
	case *feedbackEnginePb.FlowIdentifier_FeedbackCtaFlowIdentifier:
		return true
	default:
		return false
	}
}

func (f *FeedbackEngineService) getSurveysAvailableForAppVersion(ctx context.Context, surveys []*feedbackEnginePb.FeedbackSurvey, appVersion int) []string {
	var surveysAvailableForAppVersion []string
	for _, surveyDetail := range surveys {
		appVersionCheck, appVersionCheckErr := f.inAppHelpClientConfigMappingDao.GetByEntityTypeAndEntityId(ctx, entity_mapping.EntityType_ENTITY_TYPE_SURVEY_ID, surveyDetail.GetId())
		if appVersionCheckErr != nil {
			// if record is not found, we will assume there are no app version checks applicable for that survey
			if !errors.Is(appVersionCheckErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error while fetching app version checks for survey", zap.String(logger.SURVEY_ID, surveyDetail.GetId()), zap.Error(appVersionCheckErr))
			} else {
				surveysAvailableForAppVersion = append(surveysAvailableForAppVersion, surveyDetail.GetId())
			}
		} else {
			// as app version checks are available for the survey
			// we will evaluate the same for the current attempt
			if int64(appVersion) < appVersionCheck[0].GetMinAppVersion() || int64(appVersion) > appVersionCheck[0].GetMaxAppVersion() {
				logger.Debug(ctx, "survey is ineligible due to app version check", zap.Int(logger.APP_VERSION_CODE, appVersion), zap.Int64("minAppVersion", appVersionCheck[0].GetMinAppVersion()),
					zap.Int64("maxAppVersion", appVersionCheck[0].GetMaxAppVersion()), zap.String(logger.SURVEY_ID, surveyDetail.GetId()))
			} else {
				surveysAvailableForAppVersion = append(surveysAvailableForAppVersion, surveyDetail.GetId())
			}
		}
	}
	return surveysAvailableForAppVersion
}

func (f *FeedbackEngineService) getEligibleSurveyIdsByCoolOff(ctx context.Context, surveyIds []string, latestAttemptedSurveyDetails *feedbackEnginePb.FeedbackSurvey, latestAttempt *feedbackEnginePb.FeedbackSurveyAttempt) []string {
	var eligibleSurveyIdsByCoolOff []string
	for _, surveyId := range surveyIds {
		// if there are no previous attempts present for this actor
		// then we don't need to
		surveyDetails, isSurveyDetailsPresent := surveyIdToSurveyDetailsMap[surveyId]
		if !isSurveyDetailsPresent {
			logger.Error(ctx, "skipping survey while evaluating cool off as survey details not present in map", zap.String(logger.SURVEY_ID, surveyId))
			continue
		}
		// if survey start time is after current time, it means survey has not started accepting responses yet
		// if survey end time is not empty (end time is non-zero in UTC timezone) AND survey end time has passed the current time
		// then the survey is not active right now, so skipping it
		if surveyDetails.GetStartTime().AsTime().After(time.Now()) || (!surveyDetails.GetEndTime().AsTime().IsZero() && time.Now().After(surveyDetails.GetEndTime().AsTime())) {
			logger.Debug(ctx, "survey is not active right now", zap.String(logger.SURVEY_ID, surveyDetails.GetId()), zap.String("startTime", surveyDetails.GetStartTime().String()), zap.String("endTime", surveyDetails.GetEndTime().String()))
			logger.Info(ctx, "survey not eligible due to start time or end time", zap.Bool("startTimeCondition", surveyDetails.GetStartTime().AsTime().After(time.Now())), zap.Bool("endTimeCondition", !surveyDetails.GetEndTime().AsTime().IsZero() && time.Now().After(surveyDetails.GetEndTime().AsTime())))
			continue
		}
		applicableCoolOff := f.GetCoolOffDuration(ctx, latestAttemptedSurveyDetails, surveyDetails)
		if time.Since(latestAttempt.GetUpdatedAt().AsTime()) > applicableCoolOff {
			eligibleSurveyIdsByCoolOff = append(eligibleSurveyIdsByCoolOff, surveyId)
		}
	}
	return eligibleSurveyIdsByCoolOff
}

func (f *FeedbackEngineService) getSurveyIdsWithLeastAttemptsByActor(ctx context.Context, surveyIds []string, actorId string) []string {
	var surveyIdsWithLeastAttemptsByActor []string
	leastNumberOfAttempts := int64(math.MaxInt64)
	for _, eligibleSurveyId := range surveyIdsWithLeastAttemptsByActor {
		attemptCount := f.getAttemptCountByActorId(ctx, eligibleSurveyId, actorId)
		if attemptCount != math.MaxInt64 {
			if attemptCount < leastNumberOfAttempts {
				leastNumberOfAttempts = attemptCount
				surveyIdsWithLeastAttemptsByActor = []string{eligibleSurveyId}
			} else if attemptCount == leastNumberOfAttempts {
				surveyIdsWithLeastAttemptsByActor = append(surveyIdsWithLeastAttemptsByActor, eligibleSurveyId)
			}
		}
	}
	return surveyIdsWithLeastAttemptsByActor
}

func (f *FeedbackEngineService) getSurveyIdsWithHighestPriority(ctx context.Context, surveyIds []string) []string {
	var surveysWithHighestPriority []string
	highestPriority := int32(0)
	for _, surveyId := range surveyIds {
		priorityOfCurrentSurvey := surveyIdToSurveyDetailsMap[surveyId].GetPriority()
		if priorityOfCurrentSurvey != 0 {
			if priorityOfCurrentSurvey > highestPriority {
				highestPriority = priorityOfCurrentSurvey
				surveysWithHighestPriority = []string{surveyId}
			} else {
				surveysWithHighestPriority = append(surveysWithHighestPriority, surveyId)
			}
		}
	}
	return surveysWithHighestPriority
}

// GetCurrentlyActiveSurveys filter the surveys with following characteristics :
// - The survey is in approved state i.e. survey status is approved
// - The survey is in enabled state i.e. is_survey_mapping_enabled field is set to true
// - If custom evaluation for a survey is enabled
//   - If the actor is eligible, the survey is selected for further filtration
//   - If there is an error while checking eligibility, it is considered non-blocking i.e. just log the error and move on
func (f *FeedbackEngineService) GetCurrentlyActiveSurveys(ctx context.Context, mappedSurveyDetails []*feedbackEnginePb.FeedbackSurveyMapping, req *feedbackEnginePb.GetFirstFeedbackQuestionRequest) []*feedbackEnginePb.FeedbackSurvey {
	var currentlyActiveSurveys []*feedbackEnginePb.FeedbackSurvey
	for _, surveyMapping := range mappedSurveyDetails {
		if surveyMapping.GetIsSurveyMappingEnabled() &&
			surveyIdToSurveyDetailsMap[surveyMapping.GetSurveyId()].GetApprovalStatus() == feedbackEnginePb.FeedbackSurveyApprovalStatus_FEEDBACK_SURVEY_APPROVAL_STATUS_APPROVED {
			if surveyIdToSurveyDetailsMap[surveyMapping.GetSurveyId()].GetIsCustomEvaluationEnabled() {
				// if custom eligibility evaluation is enabled, we try to check with other backend service on best effort basis
				// custom eligibility evaluation
				backendServiceForSurvey := surveyIdToSurveyDetailsMap[surveyMapping.GetSurveyId()].GetOwnerService()
				customEligibilityEvaluator, customEligibilityEvaluatorErr := f.customEligibilityEvaluationFactory.GetCustomEligibilityEvaluator(backendServiceForSurvey)
				if customEligibilityEvaluatorErr != nil {
					logger.Error(ctx, "error while evaluating actor eligibility", zap.Error(customEligibilityEvaluatorErr))
					continue
				}
				eligibilityResp, eligibilityRespErr := customEligibilityEvaluator.IsActorEligibleForSurvey(ctx, &feedbackEnginePb.IsActorEligibleForSurveyRequest{
					SurveyId:            surveyMapping.GetSurveyId(),
					FlowId:              req.GetFlowId(),
					AnalyticsScreenName: req.GetScreenId(),
					SurveyType:          surveyIdToSurveyDetailsMap[surveyMapping.GetSurveyId()].GetSurveyType(),
					ActorId:             req.GetActorId(),
				})
				if te := epifigrpc.RPCError(eligibilityResp, eligibilityRespErr); te != nil {
					logger.Error(ctx, "error while evaluating actor eligibility for survey", zap.Error(te))
				} else if eligibilityResp.GetIsActorEligibleForSurvey() {
					// if the backend service has determined that the actor is eligible for the survey, select the survey for further filtrations
					currentlyActiveSurveys = append(currentlyActiveSurveys, surveyIdToSurveyDetailsMap[surveyMapping.GetSurveyId()])
				}
			} else {
				// if custom eligibility evaluation is not enabled, no check with other backend service is required
				currentlyActiveSurveys = append(currentlyActiveSurveys, surveyIdToSurveyDetailsMap[surveyMapping.GetSurveyId()])
			}
		}
	}
	return currentlyActiveSurveys
}

// nolint:funlen
func (f *FeedbackEngineService) GetFirstFeedbackQuestion(ctx context.Context, req *feedbackEnginePb.GetFirstFeedbackQuestionRequest) (*feedbackEnginePb.GetFirstFeedbackQuestionResponse, error) {
	logger.Debug(ctx, "GetFirstFeedbackQuestionRequest", zap.Any(logger.REQUEST, req))
	validateErr := validateGetFirstFeedbackQuestionRequest(ctx, req)
	if validateErr != nil {
		logger.Error(ctx, "invalid request to get first feedback question", zap.Error(validateErr))
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// get app platform and app version from context
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	// get list of survey ids mapped to given analytics screen name and flow id
	mappedSurveyDetails, mappedSurveyDetailsErr := f.feedbackSurveyMappingsDao.GetMappings(ctx, req.GetScreenId(), getFlowIdentifierAsString(ctx, req.GetFlowId()))
	if mappedSurveyDetailsErr != nil {
		logger.Error(ctx, "error while fetching surveys mapped to analytics screen name and flow id", zap.Error(mappedSurveyDetailsErr), zap.String(logger.SCREEN_ID, req.GetScreenId().String()), zap.String(logger.FLOW_ID, req.GetFlowId().String()))
		if errors.Is(mappedSurveyDetailsErr, epifierrors.ErrRecordNotFound) {
			return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// update the map with survey details for the shortlisted survey ids,
	// so we don't have to fetch from db again and again
	surveysWithFetchedDetails := f.PopulateSurveyIdToSurveyDetailsMap(ctx, mappedSurveyDetails)
	// get surveys which are currently approved and enabled
	activeSurveys := f.GetCurrentlyActiveSurveys(ctx, mappedSurveyDetails, req)
	// get the surveys which are applicable for app version for this attempt
	surveysWithFetchedDetails = f.getSurveysAvailableForAppVersion(ctx, activeSurveys, appVersion)
	// if no surveys are eligible for the given app version, we will stop here
	// but even if only a single survey is eligible, we will continue as it is
	// because we still need to check for cool off and other criterias
	// hence not using shortlistSurveyAndStartAttempt here
	if len(surveysWithFetchedDetails) == 0 {
		logger.Info(ctx, "no surveys available for app version", zap.Int(logger.APP_VERSION_CODE, appVersion), zap.String(logger.APP_PLATFORM, appPlatform.String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	// as per discussion with product, if the survey is being attempted from CTA
	// then cool off related checks won't be applicable
	// hence we will be doing cool off related checks only
	// if the survey is not attempted from a CTA
	isSurveyAttemptedFromCTA := isActorAttemptingSurveyFromCTA(req.GetFlowId())
	var eligibleSurveyIdsByCoolOff []string
	if !isSurveyAttemptedFromCTA {
		// fetch latest attempt of the actor for determining cool off, as cool off is applicable
		isFirstSurveyAttemptByActor := false
		latestAttemptOfActor, latestAttemptErr := f.feedbackSurveyAttemptsDao.GetLatestAttemptByActorId(ctx, req.GetActorId())
		if latestAttemptErr != nil {
			logger.Error(ctx, "error while fetching latest attempt of actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(latestAttemptErr))
			// if there are no survey attempts available for this actor
			// then it means there is no cool off applicable, hence setting it as 0s
			if errors.Is(latestAttemptErr, epifierrors.ErrRecordNotFound) {
				isFirstSurveyAttemptByActor = true
			} else {
				return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}
		}
		// if it's a first attempt by actor as there is no cool off applicable here
		// then all surveys would be eligible for next set of checks
		if isFirstSurveyAttemptByActor {
			eligibleSurveyIdsByCoolOff = surveysWithFetchedDetails
		} else {
			// as we are able to successfully fetch the latest attempt
			// we will retrieve the id of the latest attempted survey from the attempt details
			latestAttemptedSurveyId := latestAttemptOfActor.GetSurveyId()
			// fetching details of latest attempted survey
			latestAttemptedSurveyDetails, latestSurveyDetailsErr := f.feedbackSurveysDao.GetSurveyById(ctx, latestAttemptedSurveyId)
			if latestSurveyDetailsErr != nil {
				logger.Error(ctx, "error while fetching latest survey details", zap.Error(latestSurveyDetailsErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
				return &feedbackEnginePb.GetFirstFeedbackQuestionResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}
			eligibleSurveyIdsByCoolOff = f.getEligibleSurveyIdsByCoolOff(ctx, surveysWithFetchedDetails, latestAttemptedSurveyDetails, latestAttemptOfActor)
		}
	} else {
		// as the survey is attempted from a CTA, no cool off checks are applicable
		eligibleSurveyIdsByCoolOff = surveysWithFetchedDetails
	}
	// check if the attempt was started successfully after shortlisting a survey
	startAttemptResp, surveyShortlistedErr := f.shortListSurveyAndStartAttempt(ctx, eligibleSurveyIdsByCoolOff, &feedbackEnginePb.FeedbackSurveyAttempt{
		ActorId:       req.GetActorId(),
		AppPlatform:   appPlatform,
		AppVersion:    int32(appVersion),
		FlowId:        getFlowIdentifierAsString(ctx, req.GetFlowId()),
		AttemptStatus: feedbackEnginePb.FeedbackSurveyAttemptStatus_FEEDBACK_SURVEY_ATTEMPT_STATUS_STARTED,
		AttemptMetadata: &feedbackEnginePb.FeedbackSurveyAttemptMetadata{
			FlowIdMeta:               req.GetFlowIdMeta(),
			FlowInvocationIdentifier: req.GetFlowId().GetFlowInvocationIdentifier(),
		},
	})
	// if there was no error while shortlisting the survey, we will return the appropriate response
	if surveyShortlistedErr == nil {
		return startAttemptResp, nil
	}
	// pick the surveys having the least number of attempts by actor
	surveyIdsWithLeastAttemptsByActor := f.getSurveyIdsWithLeastAttemptsByActor(ctx, eligibleSurveyIdsByCoolOff, req.GetActorId())
	// check if the attempt was started successfully after shortlisting a survey
	startAttemptResp, surveyShortlistedErr = f.shortListSurveyAndStartAttempt(ctx, surveyIdsWithLeastAttemptsByActor, &feedbackEnginePb.FeedbackSurveyAttempt{
		ActorId:       req.GetActorId(),
		AppPlatform:   appPlatform,
		AppVersion:    int32(appVersion),
		FlowId:        getFlowIdentifierAsString(ctx, req.GetFlowId()),
		AttemptStatus: feedbackEnginePb.FeedbackSurveyAttemptStatus_FEEDBACK_SURVEY_ATTEMPT_STATUS_STARTED,
		AttemptMetadata: &feedbackEnginePb.FeedbackSurveyAttemptMetadata{
			FlowIdMeta:               req.GetFlowIdMeta(),
			FlowInvocationIdentifier: req.GetFlowId().GetFlowInvocationIdentifier(),
		},
	})
	// if there was no error while shortlisting the survey, we will return the appropriate response
	if surveyShortlistedErr == nil {
		return startAttemptResp, nil
	}
	// as we have not concluded on a survey id yet, we will now try to break the tie using priority
	// select all the surveys having the survey type with the highest priority
	surveysWithHighestPriority := f.getSurveyIdsWithHighestPriority(ctx, surveyIdsWithLeastAttemptsByActor)
	// check if the attempt was started successfully after shortlisting a survey
	startAttemptResp, surveyShortlistedErr = f.shortListSurveyAndStartAttempt(ctx, surveysWithHighestPriority, &feedbackEnginePb.FeedbackSurveyAttempt{
		ActorId:       req.GetActorId(),
		AppPlatform:   appPlatform,
		AppVersion:    int32(appVersion),
		FlowId:        getFlowIdentifierAsString(ctx, req.GetFlowId()),
		AttemptStatus: feedbackEnginePb.FeedbackSurveyAttemptStatus_FEEDBACK_SURVEY_ATTEMPT_STATUS_STARTED,
		AttemptMetadata: &feedbackEnginePb.FeedbackSurveyAttemptMetadata{
			FlowIdMeta:               req.GetFlowIdMeta(),
			FlowInvocationIdentifier: req.GetFlowId().GetFlowInvocationIdentifier(),
		},
	})
	// if there was no error while shortlisting the survey, we will return the appropriate response
	if surveyShortlistedErr == nil {
		return startAttemptResp, nil
	}
	// as we have not concluded on a survey to be asked yet, we will randomly select
	// one of the surveys having the highest priority
	randomIndex, _ := rand.Int(rand.Reader, big.NewInt(100))
	selectedSurveyId := surveysWithHighestPriority[randomIndex.Int64()]
	// we are ignoring the error here as there is only a single survey
	// so shortlisting issues would not arise and other errors would be
	// captured by the error codes in the response
	startAttemptResp, _ = f.shortListSurveyAndStartAttempt(ctx, []string{selectedSurveyId}, &feedbackEnginePb.FeedbackSurveyAttempt{
		ActorId:       req.GetActorId(),
		AppPlatform:   appPlatform,
		AppVersion:    int32(appVersion),
		FlowId:        getFlowIdentifierAsString(ctx, req.GetFlowId()),
		AttemptStatus: feedbackEnginePb.FeedbackSurveyAttemptStatus_FEEDBACK_SURVEY_ATTEMPT_STATUS_STARTED,
		AttemptMetadata: &feedbackEnginePb.FeedbackSurveyAttemptMetadata{
			FlowIdMeta:               req.GetFlowIdMeta(),
			FlowInvocationIdentifier: req.GetFlowId().GetFlowInvocationIdentifier(),
		},
	})
	return startAttemptResp, nil
}

func ValidateGetNextFeedbackQuestionRequest(ctx context.Context, request *feedbackEnginePb.GetNextFeedbackQuestionRequest) error {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	switch {
	case request.GetCurrentQuestionId() == "":
		return errors.New("current question id cannot be empty")
	case appVersion == 0:
		return errors.New("app version cannot be empty")
	case appPlatform == commontypes.Platform_PLATFORM_UNSPECIFIED:
		return errors.New("app platform cannot be unspecified")
	case request.GetAttemptId() == "":
		return errors.New("attempt id cannot be empty")
	case request.GetActorId() == "":
		return errors.New("actor id cannot be empty")
	case request.GetCurrentQuestionAnswer() == nil:
		return errors.New("answer to current question cannot be empty")
	default:
		return nil
	}
}

func (f *FeedbackEngineService) updateAttemptStatusInDB(ctx context.Context, attemptId string, attemptStatus feedbackEnginePb.FeedbackSurveyAttemptStatus) error {
	updateAttemptErr := f.feedbackSurveyAttemptsDao.UpdateByAttemptId(ctx, attemptId, &feedbackEnginePb.FeedbackSurveyAttempt{
		AttemptStatus: attemptStatus,
	}, []feedbackEnginePb.FeedbackSurveyAttemptFieldMask{feedbackEnginePb.FeedbackSurveyAttemptFieldMask_FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_ATTEMPT_STATUS})
	return updateAttemptErr
}

// nolint:funlen
func (f *FeedbackEngineService) GetNextFeedbackQuestion(ctx context.Context, request *feedbackEnginePb.GetNextFeedbackQuestionRequest) (*feedbackEnginePb.GetNextFeedbackQuestionResponse, error) {
	logger.Debug(ctx, "GetNextFeedbackQuestion", zap.Any(logger.REQUEST, request))
	validateErr := ValidateGetNextFeedbackQuestionRequest(ctx, request)
	if validateErr != nil {
		logger.Error(ctx, "invalid request to get next feedback question", zap.Error(validateErr))
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// store the answer to the current question
	_, storeFeedbackAnswerErr := f.feedbackQuestionResponsesDao.Create(ctx, &feedbackEnginePb.FeedbackQuestionResponse{
		AttemptId:    request.GetAttemptId(),
		QuestionId:   request.GetCurrentQuestionId(),
		UserResponse: request.GetCurrentQuestionAnswer(),
	})
	if storeFeedbackAnswerErr != nil {
		logger.Error(ctx, "error while storing feedback response", zap.Error(storeFeedbackAnswerErr), zap.String(logger.ATTEMPT_ID, request.GetAttemptId()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()))
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// fetch details of current question
	currentQuestionDetails, currentQuestionDetailsErr := f.feedbackQuestionsDao.GetQuestionById(ctx, request.GetCurrentQuestionId())
	if currentQuestionDetailsErr != nil {
		logger.Error(ctx, "error while retrieving details of current question", zap.Error(currentQuestionDetailsErr), zap.String(logger.ATTEMPT_ID, request.GetAttemptId()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()))
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// fetch feedback survey attempt details
	feedbackSurveyAttempt, feedbackSurveyAttemptErr := f.feedbackSurveyAttemptsDao.GetByAttemptId(ctx, request.GetAttemptId())
	if feedbackSurveyAttemptErr != nil {
		logger.Error(ctx, "error while retrieving details of survey attempt", zap.Error(feedbackSurveyAttemptErr), zap.String(logger.ATTEMPT_ID, request.GetAttemptId()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()))
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// publish feedback info event to notify subscribers about an answered question on best effort basis
	messageId, publishErr := f.feedbackInfoEventSnsPublisher.Publish(ctx, &feedbackEnginePb.FeedbackInfo{
		QuestionId:               request.GetCurrentQuestionId(),
		AttemptId:                request.GetAttemptId(),
		FeedbackAnswer:           request.GetCurrentQuestionAnswer(),
		ActorId:                  request.GetActorId(),
		FlowInvocationIdentifier: feedbackSurveyAttempt.GetAttemptMetadata().GetFlowInvocationIdentifier(),
	})
	// as we publish the event on best effort basis, if there are any errors, we just log the error
	if publishErr != nil {
		logger.Error(ctx, "error while publishing feedback info event to topic", zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()), zap.String("attemptId", request.GetAttemptId()), zap.Error(publishErr))
	} else {
		logger.Debug(ctx, "feedback info event successfully published to topic", zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()), zap.String("attemptId", request.GetAttemptId()), zap.String(logger.MESSAGE_ID, messageId))
	}

	// notify the subscriber (if present) of the current question about the response on best effort basis
	subscriberNotificationErr := f.notifyQuestionResponseSubscriber(ctx, &feedbackEnginePb.FeedbackInfo{
		QuestionId:     request.GetCurrentQuestionId(),
		AttemptId:      request.GetAttemptId(),
		FeedbackAnswer: request.GetCurrentQuestionAnswer(),
		ActorId:        request.GetActorId(),
	})
	// as we notify the subscriber on best effort basis, if there are any errors
	// we will just log the error and move on
	if subscriberNotificationErr != nil {
		logger.Error(ctx, "error while sending question response info to subscriber", zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()), zap.String("attemptId", request.GetAttemptId()), zap.Error(subscriberNotificationErr))
	}
	// check the nextQuestionId field of current question
	// in case next question is not available, mark the attempt as completed
	if currentQuestionDetails.GetNextQuestionId() == "" {
		markAttemptAsCompletedErr := f.updateAttemptStatusInDB(ctx, request.GetAttemptId(), feedbackEnginePb.FeedbackSurveyAttemptStatus_FEEDBACK_SURVEY_ATTEMPT_STATUS_COMPLETED)
		if markAttemptAsCompletedErr != nil {
			logger.Error(ctx, "error while marking attempt as completed", zap.Error(markAttemptAsCompletedErr), zap.String(logger.ATTEMPT_ID, request.GetAttemptId()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()))
			return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		// if next question field is not populated, along with a status ok code in response
		// the client assumes it as the end of the survey
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status:    rpcPb.StatusOk(),
			AttemptId: request.GetAttemptId(),
		}, nil
	}
	// mark the attempt as partially completed if next question is available
	markAttemptAsPartiallyCompletedErr := f.updateAttemptStatusInDB(ctx, request.GetAttemptId(), feedbackEnginePb.FeedbackSurveyAttemptStatus_FEEDBACK_SURVEY_ATTEMPT_STATUS_PARTIALLY_COMPLETED)
	if markAttemptAsPartiallyCompletedErr != nil {
		logger.Error(ctx, "error while marking attempt as partially completed", zap.Error(markAttemptAsPartiallyCompletedErr), zap.String(logger.ATTEMPT_ID, request.GetAttemptId()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()))
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// retrieve details of next question
	nextQuestionDetails, nextQuestionDetailsErr := f.feedbackQuestionsDao.GetQuestionById(ctx, currentQuestionDetails.GetNextQuestionId())
	if nextQuestionDetailsErr != nil {
		logger.Error(ctx, "error while retrieving next question details", zap.Error(nextQuestionDetailsErr), zap.String(logger.ATTEMPT_ID, request.GetAttemptId()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.QUESTION_ID, request.GetCurrentQuestionId()))
		return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &feedbackEnginePb.GetNextFeedbackQuestionResponse{
		Status: rpcPb.StatusOk(),
		NextFeedbackQuestion: &feedbackEnginePb.FeedbackQuestion{
			QuestionId:          nextQuestionDetails.GetId(),
			FeedbackContentType: getFeedbackContentType(nextQuestionDetails.GetAnswerType()),
			QuestionContent:     nextQuestionDetails.GetQuestionContent(),
			FeedbackAnswerType:  nextQuestionDetails.GetAnswerType(),
			AnswerOptions:       nextQuestionDetails.GetAnswerOptions(),
			IsOptional:          true,
		},
		AttemptId: request.GetAttemptId(),
	}, nil
}

func (f *FeedbackEngineService) notifyQuestionResponseSubscriber(ctx context.Context, feedbackInfo *feedbackEnginePb.FeedbackInfo) error {
	subscriberServiceName, isQuestionSubscribed := f.genConf.FeedbackEngineConfig().QuestionIdToFeedbackSubscriberServiceNameMap().Load(feedbackInfo.GetQuestionId())
	// if the question is not subscribed at all, we will return nil error
	if !isQuestionSubscribed {
		logger.Debug(ctx, "no subscribers present for given question id", zap.String(logger.QUESTION_ID, feedbackInfo.GetQuestionId()))
		return nil
	}
	// otherwise we will get the subscriber of the related service name and notify it about the response
	serviceNameEnumValue, serviceNameEnumValuePresent := types.ServiceName_value[subscriberServiceName]
	if !serviceNameEnumValuePresent {
		return fmt.Errorf("no enum value found for service name in config : %s", subscriberServiceName)
	}
	serviceNameEnum := types.ServiceName(serviceNameEnumValue)
	// now we will get the subscriber of the service corresponding the enum defined in config
	questionResponseSubscriber, questionResponseSubscriberErr := f.questionResponseSubscriptionFactory.GetFeedbackSubscriber(serviceNameEnum)
	if questionResponseSubscriberErr != nil {
		return errors.Wrap(questionResponseSubscriberErr, fmt.Sprintf("error while getting the subscriber of service : %s", serviceNameEnum))
	}
	// populate the flow id meta in the request from feedback_survey_attempts table
	attemptDetails, attemptDetailsErr := f.feedbackSurveyAttemptsDao.GetLatestAttemptByActorId(ctx, feedbackInfo.GetActorId())
	if attemptDetailsErr != nil {
		return errors.Wrap(attemptDetailsErr, "error while fetching attempt details to get flow id meta")
	}
	feedbackInfo.FlowIdMeta = attemptDetails.GetAttemptMetadata().GetFlowIdMeta()
	// send the feedback info to the subscriber
	subscriberResp, subscriberErr := questionResponseSubscriber.GetFeedbackInfo(ctx, &feedbackEnginePb.GetFeedbackInfoRequest{FeedbackInfo: feedbackInfo})
	if te := epifigrpc.RPCError(subscriberResp, subscriberErr); te != nil {
		return errors.Wrap(te, fmt.Sprintf("error while sending question response info to the subscriber of service : %s", serviceNameEnum))
	}
	// everything succeeded
	return nil
}

func getFeedbackContentType(answerType feedbackEnginePb.FeedbackAnswerType) feedbackEnginePb.FeedbackContentType {
	switch answerType {
	case feedbackEnginePb.FeedbackAnswerType_FEEDBACK_ANSWER_TYPE_NO_ANSWER:
		return feedbackEnginePb.FeedbackContentType_FEEDBACK_CONTENT_TYPE_ENGAGEMENT
	default:
		return feedbackEnginePb.FeedbackContentType_FEEDBACK_CONTENT_TYPE_QUESTION
	}
}
