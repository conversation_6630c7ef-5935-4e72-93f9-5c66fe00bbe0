// nolint:goconst,dupl,unparam
package workflow

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"go.temporal.io/sdk/log"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	activitypkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	jarvisns "github.com/epifi/be-common/pkg/epifitemporal/namespace/jarvis"

	jarvisactivitypb "github.com/epifi/gamma/api/jarvis/activity"
	jarvisworkflowpb "github.com/epifi/gamma/api/jarvis/workflow"
	"github.com/epifi/gamma/jarvis/utils"
)

type JobInfo struct {
	BuildID int64
	Status  string
	JobPath string
	JobURL  string
}

// PRDeploymentWorkflow orchestrates the PR deployment process.
func PRDeploymentWorkflow(ctx workflow.Context, request *jarvisworkflowpb.PRDeploymentWorkflowRequest) (*jarvisworkflowpb.PRDeploymentWorkflowResponse, error) {
	lg := workflow.GetLogger(ctx)
	if request == nil {
		lg.Error("Invalid request parameters", zap.Error(errors.New("request is nil")))
		return nil, errors.New("request is nil")
	}

	lg.Info("Starting PR deployment workflow",
		zap.String(logger.WORKFLOW, "PRDeploymentWorkflow"),
		zap.String("requestParams", request.String()))

	// Initialize workflow data
	workflowData := utils.InitializeWorkflowData(ctx, request)
	workflowData.Status = utils.StatusInProgress

	err := workflow.SetQueryHandler(ctx, "getWorkflowStatus", func() (*utils.WorkflowData, error) {
		return workflowData, nil
	})
	if err != nil {
		lg.Error("Failed to register query handler", zap.Error(err))
		return nil, err
	}

	// Send initial status message
	if err := updateStatusMessage(ctx, lg, request, workflowData, "Initial status message"); err != nil {
		lg.Error("Failed to send initial status message", zap.Error(err))
	}

	// Defer final status update
	defer func() {
		if err := updateFinalStatus(ctx, lg, request, workflowData); err != nil {
			lg.Error("Failed to update final status message", zap.Error(err))
		}
	}()

	// Execute workflow steps
	if err := executeWorkflowSteps(ctx, lg, request, workflowData); err != nil {
		return nil, epifitemporal.NewPermanentError(err)
	}

	lg.Info("PR deployment workflow completed successfully")
	return &jarvisworkflowpb.PRDeploymentWorkflowResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func executeWorkflowSteps(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData) error {
	// Step 1: Wait for PR merge
	if err := waitForPRMerge(ctx, lg, request, workflowData); err != nil {
		return err
	}

	// Step 2: Handle cherry-picks
	environmentToCherryPickedPRs, err := handleCherryPicks(ctx, lg, request, workflowData)
	if err != nil {
		workflowData.Status = utils.StatusFailed
		return err
	}
	workflowData.CherryPickPRs = environmentToCherryPickedPRs

	// Send cherry-pick notification
	if err := sendNotification(ctx, lg, request, jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_CHERRY_PICK, environmentToCherryPickedPRs, ""); err != nil {
		lg.Error("Failed to send cherry-pick notification", zap.Error(err))
	}

	// Step 3: Wait for cherry-picked PRs to merge
	if err := waitForCherryPickedPRs(ctx, lg, request, environmentToCherryPickedPRs, workflowData); err != nil {
		return err
	}

	// Step 4: Trigger ALL builds in parallel
	serverBuildInfo, err := triggerAllServerBuilds(ctx, lg, request, workflowData)
	if err != nil {
		workflowData.Status = utils.StatusFailed
		return err
	}

	var workerBuildInfoByEnv map[string]map[string]*JobInfo
	if len(request.GetWorkers()) > 0 {
		workerBuildInfoByEnv, err = triggerAllWorkerBinaryBuilds(ctx, lg, request, workflowData)
		if err != nil {
			workflowData.Status = utils.StatusFailed
			return err
		}
	} else {
		workerBuildInfoByEnv = make(map[string]map[string]*JobInfo)
	}

	// Step 5: Wait for everything in parallel
	serverToAmiIdMap, err := waitForAllBuildsAndDeployments(ctx, lg, request, workflowData, serverBuildInfo, workerBuildInfoByEnv)
	if err != nil {
		workflowData.Status = determineErrorStatus(err)
		return err
	}

	lg.Info("All builds and deployments completed successfully", zap.Any("amiIDs", serverToAmiIdMap))

	workflowData.Status = utils.StatusCompleted
	// Send workflow completion notification
	if err = sendNotification(ctx, lg, request, jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_WORKFLOW_COMPLETION, utils.WorkflowCompletionData{
		Status:         workflowData.Status,
		RepoName:       request.GetRepoName(),
		PRNumber:       request.GetPrNumber(),
		SlackChannelId: request.GetSlackChannelId(),
	}, workflowData.StatusMessageTS); err != nil {
		lg.Error("Failed to send completion notification", zap.Error(err))
	}
	return nil
}

type ServerBuildInfo struct {
	BuildID     int64
	Status      string
	JobPath     string
	BuildURL    string
	ServerName  string
	Environment string
}

func triggerAllServerBuilds(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData) (map[string]*ServerBuildInfo, error) {
	lg.Debug("Starting server build triggering process")
	serverBuilds := make(map[string]*ServerBuildInfo)

	for _, param := range request.GetDeploymentParams() {
		env := param.GetEnvironment().String()
		isProd := param.GetEnvironment() == jarvisworkflowpb.Environment_ENV_PROD

		if isProd {
			for _, serverName := range request.GetServers() {
				jobPath := param.GetJenkinsJobPaths().GetServerJobPath()[0]
				lg.Info("Triggering AMI builds for servers", zap.Strings("serverNames", request.GetServers()))

				serverKey := fmt.Sprintf("%s-%s", env, serverName)
				lg.Info("Triggering build_ami job", zap.String("serverName", serverName), zap.String("jobPath", jobPath))

				buildID, buildURL, err := triggerBuild(ctx, lg, jobPath, map[string]string{
					"TARGET":     serverName,
					"APP_BRANCH": param.GetCherryPickBranch(),
					"ENV":        "qa",
					"USER_EMAIL": request.GetUserEmailId(),
				})

				if err != nil {
					updateBuildStatus(ctx, lg, request, workflowData, env, serverName, utils.StatusFailed, "")
					return nil, err
				}

				serverBuilds[serverKey] = &ServerBuildInfo{
					BuildID:     buildID,
					Status:      utils.StatusInProgress,
					JobPath:     jobPath,
					BuildURL:    buildURL,
					ServerName:  serverName,
					Environment: env,
				}

				updateBuildStatus(ctx, lg, request, workflowData, env, serverName, utils.StatusPending, buildURL)
				lg.Info("Build triggered", zap.String("serverName", serverName), zap.Int64("buildID", buildID))
			}
		} else {
			// QA deployments
			for _, jobPath := range param.GetJenkinsJobPaths().GetServerJobPath() {
				serverName := utils.ExtractServerNameFromJobPath(jobPath)
				serverKey := fmt.Sprintf("%s-%s", env, serverName)

				updateBuildStatus(ctx, lg, request, workflowData, env, serverName, utils.StatusPending, "")

				buildID, buildURL, err := triggerBuild(ctx, lg, jobPath, map[string]string{
					"repo_name":  request.GetRepoName(),
					"APP_BRANCH": param.GetCherryPickBranch(),
					"USER_EMAIL": request.GetUserEmailId(),
				})

				if err != nil {
					updateBuildStatus(ctx, lg, request, workflowData, env, serverName, utils.StatusFailed, buildURL)
					return nil, err
				}

				serverBuilds[serverKey] = &ServerBuildInfo{
					BuildID:     buildID,
					Status:      utils.StatusInProgress,
					JobPath:     jobPath,
					BuildURL:    buildURL,
					ServerName:  serverName,
					Environment: env,
				}

				updateBuildStatus(ctx, lg, request, workflowData, env, serverName, utils.StatusPending, buildURL)
				lg.Info("Build triggered", zap.String("jobPath", jobPath), zap.Int64("buildID", buildID))
			}
		}
	}

	return serverBuilds, nil
}

const allBuildsTimeout = 2 * time.Hour

func waitForAllBuildsAndDeployments(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, serverBuilds map[string]*ServerBuildInfo, workerBuilds map[string]map[string]*JobInfo) (map[string]string, error) {
	lg.Info("Starting parallel monitoring of all builds and deployments")
	startTime := workflow.Now(ctx)

	serverToAmiIdMap := make(map[string]string)
	deployedWorkers := make(map[string]bool)
	workerDeployments := make(map[string]map[string]*JobInfo) // env -> worker -> buildInfo
	workerPromotions := make(map[string]*JobInfo)

	// Initialize worker deployment tracking
	for _, param := range request.GetDeploymentParams() {
		env := param.GetEnvironment().String()
		workerDeployments[env] = make(map[string]*JobInfo)
	}

	for {
		// ===== TIMEOUT CHECK =====
		if workflow.Now(ctx).Sub(startTime) > allBuildsTimeout {
			lg.Error("Timeout waiting for builds and deployments")
			updateAllTimeoutStatuses(ctx, lg, request, workflowData, serverBuilds, workerBuilds, workerDeployments, workerPromotions)
			return nil, fmt.Errorf("timeout waiting for builds and deployments")
		}
		lg.Info("Checking build and deployment statuses", zap.Any("serverBuilds", serverBuilds))

		// ===== CHECK ALL SERVER BUILDS =====
		for _, buildInfo := range serverBuilds {
			if buildInfo.Status == utils.StatusInProgress {
				if err := checkAndUpdateServerStatus(ctx, lg, request, workflowData, buildInfo, &serverToAmiIdMap); err != nil {
					return nil, err
				}
			}
		}

		// ===== CHECK ALL WORKER BINARY BUILDS =====
		for env, workers := range workerBuilds {
			for workerName, buildInfo := range workers {
				_, hasDeployment := workerDeployments[env][workerName]
				_, hasPromotion := workerPromotions[workerName]
				if buildInfo.Status == utils.StatusInProgress || (!hasDeployment && !hasPromotion) {
					if err := checkAndUpdateWorkerBinaryStatus(ctx, lg, request, workflowData, env, workerName, buildInfo, deployedWorkers, workerDeployments, workerPromotions); err != nil {
						return nil, err
					}
				}
			}
		}

		// ===== CHECK ALL WORKER DEPLOYMENTS =====
		for env, workers := range workerDeployments {
			for workerName, deployInfo := range workers {
				if deployInfo != nil && deployInfo.Status == utils.StatusInProgress {
					if err := checkAndUpdateWorkerDeploymentStatus(ctx, lg, request, workflowData, env, workerName, deployInfo, workerPromotions); err != nil {
						return nil, err
					}
				}
			}
		}

		// ===== CHECK ALL WORKER PROMOTIONS =====
		for workerName, promoteInfo := range workerPromotions {
			if promoteInfo.Status == utils.StatusInProgress {
				if err := checkAndUpdateWorkerPromotionStatus(ctx, lg, request, workflowData, workerName, promoteInfo); err != nil {
					return nil, err
				}
			}
		}
		// ===== CHECK IF EVERYTHING IS COMPLETE =====
		if allComplete := checkAllComplete(serverBuilds, workerBuilds, workerDeployments, workerPromotions); allComplete {
			lg.Info("All builds and deployments completed successfully")
			break
		}

		// Sleep once per full cycle
		if err := workflow.Sleep(ctx, utils.PollingInterval); err != nil {
			return nil, err
		}
	}

	return serverToAmiIdMap, nil
}

func checkAndUpdateServerStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, buildInfo *ServerBuildInfo, serverToAmiIdMap *map[string]string) error {
	var buildStatus jarvisactivitypb.GetJenkinsJobStatusResponse
	err := activitypkg.Execute(ctx, jarvisns.GetJenkinsJobStatus, &buildStatus, &jarvisactivitypb.GetJenkinsJobStatusRequest{
		BuildId: buildInfo.BuildID,
		JobPath: buildInfo.JobPath,
	})

	if err != nil {
		if strings.Contains(err.Error(), "404") {
			lg.Warn("Server build not found yet, likely still queued or starting",
				zap.String("server", buildInfo.ServerName),
				zap.String("jobPath", buildInfo.JobPath),
				zap.Int64("buildId", buildInfo.BuildID))
			return nil // Continue monitoring
		}

		lg.Error("Failed to check server build status", zap.Error(err))
		updateBuildStatus(ctx, lg, request, workflowData, buildInfo.Environment, buildInfo.ServerName, utils.StatusFailed, buildInfo.BuildURL)
		return nil
	}

	switch buildStatus.GetStatus() {
	case utils.StatusSuccess:
		buildInfo.Status = utils.StatusSuccess
		updateBuildStatus(ctx, lg, request, workflowData, buildInfo.Environment, buildInfo.ServerName, utils.StatusSuccess, buildInfo.BuildURL)

		// Get AMI ID for prod builds
		var amiID string
		if buildInfo.Environment == jarvisworkflowpb.Environment_ENV_PROD.String() {
			var buildAMI jarvisactivitypb.GetBuildAMIResponse
			err := activitypkg.Execute(ctx, jarvisns.GetBuildAMI, &buildAMI, &jarvisactivitypb.GetBuildAMIRequest{
				BuildId: buildInfo.BuildID,
				JobPath: buildInfo.JobPath,
			})
			if err != nil {
				lg.Error("Failed to get AMI ID", zap.Error(err))
				return fmt.Errorf("failed to get AMI ID for server %s: %w", buildInfo.ServerName, err)
			}
			amiID = buildAMI.GetAmiId()
			(*serverToAmiIdMap)[buildInfo.ServerName] = amiID
			lg.Info("Server build successful with AMI", zap.String("server", buildInfo.ServerName), zap.String("amiId", amiID))
			promoteErr := handlePromoteImages(ctx, lg, *serverToAmiIdMap)
			if promoteErr != nil {
				lg.Error("Failed to promote images", zap.Error(promoteErr))
				return promoteErr
			}
			// Send deployment notification
			deploymentMsg := utils.FormatServerProductionDeploymentMessage(request.RepoName, request.PrNumber, *serverToAmiIdMap)
			if _, err := sendSlackMessage(ctx, lg, request, deploymentMsg, jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_PRODUCTION_DEPLOYMENT, ""); err != nil {
				lg.Error("Failed to send server deployment notification", zap.Error(err))
			}
		}
	default:
		buildInfo.Status = buildStatus.GetStatus()
		updateBuildStatus(ctx, lg, request, workflowData, buildInfo.Environment, buildInfo.ServerName, buildStatus.GetStatus(), buildInfo.BuildURL)
	}

	return nil
}

func updateWorkerStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, env, worker string, stage string, status string, buildURL ...string) {
	if workflowData.DeploymentStatus == nil || workflowData.DeploymentStatus.WorkerJobs == nil {
		return
	}

	workerJobs, exists := workflowData.DeploymentStatus.WorkerJobs[env]
	if !exists {
		workerJobs = make(map[string]*utils.WorkerJobInfo)
		workflowData.DeploymentStatus.WorkerJobs[env] = workerJobs
	}

	workerInfo, exists := workerJobs[worker]
	if !exists {
		workerInfo = &utils.WorkerJobInfo{}
	}

	switch stage {
	case utils.WorkerStageBinary:
		workerInfo.BinaryBuildStatus = status
		if len(buildURL) > 0 {
			workerInfo.BinaryBuildURL = buildURL[0]
		}
	case utils.WorkerStageDeployment:
		workerInfo.DeploymentStatus = status
		if len(buildURL) > 0 {
			workerInfo.DeploymentURL = buildURL[0]
		}
	case utils.WorkerStagePromotion:
		workerInfo.PromotionStatus = status
		if len(buildURL) > 0 {
			workerInfo.PromotionURL = buildURL[0]
		}
	}

	// Set emoji based on status
	workerInfo.Emoji = utils.GetEmojiForStatus(status)
	workerJobs[worker] = workerInfo
	workflowData.DeploymentStatus.WorkerJobs[env] = workerJobs

	// Update the status message
	err := updateStatusMessage(ctx, lg, request, workflowData, "")
	if err != nil {
		lg.Error("Failed to update status message", zap.Error(err))
	}
}

func checkAndUpdateWorkerBinaryStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, env string, workerName string, buildInfo *JobInfo, deployedWorkers map[string]bool, workerDeployments map[string]map[string]*JobInfo, workerPromotions map[string]*JobInfo) error {
	var buildStatus jarvisactivitypb.GetJenkinsJobStatusResponse
	err := activitypkg.Execute(ctx, jarvisns.GetJenkinsJobStatus, &buildStatus, &jarvisactivitypb.GetJenkinsJobStatusRequest{
		BuildId: buildInfo.BuildID,
		JobPath: buildInfo.JobPath,
	})

	if err != nil {
		if strings.Contains(err.Error(), "404") {
			lg.Warn("Worker binary build not found yet, likely still queued or starting",
				zap.String("worker", workerName),
				zap.String("jobPath", buildInfo.JobPath),
				zap.Int64("buildId", buildInfo.BuildID))
			return nil
		}

		lg.Error("Failed to check worker binary build status", zap.Error(err))
		updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageBinary, utils.StatusFailed)
		return nil
	}

	switch buildStatus.GetStatus() {
	case utils.StatusSuccess:
		buildInfo.Status = utils.StatusSuccess
		updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageBinary, utils.StatusSuccess, buildInfo.JobURL)
		lg.Info("Worker binary build successful", zap.String("env", env), zap.String("worker", workerName), zap.Int64("buildID", buildInfo.BuildID), zap.Any("deploydWorkers", deployedWorkers))
		if !deployedWorkers[env+"::"+workerName] {
			deployedWorkers[env+"::"+workerName] = true
			if env == jarvisworkflowpb.Environment_ENV_QA.String() {
				// Trigger QA deployment only
				if err := triggerWorkerDeploymentForAllEnvs(ctx, lg, request, workflowData, workerName, buildInfo, workerDeployments); err != nil {
					lg.Error("Failed to trigger worker deployment", zap.Error(err))
					return nil
				}
			}
			if env == jarvisworkflowpb.Environment_ENV_PROD.String() {
				// Trigger prod promotion only
				if err := triggerWorkerPromotionIfProd(ctx, lg, request, workflowData, workerName, buildInfo, workerPromotions); err != nil {
					lg.Error("Failed to trigger worker promotion", zap.Error(err))
					return nil
				}
			}
		}

	case utils.StatusFailed:
		buildInfo.Status = utils.StatusFailed
		updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageBinary, utils.StatusFailed, buildInfo.JobURL)
		return nil

	case utils.StatusAborted:
		buildInfo.Status = utils.StatusAborted
		updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageBinary, utils.StatusAborted, buildInfo.JobURL)
		return nil
	}

	return nil
}

func triggerWorkerDeploymentForAllEnvs(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, workerName string, buildInfo *JobInfo, workerDeployments map[string]map[string]*JobInfo) error {
	prodEnv := jarvisworkflowpb.Environment_ENV_PROD.String()

	for _, param := range request.GetDeploymentParams() {
		env := param.GetEnvironment().String()

		// Skip prod environment completely - workers only get promoted in prod
		if env == prodEnv {
			lg.Info("Skipping worker deployment for prod environment - workers only get promoted", zap.String("worker", workerName))
			continue
		}

		for _, workerJobPath := range param.GetJenkinsJobPaths().GetWorkerJobPaths() {
			if workerJobPath.GetWorkerName() == workerName {
				lg.Info("Triggering worker deployment", zap.String("worker", workerName), zap.String("env", env))
				updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageDeployment, utils.StatusPending)

				deployBuildID, deployURL, err := triggerBuild(ctx, lg, workerJobPath.GetDeployPath(), map[string]string{
					"IMAGE_BUILD_ID": fmt.Sprintf("worker-%d", buildInfo.BuildID),
					"USER_EMAIL":     request.GetUserEmailId(),
				})

				if err != nil {
					updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageDeployment, utils.StatusFailed, deployURL)
					return fmt.Errorf("failed to trigger deployment for worker %s: %w", workerName, err)
				}

				deployInfo := &JobInfo{
					BuildID: deployBuildID,
					Status:  utils.StatusInProgress,
					JobPath: workerJobPath.GetDeployPath(),
					JobURL:  deployURL,
				}
				workerDeployments[env][workerName] = deployInfo

				updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageDeployment, utils.StatusPending, deployURL)
				break
			}
		}
	}
	return nil
}

func triggerWorkerPromotionIfProd(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, workerName string, buildInfo *JobInfo, workerPromotions map[string]*JobInfo) error {
	prodEnv := jarvisworkflowpb.Environment_ENV_PROD.String()

	for _, param := range request.GetDeploymentParams() {
		if param.GetEnvironment() == jarvisworkflowpb.Environment_ENV_PROD {
			for _, workerJobPath := range param.GetJenkinsJobPaths().GetWorkerJobPaths() {
				if workerJobPath.GetWorkerName() == workerName && workerJobPath.GetImagePromotionPath() != "" {
					lg.Info("Triggering worker promotion for prod", zap.String("worker", workerName))

					updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, utils.StatusPending)

					promoteBuildID, promoteURL, err := triggerBuild(ctx, lg, workerJobPath.GetImagePromotionPath(), map[string]string{
						"IMAGE":      workerName,
						"TAG":        fmt.Sprintf("worker-%d", buildInfo.BuildID),
						"ENV":        "deploy",
						"USER_EMAIL": request.GetUserEmailId(),
					})

					if err != nil {
						updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, utils.StatusFailed)
						return fmt.Errorf("failed to trigger image promotion for worker %s: %w", workerName, err)
					}

					workerPromotions[workerName] = &JobInfo{
						BuildID: promoteBuildID,
						Status:  utils.StatusInProgress,
						JobPath: workerJobPath.GetImagePromotionPath(),
						JobURL:  promoteURL,
					}

					updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, utils.StatusInProgress, promoteURL)
					return nil
				}
			}
		}
	}
	return nil
}

func checkAndUpdateWorkerDeploymentStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, env, workerName string, deployInfo *JobInfo, workerPromotions map[string]*JobInfo) error {
	var deployStatus jarvisactivitypb.GetJenkinsJobStatusResponse
	err := activitypkg.Execute(ctx, jarvisns.GetJenkinsJobStatus, &deployStatus, &jarvisactivitypb.GetJenkinsJobStatusRequest{
		BuildId: deployInfo.BuildID,
		JobPath: deployInfo.JobPath,
	})

	if err != nil {
		if strings.Contains(err.Error(), "404") {
			lg.Warn("Worker deployment not found yet, likely still queued or starting",
				zap.String("worker", workerName),
				zap.String("env", env),
				zap.String("jobPath", deployInfo.JobPath),
				zap.Int64("buildId", deployInfo.BuildID))
			return nil
		}

		lg.Error("Failed to check worker deployment status", zap.Error(err))
		updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageDeployment, utils.StatusFailed)
		return nil
	}
	deployInfo.Status = deployStatus.GetStatus()
	updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageDeployment, deployStatus.GetStatus())

	return nil
}
func checkAndUpdateWorkerPromotionStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, workerName string, promoteInfo *JobInfo) error {
	prodEnv := jarvisworkflowpb.Environment_ENV_PROD.String()

	var promoteStatus jarvisactivitypb.GetJenkinsJobStatusResponse
	err := activitypkg.Execute(ctx, jarvisns.GetJenkinsJobStatus, &promoteStatus, &jarvisactivitypb.GetJenkinsJobStatusRequest{
		BuildId: promoteInfo.BuildID,
		JobPath: promoteInfo.JobPath,
	})

	if err != nil {
		if strings.Contains(err.Error(), "404") {
			lg.Warn("Worker promotion not found yet, likely still queued or starting",
				zap.String("worker", workerName),
				zap.String("jobPath", promoteInfo.JobPath),
				zap.Int64("buildId", promoteInfo.BuildID))
			return nil
		}

		lg.Error("Failed to check worker promotion status", zap.Error(err))
		updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, utils.StatusFailed)
		return nil
	}

	switch promoteStatus.GetStatus() {
	case utils.StatusSuccess:
		promoteInfo.Status = utils.StatusSuccess
		updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, utils.StatusSuccess)

		// If promotion is successful, send deployment notification
		if promoteInfo.Status == utils.StatusSuccess {
			workerToBuildIdMap := map[string]int64{
				workerName: promoteInfo.BuildID,
			}

			// Send deployment notification
			deploymentMsg := utils.FormatWorkerProductionDeploymentMessage(request.RepoName, request.PrNumber, workerToBuildIdMap)
			if _, err := sendSlackMessage(ctx, lg, request, deploymentMsg, jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_PRODUCTION_DEPLOYMENT, ""); err != nil {
				lg.Error("Failed to send worker deployment notification", zap.Error(err))
			}
		}
	default:
		promoteInfo.Status = promoteStatus.GetStatus()
		updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, promoteStatus.GetStatus())
	}
	return nil
}

func checkAllComplete(serverBuilds map[string]*ServerBuildInfo, workerBuilds map[string]map[string]*JobInfo, workerDeployments map[string]map[string]*JobInfo, workerPromotions map[string]*JobInfo) bool {
	// Check all server builds
	for _, buildInfo := range serverBuilds {
		if buildInfo.Status == utils.StatusInProgress {
			return false
		}
	}

	// Check all worker binary builds
	for _, workers := range workerBuilds {
		for _, buildInfo := range workers {
			if buildInfo.Status == utils.StatusInProgress {
				return false
			}
		}
	}
	// Check all worker deployments
	for _, workers := range workerDeployments {
		for _, deployInfo := range workers {
			if deployInfo != nil && deployInfo.Status == utils.StatusInProgress {
				return false
			}
		}
	}

	// Check all worker promotions
	for _, promoteInfo := range workerPromotions {
		if promoteInfo.Status == utils.StatusInProgress {
			return false
		}
	}

	return true
}

func updateAllTimeoutStatuses(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, serverBuilds map[string]*ServerBuildInfo, workerBuilds map[string]map[string]*JobInfo, workerDeployments map[string]map[string]*JobInfo, workerPromotions map[string]*JobInfo) {
	prodEnv := jarvisworkflowpb.Environment_ENV_PROD.String()

	// Update all in-progress items to timeout status
	for _, buildInfo := range serverBuilds {
		if buildInfo.Status == utils.StatusInProgress {
			updateBuildStatus(ctx, lg, request, workflowData, buildInfo.Environment, buildInfo.ServerName, utils.StatusFailed, buildInfo.BuildURL)
		}
	}
	for env, workers := range workerBuilds {
		for workerName, buildInfo := range workers {
			if buildInfo.Status == utils.StatusInProgress {
				updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageBinary, utils.StatusTimeout)
			}
		}
	}

	for env, workers := range workerDeployments {
		for workerName, deployInfo := range workers {
			if deployInfo != nil && deployInfo.Status == utils.StatusInProgress {
				updateWorkerStatus(ctx, lg, request, workflowData, env, workerName, utils.WorkerStageDeployment, utils.StatusTimeout)
			}
		}
	}

	for workerName, promoteInfo := range workerPromotions {
		if promoteInfo.Status == utils.StatusInProgress {
			updateWorkerStatus(ctx, lg, request, workflowData, prodEnv, workerName, utils.WorkerStagePromotion, utils.StatusTimeout)
		}
	}
}

// waitForPRMerge waits for the PR to be merged.
func waitForPRMerge(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData) error {
	lg.Debug("Waiting for PR to merge", zap.Int64("prNumber", request.GetPrNumber()))
	startTime := workflow.Now(ctx)

	for {
		if err := checkTimeout(ctx, lg, startTime, utils.PrMergeTimeout, "PR merge"); err != nil {
			updateStatusOnError(ctx, lg, request, workflowData, "PR Merge", "Timeout waiting for PR to be merged", utils.StatusEmojiFailed, "Timeout waiting for merge")
			return err
		}

		var prMergeStatus jarvisactivitypb.GetPRMergeStatusResponse
		if err := activitypkg.Execute(ctx, jarvisns.GetPRMergeStatus, &prMergeStatus, &jarvisactivitypb.GetPRMergeStatusRequest{
			PrNumber: request.GetPrNumber(),
			RepoName: request.GetRepoName(),
		}); err != nil {
			updateStatusOnError(ctx, lg, request, workflowData, "PR Merge", fmt.Sprintf("Failed to check PR status: %s", err.Error()), utils.StatusEmojiFailed, "Error checking status")
			return err
		}

		switch prMergeStatus.GetStatus() {
		case utils.StatusMerged:
			updatePRStatus(ctx, lg, request, workflowData, utils.StatusEmojiSuccess, "Merged")
			lg.Info("PR merged successfully", zap.Int64("prNumber", request.GetPrNumber()))
			return nil
		case utils.StatusClosed:
			err := errors.New("PR is closed and not merged to master")
			updateStatusOnError(ctx, lg, request, workflowData, "PR Merge", "PR was closed without being merged", utils.StatusEmojiFailed, "Closed without merge")
			return err
		default:
			lg.Debug("PR not merged yet", zap.String("status", prMergeStatus.GetStatus()))
			updatePRStatus(ctx, lg, request, workflowData, utils.StatusEmojiPending, fmt.Sprintf("Waiting for merge (%s)", prMergeStatus.GetStatus()))
			if err := workflow.Sleep(ctx, utils.PollingInterval); err != nil {
				return err
			}
		}
	}
}

func triggerAllWorkerBinaryBuilds(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData) (map[string]map[string]*JobInfo, error) {
	workerBuilds := make(map[string]map[string]*JobInfo) // env -> worker -> buildInfo
	// Map to deduplicate builds if branches are the same
	branchWorkerToBuild := make(map[string]*JobInfo) // key: branch+worker

	for _, param := range request.GetDeploymentParams() {
		env := param.GetEnvironment().String()
		if _, ok := workerBuilds[env]; !ok {
			workerBuilds[env] = make(map[string]*JobInfo)
		}
		for _, workerJobPath := range param.GetJenkinsJobPaths().GetWorkerJobPaths() {
			worker := workerJobPath.GetWorkerName()
			branch := param.GetCherryPickBranch()
			key := branch + "::" + worker

			// If already built for this branch+worker, reuse
			if buildInfo, exists := branchWorkerToBuild[key]; exists {
				workerBuilds[env][worker] = buildInfo
				updateWorkerStatus(ctx, lg, request, workflowData, env, worker, utils.WorkerStageBinary, buildInfo.Status, buildInfo.JobURL)
				continue
			}

			lg.Info("Triggering worker binary build", zap.String("worker", worker), zap.String("env", env))
			updateWorkerStatus(ctx, lg, request, workflowData, env, worker, utils.WorkerStageBinary, utils.StatusPending)

			buildID, buildURL, err := triggerBuild(ctx, lg, workerJobPath.GetBinaryBuildPath(), map[string]string{
				"APP_BRANCH": branch,
				"USER_EMAIL": request.GetUserEmailId(),
			})
			if err != nil {
				updateWorkerStatus(ctx, lg, request, workflowData, env, worker, utils.WorkerStageBinary, utils.StatusFailed)
				return nil, fmt.Errorf("failed to trigger worker binary build: %w", err)
			}

			buildInfo := &JobInfo{
				BuildID: buildID,
				Status:  utils.StatusInProgress,
				JobPath: workerJobPath.GetBinaryBuildPath(),
				JobURL:  buildURL,
			}
			workerBuilds[env][worker] = buildInfo
			branchWorkerToBuild[key] = buildInfo
			updateWorkerStatus(ctx, lg, request, workflowData, env, worker, utils.WorkerStageBinary, utils.StatusInProgress, buildURL)
		}
	}
	return workerBuilds, nil
}

// handleCherryPicks creates cherry-pick PRs for each environment.
func handleCherryPicks(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData) (map[string]int64, error) {
	lg.Debug("Starting cherry-pick process")
	environmentToCherryPickedPRs := make(map[string]int64)

	// Create a map to track unique branches and their first environment
	branchToEnvMap := make(map[string]string)
	branchToCreatedPR := make(map[string]int64)

	for _, deploymentParam := range request.GetDeploymentParams() {
		env := deploymentParam.GetEnvironment().String()
		branch := deploymentParam.GetCherryPickBranch()

		// Record the first environment that uses this branch
		if firstEnv, exists := branchToEnvMap[branch]; !exists {
			branchToEnvMap[branch] = env
		} else {
			lg.Info("Multiple environments using same branch", zap.String("branch", branch), zap.String("firstEnv", firstEnv), zap.String("currentEnv", env))
		}
	}

	for _, deploymentParam := range request.GetDeploymentParams() {
		env := deploymentParam.GetEnvironment().String()
		branch := deploymentParam.GetCherryPickBranch()

		// Skip if this branch has already been processed
		if prNumber, exists := branchToCreatedPR[branch]; exists {
			lg.Info("Reusing existing cherry-pick PR", zap.String("environment", env), zap.String("branch", branch), zap.Int64("prNumber", prNumber))
			environmentToCherryPickedPRs[env] = prNumber
			updateCherryPickStatus(ctx, lg, request, workflowData, env, utils.StatusEmojiSuccess, "Created", prNumber)
			continue
		}

		lg.Debug("Processing cherry-pick", zap.String("environment", env))
		updateCherryPickStatus(ctx, lg, request, workflowData, env, utils.StatusEmojiPending, "Cherry-pick in progress")

		var cherryPickResult jarvisactivitypb.CherryPickResponse
		err := activitypkg.Execute(ctx, jarvisns.CherryPick, &cherryPickResult, &jarvisactivitypb.CherryPickRequest{
			Environment:      env,
			RepoName:         request.GetRepoName(),
			OriginalPrNumber: request.GetPrNumber(),
			TargetBranch:     branch,
			PrTitle:          stringPtr(request.GetDeploymentMetadata().GetPrTitle()),
			MonorailTicket:   stringPtr(request.GetDeploymentMetadata().GetMonorailTicket()),
		})
		if err != nil {
			updateCherryPickStatus(ctx, lg, request, workflowData, env, utils.StatusEmojiFailed, utils.StatusFailed)
			updateStatusOnError(ctx, lg, request, workflowData, fmt.Sprintf("Cherry-pick to %s", env), fmt.Sprintf("Cherry-pick error: %s", err.Error()), utils.StatusEmojiFailed, "Cherry-pick failed")
			return nil, err
		}

		cherryPickPR := cherryPickResult.GetCherryPickPrNumber()
		branchToCreatedPR[branch] = cherryPickPR
		environmentToCherryPickedPRs[env] = cherryPickPR
		updateCherryPickStatus(ctx, lg, request, workflowData, env, utils.StatusEmojiSuccess, "Created", cherryPickPR)
		lg.Info("Cherry-pick PR created", zap.String("environment", env), zap.String("branch", branch), zap.Int64("prNumber", cherryPickPR))
	}

	return environmentToCherryPickedPRs, nil
}

// waitForCherryPickedPRs waits for cherry-picked PRs to be merged.
func waitForCherryPickedPRs(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, environmentToCherryPickedPRs map[string]int64, workflowData *utils.WorkflowData) error {
	lg.Debug("Waiting for cherry-picked PRs to merge")
	startTime := workflow.Now(ctx)

	for {
		if err := checkTimeout(ctx, lg, startTime, utils.CherryPickTimeout, "cherry-pick PR merge"); err != nil {
			updateCherryPickTimeoutStatus(ctx, lg, request, workflowData, environmentToCherryPickedPRs)
			return err
		}

		allMerged, err := checkCherryPickPRStatus(ctx, lg, request, environmentToCherryPickedPRs, workflowData)
		if err != nil {
			return err
		}
		if allMerged {
			lg.Info("All cherry-picked PRs merged successfully")
			return nil
		}

		if err := workflow.Sleep(ctx, utils.PollingInterval); err != nil {
			return err
		}
	}
}

// updateStatusMessage updates the Slack status message.
func updateStatusMessage(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, logMsg string) error {
	lg.Debug("Updating status message", zap.String("message", logMsg))
	ts, err := utils.UpdateStatusMessage(ctx, request, workflowData)
	if err != nil {
		return err
	}
	workflowData.StatusMessageTS = ts
	return nil
}

// updateFinalStatus updates the final status message based on workflow outcome.
func updateFinalStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData) error {
	if workflowData.Status == utils.StatusInProgress {
		workflowData.Status = utils.StatusSuccess
	}
	return updateStatusMessage(ctx, lg, request, workflowData, "Final status update")
}

// updateStatusOnError updates status message on error.
func updateStatusOnError(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, stage, errorMsg, emoji, status string) {
	lg.Error("Error occurred", zap.String("stage", stage), zap.String("error", errorMsg))
	if workflowData.DeploymentStatus != nil && workflowData.DeploymentStatus.LatestError != nil {
		workflowData.DeploymentStatus.LatestError.Stage = stage
		workflowData.DeploymentStatus.LatestError.Message = errorMsg
		if stage == "PR Merge" {
			workflowData.DeploymentStatus.PRStatus.Emoji = emoji
			workflowData.DeploymentStatus.PRStatus.Status = status
		}
		if err := updateStatusMessage(ctx, lg, request, workflowData, fmt.Sprintf("Error in %s", stage)); err != nil {
			lg.Error("Failed to update status message on error", zap.Error(err))
		}
	}
}

// updatePRStatus updates PR status in workflow data.
func updatePRStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, emoji, status string) {
	if workflowData.DeploymentStatus != nil {
		workflowData.DeploymentStatus.PRStatus.Status = status
		workflowData.DeploymentStatus.PRStatus.Emoji = emoji
		if err := updateStatusMessage(ctx, lg, request, workflowData, fmt.Sprintf("PR status: %s", status)); err != nil {
			lg.Error("Failed to update PR status message", zap.Error(err))
		}
	}
}

// triggerBuild triggers a Jenkins build and returns build ID and URL.
func triggerBuild(ctx workflow.Context, lg log.Logger, jobPath string, params map[string]string) (int64, string, error) {
	var buildResult jarvisactivitypb.TriggerJenkinsJobResponse
	err := activitypkg.Execute(ctx, jarvisns.TriggerJenkinsJob, &buildResult, &jarvisactivitypb.TriggerJenkinsJobRequest{
		JobPath: jobPath,
		Params:  params,
	})
	if err != nil {
		lg.Error("Failed to trigger Jenkins build", zap.Error(err))
		return 0, "", err
	}

	buildID := buildResult.GetBuildId()
	// Build URL uses jobPath directly, excluding any server-specific suffixes for production
	buildURL := utils.FormatBuildURL(utils.JenkinsNonProdBaseURL, jobPath, buildID)
	lg.Debug("Generated build URL", zap.String("jobPath", jobPath), zap.Int64("buildID", buildID), zap.String("buildURL", buildURL))
	return buildID, buildURL, nil
}

// updateCherryPickStatus updates cherry-pick status in workflow data.
func updateCherryPickStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, env, emoji, status string, prNumber ...int64) {
	if workflowData.DeploymentStatus != nil && workflowData.DeploymentStatus.CherryPicks != nil {
		if cp, ok := workflowData.DeploymentStatus.CherryPicks[env]; ok {
			cp.Status = status
			cp.Emoji = emoji
			if len(prNumber) > 0 {
				cp.PRNumber = prNumber[0]
			}
			workflowData.DeploymentStatus.CherryPicks[env] = cp
			if err := updateStatusMessage(ctx, lg, request, workflowData, fmt.Sprintf("Cherry-pick %s: %s", env, status)); err != nil {
				lg.Error("Failed to update cherry-pick status", zap.Error(err))
			}
		}
	}
}

// updateCherryPickTimeoutStatus updates status for cherry-pick timeout.
func updateCherryPickTimeoutStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, environmentToCherryPickedPRs map[string]int64) {
	if workflowData.DeploymentStatus != nil && workflowData.DeploymentStatus.LatestError != nil {
		workflowData.DeploymentStatus.LatestError.Stage = "Cherry-pick PR Merge"
		workflowData.DeploymentStatus.LatestError.Message = "Timeout waiting for cherry-picked PRs to be merged"
		for env := range environmentToCherryPickedPRs {
			if cp, ok := workflowData.DeploymentStatus.CherryPicks[env]; ok && cp.Status != utils.StatusMerged {
				cp.Emoji = utils.StatusEmojiFailed
				cp.Status = "Timeout waiting for merge"
				workflowData.DeploymentStatus.CherryPicks[env] = cp
			}
		}
		if err := updateStatusMessage(ctx, lg, request, workflowData, "Cherry-pick timeout"); err != nil {
			lg.Error("Failed to update cherry-pick timeout status", zap.Error(err))
		}
	}
}

// updateBuildStatus updates build status in workflow data.
func updateBuildStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *utils.WorkflowData, env, serverName, status string, buildURL ...string) {
	if workflowData.DeploymentStatus == nil || workflowData.DeploymentStatus.BuildJobs == nil {
		return
	}

	jobsMap, exists := workflowData.DeploymentStatus.BuildJobs[env]
	if !exists {
		jobsMap = make(map[string]*utils.BuildJobInfo)
		workflowData.DeploymentStatus.BuildJobs[env] = jobsMap
	}

	job, exists := jobsMap[serverName]
	if !exists {
		job = &utils.BuildJobInfo{}
	}

	job.Emoji = utils.GetEmojiForStatus(status)
	job.Status = status
	if len(buildURL) > 0 {
		job.BuildURL = buildURL[0]
	}

	jobsMap[serverName] = job
	workflowData.DeploymentStatus.BuildJobs[env] = jobsMap

	if err := updateStatusMessage(ctx, lg, request, workflowData, fmt.Sprintf("Build %s: %s", serverName, status)); err != nil {
		lg.Error("Failed to update build status", zap.Error(err))
	}
}

// checkTimeout checks if the operation has timed out.
func checkTimeout(ctx workflow.Context, lg log.Logger, startTime time.Time, timeout time.Duration, stage string) error {
	if workflow.Now(ctx).Sub(startTime) > timeout {
		err := fmt.Errorf("timeout waiting for %s", stage)
		lg.Error("Timeout occurred", zap.String("stage", stage), zap.Error(err))
		return err
	}
	return nil
}

// determineErrorStatus determines the status based on the error message.
func determineErrorStatus(err error) string {
	if strings.Contains(err.Error(), "jenkins build was aborted") {
		return utils.StatusAborted
	}
	return utils.StatusFailed
}

// checkCherryPickPRStatus checks the status of cherry-picked PRs.
func checkCherryPickPRStatus(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, environmentToCherryPickedPRs map[string]int64, workflowData *utils.WorkflowData) (bool, error) {
	allMerged := true
	for env, prNumber := range environmentToCherryPickedPRs {
		var prStatus jarvisactivitypb.GetPRMergeStatusResponse
		err := activitypkg.Execute(ctx, jarvisns.GetPRMergeStatus, &prStatus, &jarvisactivitypb.GetPRMergeStatusRequest{
			PrNumber: prNumber,
			RepoName: request.GetRepoName(),
		})
		if err != nil {
			updateStatusOnError(ctx, lg, request, workflowData, fmt.Sprintf("Check %s PR status", env), fmt.Sprintf("Failed to check PR #%d status: %s", prNumber, err.Error()), utils.StatusEmojiFailed, "Status check failed")
			return false, err
		}

		if workflowData.DeploymentStatus != nil && workflowData.DeploymentStatus.CherryPicks != nil {
			if cp, ok := workflowData.DeploymentStatus.CherryPicks[env]; ok {
				previousStatus := cp.Status
				switch prStatus.GetStatus() {
				case utils.StatusMerged:
					cp.Status = utils.StatusMerged
					cp.Emoji = utils.StatusEmojiSuccess
				case utils.StatusClosed:
					cp.Status = "Closed without merge"
					cp.Emoji = utils.StatusEmojiFailed
				default:
					cp.Status = "Waiting for merge"
					cp.Emoji = utils.StatusEmojiPending
					allMerged = false
				}
				if previousStatus != cp.Status {
					workflowData.DeploymentStatus.CherryPicks[env] = cp
					if err := updateStatusMessage(ctx, lg, request, workflowData, fmt.Sprintf("Cherry-pick %s PR status: %s", env, cp.Status)); err != nil {
						lg.Error("Failed to update cherry-pick PR status", zap.Error(err))
					}
				}
			}
		}

		if prStatus.GetStatus() == utils.StatusClosed {
			err := errors.New("cherry-picked PR is closed without being merged")
			updateStatusOnError(ctx, lg, request, workflowData, fmt.Sprintf("Cherry-pick %s PR", env), fmt.Sprintf("CP PR #%d was closed without being merged", prNumber), utils.StatusEmojiFailed, "Closed without merge")
			return false, err
		}
	}
	return allMerged, nil
}

// sendNotification sends a Slack notification.
func sendNotification(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, notificationType jarvisactivitypb.NotificationType, data interface{}, threadTs string) error {
	lg.Debug("Sending notification", zap.String("type", notificationType.String()))
	messages, err := formatNotificationMessages(request, notificationType, data)
	if err != nil {
		lg.Error("Failed to format notification messages", zap.Error(err))
		return err
	}
	_, err = sendFormattedMessages(ctx, lg, request, messages, notificationType, threadTs)
	return err
}

// formatNotificationMessages formats messages for notifications.
func formatNotificationMessages(request *jarvisworkflowpb.PRDeploymentWorkflowRequest, notificationType jarvisactivitypb.NotificationType, data interface{}) ([]SlackMessage, error) {
	var messages []SlackMessage
	switch notificationType {
	case jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_CHERRY_PICK:
		cherryPickData, ok := data.(map[string]int64)
		if !ok {
			return nil, fmt.Errorf("invalid data type for cherry-pick notification: expected map[string]int64")
		}
		var qaPR, prodPR int64
		for _, deployParam := range request.GetDeploymentParams() {
			env := deployParam.GetEnvironment().String()
			if cherryPickPR, ok := cherryPickData[env]; ok {
				switch env {
				case jarvisworkflowpb.Environment_ENV_QA.String():
					qaPR = cherryPickPR
				case jarvisworkflowpb.Environment_ENV_PROD.String():
					prodPR = cherryPickPR
				}
			}
		}
		if qaPR > 0 || prodPR > 0 {
			message := utils.FormatCherryPickMessage(request, qaPR, prodPR, request.SlackChannelId)
			messages = append(messages, SlackMessage{
				EnvironmentKey: "",
				Message:        message,
			})
		}
	case jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_WORKFLOW_COMPLETION:
		completionData, ok := data.(utils.WorkflowCompletionData)
		if !ok {
			return nil, fmt.Errorf("invalid data type for workflow completion notification")
		}
		messages = append(messages, SlackMessage{
			EnvironmentKey: "completion",
			Message: utils.FormatWorkflowCompletionMessage(
				completionData.Status,
				completionData.RepoName,
				completionData.PRNumber,
				completionData.SlackChannelId,
			),
		})
	case jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_WORKFLOW_STATUS_UPDATE:
		statusData, ok := data.(*utils.PRDeploymentStatus)
		if !ok {
			return nil, fmt.Errorf("invalid data type for workflow status update")
		}
		messages = append(messages, SlackMessage{
			EnvironmentKey: "status",
			Message:        utils.FormatDeploymentStatusMessage(statusData),
		})
	case jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_PRODUCTION_DEPLOYMENT:
		serverToAmiIdMap, ok := data.(map[string]string)
		if !ok {
			return nil, fmt.Errorf("invalid data type for production deployment notification")
		}
		msg, err := utils.FormatProductionDeploymentMessage(request, serverToAmiIdMap)
		if err != nil {
			return nil, fmt.Errorf("failed to format production deployment message: %w", err)
		}
		messages = append(messages, SlackMessage{
			EnvironmentKey: "production",
			Message:        msg,
		})
	default:
		return nil, fmt.Errorf("unsupported notification type: %s", notificationType.String())
	}
	return messages, nil
}

// SlackMessage represents a formatted message to be sent to Slack.
type SlackMessage struct {
	EnvironmentKey string
	Message        string
}

// sendFormattedMessages sends formatted Slack messages.
func sendFormattedMessages(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, messages []SlackMessage, notificationType jarvisactivitypb.NotificationType, timestamp string) (string, error) {
	lg.Debug("Sending formatted messages", zap.Int("messageCount", len(messages)))
	var firstMessageTimestamp = timestamp
	for _, msg := range messages {
		ts, err := sendSlackMessage(ctx, lg, request, msg.Message, notificationType, firstMessageTimestamp)
		if err != nil {
			lg.Error("Failed to send notification", zap.String("environment", msg.EnvironmentKey), zap.Error(err))
			continue
		}
		if firstMessageTimestamp == "" {
			firstMessageTimestamp = ts
		}
	}
	return firstMessageTimestamp, nil
}

// sendSlackMessage sends a single Slack message.
func sendSlackMessage(ctx workflow.Context, lg log.Logger, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, message string, notificationType jarvisactivitypb.NotificationType, timestamp string) (string, error) {
	lg.Debug("Sending Slack message", zap.String("type", notificationType.String()))
	var notificationResult jarvisactivitypb.SendNotificationResponse
	channelId := request.GetSlackChannelId()
	if notificationType == jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_CHERRY_PICK {
		channelId = utils.CherrypickChannelId
	}
	err := activitypkg.Execute(ctx, jarvisns.SendNotification, &notificationResult, &jarvisactivitypb.SendNotificationRequest{
		ChannelId:   channelId,
		UserEmailId: request.GetUserEmailId(),
		Type:        notificationType,
		Message:     message,
		Timestamp:   timestamp,
	})
	if err != nil {
		return "", err
	}
	return notificationResult.GetTimestamp(), nil
}

// stringPtr returns a pointer to a string.
func stringPtr(s string) *string {
	return &s
}

// handlePromoteImages promotes AMIs to production.
func handlePromoteImages(ctx workflow.Context, lg log.Logger, serverToAmiIDMap map[string]string) error {
	if len(serverToAmiIDMap) == 0 {
		lg.Debug("No AMIs to promote")
		return nil
	}

	lg.Info("Promoting images to production", zap.Any("amiIDs", serverToAmiIDMap))
	var multiError []error
	// Promote each AMI to production
	for _, amiID := range serverToAmiIDMap {
		var promoteResult jarvisactivitypb.PromoteImageResponse
		err := activitypkg.Execute(ctx, jarvisns.PromoteImage, &promoteResult, &jarvisactivitypb.PromoteImageRequest{
			ImageIds:    []string{amiID},
			Environment: "qa", // Images are always shared from QA to prod
		})

		if err != nil {
			multiError = append(multiError, err)
			lg.Error("Failed to promote image", zap.String("amiID", amiID), zap.Error(err))
			continue
		}

		if promoteResult.GetStatus() != "SUCCESS" {
			lg.Warn("Image promotion did not succeed",
				zap.String("amiID", amiID),
				zap.String("status", promoteResult.GetStatus()))
			continue
		}

		lg.Info("Successfully promoted image to production", zap.String("amiID", amiID))
	}
	if len(multiError) > 0 {
		return errors.Join(multiError...)
	}
	return nil
}
