package jenkins

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"github.com/bndr/gojenkins"
	"github.com/pkg/errors"
)

type Client struct {
	jenkins *gojenkins.Jenkins
}

const (
	JenkinsBuildPollingTimeout = 30 * time.Minute
	SUCCESS                    = "SUCCESS"
	FAILURE                    = "FAILURE"
	ABORTED                    = "ABORTED"
	IN_PROGRESS                = "IN_PROGRESS"
)

// NewClient creates a new Jenkins client
func NewClient(ctx context.Context, jenkinsURL, username, apiToken string) (*Client, error) {
	jenkins := gojenkins.CreateJenkins(nil, jenkinsURL, username, apiToken)
	_, err := jenkins.Init(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to initialize Jenkins client")
	}

	return &Client{
		jenkins: jenkins,
	}, nil
}

// TriggerBuild triggers a build for the specified job with given parameters
func (c *Client) TriggerBuild(ctx context.Context, jobPath string, params map[string]string) (int64, error) {
	// added timeout to avoid infinite polling
	ctx, cancel := context.WithTimeout(ctx, JenkinsBuildPollingTimeout)
	defer cancel()

	// Get job from Jenkins folder path (e.g. job/V3_Deployment/job/qa/job/atlas)
	job, err := c.jenkins.GetJob(ctx, jobPath)
	if err != nil {
		return 0, errors.Wrap(err, "failed to get Jenkins job")
	}

	queueID, err := job.InvokeSimple(ctx, params)
	if err != nil {
		return 0, errors.Wrap(err, "failed to trigger Jenkins build")
	}

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	// get the build number after triggering the job
	for {
		select {
		case <-ctx.Done():
			return 0, errors.Wrap(ctx.Err(), "context cancelled or timed out while waiting for build number")

		case <-ticker.C:
			queueItem, err := c.jenkins.GetQueueItem(ctx, queueID)
			if err != nil {
				return 0, errors.Wrap(err, "failed to get queue item")
			}

			if queueItem.Raw.Executable.Number != 0 {
				return queueItem.Raw.Executable.Number, nil
			}
		}
	}
}

// GetBuildStatus gets the status of a build
func (c *Client) GetBuildStatus(ctx context.Context, buildID int64, jobPath string) (string, error) {
	// Get job from Jenkins folder path
	job, err := c.jenkins.GetJob(ctx, jobPath)
	if err != nil {
		return "", errors.Wrap(err, "failed to get Jenkins job")
	}

	// Get build using the build number
	build, err := job.GetBuild(ctx, buildID)
	if err != nil {
		return "", errors.Wrap(err, "failed to get Jenkins build")
	}

	// Poll for latest status
	_, err = build.Poll(ctx)
	if err != nil {
		return "", errors.Wrap(err, "failed to poll build status")
	}

	var status string
	if build.IsRunning(ctx) {
		status = IN_PROGRESS
	} else {
		result := build.GetResult()
		switch result {
		case SUCCESS:
			status = SUCCESS
		case FAILURE:
			status = FAILURE
		case ABORTED:
			status = ABORTED
		default:
			return "", fmt.Errorf("unknown build status %s", result)
		}
	}
	return status, nil
}

// GetBuildAMIFromConsole returns the AMI ID from the build console output
func (c *Client) GetBuildAMIFromConsole(ctx context.Context, buildID int64, jobPath string) (string, error) {
	// Get job from Jenkins folder path
	job, err := c.jenkins.GetJob(ctx, jobPath)
	if err != nil {
		return "", errors.Wrap(err, "failed to get Jenkins job")
	}

	// Get build using the build number
	build, err := job.GetBuild(ctx, buildID)
	if err != nil {
		return "", errors.Wrap(err, "failed to get Jenkins build")
	}

	if build == nil {
		return "", errors.New("build not found")
	}

	// Get console output
	output := build.GetConsoleOutput(ctx)
	if output == "" {
		return "", errors.New("empty console output")
	}

	// Use regex to find AMI ID
	re := regexp.MustCompile(`--resources\s+(ami-[a-f0-9]+)`)
	matches := re.FindStringSubmatch(output)
	if len(matches) < 2 {
		return "", errors.New("AMI ID not found in console output")
	}

	return matches[1], nil
}
