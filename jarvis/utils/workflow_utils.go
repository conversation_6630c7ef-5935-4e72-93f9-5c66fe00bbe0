package utils

import (
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activitypkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	jarvisns "github.com/epifi/be-common/pkg/epifitemporal/namespace/jarvis"

	jarvisactivitypb "github.com/epifi/gamma/api/jarvis/activity"
	jarvisworkflowpb "github.com/epifi/gamma/api/jarvis/workflow"
)

// WorkflowData stores and tracks information about the workflow
type WorkflowData struct {
	Status           string
	CherryPickPRs    map[string]int64
	StatusMessageTS  string
	BuildJobs        map[string]map[string]string
	DeploymentStatus *PRDeploymentStatus
}

// WorkflowCompletionData contains all data needed for workflow completion notification
type WorkflowCompletionData struct {
	Status         string
	RepoName       string
	PRNumber       int64
	CherryPickPRs  map[string]int64
	BuildJobs      map[string]map[string]string
	SlackChannelId string
}

// CherryPickFailureData contains data needed for cherry-pick failure notification
type CherryPickFailureData struct {
	TargetBranch string
	LogFilePath  string
}

// JenkinsFailureData contains data needed for Jenkins failure notification
type JenkinsFailureData struct {
	BuildURL string
	Status   string
}

// UpdateStatusMessage updates the workflow status message in Slack
func UpdateStatusMessage(ctx workflow.Context, request *jarvisworkflowpb.PRDeploymentWorkflowRequest, workflowData *WorkflowData) (string, error) {
	lg := workflow.GetLogger(ctx)

	if workflowData.DeploymentStatus == nil {
		lg.Error("DeploymentStatus is nil, cannot update message")
		return workflowData.StatusMessageTS, nil
	}

	// Send notification
	var notificationResult jarvisactivitypb.SendNotificationResponse
	err := activitypkg.Execute(ctx, jarvisns.SendNotification, &notificationResult, &jarvisactivitypb.SendNotificationRequest{
		ChannelId:   request.GetSlackChannelId(),
		UserEmailId: request.GetUserEmailId(),
		Type:        jarvisactivitypb.NotificationType_NOTIFICATION_TYPE_WORKFLOW_STATUS_UPDATE,
		Message:     FormatDeploymentStatusMessage(workflowData.DeploymentStatus),
		Timestamp:   workflowData.StatusMessageTS,
	})

	if err != nil {
		lg.Error("Failed to update status message", zap.Error(err))
		return workflowData.StatusMessageTS, err
	}

	return notificationResult.GetTimestamp(), nil
}

// InitializeWorkflowData creates and initializes a new WorkflowData struct
func InitializeWorkflowData(ctx workflow.Context, request *jarvisworkflowpb.PRDeploymentWorkflowRequest) *WorkflowData {
	// Get workflow info
	info := workflow.GetInfo(ctx)

	workflowData := &WorkflowData{
		Status:        "SUCCESS",
		CherryPickPRs: make(map[string]int64),
		BuildJobs:     make(map[string]map[string]string),
		DeploymentStatus: &PRDeploymentStatus{
			RepoName:   request.GetRepoName(),
			PRNumber:   request.GetPrNumber(),
			WorkflowID: info.WorkflowExecution.ID,
			RunID:      info.WorkflowExecution.RunID,
			PRStatus: StatusInfo{
				Emoji:  StatusEmojiPending,
				Status: "Waiting for merge",
			},
			CherryPicks: make(map[string]*CherryPickInfo),
			BuildJobs:   make(map[string]map[string]*BuildJobInfo),
			WorkerJobs:  make(map[string]map[string]*WorkerJobInfo),
			UserMention: FormatUserMention(request.GetUserEmailId()),
			DeploymentMetadata: &DeploymentMetadata{
				PrTitle:           request.GetDeploymentMetadata().GetPrTitle(),
				MonorailTicket:    request.GetDeploymentMetadata().GetMonorailTicket(),
				DevTested:         request.GetDeploymentMetadata().GetDevTested(),
				QATestingRequired: request.GetDeploymentMetadata().GetQaTestingRequired(),
				ChangeType:        request.GetDeploymentMetadata().GetChangeType(),
				VisibilityType:    request.GetDeploymentMetadata().GetVisibilityType(),
			},
			LatestError: &ErrorInfo{},
		},
	}

	// Initialize cherry-pick status placeholders for each environment
	for _, deployParam := range request.GetDeploymentParams() {
		env := deployParam.GetEnvironment().String()
		workflowData.DeploymentStatus.CherryPicks[env] = &CherryPickInfo{
			Emoji:  StatusEmojiPending,
			Branch: deployParam.GetCherryPickBranch(),
			Status: "Not started",
		}

		// Initialize build jobs placeholders
		workflowData.DeploymentStatus.BuildJobs[env] = make(map[string]*BuildJobInfo)
		isProd := deployParam.GetEnvironment() == jarvisworkflowpb.Environment_ENV_PROD

		if isProd {
			for _, serverName := range request.GetServers() {
				workflowData.DeploymentStatus.BuildJobs[env][serverName] = &BuildJobInfo{
					Emoji:  StatusEmojiPending,
					Status: "Not started",
				}
			}
		} else {
			// For QA, initialize build jobs based on Jenkins job paths
			for _, jobPath := range deployParam.GetJenkinsJobPaths().GetServerJobPath() {
				serverName := ExtractServerNameFromJobPath(jobPath)
				workflowData.DeploymentStatus.BuildJobs[env][serverName] = &BuildJobInfo{
					Emoji:  StatusEmojiPending,
					Status: "Not started",
				}
			}
		}
		workflowData.DeploymentStatus.WorkerJobs[env] = make(map[string]*WorkerJobInfo)
		for _, workerJobPath := range deployParam.GetJenkinsJobPaths().GetWorkerJobPaths() {
			workerName := workerJobPath.GetWorkerName()

			workerJobInfo := &WorkerJobInfo{
				Emoji:             StatusEmojiPending,
				Status:            "Not started",
				BinaryBuildStatus: "Not started",
				DeploymentStatus:  "Not started",
			}

			// Add promotion fields for prod environment
			if isProd {
				workerJobInfo.PromotionStatus = "Not started"
			}

			workflowData.DeploymentStatus.WorkerJobs[env][workerName] = workerJobInfo

		}
	}

	return workflowData
}
