package utils

import (
	"fmt"
	"strings"

	jarvisworkflowpb "github.com/epifi/gamma/api/jarvis/workflow"
)

// DeploymentMetadata contains information about the deployment for visibility and tracking
type DeploymentMetadata struct {
	PrTitle           string `json:"prTitle"`
	MonorailTicket    string `json:"monorailTicket"`
	ChangeType        string `json:"changeType"`
	VisibilityType    string `json:"visibilityType"`
	QATestingRequired bool   `json:"qaTestingRequired"`
	DevTested         bool   `json:"devTested"`
}

// PRDeploymentStatus holds the complete state of a deployment
type PRDeploymentStatus struct {
	// Header info
	RepoName           string
	PRNumber           int64
	DeploymentMetadata *DeploymentMetadata

	// Workflow info
	WorkflowID string
	RunID      string

	// PR status
	PRStatus StatusInfo

	// Environment statuses
	CherryPicks map[string]*CherryPickInfo
	BuildJobs   map[string]map[string]*BuildJobInfo
	WorkerJobs  map[string]map[string]*WorkerJobInfo

	// Error tracking
	LatestError *ErrorInfo

	// Metadata
	UserMention string
}

type StatusInfo struct {
	Emoji  string
	Status string
}

type CherryPickInfo struct {
	Emoji    string
	Branch   string
	PRNumber int64
	Status   string
}

type BuildJobInfo struct {
	Emoji    string
	Status   string
	BuildURL string
	AmiId    string
}

type WorkerJobInfo struct {
	Emoji             string
	Status            string
	BinaryBuildURL    string
	BinaryBuildStatus string
	DeploymentURL     string
	DeploymentStatus  string
	PromotionURL      string
	PromotionStatus   string
}

type ErrorInfo struct {
	Stage   string
	Message string
	LogLink string
}

// FormatDeploymentStatusMessage formats the complete status as a Slack message
func FormatDeploymentStatusMessage(status *PRDeploymentStatus) string {
	var sb strings.Builder

	// Header with workflow link
	workflowLink := fmt.Sprintf("%s/%s/%s", BaseTemporalURL, status.WorkflowID, status.RunID)
	sb.WriteString(fmt.Sprintf("*PR #%d Deployment Status* - (<%s/%s/pull/%d|View PR> | <%s|View Workflow>)\n",
		status.PRNumber, BaseGithubURL, status.RepoName, status.PRNumber, workflowLink))
	sb.WriteString("---\n")

	// PR Status
	sb.WriteString(fmt.Sprintf("*• PR Merge Status* - %s %s\n",
		status.PRStatus.Emoji, status.PRStatus.Status))

	// Cherry-picks
	if len(status.CherryPicks) > 0 {
		sb.WriteString("*• Cherry-Pick to Environments*\n")
		for env, cp := range status.CherryPicks {
			prLink := formatPRLink(status.RepoName, cp.PRNumber)
			sb.WriteString(fmt.Sprintf("   ◦ %s branch (`%s`):  %s %s %s\n",
				env, cp.Branch, cp.Emoji, cp.Status, prLink))
		}
		sb.WriteString("\n")
	}

	// Server Builds
	if hasAnyBuildJobs(status.BuildJobs) {
		sb.WriteString("*• Server Builds*\n")
		for env, jobs := range status.BuildJobs {
			if len(jobs) > 0 {
				envName := strings.ToUpper(env)
				sb.WriteString(fmt.Sprintf("   ◦ *%s*:\n", envName))
				for serverName, job := range jobs {
					linkText := "View"
					if job.BuildURL != "" {
						linkText = fmt.Sprintf("<%s|%s>", job.BuildURL, linkText)
					}
					sb.WriteString(fmt.Sprintf("      → %s - %s %s [%s]\n", serverName, job.Emoji, job.Status, linkText))

					// Show AMI ID for prod
					if env == "prod" && job.AmiId != "" {
						sb.WriteString(fmt.Sprintf("         AMI ID: `%s`\n", job.AmiId))
					}
				}
			}
		}
		sb.WriteString("\n")
	}

	// Worker Builds (JobInfo-based)
	if len(status.WorkerJobs) > 0 {
		sb.WriteString("*• Worker Builds*\n")
		sb.WriteString(FormatWorkerJobStatus(status.WorkerJobs))
		sb.WriteString("\n")
	}

	// Error details, if any
	if status.LatestError != nil && status.LatestError.Message != "" {
		sb.WriteString("---\n")
		sb.WriteString(fmt.Sprintf("*%s Error occurred in:* %s\n", StatusEmojiAlert, status.LatestError.Stage))
		sb.WriteString(fmt.Sprintf("> %s\n", status.LatestError.Message))
		if status.LatestError.LogLink != "" {
			sb.WriteString(fmt.Sprintf("<%s|📋 View Full Logs>\n", status.LatestError.LogLink))
		}
	}

	// Include user mention
	sb.WriteString("\ncc: " + status.UserMention)

	return sb.String()
}

// FormatServerProductionDeploymentMessage creates a separate message for server production deployment instructions
func FormatServerProductionDeploymentMessage(repoName string, prNumber int64, serverToAmiIdMap map[string]string) string {
	var sb strings.Builder

	// Header
	sb.WriteString(fmt.Sprintf("✅ Image promoted to production for PR #%d\n", prNumber))
	sb.WriteString(fmt.Sprintf("Repository: `%s`\n\n", repoName))

	// Manual deployment instructions
	sb.WriteString("🚀 **Manual Production Deployment Instructions:**\n")
	sb.WriteString("Use the following links to deploy each server manually in Jenkins.\n\n")

	// Generate deploy links for each server
	for serverName, amiId := range serverToAmiIdMap {
		deployURL := fmt.Sprintf("%sjob/V3_Deployment/job/prod/job/%s/parambuild/?IMAGE_ID=%s",
			BaseProdJenkinsURL, serverName, amiId)
		sb.WriteString(fmt.Sprintf("🔷 **%s**: <%s|Deploy Now>\n", serverName, deployURL))
	}

	return sb.String()
}

// FormatWorkerProductionDeploymentMessage creates a separate message for worker production deployment instructions
func FormatWorkerProductionDeploymentMessage(repoName string, prNumber int64, workerToBuildIdMap map[string]int64) string {
	var sb strings.Builder

	// Header
	sb.WriteString(fmt.Sprintf("✅ Worker images promoted to production for PR #%d\n", prNumber))
	sb.WriteString(fmt.Sprintf("Repository: `%s`\n\n", repoName))

	// Manual deployment instructions
	sb.WriteString("🚀 **Manual Production Deployment Instructions:**\n")
	sb.WriteString("Use the following links to deploy worker manually in Jenkins.\n\n")

	for workerName, buildId := range workerToBuildIdMap {
		imageBuildId := fmt.Sprintf("worker-%d", buildId)
		deployURL := fmt.Sprintf("%sjob/k8s/job/prod/job/%s/parambuild/?IMAGE_BUILD_ID=%s",
			BaseProdJenkinsURL, workerName, imageBuildId)
		sb.WriteString(fmt.Sprintf("🔷 **%s**: <%s|Deploy Now>\n", workerName, deployURL))
	}

	return sb.String()
}

// nolint:staticcheck
func GetEmojiForStatus(status string) string {
	switch {
	case status == StatusSuccess:
		return StatusEmojiSuccess
	case status == StatusFailed || status == StatusTimeout:
		return StatusEmojiFailed
	case status == StatusAborted:
		return StatusEmojiAborted
	default:
		return StatusEmojiPending
	}
}

// Helper to check if there are any build jobs
func hasAnyBuildJobs(buildJobs map[string]map[string]*BuildJobInfo) bool {
	for _, jobs := range buildJobs {
		if len(jobs) > 0 {
			return true
		}
	}
	return false
}

func formatBuildLink(url string) string {
	if url == "" {
		return "View"
	}
	return fmt.Sprintf("<%s|%s>", url, "View")
}

// Helper to format GitHub PR URLs
func formatPRLink(repoName string, prNumber int64) string {
	if prNumber == 0 {
		return ""
	}
	return fmt.Sprintf("<%s/%s/pull/%d|#%d>", BaseGithubURL, repoName, prNumber, prNumber)
}

// ExtractServerNameFromJobPath extracts server name from a job path
func ExtractServerNameFromJobPath(jobPath string) string {
	trimmedPath := strings.TrimSuffix(jobPath, "/")
	parts := strings.Split(trimmedPath, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return jobPath
}

func FormatBuildURL(baseURL string, jobPath string, buildID int64) string {
	return fmt.Sprintf("%s/job/%s%d/", baseURL, jobPath, buildID)
}

// FormatUserMention formats an email address as a Slack mention
func FormatUserMention(email string) string {
	if email == "" {
		return ""
	}

	username := email
	if idx := strings.Index(username, "@"); idx != -1 {
		username = username[:idx]
	}

	return fmt.Sprintf("<@%s>", username)
}

// FormatWorkerJobStatus to handle WorkerJobInfo-based worker builds
func FormatWorkerJobStatus(workerBuilds map[string]map[string]*WorkerJobInfo) string {
	var sb strings.Builder
	if len(workerBuilds) == 0 {
		return ""
	}

	// Get all unique worker names across environments
	allWorkers := make(map[string]bool)
	for _, workers := range workerBuilds {
		for workerName := range workers {
			if workerName != "" && workerName != "all" {
				allWorkers[workerName] = true
			}
		}
	}

	for workerName := range allWorkers {
		sb.WriteString(fmt.Sprintf("   ◦ %s\n", workerName))

		// QA Build
		if qaWorkers, exists := workerBuilds[jarvisworkflowpb.Environment_ENV_QA.String()]; exists {
			if qaJob, exists := qaWorkers[workerName]; exists {
				emoji := GetEmojiForStatus(qaJob.BinaryBuildStatus)
				link := formatBuildLink(qaJob.BinaryBuildURL)
				sb.WriteString(fmt.Sprintf("      → QA Binary Build: %s %s [%s]\n", emoji, qaJob.BinaryBuildStatus, link))
			}
		}
		// PROD Build
		if prodWorkers, exists := workerBuilds[jarvisworkflowpb.Environment_ENV_PROD.String()]; exists {
			if prodJob, exists := prodWorkers[workerName]; exists {
				emoji := GetEmojiForStatus(prodJob.BinaryBuildStatus)
				link := formatBuildLink(prodJob.BinaryBuildURL)
				sb.WriteString(fmt.Sprintf("      → PROD Binary Build: %s %s [%s]\n", emoji, prodJob.BinaryBuildStatus, link))
			}
		}
		// QA Deployment
		if qaWorkers, exists := workerBuilds[jarvisworkflowpb.Environment_ENV_QA.String()]; exists {
			if qaJob, exists := qaWorkers[workerName]; exists {
				emoji := GetEmojiForStatus(qaJob.DeploymentStatus)
				link := formatBuildLink(qaJob.DeploymentURL)
				sb.WriteString(fmt.Sprintf("      → QA Deployment: %s %s [%s]\n", emoji, qaJob.DeploymentStatus, link))
			}
		}
		// PROD Promotion
		if prodWorkers, exists := workerBuilds[jarvisworkflowpb.Environment_ENV_PROD.String()]; exists {
			if prodJob, exists := prodWorkers[workerName]; exists {
				emoji := GetEmojiForStatus(prodJob.PromotionStatus)
				link := formatBuildLink(prodJob.PromotionURL)
				sb.WriteString(fmt.Sprintf("      → Promotion: %s %s [%s]\n", emoji, prodJob.PromotionStatus, link))
			}
		}
	}
	return sb.String()
}
