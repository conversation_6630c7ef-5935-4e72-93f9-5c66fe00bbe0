package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/nulltypes"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/savings/extacct/dao/model"

	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
)

type BankAccountVerificationsCrdb struct {
	db types.EpifiCRDB
}

const (
	txnRetries = 3
)

var _ BankAccountVerificationsDao = &BankAccountVerificationsCrdb{}

func NewBankAccountVerificationsCrdb(db types.EpifiCRDB) *BankAccountVerificationsCrdb {
	return &BankAccountVerificationsCrdb{db: db}
}

func (crdb *BankAccountVerificationsCrdb) Create(ctx context.Context, bav *extacct.BankAccountVerification) (*extacct.BankAccountVerification, error) {
	defer metric_util.TrackDuration("savings/extacct/dao", "BankAccountVerificationsCrdb", "Create", time.Now())
	if bav.GetActorId() == "" || bav.GetOverallStatus() == extacct.OverallStatus_OVERALL_STATUS_UNSPECIFIED {
		return nil, fmt.Errorf("can not update without primary key")
	}

	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	request := convertToModel(bav)
	res := db.Create(request)
	if res.Error != nil {
		return nil, fmt.Errorf("error creating employment data entry: %w", res.Error)
	}
	return res.Statement.Model.(*model.BankAccountVerification).ConvertToProto(), nil
}

// nolint:funlen
// UpdateByFieldMask updates record fetched with id ordered by record update time descending
func (crdb *BankAccountVerificationsCrdb) UpdateByFieldMask(ctx context.Context, bav *extacct.BankAccountVerification, masks []extacct.BankAccountVerificationFieldMask) error {
	defer metric_util.TrackDuration("savings/extacct/dao", "BankAccountVerificationsCrdb", "UpdateByFieldMask", time.Now())
	if bav.GetId() == "" {
		return fmt.Errorf("can not update without id")
	}
	// DB Txn
	if txnErr := storageV2.RunCRDBIdempotentTxn(ctx, txnRetries, func(ctx context.Context) error {
		// If ctx is carrying the gorm transaction connection object, use it.
		// if not, use the gorm.DB connection provided by the dao.
		db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
		whereClause := &model.BankAccountVerification{
			Id: bav.GetId(),
		}
		dbEntry := &model.BankAccountVerification{}
		if err := db.Model(&model.BankAccountVerification{}).Where(whereClause).
			Where("deleted_at_unix = ?", 0).Take(dbEntry).Error; err != nil {
			if storageV2.IsRecordNotFoundError(err) {
				return err
			}
			logger.Error(ctx, "Error when fetching user from user id", zap.Error(err))
			return err
		}
		bavPb := dbEntry.ConvertToProto()
		bavPbOriginal := proto.Clone(bavPb)

		for _, m := range masks {
			switch m {
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_ACCOUNT_NUMBER:
				bavPb.AccountNumber = bav.GetAccountNumber()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_IFSC:
				bavPb.Ifsc = bav.GetIfsc()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK:
				bavPb.NameAtBank = bav.GetNameAtBank()
				bavPb.NameMatchData.NameAtBank = bav.GetNameAtBank()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_OVERALL_STATUS:
				bavPb.OverallStatus = bav.GetOverallStatus()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_VENDOR_STATUS:
				bavPb.VendorStatus = bav.GetVendorStatus()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_VENDOR_REQ_ID:
				bavPb.VendorReqId = bav.GetVendorReqId()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_VENDOR:
				bavPb.Vendor = bav.GetVendor()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_FAILURE_REASON:
				bavPb.FailureReason = bav.GetFailureReason()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE:
				bavPb.NameMatchData.UserGivenNameMatchScore = bav.GetNameMatchData().GetUserGivenNameMatchScore()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK_MATCH_SCORE:
				bavPb.NameMatchData.NameAtBankMatchScore = bav.GetNameMatchData().GetNameAtBankMatchScore()
			case extacct.BankAccountVerificationFieldMask_FIELD_MASK_KYC_NAME:
				bavPb.NameMatchData.KycName = bav.GetNameMatchData().GetKycName()
			default:
				logger.Info(ctx, fmt.Sprintf("unsupported mask in update bank account verification: %v", m))
				continue
			}
		}

		// optimisation to avoid unnecessary update statements
		if proto.Equal(bavPb, bavPbOriginal) {
			return nil
		}

		dbBankAccVerificationUpdate := convertToModel(bavPb)
		whereClause = &model.BankAccountVerification{
			Id: bav.GetId(),
		}

		if err := db.Model(&model.BankAccountVerification{}).Where("deleted_at_unix = ?", 0).
			Where(whereClause).Updates(&dbBankAccVerificationUpdate).Error; err != nil {
			logger.Error(ctx, "error in update bank account verification", zap.Error(err))
			return err
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in update bank account verification", zap.Error(txnErr))
		return txnErr
	}
	return nil
}

func (crdb *BankAccountVerificationsCrdb) GetByActorIdAndOverallStatus(ctx context.Context, actorId string, statuses []extacct.OverallStatus) ([]*extacct.BankAccountVerification, error) {
	defer metric_util.TrackDuration("savings/extacct/dao", "BankAccountVerificationsCrdb", "GetByActorIdAndOverallStatus", time.Now())
	if actorId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	for _, status := range statuses {
		if status == extacct.OverallStatus_OVERALL_STATUS_UNSPECIFIED {
			return nil, epifierrors.ErrInvalidArgument
		}
	}
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	whereClause := db.Where("actor_id = ?", nulltypes.NewNullString(actorId))
	if len(statuses) > 0 {
		whereClause = db.Where("actor_id = ? and overall_status IN (?)", nulltypes.NewNullString(actorId), statuses)
	}

	var bavList []*model.BankAccountVerification
	if err := db.Model(&model.BankAccountVerification{}).Where(whereClause).Where("deleted_at_unix = ?", 0).
		Order("updated_at desc").Find(&bavList).Error; err != nil {
		logger.Error(ctx, "error while fetching bank account verifications", zap.Error(err))
		return nil, err
	}
	if len(bavList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var bavListProto []*extacct.BankAccountVerification
	for _, bav := range bavList {
		bavListProto = append(bavListProto, bav.ConvertToProto())
	}
	return bavListProto, nil
}

func (crdb *BankAccountVerificationsCrdb) GetByAcctNoIfscAndOverallStatus(ctx context.Context, acctNo, ifsc string, statuses []extacct.OverallStatus) ([]*extacct.BankAccountVerification, error) {
	defer metric_util.TrackDuration("savings/extacct/dao", "BankAccountVerificationsCrdb", "GetByAcctNoIfscAndOverallStatus", time.Now())
	if acctNo == "" || ifsc == "" || len(statuses) == 0 {
		return nil, epifierrors.ErrInvalidArgument
	}
	for _, status := range statuses {
		if status == extacct.OverallStatus_OVERALL_STATUS_UNSPECIFIED {
			return nil, epifierrors.ErrInvalidArgument
		}
	}
	db := gormctxv2.FromContextOrDefault(ctx, crdb.db)
	var bavList []*model.BankAccountVerification
	if err := db.Model(&model.BankAccountVerification{}).
		Where("account_number = ? AND ifsc = ? AND overall_status IN (?)", nulltypes.NewNullString(acctNo), nulltypes.NewNullString(ifsc), statuses).
		Where("deleted_at_unix = ?", 0).Order("updated_at desc").Scan(&bavList).Error; err != nil {
		logger.Error(ctx, "error while fetching bank account verifications", zap.Error(err))
		return nil, err
	}
	if len(bavList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var bavListProto []*extacct.BankAccountVerification
	for _, bav := range bavList {
		bavListProto = append(bavListProto, bav.ConvertToProto())
	}
	return bavListProto, nil
}

func convertToModel(bav *extacct.BankAccountVerification) *model.BankAccountVerification {
	var (
		createdAt, updatedAt time.Time
	)
	if bav.GetCreatedAt() != nil {
		createdAt = bav.GetCreatedAt().AsTime()
	}
	if bav.GetUpdatedAt() != nil {
		updatedAt = bav.GetUpdatedAt().AsTime()
	}
	return &model.BankAccountVerification{
		Id:            bav.GetId(),
		ActorId:       bav.GetActorId(),
		AccountNumber: bav.GetAccountNumber(),
		Ifsc:          bav.GetIfsc(),
		NameAtBank:    bav.GetNameAtBank(),
		OverallStatus: bav.GetOverallStatus(),
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        bav.GetVendorStatus().GetCode(),
			Description: bav.GetVendorStatus().GetDescription(),
		},
		VendorReqId:   bav.GetVendorReqId(),
		Vendor:        bav.GetVendor(),
		FailureReason: bav.GetFailureReason(),
		NameMatchData: &extacct.NameMatchData{
			KycName:                 bav.GetNameMatchData().GetKycName(),
			UserGivenName:           bav.GetNameMatchData().GetUserGivenName(),
			UserGivenNameMatchScore: bav.GetNameMatchData().GetUserGivenNameMatchScore(),
			NameAtBank:              bav.GetNameMatchData().GetNameAtBank(),
			NameAtBankMatchScore:    bav.GetNameMatchData().GetNameAtBankMatchScore(),
		},
		Caller: &extacct.Caller{
			Source: bav.GetCaller().GetSource(),
			Email:  bav.GetCaller().GetEmail(),
		},
		CreatedAt:     createdAt,
		UpdatedAt:     updatedAt,
		DeletedAtUnix: bav.GetDeletedAtUnix(),
	}
}
