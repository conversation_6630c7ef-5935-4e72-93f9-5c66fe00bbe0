UPDATE loan_requests
SET
	status = 'LOAN_REQUEST_STATUS_CANCELLED',
	completed_at = NOW(),
	updated_at = NOW()
where
	status IN (
			   'LOAN_REQUEST_STATUS_CREATED',
			   'LOAN_REQUEST_STATUS_PENDING',
			   'LOAN_REQUEST_STATUS_VERIFICATION_FAILED',
	           'LOAN_REQUEST_STATUS_VERIFIED',
	           'LOAN_REQUEST_STATUS_INITIATED'
		) and
	type = 'LOAN_REQUEST_TYPE_CREATION' and
	vendor = 'ABFL' and
	loan_program = 'LOAN_PROGRAM_REAL_TIME_DISTRIBUTION'
  AND deleted_at is null
  AND completed_at is null;
