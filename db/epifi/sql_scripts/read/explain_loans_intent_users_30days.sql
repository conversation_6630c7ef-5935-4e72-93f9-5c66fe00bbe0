EXPLAIN
SELECT actor_id, computed_phone_number, acquisition_info, created_at
FROM users
WHERE acquisition_info->>'acquisitionIntent' = 'ACQUISITION_INTENT_PERSONAL_LOANS'
  AND deleted_at_unix = 0
  AND updated_at >= '2025-05-01 00:00:00+05:30'
  AND created_at >= '2025-05-01 00:00:00+05:30'
  AND updated_at <= '2025-06-01 23:59:59.999999999+05:30'
  AND created_at <= '2025-06-01 23:59:59.999999999+05:30';
