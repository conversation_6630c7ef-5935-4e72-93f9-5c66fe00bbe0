--> Migration Version: 775 

CREATE TABLE public.schema_lock (
	lock_id INT8 NOT NULL,
	CONSTRAINT schema_lock_pkey PRIMARY KEY (lock_id ASC)
);
CREATE TABLE public.schema_migrations (
	version INT8 NOT NULL,
	dirty BOOL NOT NULL,
	CONSTRAINT schema_migrations_pkey PRIMARY KEY (version ASC)
);
CREATE TABLE public.aa_consents (
	id STRING NOT NULL,
	status STRING NULL,
	start TIMESTAMPTZ NULL,
	expiry TIMESTAMPTZ NULL,
	data_life STRING NULL,
	frequency STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX aa_consents_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.actors (
	id STRING NOT NULL,
	type STRING NOT NULL,
	entity_id STRING NULL,
	name STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	ownership STRING NULL DEFAULT 'EPIFI_TECH':::STRING,
	user_layer INT2 NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX actors_entity_id_key (entity_id ASC),
	INDEX actors_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.actors.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store actors ownership.", "ref": "api.types.ownership.proto"}';
COMMENT ON COLUMN public.actors.user_layer IS '{"comment": "user_layer column stores number in the range [0..99] using FNV-1 hash algorithm on actor_id. Any change in hashing algorithm or the range should recompute the values of user_layer column for all the actors. This is used for A/B experiment discussed in this doc https://docs.google.com/document/d/1vkLCtDvirU_jiYM0SmGBNu3r6ibEqy7-jSPl29RlJkY"}';
CREATE TABLE public.connected_accounts (
	actor_id STRING NOT NULL,
	aa_ref_number STRING NOT NULL,
	masked_account_number STRING NOT NULL,
	fip_id STRING NOT NULL,
	source STRING NOT NULL,
	consent_handle STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, aa_ref_number ASC),
	UNIQUE INDEX connected_accounts_aa_ref_number_key (aa_ref_number ASC),
	INDEX connected_accounts_auto_index_fk_consent_handle_ref_aa_consents (consent_handle ASC),
	INDEX connected_accounts_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.payment_instruments (
	id STRING NOT NULL,
	type STRING NOT NULL,
	verified_name STRING NULL,
	identifier JSONB NULL,
	state STRING NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	deleted_at TIMESTAMP NULL,
	capabilities JSONB NOT NULL DEFAULT '{}':::JSONB,
	spam_count INT8 NOT NULL DEFAULT 0:::INT8,
	computed_unique_card STRING NULL AS (CASE WHEN ((((identifier->'card':::STRING)->>'secure_card_number':::STRING) IS NOT NULL) AND (((identifier->'card':::STRING)->>'expiry':::STRING) IS NOT NULL)) AND (((identifier->'card':::STRING)->>'name':::STRING) IS NOT NULL) THEN concat((identifier->'card':::STRING)->>'secure_card_number':::STRING, '_':::STRING, (identifier->'card':::STRING)->>'expiry':::STRING, '_':::STRING, (identifier->'card':::STRING)->>'name':::STRING) ELSE NULL END) STORED,
	issuer_classification STRING NOT NULL,
	computed_unique_account STRING NULL AS (CASE WHEN ((type = 'BANK_ACCOUNT':::STRING) AND (((identifier->'account':::STRING)->>'actual_account_number':::STRING) IS NOT NULL)) AND (((identifier->'account':::STRING)->>'ifsc_code':::STRING) IS NOT NULL) THEN concat((identifier->'account':::STRING)->>'actual_account_number':::STRING, '_':::STRING, (identifier->'account':::STRING)->>'ifsc_code':::STRING) WHEN ((((type = 'PARTIAL_BANK_ACCOUNT':::STRING) OR (type = 'GENERIC':::STRING)) AND (((identifier->'account':::STRING)->>'actual_account_number':::STRING) IS NOT NULL)) AND (((identifier->'account':::STRING)->>'name':::STRING) IS NOT NULL)) AND (((identifier->'account':::STRING)->>'ifsc_code':::STRING) IS NOT NULL) THEN concat(replace((identifier->'account':::STRING)->>'name':::STRING, ' ':::STRING, '':::STRING), '_':::STRING, (identifier->'account':::STRING)->>'actual_account_number':::STRING, '_':::STRING, (identifier->'account':::STRING)->>'ifsc_code':::STRING) ELSE NULL END) STORED,
	computed_unique_upi STRING NULL AS (CASE WHEN (type = 'UPI':::STRING) AND (((identifier->'upi':::STRING)->>'vpa':::STRING) IS NOT NULL) THEN (identifier->'upi':::STRING)->>'vpa':::STRING WHEN ((type = 'PARTIAL_UPI':::STRING) AND (((identifier->'upi':::STRING)->>'vpa':::STRING) IS NOT NULL)) AND (((identifier->'upi':::STRING)->>'name':::STRING) IS NOT NULL) THEN concat(replace((identifier->'upi':::STRING)->>'name':::STRING, ' ':::STRING, '':::STRING), '_':::STRING, (identifier->'upi':::STRING)->>'vpa':::STRING) ELSE NULL END) STORED,
	ownership STRING NULL DEFAULT 'EPIFI_TECH':::STRING,
	last_verified_at TIMESTAMPTZ NULL,
	derived_upi_id STRING NULL,
	computed_unique_credit_card_id STRING NULL AS (CASE WHEN (type = 'CREDIT_CARD':::STRING) AND (((identifier->'credit_card':::STRING)->>'id':::STRING) IS NOT NULL) THEN concat(type, '_':::STRING, (identifier->'credit_card':::STRING)->>'id':::STRING) ELSE NULL END) STORED,
	computed_unique_lrn STRING NULL AS (CASE WHEN (type = 'UPI_LITE':::STRING) AND (((identifier->'upi_lite':::STRING)->>'lrn':::STRING) IS NOT NULL) THEN (identifier->'upi_lite':::STRING)->>'lrn':::STRING ELSE NULL END) STORED,
	computed_unique_international_account STRING NULL AS (CASE WHEN ((((identifier->'international_account':::STRING)->>'actual_account_number':::STRING) IS NOT NULL) AND (((identifier->'international_account':::STRING)->>'swift_code':::STRING) IS NOT NULL)) AND (((identifier->'international_account':::STRING)->>'funds_forwarding_account_identifier':::STRING) IS NOT NULL) THEN concat((identifier->'international_account':::STRING)->>'actual_account_number':::STRING, '_':::STRING, (identifier->'international_account':::STRING)->>'swift_code':::STRING, '_':::STRING, (identifier->'international_account':::STRING)->>'funds_forwarding_account_identifier':::STRING) WHEN (((identifier->'international_account':::STRING)->>'actual_account_number':::STRING) IS NOT NULL) AND (((identifier->'international_account':::STRING)->>'swift_code':::STRING) IS NOT NULL) THEN concat((identifier->'international_account':::STRING)->>'actual_account_number':::STRING, '_':::STRING, (identifier->'international_account':::STRING)->>'swift_code':::STRING) ELSE NULL END) STORED,
	computed_upi_identifier STRING NULL AS (CASE WHEN CASE WHEN type = 'UPI':::STRING THEN true ELSE false END AND (((identifier->'upi':::STRING)->>'vpa':::STRING) IS NOT NULL) THEN concat(COALESCE(((identifier->'upi':::STRING)->'merchant_details':::STRING)->>'legal_name':::STRING, '':::STRING), '_':::STRING, COALESCE(verified_name, '':::STRING), '_':::STRING, (identifier->'upi':::STRING)->>'vpa':::STRING) END) STORED,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX payment_instruments_computed_unique_card_key (computed_unique_card ASC),
	UNIQUE INDEX payment_instruments_computed_unique_account_key (computed_unique_account ASC),
	UNIQUE INDEX payment_instruments_computed_unique_upi_key (computed_unique_upi ASC),
	INDEX payment_instruments_updated_at_idx (updated_at ASC),
	UNIQUE INDEX payment_instruments_derived_upi_id_key (derived_upi_id ASC),
	UNIQUE INDEX payment_instruments_computed_unique_credit_card_id_key (computed_unique_credit_card_id ASC),
	UNIQUE INDEX payment_instruments_computed_unique_lrn_idx (computed_unique_lrn ASC),
	UNIQUE INDEX payment_instruments_computed_unique_international_account_key (computed_unique_international_account ASC),
	UNIQUE INDEX payment_instruments_computed_upi_identifier_key (computed_upi_identifier ASC)
);
COMMENT ON COLUMN public.payment_instruments.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store pi ownership.", "ref": "api.types.ownership.proto"}';
COMMENT ON COLUMN public.payment_instruments.last_verified_at IS 'timestamp at which the payment instrument was last verified at';
COMMENT ON COLUMN public.payment_instruments.derived_upi_id IS 'Stores hash of concatenation of verifiedName, legalName and vpa';
CREATE TABLE public.account_pis (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	account_id STRING NOT NULL,
	account_type STRING NOT NULL,
	pi_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX account_pis_pi_id_key (pi_id ASC),
	UNIQUE INDEX account_pis_account_id_account_type_actor_id_pi_id_key (account_id ASC, account_type ASC, actor_id ASC, pi_id ASC),
	INDEX account_pis_auto_index_fk_actor_id_ref_actors (actor_id ASC),
	INDEX account_pis_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.activity_infos (
	log_id STRING NOT NULL,
	request_info STRING NULL,
	response_info STRING NULL,
	rowid INT8 NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT activity_infos_pkey PRIMARY KEY (rowid ASC),
	INDEX activity_infos_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.activity_logs (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	agent_email STRING NULL,
	ticket_id STRING NULL,
	resource_accessed STRING NULL,
	access_granted STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX activity_logs_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.actor_pi_resolutions (
	id STRING NOT NULL,
	actor_from STRING NOT NULL,
	actor_to STRING NOT NULL,
	pi_from STRING NULL,
	pi_to STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_actor_pi_resolutions_chathead_lookup (actor_from ASC, actor_to ASC),
	INDEX idx_actor_pi_resolutions_outbound_pay_lookup (actor_from ASC, pi_to ASC),
	INDEX idx_actor_pi_resolutions_inbound_pay_lookup (actor_to ASC, pi_from ASC),
	UNIQUE INDEX actor_pi_resolutions_actor_from_actor_to_pi_to_key (actor_from ASC, actor_to ASC, pi_to ASC),
	UNIQUE INDEX actor_pi_resolutions_actor_from_actor_to_pi_from_key (actor_from ASC, actor_to ASC, pi_from ASC),
	INDEX actor_pi_resolutions_auto_index_fk_pi_from_ref_payment_instruments (pi_from ASC),
	INDEX actor_pi_resolutions_auto_index_fk_pi_to_ref_payment_instruments (pi_to ASC),
	INDEX actor_pi_resolutions_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.auth_factor_updates (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	overall_status STRING NULL,
	context JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	vendor_context JSONB NULL,
	failure_reason STRING NULL,
	computed_phone STRING NULL AS (CASE WHEN ((((context->'newValues':::STRING)->'phoneNumber':::STRING)->>'countryCode':::STRING) IS NOT NULL) AND ((((context->'newValues':::STRING)->'phoneNumber':::STRING)->>'nationalNumber':::STRING) IS NOT NULL) THEN concat(((context->'newValues':::STRING)->'phoneNumber':::STRING)->>'countryCode':::STRING, ((context->'newValues':::STRING)->'phoneNumber':::STRING)->>'nationalNumber':::STRING) ELSE NULL END) STORED,
	computed_email STRING NULL AS (CASE WHEN ((context->'newValues':::STRING)->>'email':::STRING) IS NOT NULL THEN (context->'newValues':::STRING)->>'email':::STRING ELSE NULL END) STORED,
	computed_device STRING NULL AS (CASE WHEN ((context->'newValues':::STRING)->>'deviceId':::STRING) IS NOT NULL THEN (context->'newValues':::STRING)->>'deviceId':::STRING ELSE NULL END) STORED,
	attempts_history JSONB NULL DEFAULT '{}':::JSONB,
	computed_request_id STRING NULL AS (CASE WHEN ((vendor_context->>'requestId':::STRING) IS NOT NULL) AND ((vendor_context->>'requestId':::STRING) != '':::STRING) THEN vendor_context->>'requestId':::STRING ELSE NULL END) STORED,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX afu_uniq_actor_id_non_null_deleted_at (actor_id ASC) WHERE deleted_at IS NULL,
	INDEX afu_actor_id_created_at (actor_id ASC, created_at ASC),
	INDEX auth_factor_updates_updated_at_idx (updated_at ASC),
	INDEX auth_factor_updates_computed_phone_idx (computed_phone ASC, overall_status ASC),
	INDEX auth_factor_updates_computed_email_idx (computed_email ASC, overall_status ASC),
	INDEX auth_factor_updates_computed_device_idx (computed_device ASC, overall_status ASC),
	UNIQUE INDEX auth_factor_updates_computed_request_id_uniq_idx (computed_request_id ASC),
	FAMILY "primary" (id, actor_id, overall_status, context, created_at, updated_at, deleted_at, computed_phone, computed_email, computed_device),
	FAMILY update_vendor (vendor_context, failure_reason, attempts_history, computed_request_id)
);
COMMENT ON COLUMN public.auth_factor_updates.failure_reason IS '{"proto_type":"afu.FailureReason", "comment":"auth factor update failure reason to be shown to the user"}';
COMMENT ON COLUMN public.auth_factor_updates.attempts_history IS '{"proto_type":"afu.AttemptsHistory", "comment":"history stores all the vendor attempts data related to device re registration"}';
CREATE TABLE public.users (
	id STRING NOT NULL,
	last_updated TIMESTAMP NULL DEFAULT now():::TIMESTAMP,
	profile JSONB NULL,
	fed_customer_info JSONB NULL,
	computed_phone_number STRING NULL AS (CASE WHEN (((profile->'phone_number':::STRING)->>'country_code':::STRING) IS NOT NULL) AND (((profile->'phone_number':::STRING)->>'national_number':::STRING) IS NOT NULL) THEN concat((profile->'phone_number':::STRING)->>'country_code':::STRING, (profile->'phone_number':::STRING)->>'national_number':::STRING) ELSE NULL END) STORED,
	computed_hashed_phone_number STRING NULL AS (CASE WHEN (((profile->'phone_number':::STRING)->>'country_code':::STRING) IS NOT NULL) AND (((profile->'phone_number':::STRING)->>'national_number':::STRING) IS NOT NULL) THEN md5(concat((profile->'phone_number':::STRING)->>'country_code':::STRING, (profile->'phone_number':::STRING)->>'national_number':::STRING)) ELSE NULL END) STORED,
	computed_email STRING NULL AS (CASE WHEN (profile->>'email':::STRING) IS NOT NULL THEN profile->>'email':::STRING ELSE NULL END) STORED,
	computed_req_id STRING NULL AS ((fed_customer_info->'QueueRetryInfo':::STRING)->>'req_id':::STRING) STORED,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	access_revoke_state STRING NULL,
	computed_pan STRING NULL AS (CASE WHEN (profile->>'PAN':::STRING) IS NOT NULL THEN profile->>'PAN':::STRING ELSE NULL END) STORED,
	access_revoke_info JSONB NULL,
	access_revoke_details JSONB NULL,
	acquisition_info JSONB NULL,
	deletion_details JSONB NULL,
	data_verification_details JSONB NULL,
	actor_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX computed_req_id_idx (computed_req_id ASC),
	UNIQUE INDEX users_computed_phone_number_deleted_at_unix_unq_idx (computed_phone_number ASC, deleted_at_unix ASC),
	UNIQUE INDEX users_computed_email_deleted_at_unix_unq_idx (computed_email ASC, deleted_at_unix ASC),
	UNIQUE INDEX users_computed_hashed_phone_number_deleted_at_unix_uniq_idx (computed_hashed_phone_number ASC, deleted_at_unix ASC),
	INDEX users_updated_at_idx (updated_at ASC),
	INDEX users_computed_pan_idx (computed_pan ASC),
	UNIQUE INDEX users_actor_id_deleted_at_unix_uniq_idx (actor_id ASC, deleted_at_unix ASC)
);
COMMENT ON COLUMN public.users.access_revoke_state IS '{"proto_type":"User.AccessRevokeState", "comment":"enum which denotes user revoke state", "ref":"api/user/user.proto"}';
COMMENT ON COLUMN public.users.access_revoke_info IS '{"proto_type":"user.AccessRevokeInfo", "comment":"stores details for user access revoke"}';
COMMENT ON COLUMN public.users.access_revoke_details IS '{"proto_type":"user.AccessRevokeDetails", "comment":"stores details related to access revoke"}';
COMMENT ON COLUMN public.users.acquisition_info IS '{"proto_type":"users.acquisition_info", "comment":"stores acquisition information of users"}';
COMMENT ON COLUMN public.users.deletion_details IS '{"proto_type":"users.deletion_details", "comment":"stores details when user is deleted like deletion reason, new actorId if created for this user"}';
CREATE TABLE public.savings_accounts (
	id STRING NOT NULL,
	account_number STRING NULL,
	ifsc_code STRING NULL,
	primary_account_holder STRING NOT NULL,
	secondary_account_holder STRING NULL,
	phone_number STRING NOT NULL,
	email_id STRING NULL,
	partner_bank STRING NOT NULL,
	state STRING NOT NULL,
	balance_from_partner JSONB NOT NULL,
	constraints JSONB NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	created_by JSONB NOT NULL,
	updated_by JSONB NOT NULL,
	deleted_at TIMESTAMP NULL,
	queue_retry_info JSONB NULL,
	computed_balance_from_partner INT8 NOT NULL AS ((IFNULL((balance_from_partner->>'units':::STRING)::INT8, 0:::INT8) * 100:::INT8) + ((IFNULL((balance_from_partner->>'nanos':::STRING)::INT8, 0:::INT8) * 100:::INT8) / **********:::INT8)::INT8) STORED,
	balance_from_partner_updated_at TIMESTAMPTZ NULL,
	computed_req_id STRING NULL AS (queue_retry_info->>'request_id':::STRING) STORED,
	sku_info JSONB NULL,
	opening_bal JSONB NULL,
	account_creation_info JSONB NULL,
	computed_fi_creation_succeeded_at TIMESTAMPTZ NULL AS (experimental_strptime(account_creation_info->>'fiCreationSucceededAt':::STRING, '%Y-%m-%dT%T':::STRING)) STORED,
	balance_from_partener_v1 JSONB NULL,
	actor_id STRING NULL,
	sign_info JSONB NULL,
	computed_account_product_offering STRING NULL AS (CASE WHEN sku_info IS NULL THEN '':::STRING ELSE COALESCE(sku_info->>'accountProductOffering':::STRING, '':::STRING) END) STORED,
	nominee_details JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX savings_accounts_account_number_ifsc_code_key (account_number ASC, ifsc_code ASC),
	INDEX savings_accounts_auto_index_fk_primary_account_holder_ref_users (primary_account_holder ASC),
	INDEX savings_accounts_auto_index_fk_secondary_account_holder_ref_users (secondary_account_holder ASC),
	UNIQUE INDEX savings_accounts_primary_account_holder_partner_bank_key (primary_account_holder ASC, partner_bank ASC),
	INDEX computed_req_id_idx (computed_req_id ASC),
	INDEX savings_accounts_updated_at_idx (updated_at ASC),
	INDEX fi_creation_succeeded_at_idx (computed_fi_creation_succeeded_at ASC),
	UNIQUE INDEX savings_account_actor_id_partner_bank_key (actor_id ASC, partner_bank ASC),
	UNIQUE INDEX savings_accounts_actor_id_computed_account_product_offering_partner_bank_key_idx (actor_id ASC, computed_account_product_offering ASC, partner_bank ASC),
	FAMILY frequently_updated_balance (balance_from_partner, computed_balance_from_partner, balance_from_partner_updated_at, updated_at, computed_req_id, balance_from_partener_v1, actor_id, sign_info, computed_account_product_offering, nominee_details),
	FAMILY frequently_updated (queue_retry_info, state, account_creation_info, computed_fi_creation_succeeded_at),
	FAMILY seldom_updated (account_number, ifsc_code, phone_number, email_id, sku_info),
	FAMILY "primary" (id, primary_account_holder, secondary_account_holder, partner_bank, constraints, created_at, created_by, updated_by, deleted_at),
	FAMILY seldom_updated_balance_cache (opening_bal)
);
COMMENT ON COLUMN public.savings_accounts.account_creation_info IS '{"proto_type":"savings.AccountCreationInfo"}';
COMMENT ON COLUMN public.savings_accounts.balance_from_partener_v1 IS '{"proto_type":"savings_accounts.BalanceFromPartener", "comment": "Balance information such as available, lien, clearance and ledger amount"}';
COMMENT ON COLUMN public.savings_accounts.nominee_details IS 'details of nominee for the savings account';
CREATE TABLE public.cards (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	state STRING NULL,
	type STRING NULL,
	form STRING NULL,
	issuer_bank STRING NULL,
	network_type STRING NULL,
	bank_identifier STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	controls JSONB NULL,
	basic_info JSONB NULL,
	group_id STRING NOT NULL,
	card_category STRING NULL,
	savings_account_id STRING NULL,
	pin_set_otp_token JSONB NULL,
	previous_card_id STRING NULL,
	card_sku_type STRING NOT NULL DEFAULT 'CLASSIC':::STRING,
	issuance_fee JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX card_created_at_idx (created_at ASC),
	INDEX card_group_id_idx (group_id ASC),
	INDEX idx_actor_form_vendor_state_type (actor_id ASC, form ASC, issuer_bank ASC, state ASC, type ASC),
	INDEX previous_card_id_idx (previous_card_id ASC),
	INDEX cards_updated_at_idx (updated_at ASC),
	INDEX cards_bank_identifier_lookup_idx (bank_identifier ASC)
);
COMMENT ON COLUMN public.cards.issuance_fee IS '{"proto_type":"card.IssuanceFee", "comment": "amount to be charged for issuing the current card"}';
CREATE TABLE public.card_activation_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NULL,
	state STRING NULL,
	num_retries STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX card_activation_requests_auto_index_fk_card_id (card_id ASC),
	INDEX card_activation_requests_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.card_creation_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NULL,
	state STRING NULL,
	num_retries STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	request_id STRING NULL,
	failure_response_code STRING NULL,
	failure_response_reason STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX card_creation_requests_auto_index_fk_card_id (card_id ASC),
	UNIQUE INDEX card_creation_request_id_unq_idx (request_id ASC),
	INDEX card_creation_requests_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, card_id, state, num_retries, created_at, updated_at, deleted_at, request_id),
	FAMILY seldom_updated (failure_response_code, failure_response_reason)
);
COMMENT ON COLUMN public.card_creation_requests.failure_response_code IS '{"proto_type":"card_creation_request.failureResponseCode", "comment": "stores failure response code"}';
COMMENT ON COLUMN public.card_creation_requests.failure_response_reason IS '{"proto_type":"card_creation_request.failureResponseReason", "comment": "stores failure response reason"}';
CREATE TABLE public.card_pins (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NULL,
	state STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX card_pins_auto_index_fk_card_id (card_id ASC),
	INDEX card_pins_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.collected_data (
	id STRING NULL,
	type STRING NULL,
	actor_id STRING NULL,
	data JSONB NULL,
	created_at TIMESTAMP NULL,
	rowid INT8 NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT collected_data_pkey PRIMARY KEY (rowid ASC),
	UNIQUE INDEX id_type (id ASC, type ASC),
	INDEX collected_data_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.consents (
	id STRING NOT NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	deleted_at TIMESTAMP NULL,
	actor JSONB NOT NULL,
	device JSONB NOT NULL,
	consent_type STRING NULL,
	version INT8 NULL,
	computed_actor_id STRING NULL AS (actor->>'id':::STRING) STORED,
	expires_at TIMESTAMPTZ NULL,
	actor_id STRING NOT NULL DEFAULT '':::STRING,
	ip_address_token STRING NULL,
	client_req_id STRING NULL,
	provenance STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX computed_actor_id_by_consent_idx (computed_actor_id ASC, consent_type ASC),
	INDEX consents_updated_at_idx (updated_at ASC),
	INDEX actor_id_by_consent_idx (actor_id ASC, consent_type ASC),
	UNIQUE INDEX consents_client_req_id_key (client_req_id ASC)
);
COMMENT ON COLUMN public.consents.provenance IS 'source of the consent';
CREATE TABLE public.device_registrations (
	device_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	device JSONB NULL,
	device_token STRING NULL,
	user_profile_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	status STRING NOT NULL,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	metadata JSONB NULL,
	attempt_id UUID NULL,
	sim_id STRING NULL,
	vendor_device_id STRING NULL,
	vendor STRING NULL,
	CONSTRAINT device_registrations_pkey PRIMARY KEY (actor_id ASC, deleted_at_unix ASC),
	UNIQUE INDEX device_id_deleted_at_unix_unique_key (device_id ASC, deleted_at_unix ASC),
	INDEX device_registrations_device_id_created_at_idx (device_id DESC, created_at DESC),
	INDEX device_registrations_updated_at_idx (updated_at ASC),
	FAMILY "primary" (device_id, actor_id, device, device_token, user_profile_id, created_at, updated_at, deleted_at, status, deleted_at_unix, attempt_id, sim_id, vendor_device_id, vendor),
	FAMILY frequently_updated (metadata)
);
CREATE TABLE public.face_match_attempts (
	actor_id STRING NOT NULL,
	attempt_id UUID NOT NULL DEFAULT gen_random_uuid(),
	request_id UUID NOT NULL,
	video_location STRING NOT NULL,
	image_location STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	vendor_request_id STRING NULL,
	vendor STRING NULL,
	face_match_score FLOAT8 NULL,
	summary_id STRING NULL,
	annotation JSONB NULL,
	threshold FLOAT8 NULL,
	strictness_logic STRING NULL,
	inhouse_status STRING NULL,
	liveness_request_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, attempt_id ASC),
	UNIQUE INDEX face_match_attempts_attempt_id_key (attempt_id ASC),
	INDEX idx_equest_id (request_id ASC),
	INDEX face_match_attempts_updated_at_idx (updated_at ASC),
	INDEX face_match_liveness_req_id_idx (liveness_request_id ASC)
);
COMMENT ON COLUMN public.face_match_attempts.summary_id IS 'Stores the id of the summary associated with the facematch attempt';
COMMENT ON COLUMN public.face_match_attempts.annotation IS '{"proto_type":"auth.liveness.FaceMatchAnnotation", "comment":"stores notes and observation about facematch attempt"}';
COMMENT ON COLUMN public.face_match_attempts.threshold IS '{"proto_type":"FaceMatchAttempt.Threshold", "comment": "This column stores the threshold used to pass the facematch attempt"}';
COMMENT ON COLUMN public.face_match_attempts.strictness_logic IS 'Stores the strictness level associated with the facematch attempt';
COMMENT ON COLUMN public.face_match_attempts.inhouse_status IS 'Stores the in house status associated with the facematch attempt';
COMMENT ON COLUMN public.face_match_attempts.liveness_request_id IS 'Stores the liveness request id for which facematch was attempted';
CREATE TABLE public.kyc_attempts (
	attempt_id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	source STRING NULL,
	kyc_type STRING NULL,
	kyc_progress_status STRING NULL,
	face_match_request_id UUID NOT NULL DEFAULT gen_random_uuid(),
	face_match_status STRING NULL,
	active_status STRING NULL,
	request_params JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	liveness_params JSONB NULL,
	face_match_params JSONB NULL,
	scores JSONB NULL,
	failure_reason JSONB NULL,
	client_req_id STRING NULL,
	bkyc_info JSONB NULL,
	computed_ekyc_rrn STRING NULL AS (CASE WHEN (request_params->>'ekycRrnNo':::STRING) IS NOT NULL THEN request_params->>'ekycRrnNo':::STRING ELSE NULL END) STORED,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, attempt_id ASC),
	UNIQUE INDEX kyc_attempts_attempt_id_key (attempt_id ASC),
	INDEX kyc_attempts_updated_at_idx (updated_at ASC),
	INDEX computed_ekyc_rrn_idx (computed_ekyc_rrn ASC),
	UNIQUE INDEX kyc_attempts_client_req_id_uniq_idx (client_req_id ASC),
	FAMILY "primary" (attempt_id, actor_id, source, kyc_type, kyc_progress_status, face_match_request_id, face_match_status, active_status, request_params, created_at, updated_at, deleted_at, liveness_params, face_match_params, scores, client_req_id, bkyc_info, computed_ekyc_rrn),
	FAMILY frequently_updated (failure_reason)
);
COMMENT ON COLUMN public.kyc_attempts.failure_reason IS 'failure_reason stores the details of why a KYC attempt failed. proto: kyc.FailureReason. ref https://github.com/epiFi/protos/blob/master/api/kyc/internal/kyc_attempt.proto';
CREATE TABLE public.kyc_summaries (
	actor_id STRING NOT NULL,
	kyc_attempt_id UUID NULL,
	status STRING NULL,
	liveness_request_id UUID NOT NULL DEFAULT gen_random_uuid(),
	liveness_status STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	ckyc_attempt_id STRING NULL,
	ekyc_attempt_id STRING NULL,
	data_validation_retry_info JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC),
	UNIQUE INDEX kyc_summaries_liveness_request_id_key (liveness_request_id ASC),
	INDEX kyc_summaries_auto_index_fk_kyc_attempt_id (kyc_attempt_id ASC),
	INDEX kyc_summaries_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.kyc_summaries.ckyc_attempt_id IS 'ckyc_attempt_id stores the attempt_id of the most recent ckyc attempt by the user';
COMMENT ON COLUMN public.kyc_summaries.ekyc_attempt_id IS 'ekyc_attempt_id stores the attempt_id of the most recent ekyc attempt by the user';
COMMENT ON COLUMN public.kyc_summaries.data_validation_retry_info IS '{"proto_type":"kyc.DataValidationRetryInfo", "comment": "Store information like type of data failure and no. of times user attempted to enter correct information", "ref": "https://github.com/epiFi/protos/blob/master/api/kyc/internal/kyc_summary.proto"}';
CREATE TABLE public.kyc_vendor_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	kyc_attempt_id UUID NULL,
	payload_type STRING NULL,
	ttl_in_sec INT8 NULL,
	payload JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	delete_by TIMESTAMPTZ NULL,
	metadata JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX kyc_vendor_data_auto_index_fk_kyc_attempt_id (kyc_attempt_id ASC),
	INDEX kyc_vendor_data_delete_by_idx (delete_by ASC),
	INDEX kyc_vendor_data_updated_at_idx (updated_at ASC),
	INDEX kyc_vendor_data_deleted_at_delete_by_index (deleted_at ASC, delete_by ASC)
);
CREATE TABLE public.liveness_attempts (
	actor_id STRING NOT NULL,
	attempt_id UUID NOT NULL DEFAULT gen_random_uuid(),
	request_id UUID NOT NULL,
	video_location STRING NULL,
	otp STRING NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	vendor_request_id STRING NULL,
	vendor STRING NULL,
	image_frame JSONB NULL,
	otp_score FLOAT8 NULL,
	liveness_score FLOAT8 NULL,
	detected_otp STRING NULL,
	blink_confidence FLOAT8 NULL,
	blink_heuristics_confidence FLOAT8 NULL,
	blink_liveness BOOL NULL,
	vendor_response STRING NULL,
	liveness_flow STRING NULL,
	blink_liveness_status STRING NULL,
	metadata JSONB NULL,
	in_house_liveness JSONB NULL,
	annotation JSONB NULL,
	summary_id STRING NULL,
	strictness_logic STRING NULL,
	vendor_liveness_status STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, attempt_id ASC),
	UNIQUE INDEX liveness_attempts_attempt_id_key (attempt_id ASC),
	UNIQUE INDEX liveness_attempts_request_id_key (request_id ASC),
	INDEX idx_request_id (request_id ASC),
	INDEX liveness_attempts_vendor_request_idx (vendor ASC, vendor_request_id ASC),
	INDEX liveness_attempts_updated_at_idx (updated_at ASC),
	INDEX liveness_attempts_summary_id_idx (summary_id ASC)
);
COMMENT ON COLUMN public.liveness_attempts.liveness_flow IS '{"proto_type":"LivenessAttempt.LivenessFlow", "comment":"Data required to check which flow liveness attempt was triggered from"}';
COMMENT ON COLUMN public.liveness_attempts.metadata IS '{"proto_type":"LivenessAttempts.Metadata", "comment":"stores metadata of liveness attempts"}';
COMMENT ON COLUMN public.liveness_attempts.in_house_liveness IS '{"proto_type":"auth.liveness.InHouseLiveness", "comment":"stores info related to in-house liveness"}';
COMMENT ON COLUMN public.liveness_attempts.annotation IS '{"proto_type":"auth.liveness.Annotation", "comment":"stores notes and observation about liveness attempt"}';
COMMENT ON COLUMN public.liveness_attempts.summary_id IS 'Stores the id of the summary associated with the liveness attempt';
COMMENT ON COLUMN public.liveness_attempts.strictness_logic IS 'Stores the strictness level associated with the liveness attempt';
COMMENT ON COLUMN public.liveness_attempts.vendor_liveness_status IS 'Stores the vendor status associated with the liveness attempt';
CREATE TABLE public.onboarding_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	stage_details JSONB NULL,
	account_info JSONB NULL,
	card_info JSONB NULL,
	vendor STRING NOT NULL,
	current_onboarding_stage STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	user_id STRING NULL,
	completed_at TIMESTAMPTZ NULL,
	stage_metadata JSONB NULL,
	feature STRING NULL,
	fi_lite_details JSONB NULL,
	feature_details JSONB NULL,
	pan_aadhar_linkage_details JSONB NULL,
	stage_proc_last_response JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX onb_details_actor_id_deleted_at_uniq_idx (actor_id ASC, deleted_at DESC),
	INDEX onboarding_details_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, actor_id, stage_details, account_info, card_info, vendor, current_onboarding_stage, created_at, updated_at, deleted_at, user_id, completed_at, feature, fi_lite_details, feature_details, pan_aadhar_linkage_details, stage_proc_last_response),
	FAMILY stage_metadata (stage_metadata)
);
COMMENT ON COLUMN public.onboarding_details.stage_metadata IS 'stage_metadata stores metadata of all stages in the onboarding flow for various purposes. proto: onboarding.StageMetadata. ref https://github.com/epiFi/protos/blob/master/api/user/onboarding/internal/onboarding_details.proto';
COMMENT ON COLUMN public.onboarding_details.feature IS 'ENUM for the feature the current onboarding details belong to';
COMMENT ON COLUMN public.onboarding_details.fi_lite_details IS 'JSON data regarding fi lite accessibility for a user';
COMMENT ON COLUMN public.onboarding_details.feature_details IS 'JSON data for onboarding data for all the feature journeys';
CREATE TABLE public.otps (
	id INT8 NOT NULL DEFAULT unique_rowid(),
	created_at TIMESTAMPTZ NULL,
	updated_at TIMESTAMPTZ NULL,
	deleted_at TIMESTAMPTZ NULL,
	token STRING NULL,
	otp STRING NULL,
	verify_attempts INT8 NULL,
	sms_attempts INT8 NULL,
	sms_timestamp TIMESTAMPTZ NULL,
	active BOOL NULL,
	device JSONB NOT NULL,
	phone_number JSONB NULL,
	computed_phone_number STRING NULL AS (CASE WHEN ((phone_number->>'country_code':::STRING) IS NOT NULL) AND ((phone_number->>'national_number':::STRING) IS NOT NULL) THEN concat(phone_number->>'country_code':::STRING, phone_number->>'national_number':::STRING) ELSE NULL END) STORED,
	email STRING NULL,
	send_attempts INT8 NULL,
	last_sent_at TIMESTAMPTZ NULL,
	ownership STRING NULL DEFAULT 'EPIFI_TECH':::STRING,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_otps_deleted_at (deleted_at ASC),
	INDEX otps_updated_at_idx (updated_at ASC),
	INDEX otps_token_idx (token ASC),
	INDEX otps_computed_phone_number_created_at_idx (computed_phone_number ASC, created_at DESC),
	INDEX otps_email_created_at_idx (email ASC, created_at DESC)
);
COMMENT ON COLUMN public.otps.send_attempts IS 'Number of attempts made to deliver OTP';
COMMENT ON COLUMN public.otps.last_sent_at IS 'time stamp of most recent sent OTP';
COMMENT ON COLUMN public.otps.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store otps ownership.", "ref": "api.types.ownership.proto"}';
CREATE TABLE public.psp_keys (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	psp_org_id STRING NOT NULL,
	type STRING NOT NULL,
	ki STRING NOT NULL,
	key_value STRING NOT NULL,
	owner STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX psp_org_id_idx (psp_org_id ASC),
	INDEX psp_keys_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.timelines (
	id STRING NOT NULL,
	primary_actor_id STRING NOT NULL,
	secondary_actor_id STRING NOT NULL,
	primary_actor_name STRING NOT NULL,
	secondary_actor_name STRING NOT NULL,
	last_event_updated_at TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	primary_actor_state STRING NOT NULL DEFAULT 'VISIBLE':::STRING,
	secondary_actor_state STRING NOT NULL DEFAULT 'NOT_VISIBLE':::STRING,
	primary_actor_source STRING NULL,
	secondary_actor_source STRING NULL,
	ownership STRING NOT NULL DEFAULT 'EPIFI_TECH':::STRING,
	CONSTRAINT "primary" PRIMARY KEY (primary_actor_id ASC, secondary_actor_id ASC),
	UNIQUE INDEX timelines_id_key (id ASC),
	INDEX idx_timelines_secondary_actor_id (secondary_actor_id ASC),
	INDEX timelines_updated_at_idx (updated_at ASC),
	INDEX timelines_secondary_actor_id_update_at_idx (secondary_actor_id ASC, updated_at ASC)
);
COMMENT ON COLUMN public.timelines.ownership IS '{"proto_type":"timeline.ownership", "comment": "Enum to store timelines ownership.", "ref": "api.timeline.ownership.proto"}';
CREATE TABLE public.upi_account_infos (
	account_id STRING NOT NULL,
	allowed_cred JSONB NULL,
	is_mobile_banking_enabled BOOL NULL,
	is_aadhaar_banking_enabled BOOL NULL,
	vpa STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	state STRING NULL,
	account_ref STRING NULL,
	masked_account_num STRING NULL,
	pin_set_state STRING NULL,
	list_account_called_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (account_id ASC),
	INDEX upi_account_infos_updated_at_idx (updated_at ASC),
	INDEX list_account_called_at_idx (list_account_called_at ASC),
	INDEX upi_account_infos_vpa_idx (vpa ASC),
	FAMILY "primary" (account_id, allowed_cred, is_mobile_banking_enabled, is_aadhaar_banking_enabled, vpa, created_at, updated_at, deleted_at, state, account_ref, masked_account_num, list_account_called_at),
	FAMILY seldom_updated (pin_set_state)
);
CREATE TABLE public.verified_address_entries (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	merchant_vpa STRING NOT NULL,
	name STRING NULL,
	url STRING NULL,
	key_code STRING NULL,
	type STRING NULL,
	ki STRING NULL,
	key_value STRING NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX merchant_vpa_idx (merchant_vpa ASC),
	INDEX verified_address_entries_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.waitlist_users (
	user_id UUID NOT NULL DEFAULT gen_random_uuid(),
	pf_status STRING NULL,
	pf_data JSONB NULL,
	pf_request_id STRING NULL,
	non_pf_status STRING NULL,
	non_pf_data JSONB NULL,
	profile JSONB NULL,
	waitlist_status STRING NULL,
	waitlist_rank INT8 NULL,
	pf_otp_token STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	referral_token STRING NULL,
	login_status STRING NULL,
	user_type STRING NULL,
	referral_code STRING NULL,
	prospect_id STRING NULL,
	rejection_reason STRING NULL,
	pf_org_data JSONB NULL DEFAULT '{}':::JSONB,
	welcome_email_status STRING NULL,
	cbo_data JSONB NULL DEFAULT '{}':::JSONB,
	cbo_counter INT8 NULL,
	cbo_voucher_code STRING NULL,
	fl_status STRING NULL,
	fl_agent_id STRING NULL,
	fl_agent_reason STRING NULL,
	computed_phone_number STRING NULL AS (CASE WHEN (((profile->'phone_number':::STRING)->>'country_code':::STRING) IS NOT NULL) AND (((profile->'phone_number':::STRING)->>'national_number':::STRING) IS NOT NULL) THEN concat((profile->'phone_number':::STRING)->>'country_code':::STRING, (profile->'phone_number':::STRING)->>'national_number':::STRING) ELSE NULL END) STORED,
	computed_hashed_phone_number STRING NULL AS (CASE WHEN (((profile->'phone_number':::STRING)->>'country_code':::STRING) IS NOT NULL) AND (((profile->'phone_number':::STRING)->>'national_number':::STRING) IS NOT NULL) THEN md5(concat((profile->'phone_number':::STRING)->>'country_code':::STRING, (profile->'phone_number':::STRING)->>'national_number':::STRING)) ELSE NULL END) STORED,
	app_access_email_status STRING NULL,
	app_access_email_sent_time TIMESTAMPTZ NULL,
	gmail_input_email_status STRING NULL,
	gmail_input_email_sent_time TIMESTAMPTZ NULL,
	flow_name STRING NULL,
	device_info_history JSONB NULL,
	device_info JSONB NULL,
	finite_code STRING NULL,
	onboarded_user_phone_number STRING NULL,
	app_access_email_version STRING NULL,
	app_access_sms_status STRING NULL,
	app_access_sms_sent_time TIMESTAMPTZ NULL,
	app_access_sms_version STRING NULL,
	app_access_whatsapp_status STRING NULL,
	app_access_whatsapp_sent_time TIMESTAMPTZ NULL,
	app_access_whatsapp_version STRING NULL,
	onboarded_actor_id STRING NULL,
	flow_details JSONB NULL,
	comms_details JSONB NULL DEFAULT '{}':::JSONB,
	computed_email STRING NULL AS (profile->>'comm_email':::STRING) STORED,
	CONSTRAINT "primary" PRIMARY KEY (user_id ASC),
	UNIQUE INDEX waitlist_users_user_id_key (user_id ASC),
	UNIQUE INDEX waitlist_users_pf_otp_token_key (pf_otp_token ASC),
	INDEX waitlist_users_computed_phone_number_idx (computed_phone_number ASC),
	INDEX waitlist_users_otp_token_idx (pf_otp_token ASC),
	INDEX waitlist_users_referral_token_idx (referral_token ASC),
	INDEX waitlist_users_prospect_id_idx (prospect_id ASC),
	INDEX waitlist_users_cbo_voucher_code_idx (cbo_voucher_code ASC),
	INDEX waitlist_users_finite_code_idx (finite_code ASC),
	INDEX waitlist_users_waitlist_status_idx (waitlist_status ASC),
	INDEX waitlist_users_app_access_email_status_idx (app_access_email_status ASC),
	INDEX waitlist_users_onb_actor_id (onboarded_actor_id ASC),
	INDEX waitlist_users_updated_at_idx (updated_at ASC),
	INDEX idx_waitlist_users_computed_email (computed_email ASC)
);
CREATE TABLE public.orders (
	id STRING NOT NULL,
	from_actor_id STRING NULL,
	to_actor_id STRING NOT NULL,
	workflow STRING NOT NULL,
	status STRING NOT NULL,
	order_payload JSONB NULL,
	amount JSONB NOT NULL,
	computed_amount INT8 NOT NULL AS ((IFNULL((amount->>'units':::STRING)::INT8, 0:::INT8) * 100:::INT8) + ((IFNULL((amount->>'nanos':::STRING)::INT8, 0:::INT8) * 100:::INT8) / **********:::INT8)::INT8) STORED,
	amount_breakup JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	provenance STRING NOT NULL,
	expire_at TIMESTAMPTZ NOT NULL,
	external_id STRING NOT NULL,
	tags JSONB NULL DEFAULT '{}':::JSONB,
	ui_entry_point STRING NULL,
	client_req_id STRING NULL,
	wf_ref_id STRING NULL,
	from_actor_location_token STRING NULL,
	to_actor_location_token STRING NULL,
	old_from_actor_id STRING NULL,
	old_to_actor_id STRING NULL,
	next_actions JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX orders_outbound_actor_lookup_idx (from_actor_id ASC, to_actor_id ASC),
	INDEX orders_inbound_actor_lookup_idx (to_actor_id ASC, from_actor_id ASC),
	UNIQUE INDEX order_external_id_unique_idx (external_id ASC),
	INDEX orders_updated_at_idx (updated_at ASC),
	UNIQUE INDEX order_client_req_id_uniq_idx (client_req_id ASC),
	INDEX orders_from_actor_id_created_at_status_idx (from_actor_id ASC, created_at ASC, status ASC),
	INDEX orders_to_actor_id_created_at_status_idx (to_actor_id ASC, created_at ASC, status ASC),
	INDEX orders_from_actor_id_updated_at_stores_status_workflow_idx (from_actor_id ASC, updated_at DESC) STORING (status, workflow),
	INDEX orders_to_actor_id_updated_at_stores_status_workflow_idx (to_actor_id ASC, updated_at DESC) STORING (status, workflow),
	INDEX orders_to_actor_id_created_at_stored_idx (to_actor_id ASC, created_at DESC) STORING (status, workflow, tags, amount, updated_at, from_actor_id, external_id),
	INDEX orders_from_actor_id_created_at_stored_idx (from_actor_id ASC, created_at DESC) STORING (status, workflow, tags, amount, updated_at, to_actor_id, external_id),
	FAMILY frequently_updated (status, tags, updated_at),
	FAMILY "primary" (id, from_actor_id, to_actor_id, workflow, order_payload, amount, computed_amount, amount_breakup, created_at, deleted_at, provenance, expire_at, external_id, ui_entry_point, client_req_id, wf_ref_id, from_actor_location_token, to_actor_location_token, old_from_actor_id, old_to_actor_id, next_actions)
);
COMMENT ON COLUMN public.orders.wf_ref_id IS 'unique identifier to map an order to a celestial workflow request';
COMMENT ON COLUMN public.orders.from_actor_location_token IS 'location token of the payer';
COMMENT ON COLUMN public.orders.to_actor_location_token IS 'location token of the payee';
COMMENT ON COLUMN public.orders.old_from_actor_id IS 'old_from_actor_id stores the old from actor ids . It will be same actor as from_actor for user but for Merchant actor, it stores old actor which was changed due to merchant merge process';
COMMENT ON COLUMN public.orders.old_to_actor_id IS 'old_to_actor_id stores the old to actor ids. It will be same actor as to_actor for user but for Merchant actor, it stores old actor which was changed due to merchant merge process';
COMMENT ON COLUMN public.orders.next_actions IS 'contains deep links to drive next actions post payment';
CREATE TABLE public.order_attempts (
	id INT8 NOT NULL DEFAULT unique_rowid(),
	order_id STRING NOT NULL,
	stage STRING NOT NULL,
	num_attempts INT8 NOT NULL DEFAULT 0:::INT8,
	req_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT order_attempts_pkey PRIMARY KEY (order_id ASC, id ASC),
	UNIQUE INDEX order_attempts_order_id_stage_unique_idx (order_id ASC, stage ASC),
	INDEX order_attempts_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.pin_code_master (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pin_code STRING NOT NULL,
	division STRING NULL,
	region STRING NULL,
	circle STRING NULL,
	taluk STRING NULL,
	district STRING NULL,
	state STRING NULL,
	longitude STRING NULL,
	latitude STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	post_office STRING NULL,
	CONSTRAINT pin_code_master_pkey PRIMARY KEY (pin_code ASC, id ASC),
	INDEX pin_code_master_updated_at_idx (updated_at ASC),
	UNIQUE INDEX pin_code_master_pin_code_division_region_circle_post_office_district_state_uniq_idx (pin_code ASC, division ASC, region ASC, circle ASC, post_office ASC, district ASC, state ASC)
);
CREATE TABLE public.federal_city_codes (
	city_name STRING NOT NULL,
	city_alias STRING NOT NULL,
	rowid INT8 NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT federal_city_codes_pkey PRIMARY KEY (rowid ASC),
	INDEX federal_city_codes_city_name_idx (city_name ASC),
	INDEX federal_city_codes_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.user_mail_access_infos (
	actor_id STRING NOT NULL,
	email_id STRING NOT NULL,
	access_info STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT user_mail_access_infos_pkey PRIMARY KEY (actor_id ASC, email_id ASC),
	INDEX user_mail_access_infos_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.message_processing_states (
	email_id STRING NOT NULL,
	message_id STRING NOT NULL,
	merchant STRING NOT NULL,
	state STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT message_processing_states_pkey PRIMARY KEY (email_id ASC, message_id ASC),
	INDEX idx_email_id_state (email_id ASC, state ASC),
	INDEX idx_email_id_merchant (email_id ASC, merchant ASC),
	INDEX message_processing_states_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.referrals (
	actor_id STRING NOT NULL,
	attempt_id UUID NOT NULL DEFAULT gen_random_uuid(),
	referral_type STRING NOT NULL,
	referral_code STRING NOT NULL,
	referral_limit INT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	tickets STRING[] NULL,
	CONSTRAINT referrals_pkey PRIMARY KEY (actor_id ASC, attempt_id ASC),
	UNIQUE INDEX referrals_attempt_id_key (attempt_id ASC),
	UNIQUE INDEX actor_type (actor_id ASC, referral_type ASC),
	UNIQUE INDEX code_type (referral_code ASC, referral_type ASC),
	UNIQUE INDEX referral_codes_unique (referral_code ASC),
	INDEX referrals_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.referral_relations (
	referrer_actor_id STRING NOT NULL,
	referee_actor_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	ticket_code STRING NULL,
	referral_type STRING NULL,
	CONSTRAINT referral_relations_pkey PRIMARY KEY (referee_actor_id ASC),
	INDEX referral_relations_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.user_spendings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	merchant STRING NOT NULL,
	date DATE NOT NULL,
	amount FLOAT4 NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	CONSTRAINT user_spendings_pkey PRIMARY KEY (id ASC),
	INDEX idx_actor_id_state (actor_id ASC, merchant ASC, date ASC) STORING (amount),
	INDEX user_spendings_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.user_mail_details (
	message_id STRING NOT NULL,
	mail_detail JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT user_mail_details_pkey PRIMARY KEY (message_id ASC),
	INDEX user_mail_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.ckyc_state_codes (
	state_code STRING NOT NULL,
	state STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT ckyc_state_codes_pkey PRIMARY KEY (state_code ASC),
	UNIQUE INDEX ckyc_state_codes_state_key (state ASC),
	INDEX ckyc_state_codes_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.blocked_actors_map (
	id INT8 NOT NULL DEFAULT unique_rowid(),
	actor_id STRING NOT NULL,
	blocked_actor_id STRING NOT NULL,
	is_spam BOOL NOT NULL DEFAULT false,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	id_v2 UUID NOT NULL DEFAULT gen_random_uuid(),
	CONSTRAINT blocked_actors_map_pkey PRIMARY KEY (id ASC),
	INDEX blocked_actors_map_actor_id_blocked_actor_id_idx (actor_id ASC, blocked_actor_id ASC),
	INDEX blocked_actors_map_updated_at_idx (updated_at ASC),
	UNIQUE INDEX blocked_actors_map_id_v2 (id_v2 ASC)
);
CREATE TABLE public.vendor_area_codes (
	vendor STRING NOT NULL,
	state STRING NOT NULL,
	district STRING NOT NULL,
	state_code STRING NOT NULL,
	city_code STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT vendor_area_codes_pkey PRIMARY KEY (vendor ASC, state ASC, district ASC, state_code ASC),
	INDEX vendor_area_codes_vendor_city_code_state_code_idx (vendor ASC, state_code ASC, city_code ASC),
	INDEX vendor_area_codes_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.device_locations (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	latitude DECIMAL(8,4) NOT NULL,
	longitude DECIMAL(8,4) NOT NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX device_locations_lat_long_uniq_idx (latitude ASC, longitude ASC),
	INDEX device_locations_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.nominees (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	relationship STRING NOT NULL,
	name STRING NOT NULL,
	dob TIMESTAMPTZ NOT NULL,
	address JSONB NOT NULL,
	address_type STRING NOT NULL,
	phone_number JSONB NOT NULL,
	email_id STRING NOT NULL,
	guardian_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	pan STRING NULL,
	document JSONB NULL,
	CONSTRAINT nominees_pkey PRIMARY KEY (actor_id ASC, id ASC),
	INDEX nominees_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.transactions (
	id STRING NOT NULL,
	pi_from STRING NOT NULL,
	pi_to STRING NOT NULL,
	partner_ref_id STRING NULL,
	utr STRING NULL,
	partner_bank STRING NOT NULL,
	parent_transaction_id STRING NULL,
	amount JSONB NOT NULL,
	computed_amount INT8 NOT NULL AS ((IFNULL((amount->>'units':::STRING)::INT8, 0:::INT8) * 100:::INT8) + ((IFNULL((amount->>'nanos':::STRING)::INT8, 0:::INT8) * 100:::INT8) / **********:::INT8)::INT8) STORED,
	status STRING NOT NULL,
	detailed_status JSONB NULL,
	payment_protocol STRING NOT NULL,
	trans_remarks STRING NULL,
	payment_req_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	partner_executed_at TIMESTAMPTZ NULL,
	deleted_at TIMESTAMPTZ NULL,
	computed_req_id STRING NULL AS (payment_req_info->>'req_id':::STRING) STORED,
	protocol_status STRING NULL,
	computed_merchant_ref_id STRING NULL AS ((payment_req_info->'upi_info':::STRING)->>'merchant_ref_id':::STRING) STORED,
	disputed_at TIMESTAMPTZ NULL,
	location_token STRING NULL,
	particulars STRING NULL,
	debited_at TIMESTAMPTZ NULL,
	credited_at TIMESTAMPTZ NULL,
	partner_ref_id_debit STRING NULL,
	partner_ref_id_credit STRING NULL,
	particulars_debit STRING NULL,
	particulars_credit STRING NULL,
	notification_details JSONB NULL,
	raw_notification_details JSONB NULL,
	dedupe_id STRING NULL,
	order_ref_id STRING NULL,
	ownership STRING NULL,
	computed_executed_at TIMESTAMPTZ NULL AS (CASE WHEN debited_at IS NOT NULL THEN debited_at WHEN credited_at IS NOT NULL THEN credited_at ELSE updated_at END) STORED,
	metadata JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX transactions_partner_ref_id_key (partner_ref_id ASC),
	UNIQUE INDEX transactions_computed_req_id_key (computed_req_id ASC),
	INDEX transactions_auto_index_fk_pi_from_ref_payment_instruments (pi_from ASC),
	INDEX transactions_auto_index_fk_pi_to_ref_payment_instruments (pi_to ASC),
	INDEX transactions_created_at_idx (created_at ASC),
	INDEX transactions_partner_executed_at_idx (partner_executed_at ASC),
	UNIQUE INDEX transactions_partner_ref_id_debit_idx (partner_ref_id_debit ASC),
	UNIQUE INDEX transactions_partner_ref_id_credit_idx (partner_ref_id_credit ASC),
	INDEX transactions_updated_at_idx (updated_at ASC),
	INDEX transactions_pi_from_created_at_status_idx (pi_from ASC, created_at ASC, status ASC),
	INDEX transactions_pi_to_created_at_status_idx (pi_to ASC, created_at ASC, status ASC),
	UNIQUE INDEX transactions_unique_dedupe_key (dedupe_id ASC),
	INDEX transactions_order_ref_id_idx (order_ref_id ASC),
	INDEX pi_from_computed_executed_at_status (pi_from ASC, computed_executed_at ASC, status ASC) STORING (trans_remarks, payment_protocol, order_ref_id, partner_bank, utr, computed_amount),
	INDEX pi_to_computed_executed_at_status (pi_to ASC, computed_executed_at ASC, status ASC) STORING (trans_remarks, payment_protocol, order_ref_id, partner_bank, utr, computed_amount),
	INDEX pi_from_computed_executed_at_status_stored_idx (pi_from ASC, computed_executed_at ASC, status ASC) STORING (pi_to, trans_remarks, payment_protocol, order_ref_id, partner_bank, utr, computed_amount),
	INDEX pi_to_computed_executed_at_status_stored_idx (pi_to ASC, computed_executed_at ASC, status ASC) STORING (pi_from, trans_remarks, payment_protocol, order_ref_id, partner_bank, utr, computed_amount),
	FAMILY frequently_updated (status, detailed_status, updated_at, protocol_status, dedupe_id),
	FAMILY seldom_updated (partner_ref_id, utr, parent_transaction_id, partner_executed_at, disputed_at, particulars, debited_at, credited_at, partner_ref_id_debit, partner_ref_id_credit, particulars_debit, particulars_credit, notification_details, raw_notification_details, computed_executed_at),
	FAMILY seldom_updated_req_info (payment_req_info, computed_req_id, computed_merchant_ref_id, metadata),
	FAMILY "primary" (id, pi_from, pi_to, partner_bank, amount, computed_amount, payment_protocol, trans_remarks, created_at, deleted_at, location_token, order_ref_id, ownership)
);
COMMENT ON COLUMN public.transactions.notification_details IS '{"proto_type":order.payment.NotificationDetails, "comment":"notification_details stores the transaction related information received from notification from the partner banks"}';
COMMENT ON COLUMN public.transactions.raw_notification_details IS '{"proto_type":order.payment.NotificationDetails, "comment":"raw_notification_details stores the transaction related information received from notification from the partner banks, it is a map where key is Accounting Entry Type(debit, credit) and value is order.payment.NotificationDetails"}';
COMMENT ON COLUMN public.transactions.dedupe_id IS '{"proto_type":"order.payment.transaction.dedupe_id", "comment": "Unique derived id to uniquely identify transaction from provided vendor data"}';
COMMENT ON COLUMN public.transactions.order_ref_id IS 'unique order identifier to which given transaction is mapped to';
COMMENT ON COLUMN public.transactions.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store ownership.", "ref": "api.types.ownership.proto"}';
COMMENT ON COLUMN public.transactions.computed_executed_at IS 'executed time stamp based upon COALESCE(debitedAt,creditedAt,updatedAt)';
COMMENT ON COLUMN public.transactions.metadata IS '{"proto_type":"order.payment.transaction.metadata", "comment": "Enum to store metadata for transactions", "ref": "api.order.payment.transaction.metadata"}';
CREATE TABLE public.vendor_otps (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	phone STRING NOT NULL,
	request_id STRING NOT NULL,
	request_type STRING NOT NULL,
	vendor STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX vendor_otps_request_id_key (request_id ASC),
	INDEX vendor_otps_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.pi_state_logs (
	id INT8 NOT NULL DEFAULT unique_rowid(),
	pi_id STRING NOT NULL,
	source STRING NOT NULL,
	state STRING NOT NULL,
	reason STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	id_v2 UUID NOT NULL DEFAULT gen_random_uuid(),
	CONSTRAINT pi_state_logs_pkey PRIMARY KEY (pi_id ASC, id ASC),
	UNIQUE INDEX pi_state_log_id (id ASC),
	INDEX pi_state_logs_updated_at_idx (updated_at ASC),
	UNIQUE INDEX pi_state_log_id_v2 (id_v2 ASC)
);
CREATE TABLE public.account_upi_pin_infos (
	id INT8 NOT NULL DEFAULT unique_rowid(),
	account_id STRING NOT NULL,
	user_action STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	status STRING NOT NULL,
	detailed_status_list JSONB NULL,
	vendor_request_id STRING NULL,
	CONSTRAINT account_upi_pin_infos_pkey PRIMARY KEY (account_id ASC, id ASC),
	UNIQUE INDEX upi_pin_info_id (id ASC),
	INDEX account_upi_pin_infos_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, account_id, user_action, created_at, updated_at, deleted_at),
	FAMILY frequently_updated (status),
	FAMILY seldom_updated (detailed_status_list, vendor_request_id)
);
COMMENT ON COLUMN public.account_upi_pin_infos.detailed_status_list IS '{proto_type:upi.DetailedStatus}';
COMMENT ON COLUMN public.account_upi_pin_infos.vendor_request_id IS 'RequestId used to make vendor request for upi pin set';
CREATE TABLE public.golden_tickets (
	golden_ticket_code STRING NOT NULL,
	referral_code STRING NOT NULL,
	status STRING NULL,
	referee_email STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	referee_name JSONB NULL,
	referee_actor_id STRING NULL,
	CONSTRAINT golden_tickets_pkey PRIMARY KEY (golden_ticket_code ASC),
	INDEX idx_referral_code (referral_code ASC),
	INDEX golden_tickets_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.shipping_preferences (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	shipping_item STRING NOT NULL,
	address_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT shipping_preferences_pkey PRIMARY KEY (actor_id ASC, shipping_item ASC, id ASC),
	UNIQUE INDEX shipping_preference_id_unique (id ASC),
	INDEX shipping_preferences_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.deposit_accounts (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	account_number STRING NULL,
	name STRING NULL,
	type STRING NOT NULL,
	scheme_code STRING NOT NULL,
	state STRING NOT NULL,
	principal_amount JSONB NULL DEFAULT '{}':::JSONB,
	running_balance JSONB NULL DEFAULT '{}':::JSONB,
	maturity_amount JSONB NULL DEFAULT '{}':::JSONB,
	maturity_date TIMESTAMPTZ NULL,
	interest_rate STRING NOT NULL,
	renew_info JSONB NULL DEFAULT '{}':::JSONB,
	operative_account_number STRING NOT NULL,
	repay_account_number STRING NOT NULL,
	partner_bank STRING NOT NULL,
	ifsc_code STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	term JSONB NOT NULL DEFAULT '{}':::JSONB,
	nominee_details JSONB NOT NULL DEFAULT '{}':::JSONB,
	is_add_funds_allowed BOOL NOT NULL DEFAULT false,
	closure_info JSONB NULL DEFAULT '{}':::JSONB,
	provenance STRING NOT NULL DEFAULT 'USER_APP':::STRING,
	deposit_template_id STRING NULL,
	goal_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX deposit_accounts_id_key (id ASC),
	UNIQUE INDEX deposit_accounts_account_number_ifsc_code_key (account_number ASC, ifsc_code ASC),
	INDEX deposit_accounts_maturity_date_idx (maturity_date ASC),
	INDEX deposit_accounts_updated_at_idx (updated_at ASC),
	INDEX deposit_account_template_id_idx (deposit_template_id ASC),
	INDEX deposit_accounts_goal_id_idx (goal_id ASC),
	FAMILY frequently_updated (principal_amount, running_balance, updated_at, is_add_funds_allowed, goal_id),
	FAMILY seldomly_updated (state, maturity_date, maturity_amount, interest_rate, closure_info, deposit_template_id),
	FAMILY "primary" (id, actor_id, account_number, name, type, scheme_code, renew_info, operative_account_number, repay_account_number, partner_bank, ifsc_code, created_at, deleted_at, term, nominee_details, provenance)
);
COMMENT ON COLUMN public.deposit_accounts.goal_id IS 'stores goal_id if a goal is linked with the deposit';
CREATE TABLE public.deposit_requests (
	id STRING NOT NULL,
	request_id STRING NULL,
	client_request_id STRING NOT NULL,
	deposit_account_id STRING NULL,
	type STRING NOT NULL,
	state STRING NOT NULL,
	deposit_info JSONB NULL DEFAULT '{}':::JSONB,
	partner_bank STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	actor_id STRING NOT NULL,
	last_attempt BOOL NOT NULL DEFAULT false,
	detailed_status JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX deposit_requests_id_key (id ASC),
	UNIQUE INDEX deposit_requests_request_id_key (request_id ASC),
	INDEX deposit_requests_client_request_id_idx (client_request_id ASC),
	INDEX deposit_requests_deposit_account_id_idx (deposit_account_id ASC),
	INDEX deposit_requests_updated_at_idx (updated_at ASC),
	INDEX deposit_requests_created_at_idx (created_at ASC),
	INDEX deposit_requests_state_idx (state ASC),
	FAMILY frequently_updated (state, detailed_status, updated_at),
	FAMILY seldomly_updated (last_attempt, deleted_at),
	FAMILY "primary" (id, request_id, client_request_id, deposit_account_id, type, deposit_info, partner_bank, created_at, actor_id)
);
CREATE TABLE public.card_limits (
	card_id UUID NOT NULL,
	card_limit_data JSONB NOT NULL,
	state STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (card_id ASC),
	INDEX card_limits_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.contacts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	phone_number_hash STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT contacts_pkey PRIMARY KEY (actor_id ASC, phone_number_hash ASC),
	UNIQUE INDEX contacts_id_uniq_idx (id ASC),
	INDEX contacts_updated_at_idx (updated_at ASC),
	INDEX contacts_phone_number_hash_idx (phone_number_hash ASC)
);
CREATE TABLE public.savings_ledger_recons (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	savings_account_id STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	partner_bank STRING NOT NULL,
	last_reconciled_at TIMESTAMPTZ NULL,
	last_transaction_timestamp TIMESTAMPTZ NULL,
	CONSTRAINT savings_ledger_recons_pkey PRIMARY KEY (savings_account_id ASC),
	UNIQUE INDEX account_recons_uniq_idx (id ASC),
	INDEX savings_ledger_recons_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (status, updated_at, last_reconciled_at, last_transaction_timestamp),
	FAMILY "primary" (id, savings_account_id, created_at, deleted_at, partner_bank)
);
CREATE TABLE public.privacy_preferences (
	actor_id STRING NOT NULL,
	discoverability STRING NOT NULL DEFAULT 'DISCOVERABLE':::STRING,
	view_balance_on_home_screen STRING NOT NULL DEFAULT 'SHOW':::STRING,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT privacy_preferences_pkey PRIMARY KEY (actor_id ASC),
	INDEX privacy_preferences_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.card_delivery_trackings (
	card_id UUID NOT NULL,
	state STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	card_qr_data JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (card_id ASC),
	INDEX card_delivery_trackings_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.card_delivery_trackings.card_qr_data IS '{"proto_type":"CardDeliveryTracking.CARD_QR_DATA", "comment":"defines data required for physical card activation (qr code scan) and attributes related to it.}';
CREATE TABLE public.vkyc_summaries (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	metadata JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX vkyc_summaries_actor_id_key (actor_id ASC),
	INDEX vkyc_summaries_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.vkyc_call_schedules (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	start_time TIMESTAMPTZ NOT NULL,
	end_time TIMESTAMPTZ NOT NULL,
	agent_id STRING NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	ref_id STRING NOT NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX vkyc_call_schedules_ref_id_idx (ref_id ASC),
	INDEX vkyc_call_schedules_agent_id_idx (agent_id ASC),
	INDEX vkyc_call_schedules_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (status, sub_status, updated_at),
	FAMILY "primary" (id, start_time, end_time, agent_id, vendor, created_at, ref_id)
);
CREATE TABLE public.vkyc_attempts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	vkyc_summary_id UUID NOT NULL,
	attempt_source STRING NOT NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	customer_info_id UUID NOT NULL,
	attempt_reason STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX vkyc_attempts_vkyc_summary_id (vkyc_summary_id ASC),
	INDEX vkyc_attempts_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (status, sub_status, updated_at, vendor, attempt_reason),
	FAMILY "primary" (id, vkyc_summary_id, attempt_source, created_at, deleted_at, customer_info_id)
);
CREATE TABLE public.vkyc_karza_call_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	call_type STRING NOT NULL,
	call_metadata JSONB NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	vkyc_attempt_id UUID NOT NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX vkyc_karza_call_infos_vkyc_attempt_id (vkyc_attempt_id ASC),
	INDEX vkyc_karza_call_infos_updated_at_idx (updated_at ASC),
	INDEX vkyc_karza_call_infos_status_created_at_idx (status ASC, created_at ASC),
	FAMILY frequently_updated (status, sub_status, updated_at),
	FAMILY "primary" (id, call_type, call_metadata, created_at, vkyc_attempt_id)
);
CREATE TABLE public.vkyc_karza_customer_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	vkyc_summary_id UUID NOT NULL,
	transaction_metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	transaction_id STRING NOT NULL,
	customer_id STRING NOT NULL,
	vkyc_priority_type STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX vkyc_karza_customer_infos_vkyc_summary_id (vkyc_summary_id ASC),
	INDEX vkyc_karza_customer_infos_transaction_id (transaction_id ASC),
	INDEX vkyc_karza_customer_infos_updated_at_idx (updated_at ASC),
	UNIQUE INDEX vkyc_karza_customer_infos_customer_id_transaction_id_idx (customer_id ASC, transaction_id ASC)
);
COMMENT ON COLUMN public.vkyc_karza_customer_infos.vkyc_priority_type IS '{"proto_type":"kyc.vkyc.VKYCPriorityType", "comment": "This is enum to denote user priority", "ref": "api/kyc/vkyc/internal/vkyc_karza_customer_info.proto"}';
CREATE TABLE public.user_group_mappings (
	user_email STRING NULL,
	user_group STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	identifier_type STRING NOT NULL,
	identifier_value STRING NOT NULL,
	agent_details JSONB NULL,
	CONSTRAINT user_group_mappings_pkey PRIMARY KEY (identifier_value ASC, identifier_type ASC, user_group ASC, deleted_at_unix ASC),
	INDEX user_group_mappings_updated_at_idx (updated_at ASC),
	INDEX user_group_mappings_user_email_idx (user_email ASC)
);
COMMENT ON COLUMN public.user_group_mappings.identifier_type IS 'stores type of identifier like email, identifier value, phone number, etc.';
COMMENT ON COLUMN public.user_group_mappings.identifier_value IS 'stores value of identifier';
COMMENT ON COLUMN public.user_group_mappings.agent_details IS 'stores details of agents who created or deleted this entry';
CREATE TABLE public.card_block_details (
	card_id UUID NOT NULL,
	reason STRING NOT NULL,
	provenance STRING NOT NULL,
	state STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (card_id ASC),
	INDEX card_block_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.shipping_preference_vendor_requests (
	id STRING NOT NULL,
	vendor STRING NOT NULL,
	preference_id STRING NOT NULL,
	status STRING NOT NULL,
	request_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT shipping_preference_vendor_requests_pkey PRIMARY KEY (vendor ASC, request_id ASC),
	UNIQUE INDEX shipping_preference_vendor_requests_request_id_key (request_id ASC),
	UNIQUE INDEX shipping_preference_vendor_requests_id_key (id ASC),
	INDEX shipping_preference_vendor_requests_request_id_idx (request_id ASC),
	INDEX shipping_preference_vendor_requests_updated_at_idx (updated_at ASC),
	INDEX shipping_preference_vendor_requests_created_at_status_idx (created_at ASC, status ASC),
	INDEX shipping_preference_vendor_requests_vendor_preference_id_idx (vendor ASC, preference_id ASC)
);
CREATE TABLE public.vkyc_karza_call_histories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	call_info_id UUID NULL,
	transaction_id STRING NULL,
	metadata JSONB NULL,
	event_time TIMESTAMPTZ NULL,
	vkyc_karza_call_info_sub_status STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	request_id STRING NOT NULL DEFAULT '':::STRING,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX vkyc_karza_call_histories_transaction_id (transaction_id ASC),
	INDEX vkyc_karza_call_histories_call_info_id (call_info_id ASC),
	UNIQUE INDEX vkyc_karza_call_histories_request_id_uniq_idx (request_id ASC),
	INDEX vkyc_karza_call_histories_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.transaction_notification_map (
	transaction_id STRING NOT NULL,
	credit_sms_id STRING NULL,
	debit_sms_id STRING NULL,
	credit_push_notif_id STRING NULL,
	debit_push_notif_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	rowid INT8 NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	collect_pn_id STRING NULL,
	reversal_sms_id STRING NULL,
	reversal_push_notif_id STRING NULL,
	comms_id_for_credit_email STRING NULL,
	CONSTRAINT transaction_notification_map_pkey PRIMARY KEY (rowid ASC),
	INDEX transaction_notification_map_transaction_id_idx (transaction_id ASC),
	INDEX transaction_notification_map_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.transaction_notification_map.reversal_sms_id IS 'column added to add persistence to SMS of reversed transactions';
COMMENT ON COLUMN public.transaction_notification_map.reversal_push_notif_id IS 'column added to add persistence to notification of reversed transactions';
CREATE TABLE public.merchants (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	logo_url STRING NULL,
	display_name STRING NULL,
	verified_name STRING NULL,
	brand_name STRING NULL,
	legal_name STRING NULL,
	mcc JSONB NULL,
	mid JSONB NULL,
	merchant_size STRING NULL,
	merchant_genre STRING NULL,
	onboarding_source STRING NULL,
	ownership_type STRING NULL,
	extra_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	capabilities JSONB NOT NULL DEFAULT '{}':::JSONB,
	ds_merchant_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX merchants_updated_at_idx (updated_at ASC),
	UNIQUE INDEX merchants_ds_merchant_id_key (ds_merchant_id ASC),
	INDEX merchants_display_name_idx (display_name ASC),
	FAMILY seldom_updated (logo_url, display_name, verified_name, brand_name, legal_name, merchant_size, merchant_genre, onboarding_source, ownership_type, capabilities, ds_merchant_id),
	FAMILY seldom_updated_merhcant_identifiers (mcc, mid, extra_info, updated_at),
	FAMILY "primary" (id, created_at, deleted_at)
);
COMMENT ON TABLE public.merchants IS 'main merchants table which stores bare minimum merchant information';
COMMENT ON COLUMN public.merchants.ds_merchant_id IS 'This column store the Merchant Id from data science team, it is  identifier for internal purpose.';
CREATE TABLE public.merchant_pis (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	merchant_id UUID NOT NULL,
	pi_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	old_merchant_id UUID NULL,
	merge_provenance STRING NULL,
	ds_confidence STRING NULL,
	ds_model_version STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX pi_id_unique_idx (pi_id ASC),
	INDEX merchant_pis_updated_at_idx (updated_at ASC),
	INDEX merchant_pis_merchant_id_idx (merchant_id ASC),
	INDEX merchant_pis_old_merchant_id_key (old_merchant_id ASC) STORING (merchant_id)
);
COMMENT ON TABLE public.merchant_pis IS 'This table holds mapping between merchant id and payment instrument id, one merchant can have multiple payment instrument id';
COMMENT ON COLUMN public.merchant_pis.merge_provenance IS '{"proto_type":"enums.MergeProvenance", "comment": "Enum to store provenance.", "ref": "api.merchant.enums.merchant_pi.proto"}';
COMMENT ON COLUMN public.merchant_pis.ds_confidence IS '{"proto_type":"merchant.DsConfidence", "comment": "To store confidence from merchant resolution api.", "ref": "api.merchant.merchant_pi.proto"}';
COMMENT ON COLUMN public.merchant_pis.ds_model_version IS '{"proto_type":"merchant.DsModelVersion", "comment": "To store model version from merchant resolution api.", "ref": "api.merchant.merchant_pi.proto"}';
CREATE TABLE public.vpa_merchant_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	merchant_store_id STRING NULL,
	merchant_terminal_id STRING NULL,
	merchant_genre STRING NULL,
	brand_name STRING NULL,
	legal_name STRING NULL,
	franchise_name STRING NULL,
	ownership_type STRING NULL,
	onboarding_type STRING NULL,
	merchant_type STRING NULL,
	sub_code STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	vpa STRING NOT NULL,
	mcc STRING NULL,
	restricted_account_type_details JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX vpa_merchant_infos_vpas_unique_idx (vpa ASC),
	INDEX vpa_merchant_infos_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.vpa_merchant_infos IS 'stores the information related to VPA of a merchant. This entity will sit with UPI service. Right now we have used json b as info column and dumped everything we get here. The idea is to slowly get a understanding of what fields are actually needed and queryable and then move them out as a separate column';
COMMENT ON COLUMN public.vpa_merchant_infos.restricted_account_type_details IS 'Stores account types which are restricted by merchant.';
CREATE TABLE public.do_once_tasks (
	task_name STRING NOT NULL,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT do_once_tasks_pkey PRIMARY KEY (task_name ASC, deleted_at_unix ASC),
	INDEX do_once_tasks_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.card_auth_attempts (
	attempt_id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NOT NULL,
	card_action STRING NOT NULL,
	state STRING NOT NULL,
	auth_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	action_state STRING NULL,
	action_stage_details JSONB NULL,
	CONSTRAINT card_auth_attempts_pkey PRIMARY KEY (attempt_id ASC),
	INDEX card_auth_attempts_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.card_auth_attempts IS 'table to store details regarding the auth attempts details required for a given card action';
COMMENT ON COLUMN public.card_auth_attempts.card_action IS '{"proto_type":"card.CardAuthAttempt.cardAction", "comment": "Defines card action for which auth attempt is initiated. Valid values: RESET_PIN, ACTIVATE, etc"}';
COMMENT ON COLUMN public.card_auth_attempts.state IS '{"proto_type":"card.CardAuthAttempt.state", "comment": "Defines state of auth attempt. Valid values: SUCCESS, LIVENESS_FAILED, FACEMATCH_FAILED, etc"}';
COMMENT ON COLUMN public.card_auth_attempts.auth_type IS '{"proto_type":"card.CardAuthAttempt.authType", "comment": "Auth required for a given card action. Valid values: LIVENESS_AND_FACEMATCH, etc"}';
COMMENT ON COLUMN public.card_auth_attempts.action_state IS '{"proto_type":"CardAuthAttempt.ActionState", "comment": "Terminal state corresponding to the card action for which auth was initiated"}';
COMMENT ON COLUMN public.card_auth_attempts.action_stage_details IS '{"proto_type":"CardAuthAttempt.ActionStageDetails", "comment": "Stage details corresponding to each stage performed to complete an action along with the failure response codes and reason from vendor"}';
CREATE TABLE public.vkyc_notifications (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	comms_message_id STRING NOT NULL,
	notification_metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX vkyc_notifications_actor_id_idx (actor_id ASC),
	INDEX vkyc_notifications_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.vkyc_notifications IS 'This table stores the information about notifications sent to users related to vkyc, depends on comms for more information using comms_message_id';
COMMENT ON COLUMN public.vkyc_notifications.notification_metadata IS '{"proto_type":"vkyc.notification_metadata", "comment":"defines the communication medium used to send notification. Also contains notification type in case of push notification", "ref": "https://github.com/epiFi/protos/blob/master/api/kyc/vkyc/internal/vkyc_notification.proto"}';
CREATE TABLE public.card_merchant_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pi_id STRING NOT NULL,
	mcc STRING NULL,
	mid STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	address JSONB NULL,
	tid STRING NULL,
	acquirer STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX pi_id_unique_idx (pi_id ASC),
	INDEX card_merchant_infos_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.card_merchant_infos IS 'stores merchant information card merchants, comes under the scope of merchant service';
COMMENT ON COLUMN public.card_merchant_infos.address IS '{"proto_type":"types.PostalAddress", "comment":"address of the merchant"}';
COMMENT ON COLUMN public.card_merchant_infos.tid IS '{"proto_type":"merhant.card_merchant_info.Tid", "comment": "To store Terminal id of the source of the transaction of the  Merchant", "ref": "api.merchant.card_merchant_info.proto"}';
COMMENT ON COLUMN public.card_merchant_infos.acquirer IS '{"proto_type":"merhant.card_merchant_info.Acquirer", "comment": "to store Acquirer id of the Source of the transaction of the Merchant","ref": "api.merchant.card_merchant_info.proto"}';
CREATE TABLE public.vkyc_holidays (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	start_time TIMESTAMPTZ NOT NULL,
	end_time TIMESTAMPTZ NOT NULL,
	deleted_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX vkyc_holidays_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.vkyc_holidays IS 'This table stores the information about holiday where start_time and end_time denotes the time interval between which banner will not be shown';
COMMENT ON COLUMN public.vkyc_holidays.start_time IS '{"proto_type":"google.protobuf.Timestamp"}';
COMMENT ON COLUMN public.vkyc_holidays.end_time IS '{"proto_type":"google.protobuf.Timestamp"}';
CREATE TABLE public.vendor_responses (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	vendor STRING NOT NULL,
	response_code STRING NULL,
	rpc_status_code STRING NOT NULL,
	api STRING NOT NULL,
	request_id STRING NULL,
	trace_id STRING NULL,
	deleted_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	response_description STRING NULL,
	raw_response STRING NULL,
	CONSTRAINT vendor_responses_pkey PRIMARY KEY (api ASC, actor_id DESC, created_at DESC, id ASC),
	UNIQUE INDEX vendor_responses_id_key (id ASC),
	INDEX vendor_responses_request_id_idx (request_id ASC),
	INDEX vendor_responses_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.vendor_responses IS 'It store response code and description when a vendor API is called';
COMMENT ON COLUMN public.vendor_responses.response_code IS '{"comment": "Denotes the response code received from vendor"}';
COMMENT ON COLUMN public.vendor_responses.rpc_status_code IS '{"comment": "Denotes status code for api as interpreted by backend"}';
COMMENT ON COLUMN public.vendor_responses.api IS '{"comment": "Denotes the api called. eg - Customer creation, Account creation"}';
COMMENT ON COLUMN public.vendor_responses.response_description IS '{"comment":"response_description will store description from vendor)"}';
CREATE TABLE public.disputed_transactions (
	transaction_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	dispute_state STRING NOT NULL,
	disputed_at TIMESTAMPTZ NOT NULL,
	last_dispute_event_published_at TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT disputed_transactions_pkey PRIMARY KEY (transaction_id ASC, actor_id ASC),
	INDEX disputed_transactions_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (last_dispute_event_published_at, dispute_state, updated_at),
	FAMILY seldom_updated (disputed_at),
	FAMILY "primary" (transaction_id, actor_id, created_at, deleted_at)
);
CREATE TABLE public.ios_device_attestation_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	device_id STRING NOT NULL,
	public_key STRING NULL,
	receipt STRING NULL,
	counter INT8 NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_ios_device_attestation_data_device_id (device_id ASC),
	INDEX idx_ios_device_attestation_data_public_key (public_key ASC),
	INDEX ios_device_attestation_data_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, device_id, public_key, receipt, created_at, deleted_at),
	FAMILY frequently_updated (counter, updated_at)
);
CREATE TABLE public.card_skus (
	card_sku_type STRING NOT NULL,
	free_card_replacements INT8 NOT NULL,
	vendor_card_sku JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT card_skus_pkey PRIMARY KEY (card_sku_type ASC),
	INDEX card_skus_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.card_skus IS 'This table stores the information about card SKU and corresponding vendor specific info for that sku type';
COMMENT ON COLUMN public.card_skus.vendor_card_sku IS '{"proto_type":"CardSKU.vendor_card_sku", "comment":"defines vendor specific data about card variants}';
CREATE TABLE public.card_sku_overrides (
	actor_id STRING NOT NULL,
	card_sku_type STRING NOT NULL,
	free_card_replacements INT8 NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	free_card_replacements_issued INT8 NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, card_sku_type ASC),
	INDEX card_sku_overrides_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.card_sku_overrides IS 'Card SKU data is defined across actors. For example: number of free cards is a property of a card variant and applies to all actors. However, there could be exceptions per actor. For eg: higher free card for privileged users. An entry in this table overrides the global SKU data for an actor for a sku type.';
COMMENT ON COLUMN public.card_sku_overrides.free_card_replacements_issued IS '{"proto_type":"CardSKUOverride.FreeCardReplacementIssued", "comment": "Number of free cards replacements already issued to the user"}';
CREATE TABLE public.device_reg_attempts (
	attempt_id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	recepient_phone_number STRING NOT NULL,
	request_id STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	device_id STRING NOT NULL,
	failure_reason STRING NULL,
	sms_info JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT device_reg_attempts_pkey PRIMARY KEY (actor_id ASC, attempt_id ASC),
	UNIQUE INDEX device_reg_attempts_attempt_id_key (attempt_id ASC),
	INDEX actor_idx (actor_id ASC, updated_at ASC),
	INDEX updated_at_idx (updated_at ASC, recepient_phone_number ASC, status ASC),
	INDEX device_reg_attempts_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.finite_codes (
	id STRING NOT NULL,
	code STRING NOT NULL,
	actor_id STRING NULL,
	channel STRING NOT NULL,
	type STRING NOT NULL,
	claim_limit INT8 NOT NULL,
	claimed_count INT8 NOT NULL,
	is_active BOOL NULL DEFAULT true,
	skip_employment_check_privilege BOOL NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	referral_invite_link_info JSONB NULL DEFAULT '{}':::JSONB,
	phone_number_referral_code STRING NULL,
	CONSTRAINT finite_code_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX finite_code_id_key (id ASC),
	UNIQUE INDEX finite_code_code_key (code ASC),
	INDEX finite_code_updated_at_idx (updated_at ASC),
	UNIQUE INDEX finite_code_actor_id_channel_type_is_active_key (actor_id ASC, channel ASC, type ASC, is_active ASC),
	INDEX finite_codes_type_idx (type ASC),
	UNIQUE INDEX finite_code_phone_number_referral_code_idx (phone_number_referral_code ASC),
	FAMILY "primary" (id, code, actor_id, channel, type, claim_limit, skip_employment_check_privilege, created_at, deleted_at, referral_invite_link_info, phone_number_referral_code),
	FAMILY frequently_updated (claimed_count, updated_at),
	FAMILY seldomly_updated (is_active)
);
COMMENT ON TABLE public.finite_codes IS 'table to store finite codes shared by referrer and used by referee to onboard on the app';
COMMENT ON COLUMN public.finite_codes.channel IS '{"proto_type":"inappreferral.FiniteCodeChannel", "comment": "enum to specify the channel for which a finite code is generated"}';
COMMENT ON COLUMN public.finite_codes.type IS '{"proto_type":"inappreferral.FiniteCodeType", "comment": "enum to specify the type of a finite code, i.e. regular, golden_ticket, etc."}';
COMMENT ON COLUMN public.finite_codes.referral_invite_link_info IS '{"proto_type":"inappreferral.ReferralInviteLinkInfo", "comment":"stores info regarding a unique link which can be shared across for inviting, i.e. refer"}';
COMMENT ON COLUMN public.finite_codes.phone_number_referral_code IS '{"comment":"stores phone number which can be used as referral code"}';
CREATE TABLE public.finite_code_claims (
	id STRING NOT NULL,
	referrer_actor_id STRING NULL,
	referee_actor_id STRING NOT NULL,
	finite_code_id STRING NOT NULL,
	referee_onboarding_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	comms_info JSONB NULL DEFAULT '{}':::JSONB,
	code_metadata JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT finite_code_claims_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX finite_code_claims_id_key (id ASC),
	UNIQUE INDEX finite_code_claims_referee_actor_id_key (referee_actor_id ASC),
	INDEX finite_code_claims_updated_at_idx (updated_at ASC),
	INDEX finite_code_claims_referrer_actor_id_finite_code_id_idx (referrer_actor_id ASC, finite_code_id ASC),
	FAMILY "primary" (id, referrer_actor_id, referee_actor_id, finite_code_id, created_at, deleted_at, code_metadata),
	FAMILY frequently_updated (referee_onboarding_status, updated_at, comms_info)
);
COMMENT ON TABLE public.finite_code_claims IS 'table to store finite codes claim records helping in figuring out what all finite code were claimed by referee to onboard';
COMMENT ON COLUMN public.finite_code_claims.comms_info IS '{"proto_type":"inappreferral.FiniteCodeClaim.CommsInfo", "comment": "field storing comms related information specific to referrals"}';
COMMENT ON COLUMN public.finite_code_claims.code_metadata IS '{"proto_type":"inappreferral.ReferralCodeMetadata", "comment":"stores extra info regarding the code that was used while claiming referral"}';
CREATE TABLE public.card_tracking_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NOT NULL,
	order_id UUID NOT NULL DEFAULT gen_random_uuid(),
	awb STRING NULL,
	carrier STRING NULL,
	request_state STRING NULL,
	delivery_state STRING NULL,
	scans JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	fetch_awb_retry_count INT8 NULL,
	register_shipment_retry_count INT8 NULL,
	pickup_date TIMESTAMPTZ NULL,
	uploaded_at TIMESTAMPTZ NULL,
	printing_vendor STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX card_tracking_requests_order_id_unique_idx (order_id ASC),
	INDEX card_id_lookup_idx (card_id ASC, created_at ASC),
	INDEX request_state_lookup_idx (request_state ASC),
	INDEX card_tracking_requests_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.card_tracking_requests IS 'table to store card tracking requests information such as awb number, carrier, order_id(unique identifier for each shipment), card_id(unique card identifier), tracking states and scans for a given shipment.';
COMMENT ON COLUMN public.card_tracking_requests.request_state IS '{"proto_type":"cardTrackingRequest.RequestState", "comment": "enum to store various states such as AWB_FETCHED_FROM_BANK, REGISTERED_AT_SHIPWAY etc}';
COMMENT ON COLUMN public.card_tracking_requests.delivery_state IS '{"proto_type":"cardTrackingRequest.DeliveryState", "comment": "enum to store delivery states of shipment such as SHIPPED, IN_TRANSIT, OUT_FOR_DELIVERY, DELIVERED etc}';
COMMENT ON COLUMN public.card_tracking_requests.scans IS '{"proto_type":"cardTrackingRequest.Scans", "comment": A package is scanned multiple times throughout the journey. On every scan, there is an update in tracking information. Storing information for all the scans"}';
COMMENT ON COLUMN public.card_tracking_requests.uploaded_at IS '{"proto_type":"CardTrackingRequest.UploadedAt", "comment":"time at which tracking details such as awb and courier partner is received"}';
COMMENT ON COLUMN public.card_tracking_requests.printing_vendor IS '{"proto_type":"CardTrackingRequest.PrintingVendor", "comment":"card printing vendor"}';
CREATE TABLE public.credit_reports (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	presence_status STRING NULL,
	verification_status STRING NULL,
	credit_report_data JSONB NULL,
	verification_failure_reasons JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	download_consent STRING NULL,
	download_status STRING NULL,
	vendor STRING NOT NULL,
	credit_report_data_raw JSONB NULL,
	credit_presence_check_at TIMESTAMPTZ NULL,
	credit_consent_given_at TIMESTAMPTZ NULL,
	credit_verification_at TIMESTAMPTZ NULL,
	credit_score_threshold INT8 NULL,
	computed_is_data_purged BOOL NULL AS ((credit_report_data IS NULL) AND (credit_report_data_raw IS NULL)) STORED,
	purged_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX credit_report_created_at_computed_is_data_purged_index (created_at ASC, computed_is_data_purged ASC),
	INDEX credit_reports_updated_at_idx (updated_at ASC),
	INDEX credit_reports_actor_id_created_at_idx (actor_id ASC, created_at DESC),
	INDEX credit_reports_computed_is_data_purged_credit_consent_given_at_idx (computed_is_data_purged ASC, credit_consent_given_at ASC)
);
COMMENT ON COLUMN public.credit_reports.presence_status IS '{"proto_type":"user.creditreport.PresenceStatus", "comment": "enum to store possible status of report presence check as its an async call", "ref": "api/user/creditreport/internal/credit_report.proto"}';
COMMENT ON COLUMN public.credit_reports.verification_status IS '{"proto_type":"user.creditreport.VerificationStatus", "comment": "enum to store possible statuses for credit report verification", "ref": "api/user/creditreport/internal/credit_report.proto"}';
COMMENT ON COLUMN public.credit_reports.credit_report_data IS '{"proto_type":"user.creditreport.CreditReportData", "comment": "This stores credit report data for an user", "ref": "api/user/creditreport/internal/credit_report.proto"}';
COMMENT ON COLUMN public.credit_reports.verification_failure_reasons IS '{"proto_type":"user.creditreport.VerificationFailureReasons", "comment": "This stores failure reasons for credit report verification", "ref": "api/user/creditreport/internal/credit_report.proto"}';
COMMENT ON COLUMN public.credit_reports.credit_report_data_raw IS '{"proto_type":"vendorgateway.credit_report.CreditReportData", "comment": "stores data received from vendor as it is"}';
COMMENT ON COLUMN public.credit_reports.credit_presence_check_at IS 'credit_presence_check_at stores the time at which credit report presence check is done';
COMMENT ON COLUMN public.credit_reports.credit_consent_given_at IS 'credit_consent_given_at stores the time at which consent to download credit report is recorded';
COMMENT ON COLUMN public.credit_reports.credit_verification_at IS 'credit_verification_at stores the time at which credit report verification is done';
COMMENT ON COLUMN public.credit_reports.credit_score_threshold IS 'credit_score_threshold stores the threshold against which user credit score is matched';
CREATE TABLE public.employment_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	employment_type STRING NULL,
	employment_info JSONB NULL,
	org_pf_data JSONB NULL,
	verification_result STRING NULL,
	verification_process_status STRING NULL,
	rejection_reason STRING NULL,
	hold_reason STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	accepted_reason STRING NULL,
	processing_intent STRING NULL,
	employer_id STRING NULL,
	updated_by_source STRING NULL,
	updated_by_source_identifier STRING NULL,
	occupation_type STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_employment_data_updated_at (updated_at ASC),
	UNIQUE INDEX uinq_idx_employment_data_actor_id_deleted_at (actor_id ASC) WHERE deleted_at IS NULL,
	FAMILY "primary" (id, actor_id, employment_type, employment_info, verification_result, verification_process_status, rejection_reason, hold_reason, created_at, updated_at, deleted_at, accepted_reason, processing_intent, employer_id, updated_by_source, updated_by_source_identifier, occupation_type),
	FAMILY seldom_updated (org_pf_data)
);
COMMENT ON COLUMN public.employment_data.employment_type IS '{"proto_type":"employment.EmploymentType", "comment": "Enum to store employment type of the user. eg. SALARIED, SELF_EMPLOYED", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.employment_info IS '{"proto_type":"employment.EmploymentInfo", "comment": "This stores the information user has provided about their employment.", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.verification_result IS '{"proto_type":"employment.EmploymentVerificationResult", "comment": "Enum to store the outcome of the employment verification process", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.verification_process_status IS '{"proto_type":"employment.EmploymentVerificationProcessStatus", "comment": "Enum to store the status of the employment verification process", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.rejection_reason IS '{"proto_type":"employment.RejectionReason", "comment": "Reason for rejecting the user during verification process", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.hold_reason IS '{"proto_type":"employment.HoldReason", "comment": "Reason for keeping the user in HOLD status", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.accepted_reason IS '{"proto_type":"employment.AcceptedReason", "comment": "Reason for keeping the user in ACCEPTED status", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.processing_intent IS '{"proto_type":"employment.processing_intent", "comment": "Enum to store employment processing-intent for the user. eg. DECLARATION_ONLY, VERIFICATION", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.updated_by_source IS '{"proto_type":"employment.UpdateSource", "comment": "stores the source by which employment details of the user was updated", "ref": "api.employment.employment_data.proto"}';
COMMENT ON COLUMN public.employment_data.updated_by_source_identifier IS 'stores identifier of the update source. ex. agent email id in case update source is sherlock dashboards';
CREATE TABLE public.actor_add_fund_options_map (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	add_fund_option JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT actor_add_fund_options_map_pkey PRIMARY KEY (id ASC),
	INDEX actor_add_fund_options_map_actor_id_idx (actor_id ASC),
	INDEX actor_add_fund_options_map_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.actor_add_fund_options_map IS 'table to store add fund option for an actor';
COMMENT ON COLUMN public.actor_add_fund_options_map.actor_id IS '{"proto_type":"order.ActorAddFunOptionMap", "comment":"actor id for whom the add fund option is"}';
COMMENT ON COLUMN public.actor_add_fund_options_map.add_fund_option IS '{"proto_type":"order.AddFundOption", "comment":"contains the option show to user for adding funds, like default amount, suggested amount etc."}';
CREATE TABLE public.account_statement_requests (
	id STRING NOT NULL,
	account_id STRING NOT NULL,
	account_type STRING NOT NULL,
	stage STRING NOT NULL,
	from_date TIMESTAMPTZ NOT NULL,
	to_date TIMESTAMPTZ NOT NULL,
	req_type STRING NULL,
	email_msg_id STRING NULL,
	notification_msg_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	client_req_id STRING NULL,
	CONSTRAINT account_statement_requests_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX account_statement_requests_id_key (id ASC),
	UNIQUE INDEX account_statement_requests_email_msg_id_key (email_msg_id ASC),
	UNIQUE INDEX account_statement_requests_notification_msg_id_key (notification_msg_id ASC),
	INDEX idx_from_date_to_date (from_date ASC, to_date ASC),
	INDEX idx_account_id_account_type (account_id ASC, account_type ASC),
	INDEX account_statement_requests_updated_at_idx (updated_at ASC),
	UNIQUE INDEX account_statement_requests_client_req_id_key (client_req_id ASC),
	FAMILY "primary" (id, account_id, account_type, from_date, to_date, req_type, created_at, deleted_at, client_req_id),
	FAMILY frequently_updated (stage, updated_at),
	FAMILY seldom_updated (email_msg_id, notification_msg_id)
);
COMMENT ON COLUMN public.account_statement_requests.client_req_id IS '{"proto_type": "account.statement.AccountStatementRequests.ClientReqId" ,"comment": "it is used by other service to get statement."}';
CREATE TABLE public.oauth_signup_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	user_id STRING NOT NULL,
	oauth_provider STRING NOT NULL,
	profile JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	refresh_token_last_validated_at TIMESTAMPTZ NULL,
	computed_email STRING NULL AS (CASE WHEN (profile->>'email':::STRING) IS NOT NULL THEN profile->>'email':::STRING ELSE NULL END) STORED,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX oauth_signup_data_user_id_created_at_idx (user_id ASC, created_at ASC),
	INDEX oauth_signup_data_computed_email_created_at_idx (computed_email ASC, created_at DESC)
);
COMMENT ON TABLE public.oauth_signup_data IS 'table to store the data received from oauth providers such as user identifier, name, email';
COMMENT ON COLUMN public.oauth_signup_data.oauth_provider IS '{"proto_type":"OAuthProvider", "comment": "OAuth Provider of a users account."}';
COMMENT ON COLUMN public.oauth_signup_data.profile IS '{"proto_type":"OAuthSignupData.Profile", "comment": "User profile details such as first_name, family_name and email"}';
CREATE TABLE public.liveness_summaries (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	request_id STRING NOT NULL,
	liveness_attempt_id STRING NULL,
	facematch_attempt_id STRING NULL,
	liveness_flow STRING NOT NULL,
	facematch_image JSONB NULL,
	max_attempts INT8 NOT NULL,
	attempts_count INT8 NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	summary_liveness_status STRING NULL,
	summary_facematch_status STRING NULL,
	strictness_logic STRING NULL,
	force_manual_review STRING NULL,
	expire_at TIMESTAMPTZ NULL,
	client_request_params JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX actor_id_request_id_liveness_flow_key (actor_id ASC, request_id ASC, liveness_flow ASC),
	UNIQUE INDEX id_key (id ASC),
	INDEX liveness_summaries_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.liveness_summaries IS 'table to store status of attempts for liveness and facematch check';
COMMENT ON COLUMN public.liveness_summaries.liveness_flow IS '{"proto_type":"auth.liveness.LivenessFlow", "comment": "flow instance where liveness check is invoked"}';
COMMENT ON COLUMN public.liveness_summaries.status IS '{"proto_type":"auth.liveness.SummaryStatus", "comment": "possible outcomes of user liveness check"}';
COMMENT ON COLUMN public.liveness_summaries.summary_liveness_status IS 'Stores the liveness status of the current liveness attempt in summary';
COMMENT ON COLUMN public.liveness_summaries.summary_facematch_status IS 'Stores the facematch status of the current facematch attempt in summary';
COMMENT ON COLUMN public.liveness_summaries.strictness_logic IS 'Stores the strictness logic of liveness summary';
COMMENT ON COLUMN public.liveness_summaries.force_manual_review IS 'Stores if we want to put the user through manual review';
COMMENT ON COLUMN public.liveness_summaries.expire_at IS 'Stores the expiry time of the liveness summary record';
CREATE TABLE public.inappreferral_unlocks (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	is_unlocked BOOL NOT NULL DEFAULT false,
	average_eod_balance JSONB NOT NULL DEFAULT '{}':::JSONB,
	unlocked_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	comms_info JSONB NOT NULL DEFAULT '{}':::JSONB,
	finite_code_type STRING NOT NULL DEFAULT 'REGULAR':::STRING,
	CONSTRAINT inappreferral_unlocks_pkey PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX inappreferral_unlocks_id_key (id ASC),
	INDEX inappreferral_unlocks_updated_at_idx (updated_at ASC),
	UNIQUE INDEX inappreferral_unlocks_actor_id_finite_code_type_uniq_idx (actor_id ASC, finite_code_type ASC),
	FAMILY "primary" (id, actor_id, created_at, deleted_at, finite_code_type),
	FAMILY frequently_updated (average_eod_balance, updated_at, comms_info),
	FAMILY seldomly_updated (is_unlocked, unlocked_at)
);
COMMENT ON COLUMN public.inappreferral_unlocks.comms_info IS '{"proto_type":"inappreferral.InAppReferralUnlock.CommsInfo", "comment": "field storing comms related information specific to referrals"}';
COMMENT ON COLUMN public.inappreferral_unlocks.finite_code_type IS '{"proto_type":"inappreferral.enums.FiniteCodeType", "comment": "field storing the type of finite code for which in-app referral is/was/will-be unlocked"}';
CREATE TABLE public.pay_campaigns (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	campaign_type STRING NOT NULL,
	last_notification_sent_at TIMESTAMPTZ NULL,
	total_notifications INT8 NOT NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT pay_campaigns_pkey PRIMARY KEY (actor_id ASC, campaign_type ASC),
	FAMILY frequently_updated (last_notification_sent_at, total_notifications, updated_at),
	FAMILY "primary" (id, actor_id, campaign_type, created_at, deleted_at)
);
CREATE TABLE public.upi_request_info (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	request_id STRING NOT NULL,
	response_code STRING NOT NULL,
	total_attempts INT8 NOT NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT upi_request_info_pkey PRIMARY KEY (request_id ASC, response_code ASC),
	FAMILY frequently_updated (total_attempts, updated_at),
	FAMILY "primary" (id, request_id, response_code, created_at, deleted_at)
);
COMMENT ON TABLE public.upi_request_info IS 'table to track the number of times same response code is received for a request';
COMMENT ON COLUMN public.upi_request_info.request_id IS 'unique identifier to identify a request';
COMMENT ON COLUMN public.upi_request_info.response_code IS 'response code received for the request';
COMMENT ON COLUMN public.upi_request_info.total_attempts IS 'total attempts for which same response code is received';
CREATE TABLE public.device_integrity_results (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	device_id STRING NULL,
	attestation STRING NULL,
	device_os STRING NULL,
	result STRING NULL,
	advice STRING NULL,
	failure_reason STRING NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	deleted_at TIMESTAMP NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_device_integrity_actor_id_device_id_created_at (actor_id ASC, device_id ASC, created_at DESC),
	INDEX device_integrity_results_updated_at_idx (updated_at ASC),
	INDEX device_integrity_results_device_id_created_at_idx (device_id ASC, created_at DESC)
);
COMMENT ON COLUMN public.device_integrity_results.device_os IS '{"proto_type":"auth.DeviceOs", "comment": "os type of client ex. android, ios", "ref": "api/auth/device_integrity_result.proto"}';
COMMENT ON COLUMN public.device_integrity_results.result IS '{"proto_type":"auth.Result", "comment": "outcome of device integrity verification", "ref": "api/auth/device_integrity_result.proto"}';
COMMENT ON COLUMN public.device_integrity_results.advice IS '{"comment": "A suggestion for how to get a device back into a good state"}';
COMMENT ON COLUMN public.device_integrity_results.failure_reason IS '{"proto_type":"auth.FailureReason", "comment": "reason why device verification failed", "ref": "api/auth/device_integrity_result.proto"}';
CREATE TABLE public.employment_verification_processes (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	employment_data_id UUID NOT NULL,
	process_name STRING NOT NULL,
	verification_result STRING NULL,
	verification_process_status JSONB NULL,
	accepted_reason STRING NULL,
	hold_reason STRING NULL,
	rejection_reason STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	metadata JSONB NULL,
	computed_work_email STRING NULL AS (IFNULL((metadata->'workEmailVerificationMetadata':::STRING)->>'email':::STRING, NULL)) STORED,
	client_req_id STRING NULL,
	client STRING NOT NULL DEFAULT 'VERIFICATION_PROCESS_CLIENT_UNSPECIFIED':::STRING,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_employment_verification_process_actor_id (actor_id ASC),
	UNIQUE INDEX employment_verification_processes_client_req_id_process_name_idx (client_req_id ASC, process_name ASC),
	INDEX employment_verification_processes_updated_at_idx (updated_at ASC),
	INDEX employment_verification_processes_result_client_computed_work_email_deleted_at_idx (verification_result ASC, client ASC, computed_work_email ASC, deleted_at ASC)
);
COMMENT ON COLUMN public.employment_verification_processes.process_name IS '{"proto_type":"employment.ProcessName", "comment": "Enum to store the verification process to be performed", "ref": "api/employment/employment_verification_process.proto"}';
COMMENT ON COLUMN public.employment_verification_processes.verification_result IS '{"proto_type":"employment.EmploymentVerificationResult", "comment": "Enum to store the outcome of the employment verification process", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_verification_processes.verification_process_status IS '{"comment": "oneof field to  store the status of the employment verification process", "ref": "api/employment/employmenzt_verification_process.proto"}';
COMMENT ON COLUMN public.employment_verification_processes.accepted_reason IS '{"proto_type":"employment.AcceptedReason", "comment": "Reason for keeping the user in ACCEPTED status", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_verification_processes.hold_reason IS '{"proto_type":"employment.HoldReason", "comment": "Reason for keeping the user in HOLD status", "ref": "api/employment/employment_data.proto"}';
COMMENT ON COLUMN public.employment_verification_processes.rejection_reason IS '{"proto_type":"employment.RejectionReason", "comment": "Reason for rejecting the user during verification process", "ref": "api/employment/employment_data.proto"}';
CREATE TABLE public.employment_verification_checks (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	verification_process_id UUID NOT NULL,
	name STRING NULL,
	stage_details JSONB NULL,
	metadata JSONB NULL,
	result STRING NULL,
	reason STRING NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	deleted_at TIMESTAMP NULL,
	result_metadata JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX idx_empl_verification_process_id_name_created_at (verification_process_id ASC, name ASC, created_at DESC),
	INDEX idx_empl_verification_checks_actor_id (actor_id ASC),
	INDEX employment_verification_checks_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.employment_verification_checks.name IS '{"proto_type":"employment.CheckName", "comment": "Enum to store the name of the employment verification check", "ref": "api/employment/employment_verification_check.proto"}';
COMMENT ON COLUMN public.employment_verification_checks.stage_details IS '{"proto_type":"employment.CheckStageDetails", "comment": "oneof field to store status of the all stages that are part of the check", "ref": "api/employment/employment_verification_check.proto"}';
COMMENT ON COLUMN public.employment_verification_checks.result IS '{"proto_type":"employment.CheckResult", "comment": "enum to store the outcome of the verification", "ref": "api/employment/employment_verification_check.proto"}';
COMMENT ON COLUMN public.employment_verification_checks.reason IS '{"proto_type":"employment.CheckResultReason", "comment": "Enum to store the reason for the result", "ref": "api/employment/employment_verification_check.proto"}';
COMMENT ON COLUMN public.employment_verification_checks.result_metadata IS '{"proto_type":"employment.ResultMetadata", "comment": "oneof field to store the data used for verification", "ref": "api/employment/employment_verification_check.proto"}';
CREATE TABLE public.persistent_queue (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	payload_type STRING NOT NULL,
	payload JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	priority FLOAT8 NULL,
	CONSTRAINT "primary" PRIMARY KEY (payload_type ASC, deleted_at_unix ASC, created_at DESC, id ASC),
	UNIQUE INDEX persistent_queue_id_unique_idx (id ASC),
	INDEX persistent_queue_actor_id_index (actor_id ASC, payload_type ASC, deleted_at_unix ASC),
	INDEX persistent_queue_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.persistent_queue IS 'table to store various types of payload in the persistent queue';
COMMENT ON COLUMN public.persistent_queue.payload_type IS '{"proto_type":"persistentqueue.PayloadType", "comment": "the payload stored is based on this type"}';
COMMENT ON COLUMN public.persistent_queue.payload IS '{"proto_type":"persistentqueue.Payload", "comment": "The payload that needs to be stored in the queue"}';
CREATE TABLE public.deposit_account_bonus_payouts (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	deposit_account_id STRING NOT NULL,
	payout_date TIMESTAMPTZ NOT NULL,
	start_date TIMESTAMPTZ NOT NULL,
	end_date TIMESTAMPTZ NOT NULL,
	bonus_amount JSONB NULL DEFAULT '{}':::JSONB,
	is_valid BOOL NULL DEFAULT true,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	payout_number INT8 NOT NULL DEFAULT 1:::INT8,
	deposit_account_number STRING NOT NULL DEFAULT '':::STRING,
	CONSTRAINT deposit_account_bonus_payouts_pkey PRIMARY KEY (deposit_account_id ASC, id ASC),
	UNIQUE INDEX deposit_account_bonus_payouts_id_key (id ASC),
	INDEX deposit_account_bonus_payouts_payout_date_idx (payout_date ASC),
	INDEX deposit_account_bonus_payouts_updated_at_idx (updated_at ASC),
	UNIQUE INDEX deposit_account_id_payout_number_idx (deposit_account_id ASC, payout_number ASC),
	FAMILY "primary" (id, actor_id, deposit_account_id, payout_date, start_date, end_date, bonus_amount, created_at, deleted_at, payout_number, deposit_account_number),
	FAMILY seldom_updated (is_valid, updated_at)
);
COMMENT ON TABLE public.deposit_account_bonus_payouts IS 'table to store bonus payouts for a deposit account, example for booster SD';
CREATE TABLE public.card_action_attempts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NOT NULL,
	actor_id STRING NOT NULL,
	action STRING NOT NULL,
	state STRING NOT NULL,
	vendor_response_code STRING NULL,
	vendor_response_reason STRING NULL,
	internal_response_code STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX card_action_attempts_card_id_lookup_idx (card_id ASC),
	INDEX card_action_attempts_actor_id_lookup_idx (actor_id ASC)
);
COMMENT ON TABLE public.card_action_attempts IS 'table to store details of each card action attempts along with vendor response code and reason';
CREATE TABLE public.aa_account_pis (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	account_id STRING NOT NULL,
	account_type STRING NOT NULL,
	pi_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX aa_account_pis_pi_id_key (pi_id ASC),
	UNIQUE INDEX aa_account_pis_account_id_account_type_actor_id_pi_id_key (account_id ASC, account_type ASC, actor_id ASC, pi_id ASC),
	INDEX aa_account_pis_actor_id_key (actor_id ASC),
	INDEX aa_account_pis_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.aa_account_pis IS '{"proto_type":api.payment_instruments.account_pi.aa_account_pi.proto, "comment":"aa_account_pis stores relation between aa accounts and pis from connected account."}';
CREATE TABLE public.paused_events (
	id STRING NOT NULL,
	event_name STRING NOT NULL,
	start_time TIMESTAMPTZ NOT NULL,
	end_time TIMESTAMPTZ NOT NULL,
	display_message JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT paused_events_pkey PRIMARY KEY (event_name ASC, end_time ASC, start_time ASC),
	UNIQUE INDEX paused_events_id_key (id ASC),
	CONSTRAINT check_end_time_end_time_start_time CHECK ((end_time > now():::TIMESTAMPTZ) AND (end_time >= start_time))
);
CREATE TABLE public.auth_attempts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	phone_number STRING NOT NULL,
	next_action JSONB NULL,
	status_code STRING NOT NULL,
	status_message STRING NOT NULL,
	error_code STRING NULL,
	error_message STRING NULL,
	error_view JSONB NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (phone_number ASC, created_at DESC, id ASC),
	UNIQUE INDEX auth_attempts_id_key (id ASC),
	INDEX auth_attempts_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.auth_attempts IS 'table to store the auth attempt failure scenarios';
COMMENT ON COLUMN public.auth_attempts.next_action IS '{"proto_type":"frontend.Deeplink", "comment": "next action for the user"}';
COMMENT ON COLUMN public.auth_attempts.error_view IS '{"proto_type":"frontend.ErrorView", "comment": "error UI for the client"}';
CREATE TABLE public.recurring_payments (
	id STRING NOT NULL,
	from_actor_id STRING NOT NULL,
	to_actor_id STRING NOT NULL,
	type STRING NOT NULL,
	pi_from STRING NOT NULL,
	pi_to STRING NOT NULL,
	amount JSONB NOT NULL,
	amount_type STRING NOT NULL,
	start_date TIMESTAMPTZ NULL,
	end_date TIMESTAMPTZ NULL,
	recurrence_rule JSONB NOT NULL,
	maximum_allowed_txns INT8 NULL,
	partner_bank STRING NOT NULL,
	preferred_payment_protocol STRING NULL,
	state STRING NOT NULL,
	ownership STRING NOT NULL,
	provenance STRING NULL,
	ui_entry_point STRING NULL,
	initiated_by STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	external_id STRING NOT NULL,
	share_to_payee BOOL NOT NULL DEFAULT true,
	remarks STRING NULL,
	pause_interval JSONB NULL,
	payment_route STRING NOT NULL DEFAULT 'RECURRING_PAYMENT_ROUTE_NATIVE':::STRING,
	entity_ownership STRING NOT NULL DEFAULT 'EPIFI_TECH':::STRING,
	CONSTRAINT recurring_payments_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX recurring_payments_id_key (id ASC),
	INDEX from_actor_id_lookup_idx (from_actor_id ASC),
	UNIQUE INDEX recurring_payments_external_id_unique_idx (external_id ASC),
	INDEX recurring_payments_to_actor_id_covering_idx (to_actor_id ASC) STORING (from_actor_id, type, state, initiated_by, deleted_at, share_to_payee),
	INDEX recurring_payments_from_actor_id_covering_idx (from_actor_id ASC) STORING (to_actor_id, type, state, initiated_by, deleted_at, share_to_payee),
	INDEX recurring_payments_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, from_actor_id, to_actor_id, type, pi_from, pi_to, partner_bank, preferred_payment_protocol, ownership, provenance, ui_entry_point, initiated_by, amount_type, created_at, deleted_at, external_id, share_to_payee, remarks, payment_route, entity_ownership),
	FAMILY frequently_updated (amount, start_date, end_date, recurrence_rule, maximum_allowed_txns, state, updated_at),
	FAMILY seldom_updated (pause_interval)
);
COMMENT ON TABLE public.recurring_payments IS 'table to store information related to recurring payments';
COMMENT ON COLUMN public.recurring_payments.type IS '{"proto_type": "RecurringPayment.RecurringPaymentType", "comment":"types of recurring payment such as standing instruction, upi mandates, e-mandates"}';
COMMENT ON COLUMN public.recurring_payments.state IS '{"proto_type": "RecurringPayment.RecurringPaymentState", "comment":"current state, it can be ACTIVE, PAUSED, REVOKED, EXPIRED"}';
COMMENT ON COLUMN public.recurring_payments.pause_interval IS '{"proto_type":"recurringpayment.pauseInterval", "comment": "time interval for which recurring payment is paused"}';
COMMENT ON COLUMN public.recurring_payments.payment_route IS 'column to identify if a  recurring payment is happening within the fi-federal ecosystem or is it an external';
COMMENT ON COLUMN public.recurring_payments.entity_ownership IS 'column to identify the ownership of the entities that will be created as a result of this recurring payment flow';
CREATE TABLE public.recurring_payments_actions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	recurring_payment_id STRING NOT NULL,
	client_request_id STRING NOT NULL,
	action STRING NOT NULL,
	action_metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	state STRING NOT NULL DEFAULT '':::STRING,
	vendor_request_id STRING NULL,
	expire_at TIMESTAMPTZ NULL,
	initiated_by STRING NOT NULL DEFAULT 'PAYEE':::STRING,
	action_detailed_status JSONB NULL DEFAULT '{}':::JSONB,
	remarks STRING NULL,
	next_actions JSONB NULL,
	action_detailed_status_info JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX recurring_payment_id_action_lookup_idx (recurring_payment_id ASC, action ASC, created_at ASC),
	INDEX recurring_payment_id_action_state_updated_at_lookup_idx (recurring_payment_id ASC, action ASC, state ASC, updated_at ASC),
	UNIQUE INDEX recurring_payment_actions_request_id_unique_idx (vendor_request_id ASC),
	INDEX recurring_payment_actions_client_req_id_lookup_idx (client_request_id ASC),
	INDEX recurring_payments_actions_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, recurring_payment_id, client_request_id, action, action_metadata, created_at, updated_at, deleted_at, vendor_request_id, expire_at, initiated_by, remarks, next_actions, action_detailed_status_info),
	FAMILY frequently_updated (state, action_detailed_status)
);
COMMENT ON TABLE public.recurring_payments_actions IS 'table to store information related to actions performed for a recurring payment, it also acts as a mapping between action performed and its corresponding client request id which is same as the client request id for an order';
COMMENT ON COLUMN public.recurring_payments_actions.action IS '{"proto_type": "RecurringPaymentActions.Action", "comment":"action performed for a recurring payment"}';
COMMENT ON COLUMN public.recurring_payments_actions.action_metadata IS '{proto_type": "RecurringPaymentActions.ActionMetadata", "comment":"details corresponding to an action. For eg: For modify action we will store details of existing and updated parameters"}';
COMMENT ON COLUMN public.recurring_payments_actions.action_detailed_status IS '{"comment": "stores the status details we get from status check"}';
COMMENT ON COLUMN public.recurring_payments_actions.next_actions IS 'contains deep links to drive next actions for recurring payment actions';
COMMENT ON COLUMN public.recurring_payments_actions.action_detailed_status_info IS '{"proto_type":"recurringpayment.ActionDetailedStatusInfo", "comment":"stores the detailed status in form of fi status code for recurring payment actions, generally useful in failure cases where we want know granular reason for failure."}';
CREATE TABLE public.standing_instructions (
	id STRING NOT NULL,
	recurring_payment_id STRING NOT NULL,
	secure_token STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT standing_instructions_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX standing_instructions_id_key (id ASC),
	UNIQUE INDEX recurring_payment_id_lookup_idx (recurring_payment_id ASC)
);
COMMENT ON TABLE public.standing_instructions IS 'table to store information related to a standing instruction';
COMMENT ON COLUMN public.standing_instructions.secure_token IS '{"proto_type": "StandingInstruction.SecureToken", "comment":"unique token which acts as an auth for payment via standing instruction"}';
CREATE TABLE public.standing_instruction_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	standing_instruction_id STRING NOT NULL,
	vendor_request_id STRING NOT NULL,
	request_type STRING NOT NULL,
	state STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX standing_instruction_id_request_type_lookup_idx (standing_instruction_id ASC, request_type ASC, created_at ASC),
	INDEX standing_instruction_requests_vendor_req_id_lookup_idx (vendor_request_id ASC)
);
COMMENT ON TABLE public.standing_instruction_requests IS 'table to store information related to standing instruction requests';
COMMENT ON COLUMN public.standing_instruction_requests.request_type IS '{"proto_type": "StandingInstructionRequests.RequestType", "comment":"type of request such as CREATE, MODIFY, REVOKE"}';
CREATE TABLE public.upi_mandates (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	recurring_payment_id STRING NOT NULL,
	req_id STRING NOT NULL,
	umn STRING NULL,
	revokeable BOOL NOT NULL,
	share_to_payee BOOL NOT NULL,
	block_fund BOOL NOT NULL,
	initiated_by STRING NOT NULL,
	signed_token STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT upi_mandates_pkey PRIMARY KEY (recurring_payment_id ASC),
	UNIQUE INDEX upi_mandates_req_id_idx (req_id ASC),
	UNIQUE INDEX upi_mandates_umn_idx (umn ASC),
	INDEX upi_mandates_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (updated_at),
	FAMILY "primary" (id, recurring_payment_id, req_id, umn, revokeable, share_to_payee, block_fund, initiated_by, signed_token, created_at, deleted_at)
);
COMMENT ON TABLE public.upi_mandates IS 'table to store mandate entity for upi mandates';
COMMENT ON COLUMN public.upi_mandates.req_id IS '{"comment": "request id for the mandate shared with vendor"}';
COMMENT ON COLUMN public.upi_mandates.umn IS '{"comment": "unique mandate number for the mandate"}';
COMMENT ON COLUMN public.upi_mandates.revokeable IS '{"comment": "denotes if the mandate can be revoked or not"}';
COMMENT ON COLUMN public.upi_mandates.share_to_payee IS '{"comment": "denotes if the mandate should be shared by the payee or not"}';
COMMENT ON COLUMN public.upi_mandates.block_fund IS '{"comment": "denotes if the fund is blocked for the mandate"}';
COMMENT ON COLUMN public.upi_mandates.initiated_by IS '{"proto_type":"Mandate.MandateInitiatedBy", "comment": "mandate initiated by payer/payee"}';
COMMENT ON COLUMN public.upi_mandates.signed_token IS '{"comment": "signed token to authorise mandate execution"}';
CREATE TABLE public.upi_mandate_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	mandate_id STRING NOT NULL,
	req_id STRING NOT NULL,
	action STRING NOT NULL,
	req_info JSONB NULL,
	status STRING NOT NULL,
	stage STRING NOT NULL,
	expire_at TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT upi_mandate_requests_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX upi_mandate_requests_req_id_idx (req_id ASC),
	INDEX upi_mandate_requests_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (status, stage, updated_at),
	FAMILY "primary" (id, mandate_id, req_id, action, req_info, expire_at, created_at, deleted_at)
);
COMMENT ON TABLE public.upi_mandate_requests IS 'table to store request info for mandates';
COMMENT ON COLUMN public.upi_mandate_requests.req_id IS '{"comment": "request id for the mandate shared with vendor"}';
COMMENT ON COLUMN public.upi_mandate_requests.action IS '{"proto_type":"MandateRequest.Action", "comment": "action corresponding the row eg. Create, Update"}';
COMMENT ON COLUMN public.upi_mandate_requests.req_info IS '{"proto_type":"MandateRequest.ReqInfo", "comment": "payload containing mandate request related info like refId"}';
COMMENT ON COLUMN public.upi_mandate_requests.stage IS '{"proto_type":"MandateRequest.Stage", "comment": "stage of the mandate request. eg. REQ_MANDATE_RECEIVED"}';
CREATE TABLE public.savings_account_aggregations (
	id STRING NOT NULL,
	savings_account_id STRING NOT NULL,
	total_credited_amount INT8 NULL,
	total_debited_amount INT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT savings_account_aggregations_pkey PRIMARY KEY (savings_account_id ASC, id ASC),
	UNIQUE INDEX savings_account_aggregations_id_key (id ASC),
	UNIQUE INDEX savings_account_aggregations_savings_account_id_key (savings_account_id ASC),
	FAMILY "primary" (id, savings_account_id, created_at, deleted_at),
	FAMILY frequently_updated (total_credited_amount, total_debited_amount, updated_at)
);
COMMENT ON TABLE public.savings_account_aggregations IS 'table to store aggregations for savings account';
CREATE TABLE public.app_instance_identifiers (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	prospect_id STRING NOT NULL,
	firebase_app_instance_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT app_instance_identifiers_pkey PRIMARY KEY (actor_id ASC),
	UNIQUE INDEX app_instance_identifiers_id_key (id ASC),
	UNIQUE INDEX app_instance_identifiers_actor_id_key (actor_id ASC),
	UNIQUE INDEX app_instance_identifiers_firebase_app_instance_id_key (firebase_app_instance_id ASC),
	INDEX app_instance_identifiers_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.aa_data_purge_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	account_id STRING NOT NULL,
	txn_execution_from TIMESTAMPTZ NOT NULL,
	txn_execution_to TIMESTAMPTZ NOT NULL,
	data_identifiers JSONB NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT aa_data_purge_requests_pkey PRIMARY KEY (account_id ASC, txn_execution_from ASC, txn_execution_to ASC),
	UNIQUE INDEX aa_data_purge_requests_id_uniq_idx (id ASC),
	FAMILY frequently_updated (status, updated_at),
	FAMILY "primary" (id, account_id, txn_execution_from, txn_execution_to, data_identifiers, created_at, deleted_at)
);
COMMENT ON TABLE public.aa_data_purge_requests IS 'table to store and track aa data purge requests, where each row represents a batch of data to be deleted';
COMMENT ON COLUMN public.aa_data_purge_requests.data_identifiers IS '{"proto_type": "order.aa.DataPurgeIdentifiers", "comment":"stores a pre-computed list of all the identifiers to be purged as a part of the request"}';
COMMENT ON COLUMN public.aa_data_purge_requests.status IS '{"proto_type": "order.aa.DataPurgeStatus", "comment":"status for data purge request"}';
CREATE TABLE public.payment_instrument_purge_audits (
	pi_id STRING NOT NULL,
	payload JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT payment_instrument_purge_audits_pkey PRIMARY KEY (pi_id ASC),
	INDEX payment_instrument_purge_audits_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.payment_instrument_purge_audits.payload IS '{"proto_type":"payment_instruments.PaymentInstrument", "comment": "stores all pi data corresponding to the pi id"}';
CREATE TABLE public.deposit_transactions (
	id STRING NOT NULL,
	deposit_account_id STRING NOT NULL,
	txn_id STRING NOT NULL,
	txn_type STRING NOT NULL,
	txn_amount JSONB NOT NULL,
	txn_timestamp TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT deposit_transactions_pkey PRIMARY KEY (deposit_account_id ASC, id ASC),
	UNIQUE INDEX deposit_transactions_id_key (id ASC),
	UNIQUE INDEX deposit_transactions_txn_id_key (txn_id ASC),
	INDEX deposit_transactions_updated_at_idx (updated_at ASC),
	FAMILY "primary" (id, deposit_account_id, txn_id, txn_type, txn_amount, txn_timestamp, created_at, deleted_at),
	FAMILY frequently_updated (updated_at)
);
COMMENT ON TABLE public.deposit_transactions IS 'table to store a mapping of deposit account and its transactions';
COMMENT ON COLUMN public.deposit_transactions.txn_type IS '{"proto_type":"deposit.DepositTxnType", "comment": "enum to specify the deposit txn type"}';
CREATE TABLE public.p2p_investors (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	vendor_investor_id STRING NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	details JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	total_investment_count INT8 NOT NULL DEFAULT 0:::INT8,
	used_investment_count INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT p2p_investors_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_vendor_investor_id_and_vendor (vendor_investor_id ASC, vendor ASC),
	UNIQUE INDEX unique_index_conditional_on_actor_id_and_vendor (actor_id ASC, vendor ASC) WHERE deleted_at IS NULL,
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, vendor, created_at, total_investment_count, used_investment_count),
	FAMILY seldom_updated (vendor_investor_id, deleted_at)
);
COMMENT ON TABLE public.p2p_investors IS 'table to store all the details related to investor created at vendor for p2p investment';
COMMENT ON COLUMN public.p2p_investors.actor_id IS 'actor for which investor is created';
COMMENT ON COLUMN public.p2p_investors.vendor_investor_id IS 'identifier of investor received from vendor';
COMMENT ON COLUMN public.p2p_investors.vendor IS '{"proto_type":"p2pinvestment.Vendor", vendor in which investor is created"}';
COMMENT ON COLUMN public.p2p_investors.status IS '{"proto_type":"p2pinvestment.InvestorStatus", "comment":"investor creation status"}';
COMMENT ON COLUMN public.p2p_investors.sub_status IS '{"proto_type":"p2pinvestment.InvestorSubStatus", "comment":"investor creation status or granular info on status"}';
COMMENT ON COLUMN public.p2p_investors.details IS '{"proto_type":"p2pinvestment.InvestorDetails", "comment":"details of investor which are not queryable"}';
COMMENT ON COLUMN public.p2p_investors.total_investment_count IS 'stores total number of investments allowed to the user';
COMMENT ON COLUMN public.p2p_investors.used_investment_count IS 'stores number of used investments by the user';
CREATE TABLE public.p2p_investment_schemes (
	id STRING NOT NULL,
	vendor_scheme_id STRING NOT NULL,
	vendor STRING NOT NULL,
	details JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	name STRING NOT NULL DEFAULT 'SCHEME_NAME_UNSPECIFIED':::STRING,
	CONSTRAINT p2p_investment_schemes_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_vendor_scheme_id_and_vendor (vendor_scheme_id ASC, vendor ASC),
	UNIQUE INDEX p2p_investment_schemes_name_unique (name ASC),
	FAMILY seldom_updated (details, deleted_at, updated_at, id, vendor_scheme_id, vendor, created_at, name)
);
COMMENT ON TABLE public.p2p_investment_schemes IS 'stores all the schemes for investments';
COMMENT ON COLUMN public.p2p_investment_schemes.vendor_scheme_id IS 'corresponding vendor scheme id';
COMMENT ON COLUMN public.p2p_investment_schemes.vendor IS '{"proto_type":"p2pinvestment.Vendor", "comment":"vendor through which corresponding scheme id mapped"}';
COMMENT ON COLUMN public.p2p_investment_schemes.details IS '{"proto_type":"p2pinvestment.SchemeDetails", "comment":"scheme details provided by vendor and info added by epifi"}';
CREATE TABLE public.p2p_investment_transactions (
	id STRING NOT NULL,
	investor_id STRING NOT NULL,
	scheme_id STRING NOT NULL,
	order_client_req_id STRING NOT NULL,
	payment_ref_id STRING NULL,
	vendor_ref_id STRING NULL,
	type STRING NOT NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	details JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	computed_transaction_amount INT8 NOT NULL AS (CASE WHEN (details->'amount':::STRING) IS NOT NULL THEN IFNULL(((details->'amount':::STRING)->>'units':::STRING)::INT8, 0:::INT8) ELSE 0:::INT8 END) STORED,
	CONSTRAINT p2p_investment_transactions_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_order_client_req_id (order_client_req_id ASC),
	INDEX index_on_investor_id (investor_id ASC),
	INDEX index_on_txn_type_txn_status_and_txn_sub_status (type ASC, status ASC, sub_status ASC),
	INDEX created_at_idx (created_at ASC),
	INDEX p2p_investment_transactions_investor_scheme_type_status_idx (investor_id ASC, scheme_id ASC, type ASC, status ASC, sub_status ASC),
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, investor_id, scheme_id, order_client_req_id, type, vendor, created_at, computed_transaction_amount),
	FAMILY seldom_updated (payment_ref_id, vendor_ref_id, deleted_at)
);
COMMENT ON TABLE public.p2p_investment_transactions IS 'stores all the transaction done by an investor';
COMMENT ON COLUMN public.p2p_investment_transactions.investor_id IS 'foreign key from investors table';
COMMENT ON COLUMN public.p2p_investment_transactions.scheme_id IS 'scheme id for which transaction is done';
COMMENT ON COLUMN public.p2p_investment_transactions.order_client_req_id IS 'request id passed to order service in create call';
COMMENT ON COLUMN public.p2p_investment_transactions.payment_ref_id IS 'payment reference provided by bank when money is transferred from customer account to vendor account';
COMMENT ON COLUMN public.p2p_investment_transactions.vendor_ref_id IS 'transaction reference provided by vendor';
COMMENT ON COLUMN public.p2p_investment_transactions.type IS '{"proto_type":"p2pinvestment.TransactionType", "comment":"type of transaction Investment/Withdrawal"}';
COMMENT ON COLUMN public.p2p_investment_transactions.vendor IS '{"proto_type":"p2pinvestment.Vendor", "comment":"vendor for which transaction is done"}';
COMMENT ON COLUMN public.p2p_investment_transactions.status IS '{"proto_type":"p2pinvestment.TransactionStatus", "comment":"status of transaction"}';
COMMENT ON COLUMN public.p2p_investment_transactions.sub_status IS '{"proto_type":"p2pinvestment.TransactionSubStatus", "comment":"transaction sub-status or granular info on status"}';
COMMENT ON COLUMN public.p2p_investment_transactions.details IS '{"proto_type":"p2pinvestment.InvestmentTransactionDetails", "comment":"stores details of an investment transaction"}';
COMMENT ON COLUMN public.p2p_investment_transactions.computed_transaction_amount IS 'computed_transaction_amount column is derived from the transaction amount in details jsonb column. It would be 0 if transaction amount is not found';
CREATE TABLE public.upi_processed_phone_numbers (
	phone_number JSONB NOT NULL,
	computed_phone_number STRING NOT NULL AS (concat(phone_number->>'country_code':::STRING, phone_number->>'national_number':::STRING)) STORED,
	verified_at TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	CONSTRAINT upi_processed_phone_numbers_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX upi_processed_phone_numbers_computed_phone_number_key (computed_phone_number ASC)
);
COMMENT ON TABLE public.upi_processed_phone_numbers IS 'upi_processed_phone_numbers table store phone number and time at which VPAs for that phone number are verified';
COMMENT ON COLUMN public.upi_processed_phone_numbers.phone_number IS 'phone number for which VPAs are verified';
COMMENT ON COLUMN public.upi_processed_phone_numbers.computed_phone_number IS 'computed phone number as string';
COMMENT ON COLUMN public.upi_processed_phone_numbers.verified_at IS 'time at which VPAs corresponding to phone number are verified';
COMMENT ON COLUMN public.upi_processed_phone_numbers.id IS 'unique identifier for each row';
CREATE TABLE public.user_device_properties (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	phone_number STRING NOT NULL,
	property STRING NOT NULL,
	value JSONB NOT NULL,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT user_device_properties_pkey PRIMARY KEY (actor_id ASC, phone_number ASC, property ASC, deleted_at_unix ASC),
	UNIQUE INDEX user_device_properties_actor_id_property_deleted_at_idx (actor_id ASC, property ASC, deleted_at_unix ASC),
	INDEX user_device_properties_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.user_device_properties IS 'stores data for device related user properties';
COMMENT ON COLUMN public.user_device_properties.property IS 'stores the type of property eg. device language, device model etc.';
COMMENT ON COLUMN public.user_device_properties.value IS '{"proto_type": "user.UserDeviceProperty", "message": "stores the value corresponding to the device property"}';
CREATE TABLE public.event_publish_outbox (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	payload JSONB NOT NULL,
	queue STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT event_publish_outbox_pkey PRIMARY KEY (id ASC)
);
COMMENT ON COLUMN public.event_publish_outbox.payload IS 'stores the event content and might depends on event which has to be published eg. for payment enquiry, payload consist of transactionId, phoneNumber, actorId, customerId.';
CREATE TABLE public.address_for_identifiers (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	identifier_type STRING NOT NULL,
	identifier_value STRING NOT NULL,
	address JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	coordinates JSONB NULL,
	h3_index STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX unique_row (identifier_type ASC, identifier_value ASC, deleted_at_unix ASC),
	INDEX address_for_identifiers_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.address_for_identifiers IS 'table to store geological addresses for certain identifiers like IP, LOCATION_TOKEN';
COMMENT ON COLUMN public.address_for_identifiers.h3_index IS 'stores the H3 spatial index of the corresponding co-ordinates';
CREATE TABLE public.physical_card_dispatch_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	card_id UUID NOT NULL,
	state STRING NULL,
	request_id STRING NULL,
	num_retries INT8 NULL,
	failure_response_code STRING NULL,
	failure_response_reason STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	details JSONB NULL,
	client_req_id STRING NULL,
	sub_status STRING NULL,
	current_stage STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX physical_card_dispatch_requests_request_id_unique_idx (request_id ASC),
	INDEX card_id_lookup_idx (card_id ASC, state ASC),
	INDEX physical_card_dispatch_requests_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (state, num_retries, updated_at, sub_status, current_stage),
	FAMILY "primary" (id, card_id, request_id, failure_response_code, failure_response_reason, created_at, deleted_at, details, client_req_id)
);
COMMENT ON TABLE public.physical_card_dispatch_requests IS 'table to store physical card dispatch requests for storing vendor requests';
COMMENT ON COLUMN public.physical_card_dispatch_requests.state IS '{"proto_type":"PhysicalCardDispatchRequests.State", "comment": "dispatch request state for maintaining state of the request"}';
COMMENT ON COLUMN public.physical_card_dispatch_requests.details IS '{"proto_type":"card.provisioning.PhysicalCardDispatchRequest.PhysicalCardDispatchRequestDetails", "comment": "contain details associated with a physical card dispatch flow", "ref": "api.card.provisioning.physical_card_dispatch_request.proto"}';
COMMENT ON COLUMN public.physical_card_dispatch_requests.client_req_id IS 'stores the client_req_id for card dispatch workflow';
CREATE TABLE public.seasons (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	reward_offer_group_id STRING NOT NULL,
	display_details JSONB NOT NULL,
	season_levels JSONB NOT NULL,
	active_since TIMESTAMPTZ NOT NULL,
	active_till TIMESTAMPTZ NOT NULL,
	display_since TIMESTAMPTZ NOT NULL,
	display_till TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT seasons_pkey PRIMARY KEY (id ASC),
	INDEX seasons_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.seasons.reward_offer_group_id IS 'links the season with the reward-offers for rewards orchestration via reward-offers group-id';
COMMENT ON COLUMN public.seasons.display_details IS 'contains display related information and config required for the season to be rendered in the app';
COMMENT ON COLUMN public.seasons.season_levels IS 'config for different levels within a season';
CREATE TABLE public.merchant_pi_gplace_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pi_id STRING NULL,
	gplace_types STRING[] NULL,
	business_status STRING NULL,
	formatted_address STRING NULL,
	"geometry" JSONB NULL,
	icon_url STRING NULL,
	icon_mask_base_uri STRING NULL,
	icon_background_color STRING NULL,
	place_name STRING NULL,
	place_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX merchant_pi_gplace_data_pi_id_unique_idx (pi_id ASC),
	INDEX merchant_pi_gplace_data_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.merchant_pi_gplace_data IS 'use to cache gplace data for a given merchant pi';
CREATE TABLE public.bank_account_verifications (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	account_number STRING NULL,
	ifsc STRING NULL,
	name_at_bank STRING NULL,
	overall_status STRING NOT NULL,
	vendor_status JSONB NULL,
	vendor_req_id STRING NULL,
	vendor STRING NULL,
	failure_reason STRING NULL,
	name_match_data JSONB NULL,
	metadata JSONB NULL,
	caller JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT bank_account_verifications_pkey PRIMARY KEY (actor_id ASC, overall_status ASC, id ASC),
	INDEX bank_account_verifications_actor_id_overall_status_key (actor_id ASC, overall_status ASC),
	UNIQUE INDEX bank_account_verifications_id_key (id ASC),
	INDEX bank_account_verifications_updated_at_key (updated_at ASC)
);
COMMENT ON TABLE public.bank_account_verifications IS 'stores data of external bank validations of a user';
COMMENT ON COLUMN public.bank_account_verifications.vendor IS 'stores enum string of vendor response';
COMMENT ON COLUMN public.bank_account_verifications.name_match_data IS 'stores the names and name match scores of user entered names and name at the bank account if valid';
COMMENT ON COLUMN public.bank_account_verifications.caller IS 'stores the source of the call (user actor_id or sherlock agent email_id)';
CREATE TABLE public.change_feeds (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	row_identifier STRING NOT NULL,
	updated_column STRING NOT NULL,
	table_name STRING NOT NULL,
	change_log JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (table_name ASC, row_identifier ASC, updated_column ASC, created_at ASC, id ASC),
	UNIQUE INDEX id_key (id ASC),
	INDEX row_id_by_table_name_idx (row_identifier ASC, table_name ASC),
	INDEX change_feeds_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.change_feeds IS 'A table to store the changefeed for tracked columns of any table';
COMMENT ON COLUMN public.change_feeds.row_identifier IS 'the uuid of the row being tracked';
COMMENT ON COLUMN public.change_feeds.table_name IS 'the parent table of the row being tracked';
COMMENT ON COLUMN public.change_feeds.change_log IS e'type:"type Change struct {\n\tType string      `json:"type"`\n\tPath []string    `json:"path"`\n\tFrom interface{} `json:"from"`\n\tTo   interface{} `json:"to"`\n}", description: "the changelog object of the row being tracked"';
CREATE TABLE public.segments (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	provider_name STRING NOT NULL,
	provider_segment_id STRING NOT NULL,
	provider_segment_metadata JSONB NOT NULL,
	segment_details JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX segments_provider_segment_id_provider_name_key (provider_segment_id ASC, provider_name ASC),
	UNIQUE INDEX segments_updated_at_key (updated_at ASC)
);
COMMENT ON TABLE public.segments IS 'stores mapping of provider segment id and internal segment id';
COMMENT ON COLUMN public.segments.provider_name IS 'stores provider name like AWS or GCP';
COMMENT ON COLUMN public.segments.provider_segment_id IS 'stores provider generated segment id';
COMMENT ON COLUMN public.segments.provider_segment_metadata IS 'stores provider specific segment metadata in raw form';
COMMENT ON COLUMN public.segments.segment_details IS 'stores segment specific details in raw form';
CREATE TABLE public.pii_tokens (
	token STRING NOT NULL,
	identifier_type STRING NOT NULL,
	identifier_value STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT "primary" PRIMARY KEY (token ASC),
	UNIQUE INDEX unique_row (identifier_type ASC, identifier_value ASC, deleted_at_unix ASC),
	INDEX pii_tokens_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.pii_tokens IS 'table to store obfuscated tokens for PII data';
CREATE TABLE public.employer (
	id STRING NOT NULL,
	name_by_source STRING NOT NULL,
	trade_name STRING NULL,
	pan_name STRING NULL,
	gstin STRING NULL,
	source STRING NULL,
	is_epf_verified STRING NULL,
	remitter_names STRING[] NULL,
	former_names STRING[] NULL,
	metadata JSONB NULL,
	verification_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	salary_program_channel STRING NOT NULL DEFAULT 'B2C':::STRING,
	min_required_salary_amount JSONB NOT NULL DEFAULT '{"currency_code": "INR", "units": 10000}':::JSONB,
	salary_program_eligibility_status STRING NOT NULL DEFAULT 'ELIGIBLE':::STRING,
	available_health_insurance_policy_types JSONB NULL,
	CONSTRAINT employer_pkey PRIMARY KEY (id ASC),
	INDEX employer_name_by_source_idx (name_by_source ASC),
	INDEX employer_gstin_idx (gstin ASC),
	INDEX employer_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.employer.salary_program_channel IS '{"proto_type":"employmentPb.EmployerSalaryProgramChannel","comment": "stores the channel by which the employer is onboarded for the salary program"}';
COMMENT ON COLUMN public.employer.min_required_salary_amount IS '{"struct_type":"moneyPkg.Money","comment": "stores the minimum required for detecting a txn as salary from this employer"}';
COMMENT ON COLUMN public.employer.salary_program_eligibility_status IS '{"proto_type":"employmentPb.EmployerSalaryProgramEligibilityStatus","comment": "stores the eligibility status of the employer for salary program"}';
COMMENT ON COLUMN public.employer.available_health_insurance_policy_types IS '{"proto_type":"employment.AvailableHealthInsurancePolicyTypes", "comment":"contains list of available policy types for employer"}';
CREATE TABLE public.workflow_requests (
	id STRING NOT NULL,
	actor_id STRING NULL,
	stage STRING NOT NULL,
	status STRING NOT NULL,
	version STRING NOT NULL,
	type STRING NOT NULL,
	payload BYTES NULL,
	client_req_id STRING NOT NULL,
	ownership STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	next_action JSONB NULL,
	CONSTRAINT workflow_requests_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX workflow_requests_client_req_id_key (client_req_id ASC),
	INDEX workflow_requests_updated_at_idx (updated_at ASC),
	INDEX workflow_requests_type_stage_status_created_at_idx (type ASC, stage ASC, status ASC, created_at ASC),
	FAMILY frequently_updated (stage, status, updated_at, next_action),
	FAMILY "primary" (id, actor_id, version, type, payload, client_req_id, ownership, created_at, deleted_at)
);
COMMENT ON COLUMN public.workflow_requests.payload IS e'An opaque blob containing the data needed for processing activity for a workflow.\n     This might vary based on the type of workflow. The data inside the blob will depend on underlying domain service';
COMMENT ON COLUMN public.workflow_requests.client_req_id IS e'Client details corresponding to the service initiating workflow request.\n     The combination of client and client_req_id must be unique';
CREATE TABLE public.workflow_histories (
	wf_req_id STRING NOT NULL,
	stage STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	attempts INT8 NOT NULL DEFAULT 1:::INT8,
	failure_description STRING NULL,
	rowid INT8 NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	payload BYTES NULL,
	ext_req_id STRING NULL,
	id UUID NULL DEFAULT gen_random_uuid(),
	CONSTRAINT workflow_histories_pkey PRIMARY KEY (rowid ASC),
	INDEX workflow_histories_updated_at_idx (updated_at ASC),
	INDEX workflow_histories_wf_req_id_stage_idx (wf_req_id ASC, stage ASC),
	FAMILY frequently_updated (stage, status, updated_at, attempts, rowid, payload, ext_req_id),
	FAMILY "primary" (wf_req_id, created_at, deleted_at, completed_at, failure_description, id)
);
COMMENT ON COLUMN public.workflow_histories.wf_req_id IS 'foreign key from workflows req table';
COMMENT ON COLUMN public.workflow_histories.stage IS 'Current stage of the workflow';
COMMENT ON COLUMN public.workflow_histories.status IS 'Latest status of the stage';
COMMENT ON COLUMN public.workflow_histories.attempts IS 'Number of attempts made to process the order stage. This can be used by the orchestration engine to handle poison pill request during batch processing, etc.';
COMMENT ON COLUMN public.workflow_histories.failure_description IS 'Optional: Failure description as returned from the activity. The same can be used to display message on the UI if needed. ';
COMMENT ON COLUMN public.workflow_histories.payload IS 'payload is a json field used to store stage specific payload';
COMMENT ON COLUMN public.workflow_histories.ext_req_id IS 'If a stage is required to raise request with other services, ext_req_id will store the Identifier received. It can be mainly used for reverse lookups';
CREATE TABLE public.user_preferences (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	preference_type STRING NOT NULL,
	preference_value JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT user_preferences_pkey PRIMARY KEY (actor_id ASC, preference_type ASC, deleted_at_unix ASC),
	UNIQUE INDEX id_key (id ASC),
	INDEX user_preferences_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.user_preferences IS 'Table to store the user preferences';
COMMENT ON COLUMN public.user_preferences.actor_id IS 'actor id of the user for whom the preference is stored';
COMMENT ON COLUMN public.user_preferences.preference_type IS '{"proto_type":"user.preferences.PreferenceType", "comment":"type of the user preference being stored eg: CALL_LANGUAGE"}';
COMMENT ON COLUMN public.user_preferences.preference_value IS '{"proto_type":"user.preferences.PreferenceValue", "comment":"value of the preference dependent upon the preference_type"}';
CREATE TABLE public.account_merchant_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pi_id STRING NOT NULL,
	mcc STRING NULL,
	address STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX account_merchant_infos_pi_id_idx (pi_id ASC),
	FAMILY "primary" (id, mcc, created_at, deleted_at, pi_id, address),
	FAMILY frequently_updated (updated_at)
);
COMMENT ON TABLE public.account_merchant_infos IS '{"proto_type":"merchant.AccountMerchantInfo", "comment": "model to store merchants with account and ifsc as pi", "ref": "api/merchant/account_merchant_info.proto"}';
COMMENT ON COLUMN public.account_merchant_infos.pi_id IS '{"proto_type":"merchant.AccountMerchantInfo.pi_id", "comment": "payment instrument id for account merchant", "ref": "api/merchant/account_merchant_info.proto"}';
COMMENT ON COLUMN public.account_merchant_infos.mcc IS '{"proto_type":"merchant.AccountMerchantInfo.mcc", "comment": "merchant classification code", "ref": "api/merchant/account_merchant_info.proto"}';
COMMENT ON COLUMN public.account_merchant_infos.address IS '{"proto_type":"merchant.AccountMerchantInfo.address", "comment": "address of account merchant", "ref": "api/merchant/account_merchant_info.proto"}';
CREATE TABLE public.p2p_dynamic_inventory (
	id STRING NOT NULL,
	investor_id STRING NOT NULL,
	investment_transaction_id STRING NULL,
	expiry_date TIMESTAMPTZ NULL,
	details JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT p2p_dynamic_inventory_pkey PRIMARY KEY (id ASC),
	INDEX p2p_dynamic_inventory_updated_at_idx (updated_at DESC),
	FAMILY frequently_updated (investment_transaction_id, details, updated_at, id, investor_id, created_at),
	FAMILY seldom_updated (expiry_date, deleted_at)
);
COMMENT ON TABLE public.p2p_dynamic_inventory IS 'stores al pass details for investor';
COMMENT ON COLUMN public.p2p_dynamic_inventory.investor_id IS 'foreign key from investors table';
COMMENT ON COLUMN public.p2p_dynamic_inventory.investment_transaction_id IS 'investment transaction id for which pass is used';
COMMENT ON COLUMN public.p2p_dynamic_inventory.expiry_date IS 'date on which the pass will expire';
COMMENT ON COLUMN public.p2p_dynamic_inventory.details IS '{"proto_type":"p2pinvestment.DynamicInventoryDetails", "comment":"stores details for dynamic inventory"}';
CREATE TABLE public.credit_report_summaries (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	credit_report_dowloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	bureau_score STRING NULL,
	bureau_score_confidence_level STRING NULL,
	bureau_pl_score INT8 NULL,
	leverage_score INT8 NULL,
	residence_stability_score INT8 NULL,
	no_hit_score INT8 NULL,
	num_credit_accounts_active STRING NULL,
	num_credit_accounts_closed STRING NULL,
	num_credit_accounts_default STRING NULL,
	num_credit_accounts_total STRING NULL,
	outstanding_balance_all STRING NULL,
	outstanding_balance_secured STRING NULL,
	outstanding_balance_secured_percentage STRING NULL,
	outstanding_balance_unsecured STRING NULL,
	outstanding_balance_unsecured_percentage STRING NULL,
	total_caps_last180_days STRING NULL,
	total_caps_last90_days STRING NULL,
	total_caps_last30_days STRING NULL,
	total_caps_last7_days STRING NULL,
	caps_last_180_days STRING NULL,
	caps_last_90_days STRING NULL,
	caps_last_30_days STRING NULL,
	caps_last_7_days STRING NULL,
	non_credit_caps_last_180_days STRING NULL,
	non_credit_caps_last_90_days STRING NULL,
	non_credit_caps_last_30_days STRING NULL,
	non_credit_caps_last_7_days STRING NULL,
	enquiry_username STRING NULL,
	subscriber STRING NULL,
	subscriber_name STRING NULL,
	version STRING NULL,
	exact_match STRING NULL,
	income_segment STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX credit_report_summaries_updated_at_idx (updated_at ASC),
	UNIQUE INDEX credit_report_summaries_dedup_id_key (dedup_id ASC)
);
CREATE TABLE public.credit_account_details (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	credit_report_dowloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	account_holder_type_code STRING NULL,
	account_number STRING NULL,
	account_status STRING NULL,
	account_type STRING NULL,
	amount_past_due STRING NULL,
	credit_limit_amount STRING NULL,
	currency_code STRING NULL,
	current_balance STRING NULL,
	date_closed STRING NULL,
	date_of_addition STRING NULL,
	date_of_last_payment STRING NULL,
	date_reported STRING NULL,
	highest_credit_or_original_loan_amount STRING NULL,
	income STRING NULL,
	identification_number STRING NULL,
	open_date STRING NULL,
	original_charge_off_amount STRING NULL,
	payment_history_profile STRING NULL,
	payment_rating STRING NULL,
	portfolio_type STRING NULL,
	repayment_tenure STRING NULL,
	scheduled_monthly_payment_amount STRING NULL,
	settlement_amount STRING NULL,
	subscriber_name STRING NULL,
	terms_duration STRING NULL,
	value_of_collateral STRING NULL,
	value_of_credits_last_month STRING NULL,
	written_off_amt_principal STRING NULL,
	written_off_amt_total STRING NULL,
	terms_frequency STRING NULL,
	special_comment STRING NULL,
	date_of_first_delinquency STRING NULL,
	suit_filed_wilful_default_written_off_status STRING NULL,
	suit_filed_wilful_default STRING NULL,
	credit_facility_status STRING NULL,
	type_of_collateral STRING NULL,
	rate_of_interest STRING NULL,
	promotional_rate_flag STRING NULL,
	income_indicator STRING NULL,
	income_frequency_indicator STRING NULL,
	default_status_date STRING NULL,
	litigation_status_date STRING NULL,
	written_off_status_date STRING NULL,
	subscriber_comments STRING NULL,
	consumer_comments STRING NULL,
	customer_segment STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX credit_account_details_updated_at_idx (updated_at ASC),
	UNIQUE INDEX credit_account_details_dedup_id_key (dedup_id ASC)
);
CREATE TABLE public.loan_requests (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	offer_id STRING NULL,
	orch_id STRING NULL,
	loan_account_id STRING NULL,
	vendor_request_id STRING NULL,
	vendor STRING NOT NULL,
	details JSONB NULL,
	type STRING NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_requests_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_loan_requests_actor_vendor_active_request (actor_id ASC, vendor ASC) WHERE completed_at IS NULL,
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, orch_id, vendor, type, created_at, completed_at),
	FAMILY seldom_updated (vendor_request_id, deleted_at, loan_account_id, offer_id)
);
COMMENT ON TABLE public.loan_requests IS 'Table to keep a track of all the loan applications that have been raised with the vendor and to pre-close a loan account with vendor';
COMMENT ON COLUMN public.loan_requests.actor_id IS 'actor for which loan request is created';
COMMENT ON COLUMN public.loan_requests.offer_id IS 'Unique id returned by vendor';
COMMENT ON COLUMN public.loan_requests.orch_id IS 'Client request ID used for loan request orchestration purpose';
COMMENT ON COLUMN public.loan_requests.loan_account_id IS 'Loan account associated with a loan request';
COMMENT ON COLUMN public.loan_requests.vendor_request_id IS 'ID to uniquely identify a loan request at vendor';
COMMENT ON COLUMN public.loan_requests.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"Vendor string who provided this loan offer"}';
COMMENT ON COLUMN public.loan_requests.details IS '{"proto_type":"preapprovedloan.LoanRequestDetails", "comment":"Details passed to vendor while creating the request and info got in response"}';
COMMENT ON COLUMN public.loan_requests.type IS '{"proto_type":"preapprovedloan.LoanRequestType", "comment":"Creation, Closure"}';
COMMENT ON COLUMN public.loan_requests.status IS '{"proto_type":"preapprovedloan.LoanRequestStatus", "comment":"status of the request"}';
COMMENT ON COLUMN public.loan_requests.sub_status IS '{"proto_type":"preapprovedloan.LoanRequestSubStatus", "comment":"Granular info on status"}';
CREATE TABLE public.loan_accounts (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	loan_account_id STRING NULL,
	loan_type STRING NOT NULL,
	ifsc_code STRING NULL,
	loan_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	disbursed_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	outstanding_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	total_payable_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	loan_end_date DATE NULL,
	maturity_date DATE NULL,
	vendor STRING NOT NULL,
	details JSONB NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_accounts_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_loan_account_id_and_ifsc_code (loan_account_id ASC, ifsc_code ASC),
	FAMILY frequently_updated (disbursed_amount, outstanding_amount, total_payable_amount, details, status, updated_at, id, actor_id, loan_type, vendor, created_at),
	FAMILY seldom_updated (loan_account_id, ifsc_code, loan_amount, loan_end_date, maturity_date, deleted_at)
);
COMMENT ON TABLE public.loan_accounts IS 'Table to keep a track of all the loan account by a user';
COMMENT ON COLUMN public.loan_accounts.actor_id IS 'actor for which loan request is created';
COMMENT ON COLUMN public.loan_accounts.loan_account_id IS 'Loan account number returned by vendor';
COMMENT ON COLUMN public.loan_accounts.loan_type IS '{"proto_type":"preapprovedloan.LoanType", "comment":"Loan Type"}';
COMMENT ON COLUMN public.loan_accounts.ifsc_code IS 'IFSC code returned by vendor';
COMMENT ON COLUMN public.loan_accounts.loan_amount IS 'Loan Amount';
COMMENT ON COLUMN public.loan_accounts.disbursed_amount IS e'Total amount credited to user\U00002019s account';
COMMENT ON COLUMN public.loan_accounts.outstanding_amount IS 'Amount remaining to be paid against the loan taken. This will keep on reducing after every EMI/Lump sum payment';
COMMENT ON COLUMN public.loan_accounts.total_payable_amount IS 'Total amount to be paid against the loan taken. This will include interest amount, fees';
COMMENT ON COLUMN public.loan_accounts.loan_end_date IS 'Loan End date';
COMMENT ON COLUMN public.loan_accounts.maturity_date IS 'Maturity date';
COMMENT ON COLUMN public.loan_accounts.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"Vendor string who provided this loan offer"}';
COMMENT ON COLUMN public.loan_accounts.details IS '{"proto_type":"preapprovedloan.LoanAccountDetails", "comment":"Details provided by vendor for the loan"}';
COMMENT ON COLUMN public.loan_accounts.status IS '{"proto_type":"preapprovedloan.LoanAccountStatus", "comment":"active/closed/pre-closed"}';
CREATE TABLE public.loan_offers (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	vendor_offer_id STRING NOT NULL,
	vendor STRING NOT NULL,
	offer_constraints JSONB NOT NULL,
	processing_info JSONB NOT NULL,
	valid_since TIMESTAMPTZ NOT NULL,
	valid_till TIMESTAMPTZ NOT NULL,
	deactivated_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_offers_pkey PRIMARY KEY (id ASC)
);
COMMENT ON TABLE public.loan_offers IS 'table to store all the loan offers received from vendor';
COMMENT ON COLUMN public.loan_offers.actor_id IS 'actor for which loan offer is available';
COMMENT ON COLUMN public.loan_offers.vendor_offer_id IS 'id for the offer provided by vendor';
COMMENT ON COLUMN public.loan_offers.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"vendor who has offered loan"}';
COMMENT ON COLUMN public.loan_offers.offer_constraints IS '{"proto_type":"preapprovedloanPb.OfferConstraints", "comment":"loan offer constraints like max loan amount, max EMI amount, max loan tenure"}';
COMMENT ON COLUMN public.loan_offers.processing_info IS '{"proto_type":"preapprovedloanPb.ProcessingInfo", "comment":"loan offer processing info like interest rate, processing fee"}';
COMMENT ON COLUMN public.loan_offers.valid_since IS 'loan offer validity start time';
COMMENT ON COLUMN public.loan_offers.valid_till IS 'loan offer validity end time';
COMMENT ON COLUMN public.loan_offers.deactivated_at IS 'loan offer deactivation time';
COMMENT ON COLUMN public.loan_offers.created_at IS 'loan offer creation time';
COMMENT ON COLUMN public.loan_offers.updated_at IS 'loan offer latest update time';
CREATE TABLE public.risk_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	risk_param STRING NOT NULL,
	payload JSONB NULL,
	result STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	score FLOAT8 NULL,
	potential_risk_flags STRING[] NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, risk_param ASC, deleted_at_unix ASC, id ASC),
	UNIQUE INDEX risk_data_actor_id_risk_param_deleted_at_unix_unique_idx (actor_id ASC, risk_param ASC, deleted_at_unix ASC),
	UNIQUE INDEX risk_data_id_unique_idx (id ASC),
	INDEX risk_data_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.risk_data IS 'table to store data for risk model';
COMMENT ON COLUMN public.risk_data.score IS 'stores the score for a risk check performed';
COMMENT ON COLUMN public.risk_data.potential_risk_flags IS '{"comment": "contains list of potential risk flags identified during given screener check"}';
CREATE TABLE public.red_listers (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	category STRING NOT NULL,
	value STRING NOT NULL,
	risk_score FLOAT8 NOT NULL,
	reason JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL,
	metadata JSONB NULL,
	expires_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (category ASC, id ASC),
	UNIQUE INDEX red_listers_unique_idx (category ASC, value ASC, deleted_at_unix ASC),
	INDEX red_listers_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.red_listers IS 'red_listers stores the red listed values for different categories';
CREATE TABLE public.domain_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	domain STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	raw_vendor_response STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	domain_type STRING NULL,
	domain_classification JSONB NULL,
	CONSTRAINT domain_details_pkey PRIMARY KEY (id ASC),
	INDEX domain_details_updated_at_key (updated_at ASC),
	UNIQUE INDEX domain_details_domain_name_domain_type_idx (domain ASC, domain_type ASC)
);
COMMENT ON TABLE public.domain_details IS 'stores domain details like domain name, verification status(pass/fail...)';
COMMENT ON COLUMN public.domain_details.domain IS 'domain name';
COMMENT ON COLUMN public.domain_details.status IS 'stores enum string stating if the domain is verified or not';
COMMENT ON COLUMN public.domain_details.sub_status IS 'stores enum string giving finer details like if the domain is blacklisted, verified';
COMMENT ON COLUMN public.domain_details.raw_vendor_response IS 'stores the raw json response of the domain verification api as string';
COMMENT ON COLUMN public.domain_details.domain_type IS 'domain type describes what type of domain it is (student email domain, work email domain etc.)';
COMMENT ON COLUMN public.domain_details.domain_classification IS 'domain classification stores a list of enums for which special handling is required';
CREATE TABLE public.goals (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	target_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	target_date TIMESTAMPTZ NOT NULL,
	name STRING NOT NULL,
	state STRING NOT NULL,
	provenance STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT goals_pkey PRIMARY KEY (id ASC),
	INDEX goals_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.goals IS 'stores goal details';
COMMENT ON COLUMN public.goals.actor_id IS 'foreign key from actors table';
CREATE TABLE public.goal_investment_instrument_mappings (
	id STRING NOT NULL,
	goal_id STRING NOT NULL,
	investment_instrument_type STRING NOT NULL,
	investment_instrument_state STRING NOT NULL,
	investment_instrument_money_constraints JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT goal_investment_instrument_mappings_pkey PRIMARY KEY (id ASC),
	INDEX goal_entities_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.goal_investment_instrument_mappings IS 'stores goal investment instrument mappings';
COMMENT ON COLUMN public.goal_investment_instrument_mappings.goal_id IS 'foreign key from goals table';
COMMENT ON COLUMN public.goal_investment_instrument_mappings.investment_instrument_money_constraints IS '{"proto_type":"goals.InvestmentInstrumentMoneyConstraints", "comment":"stores constraints for goal_investment_instrument_mapping"}';
CREATE TABLE public.loan_step_executions (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	ref_id STRING NOT NULL,
	orch_id STRING NULL,
	flow STRING NOT NULL,
	step_name STRING NOT NULL,
	details JSONB NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	staled_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_step_executions_pkey PRIMARY KEY (id ASC),
	INDEX loan_step_executions_updated_at (updated_at DESC),
	INDEX loan_step_executions_reference_id (ref_id ASC),
	UNIQUE INDEX loan_step_executions_unique_index_on_orch_id (orch_id ASC),
	UNIQUE INDEX unique_loan_step_executions_ref_id_step_name_active_execution (ref_id ASC, step_name ASC) WHERE completed_at IS NULL,
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, ref_id, orch_id, flow, step_name, created_at),
	FAMILY seldom_updated (completed_at, staled_at, deleted_at)
);
COMMENT ON TABLE public.loan_step_executions IS 'Table to keep a track of all the steps in verification for loan';
COMMENT ON COLUMN public.loan_step_executions.ref_id IS 'Ref to the entity for which step is executing';
COMMENT ON COLUMN public.loan_step_executions.orch_id IS 'Orchestration identifier which has started this execution';
COMMENT ON COLUMN public.loan_step_executions.flow IS '{"proto_type":"preapprovedloan.LoanStepExecutionFlow", "comment":"Either loan application/loan closure/offers"}';
COMMENT ON COLUMN public.loan_step_executions.step_name IS '{"proto_type":"preapprovedloan.LoanStepExecutionStep", "comment":"Step specific details"}';
COMMENT ON COLUMN public.loan_step_executions.details IS '{"proto_type":"preapprovedloan.LoanStepExecutionDetails", "comment":"Details passed to vendor while creating the request and info got in response"}';
COMMENT ON COLUMN public.loan_step_executions.status IS '{"proto_type":"preapprovedloan.LoanStepExecutionStatus", "comment":"status of the request"}';
COMMENT ON COLUMN public.loan_step_executions.sub_status IS '{"proto_type":"preapprovedloan.LoanStepExecutionSubStatus", "comment":"Granular info on status"}';
COMMENT ON COLUMN public.loan_step_executions.staled_at IS 'will be used to make step stale so that re-execution of the step can be done';
COMMENT ON COLUMN public.loan_step_executions.completed_at IS 'will be used to mark step completed at and note the time with respect to it';
CREATE TABLE public.bank_customers (
	id STRING NOT NULL,
	vendor_customer_id STRING NULL,
	actor_id STRING NOT NULL,
	user_id STRING NOT NULL,
	name JSONB NULL,
	vendor STRING NOT NULL,
	status STRING NULL,
	creation_started_at TIMESTAMPTZ NULL,
	vendor_creation_succeeded_at TIMESTAMPTZ NULL,
	fi_creation_succeeded_at TIMESTAMPTZ NULL,
	kyc_level_update_flow STRING NULL,
	vendor_metadata JSONB NULL,
	dedupe_info JSONB NULL,
	failure_reason JSONB NULL,
	account_closure_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	computed_cif_request_id STRING NULL AS ((vendor_metadata->'federalMetadata':::STRING)->>'cifRequestId':::STRING) STORED,
	computed_kyc_level STRING NULL AS (dedupe_info->>'kycLevel':::STRING) STORED,
	computed_ekyc_rrn STRING NULL AS ((vendor_metadata->'federalMetadata':::STRING)->>'ekycRrnNo':::STRING) STORED,
	source STRING NULL,
	kyc_info JSONB NULL,
	customer_inactive_info JSONB NULL,
	residency_info JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX bank_customers_user_id_deleted_at_unix_idx (user_id ASC, vendor ASC, deleted_at_unix ASC),
	UNIQUE INDEX bank_customers_actor_id_deleted_at_idx (actor_id ASC, vendor ASC, deleted_at_unix ASC),
	INDEX bank_customers_updated_at_idx (updated_at ASC),
	UNIQUE INDEX bank_customers_id_unique_idx (id ASC),
	UNIQUE INDEX bank_customers_vendor_customer_id_vendor_deleted_at_unix_idx (vendor_customer_id ASC, vendor ASC, deleted_at_unix DESC),
	INDEX computed_ekyc_rrn_idx (computed_ekyc_rrn ASC),
	UNIQUE INDEX bank_customers_computed_cif_request_id_uniq_idx (computed_cif_request_id ASC),
	FAMILY seldom_updated (vendor_metadata, computed_ekyc_rrn, source, kyc_info, customer_inactive_info, residency_info),
	FAMILY "primary" (id, vendor_customer_id, actor_id, user_id, name, vendor, status, creation_started_at, vendor_creation_succeeded_at, fi_creation_succeeded_at, kyc_level_update_flow, dedupe_info, failure_reason, account_closure_info, created_at, updated_at, deleted_at_unix, computed_cif_request_id, computed_kyc_level)
);
COMMENT ON COLUMN public.bank_customers.status IS '{"proto_type":"user.bank_customer.Status", "comment": "indicates the overall status of customer creation"}';
COMMENT ON COLUMN public.bank_customers.kyc_level_update_flow IS '{"proto_type":"user.bank_customer.KycLevelUpdateFlow", "comment": "denotes the flow via which kyc level was updated"}';
COMMENT ON COLUMN public.bank_customers.vendor_metadata IS '{"proto_type":"user.bank_customer.VendorMetadata", "comment": "contains vendor specific data"}';
COMMENT ON COLUMN public.bank_customers.account_closure_info IS '{"proto_type":"user.bank_customer.AccountClosureInfo", "comment": "contains account closure info}';
CREATE TABLE public.credit_account_histories (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	credit_account_details_id STRING NOT NULL,
	credit_report_downloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	asset_classification STRING NULL,
	days_past_due STRING NULL,
	month STRING NULL,
	year STRING NULL,
	year_month STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX credit_account_histories_dedup_id_key (dedup_id ASC),
	INDEX credit_account_history_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.caps_details (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	credit_report_downloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	subscriber_code STRING NULL,
	subscriber_name STRING NULL,
	date_of_request STRING NULL,
	report_time_caps STRING NULL,
	report_id_caps STRING NULL,
	enquiry_reason STRING NULL,
	finance_purpose STRING NULL,
	amount_financed STRING NULL,
	duration_of_agreement STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX caps_details_dedup_id_key (dedup_id ASC),
	INDEX caps_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.caps_applicant_details (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	caps_details_id STRING NOT NULL,
	credit_report_downloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	first_name STRING NULL,
	middle_name1 STRING NULL,
	middle_name2 STRING NULL,
	middle_name3 STRING NULL,
	last_name STRING NULL,
	gender_code STRING NULL,
	pan STRING NULL,
	pan_issue_date STRING NULL,
	pan_expiration_date STRING NULL,
	passport_number STRING NULL,
	passport_issue_date STRING NULL,
	passport_expiration_date STRING NULL,
	voter_id_number STRING NULL,
	voter_id_issue_date STRING NULL,
	voter_id_expiration_date STRING NULL,
	driver_license_number STRING NULL,
	driver_license_issue_date STRING NULL,
	driver_license_expiration_date STRING NULL,
	ration_card_number STRING NULL,
	ration_card_issue_date STRING NULL,
	ration_card_expiration_date STRING NULL,
	universal_id_number STRING NULL,
	universal_id_issue_date STRING NULL,
	universal_id_expiration_date STRING NULL,
	date_of_birth_applicant STRING NULL,
	telephone_number_applicant1_st STRING NULL,
	telephone_extension STRING NULL,
	telephone_type STRING NULL,
	mobile_phone_number STRING NULL,
	email_id STRING NULL,
	income STRING NULL,
	marital_status STRING NULL,
	employment_status STRING NULL,
	time_with_employer STRING NULL,
	number_of_major_credit_card_held STRING NULL,
	flat_no_plot_no_house_no_cpaad STRING NULL,
	bldg_no_society_name_cpaad STRING NULL,
	road_no_name_area_locality_cpaad STRING NULL,
	city_cpaad STRING NULL,
	landmark_cpaad STRING NULL,
	state_cpaad STRING NULL,
	pin_code_cpaad STRING NULL,
	country_code_cpaad STRING NULL,
	flat_no_plot_no_house_no_cpaaad STRING NULL,
	bldg_no_society_name_cpaaad STRING NULL,
	road_no_name_area_locality_cpaaad STRING NULL,
	city_cpaaad STRING NULL,
	landmark_cpaaad STRING NULL,
	state_cpaaad STRING NULL,
	pin_code_cpaaad STRING NULL,
	country_code_cpaaad STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX caps_applicant_details_dedup_id_key (dedup_id ASC),
	INDEX credit_account_holder_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.credit_account_holder_details (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	credit_account_details_id STRING NOT NULL,
	credit_report_downloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	first_name_non_normalized STRING NULL,
	middle_name1_non_normalized STRING NULL,
	middle_name2_non_normalized STRING NULL,
	middle_name3_non_normalized STRING NULL,
	surname_non_normalized STRING NULL,
	alias STRING NULL,
	gender_code STRING NULL,
	pan_cahd STRING NULL,
	passport_number_cahd STRING NULL,
	voter_id_number_cahd STRING NULL,
	date_of_birth STRING NULL,
	first_line_of_address_non_normalized STRING NULL,
	second_line_of_address_non_normalized STRING NULL,
	third_line_of_address_non_normalized STRING NULL,
	city_non_normalized STRING NULL,
	fifth_line_of_address_non_normalized STRING NULL,
	state_non_normalized STRING NULL,
	zip_postal_code_non_normalized STRING NULL,
	country_code_non_normalized STRING NULL,
	address_indicator_non_normalized STRING NULL,
	residence_code_non_normalized STRING NULL,
	telephone_number STRING NULL,
	telephone_type STRING NULL,
	telephone_extension STRING NULL,
	mobile_telephone_number STRING NULL,
	fax_number STRING NULL,
	email_id_cahpd STRING NULL,
	pan_cahid STRING NULL,
	pan_issue_date STRING NULL,
	pan_expiration_date STRING NULL,
	passport_number_cahid STRING NULL,
	passport_issue_date STRING NULL,
	passport_expiration_date STRING NULL,
	voter_id_number_cahid STRING NULL,
	voter_id_issue_date STRING NULL,
	voter_id_expiration_date STRING NULL,
	driving_license_number STRING NULL,
	driving_license_issue_date STRING NULL,
	driving_license_expiration_date STRING NULL,
	ration_card_number STRING NULL,
	ration_card_issue_date STRING NULL,
	ration_card_expiration_date STRING NULL,
	universal_id_number STRING NULL,
	universal_id_issue_date STRING NULL,
	universal_id_expiration_date STRING NULL,
	email_id_cahid STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX credit_account_holder_details_dedup_id_key (dedup_id ASC),
	INDEX credit_account_holder_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.current_application_details (
	id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	credit_report_downloaded_at TIMESTAMPTZ NOT NULL,
	actor_id STRING NOT NULL,
	report_id STRING NULL,
	report_date STRING NULL,
	report_time STRING NULL,
	enquiry_reason STRING NULL,
	amount_financed STRING NULL,
	finance_purpose STRING NULL,
	duration_of_agreement STRING NULL,
	first_name STRING NULL,
	middle_name1 STRING NULL,
	middle_name2 STRING NULL,
	middle_name3 STRING NULL,
	last_name STRING NULL,
	gender_code STRING NULL,
	pan STRING NULL,
	pan_issue_date STRING NULL,
	pan_expiration_date STRING NULL,
	passport_number STRING NULL,
	passport_issue_date STRING NULL,
	passport_expiration_date STRING NULL,
	voter_id_number STRING NULL,
	voter_id_issue_date STRING NULL,
	voter_id_expiration_date STRING NULL,
	driving_license_number STRING NULL,
	driving_license_issue_date STRING NULL,
	driving_license_expiration_date STRING NULL,
	ration_card_number STRING NULL,
	ration_card_issue_date STRING NULL,
	ration_card_expiration_date STRING NULL,
	universal_id_number STRING NULL,
	universal_id_issue_date STRING NULL,
	universal_id_expiration_date STRING NULL,
	date_of_birth_applicant STRING NULL,
	telephone_number_applicant1st STRING NULL,
	telephone_extension STRING NULL,
	telephone_type STRING NULL,
	mobile_phone_number STRING NULL,
	email_id STRING NULL,
	income STRING NULL,
	marital_status STRING NULL,
	employment_status STRING NULL,
	time_with_employer STRING NULL,
	number_of_major_credit_card_held STRING NULL,
	flat_no_plot_no_house_catad STRING NULL,
	bldg_no_society_name_catad STRING NULL,
	road_no_name_area_locality_catad STRING NULL,
	city_catad STRING NULL,
	landmark_catad STRING NULL,
	state_catad STRING NULL,
	pin_code_catad STRING NULL,
	country_code_catad STRING NULL,
	flat_no_plot_no_house_no_cataad STRING NULL,
	bldg_no_society_name_cataad STRING NULL,
	road_no_name_area_locality_cataad STRING NULL,
	city_cataad STRING NULL,
	landmark_cataad STRING NULL,
	state_cataad STRING NULL,
	pin_code_cataad STRING NULL,
	country_code_cataad STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	purged_at TIMESTAMPTZ NULL,
	dedup_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX current_application_details_dedup_id_key (dedup_id ASC),
	INDEX current_application_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.user_actions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	action_group STRING NOT NULL,
	action_details JSONB NULL,
	action_metadata JSONB NULL,
	overall_status STRING NULL,
	requester_metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	client_request_id STRING NOT NULL,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, action_group ASC, deleted_at_unix ASC),
	UNIQUE INDEX user_actions_id_unique_idx (id ASC),
	INDEX user_actions_actor_id_created_at_idx (actor_id ASC, created_at DESC),
	INDEX user_actions_actor_id_deleted_at_unix_idx (actor_id ASC, deleted_at_unix ASC, created_at ASC),
	INDEX user_actions_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.user_actions.action_details IS 'stores the statuses of actions within the action group';
COMMENT ON COLUMN public.user_actions.action_metadata IS 'stores the metadata of all the actions within the action group';
COMMENT ON COLUMN public.user_actions.requester_metadata IS 'stores the details of the requester and the remarks given by him';
CREATE TABLE public.loan_offer_eligibility_criteria (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	vendor_response JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	offer_id STRING NULL,
	CONSTRAINT loan_offer_eligibility_criteria_pkey PRIMARY KEY (id ASC),
	INDEX loan_offer_eligibility_criteria_actor_id_idx (actor_id ASC),
	INDEX loan_offer_eligibility_criteria_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.loan_offer_eligibility_criteria IS 'table to store all the actors who are eligible for loan from BA side';
COMMENT ON COLUMN public.loan_offer_eligibility_criteria.actor_id IS 'actor who is eligible for loan from BA';
COMMENT ON COLUMN public.loan_offer_eligibility_criteria.vendor IS '{"proto_type":"preapprovedloan.Vendor", "comment":"vendor to whom loan offer is requested"}';
COMMENT ON COLUMN public.loan_offer_eligibility_criteria.status IS '{"proto_type":"preapprovedloan.LoanOfferEligibilityCriteriaStatus", "comment":"status of the eligibility"}';
COMMENT ON COLUMN public.loan_offer_eligibility_criteria.vendor_response IS '{"proto_type":"preapprovedloan.VendorResponse", "comment":"response timestamp from vendor"}';
CREATE TABLE public.loan_installment_info (
	id STRING NOT NULL,
	account_id STRING NOT NULL,
	total_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	start_date DATE NOT NULL,
	end_date DATE NOT NULL,
	total_installment_count INT8 NOT NULL,
	next_installment_date DATE NULL,
	details JSONB NULL,
	status STRING NOT NULL,
	deactivated_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_installment_info_pkey PRIMARY KEY (id ASC),
	INDEX loan_installment_info_updated_at (updated_at DESC),
	INDEX loan_installment_info_account_id (account_id ASC),
	FAMILY frequently_updated (total_installment_count, next_installment_date, details, status, updated_at, id, deactivated_at),
	FAMILY seldom_updated (account_id, total_amount, start_date, end_date, created_at, deleted_at)
);
COMMENT ON TABLE public.loan_installment_info IS 'Represents the entity holding high level information related to the Installments associated with a loan account. It will also hold historical information if the installment details/schedule for a loan account is changed before the loan end date';
COMMENT ON COLUMN public.loan_installment_info.account_id IS 'In case of loan this will be loan account id';
COMMENT ON COLUMN public.loan_installment_info.total_amount IS 'Total amount to be collected through EMI';
COMMENT ON COLUMN public.loan_installment_info.total_installment_count IS 'Total installment count under this schedule/info';
COMMENT ON COLUMN public.loan_installment_info.details IS '{"proto_type":"preapprovedloan.LoanInstallmentInfoDetails", "comment":"Additional info in like payout mode, penalty rate, schedule etc"}';
COMMENT ON COLUMN public.loan_installment_info.status IS '{"proto_type":"preapprovedloan.LoanInstallmentInfoStatus", "comment":"active/completed/closed"}';
CREATE TABLE public.upi_accounts (
	id UUID NOT NULL DEFAULT uuid_generate_v4(),
	actor_id STRING NOT NULL,
	account_ref_id STRING NULL,
	account_ref_number STRING NOT NULL,
	masked_account_number STRING NOT NULL,
	status STRING NOT NULL,
	account_type STRING NOT NULL,
	account_meta_info JSONB NULL,
	account_preference STRING NULL,
	pin_set_status STRING NOT NULL,
	ifsc_code STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	bank_name STRING NULL,
	upi_controls_info JSONB NULL DEFAULT '{"UpiControls": ["UPI_CONTROL_DOMESTIC_PAYMENTS"]}':::JSONB,
	account_product_offering STRING NOT NULL DEFAULT 'ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED':::STRING,
	CONSTRAINT upi_accounts_pkey PRIMARY KEY (id ASC),
	INDEX upi_accounts_actor_id_idx (actor_id ASC),
	INDEX upi_accounts_updated_at_idx (updated_at DESC),
	UNIQUE INDEX upi_accounts_actor_id_account_type_masked_account_number_ifsc_code_key (actor_id ASC, account_type ASC, masked_account_number ASC, ifsc_code ASC),
	INDEX partial_account_ref_id_idx (account_ref_id ASC) WHERE (account_ref_id IS NOT NULL) AND (account_ref_id != '':::STRING),
	FAMILY frequently_updated (pin_set_status, account_preference, status, updated_at),
	FAMILY "primary" (id, actor_id, account_ref_id, account_ref_number, masked_account_number, account_type, account_meta_info, ifsc_code, created_at, deleted_at, bank_name, upi_controls_info, account_product_offering)
);
COMMENT ON TABLE public.upi_accounts IS 'table to store accounts connected through upi';
COMMENT ON COLUMN public.upi_accounts.id IS 'Unique identifier for each row';
COMMENT ON COLUMN public.upi_accounts.account_ref_id IS 'reference id for existing internal fi account eg. Savings account id';
COMMENT ON COLUMN public.upi_accounts.account_ref_number IS 'encrypted account number from NPCI';
COMMENT ON COLUMN public.upi_accounts.masked_account_number IS 'stores the masked account number';
COMMENT ON COLUMN public.upi_accounts.status IS '{"proto_type":"upi.onboarding.enums.UpiAccountStatus", "comment": "current status of upi account", "ref": "api.upi.onboarding.enums.UpiAccountStatus.proto"}';
COMMENT ON COLUMN public.upi_accounts.account_preference IS 'denotes the preference of the upi account. eg-primary';
COMMENT ON COLUMN public.upi_accounts.pin_set_status IS '{"proto_type":"upi.onboarding.enums.UpiPinSetStatus", "comment": "pin set status of the account", "ref": "api.upi.onboarding.enums.UpiPinSetStatus.proto"}';
COMMENT ON COLUMN public.upi_accounts.ifsc_code IS 'ifsc code for the upi account';
COMMENT ON COLUMN public.upi_accounts.bank_name IS 'Account Product Offering associated with the AccountType. e.g. AccountType: SAVINGS, AccountProductOffering: NRE,NRO etc.';
COMMENT ON COLUMN public.upi_accounts.upi_controls_info IS '{"proto_type":"upi.onboarding.enums.UpiControl", "comment":"stores the list of allowed payment types for an account (E.g. Domestic, International etc.)", "ref":"api.upi.onboarding.enums.upi_account_enums.proto"}';
CREATE TABLE public.upi_onboarding_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	account_id STRING NOT NULL,
	vendor STRING NOT NULL,
	vpa STRING NULL,
	client_request_id STRING NOT NULL,
	action STRING NOT NULL,
	status STRING NOT NULL,
	vendor_request_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	payload JSONB NULL,
	CONSTRAINT upi_onboarding_details_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX upi_onboarding_details_client_request_id_key (client_request_id ASC),
	UNIQUE INDEX upi_onboarding_details_vendor_request_id_key (vendor_request_id ASC),
	INDEX upi_onboarding_details_account_id_idx (account_id ASC),
	INDEX upi_onboarding_details_vpa_idx (vpa ASC),
	INDEX upi_onboarding_details_updated_at_idx (updated_at DESC),
	FAMILY frequently_updated (status, updated_at),
	FAMILY "primary" (id, account_id, vendor, vpa, client_request_id, action, vendor_request_id, created_at, deleted_at, payload)
);
COMMENT ON TABLE public.upi_onboarding_details IS 'table to store all the details related to onboarding an upi account';
COMMENT ON COLUMN public.upi_onboarding_details.id IS 'Unique identifier for each row';
COMMENT ON COLUMN public.upi_onboarding_details.account_id IS 'Account id of the upi account';
COMMENT ON COLUMN public.upi_onboarding_details.vendor IS '{"vendor":"vendorgateway.Vendor", "comment": "Upi onboarding partner bank", "ref": "api.vendorgateway.vendor.proto"}';
COMMENT ON COLUMN public.upi_onboarding_details.vpa IS 'Vpa to be linked with account';
COMMENT ON COLUMN public.upi_onboarding_details.client_request_id IS 'req Id use for creating a workflow request in celestial for onboarding';
COMMENT ON COLUMN public.upi_onboarding_details.action IS '{"vendor":"upi.onboarding.enums.UpiOnboardingAction", "comment": "type of the action taken - LINK/DELINK", "ref": "api.upi.onboarding.enums.UpiOnboardingAction.proto"}';
COMMENT ON COLUMN public.upi_onboarding_details.status IS '{"vendor":"upi.onboarding.enums.UpiOnboardingStatus", "comment": "Current status of the account onboarding", "ref": "api.upi.onboarding.enums.UpiOnboardingStatus.proto"}';
COMMENT ON COLUMN public.upi_onboarding_details.vendor_request_id IS 'Req id for the request initiated with vendor';
COMMENT ON COLUMN public.upi_onboarding_details.payload IS '{"proto_type":"upi.onboarding.UpiOnboardingDetail", "comment":"stores details required for upi account actions"}';
CREATE TABLE public.loan_activities (
	id STRING NOT NULL,
	loan_account_id STRING NULL,
	details JSONB NULL,
	type STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_activities_pkey PRIMARY KEY (id ASC),
	INDEX loan_activities_updated_at (updated_at DESC),
	INDEX loan_activities_loan_account_id (loan_account_id ASC),
	FAMILY frequently_updated (details, type, updated_at, id, created_at),
	FAMILY seldom_updated (deleted_at, loan_account_id)
);
COMMENT ON TABLE public.loan_activities IS 'Table to keep a track of any activity related to a loan account such as an EMI Payment, Lump-sum payment, penalty fee payment, etc. and not just Fi initiated, but from external vendors too';
COMMENT ON COLUMN public.loan_activities.loan_account_id IS 'Loan account associated with the loan';
COMMENT ON COLUMN public.loan_activities.details IS '{"proto_type":"preapprovedloan.LoanActivitiesDetails", "comment":""}';
COMMENT ON COLUMN public.loan_activities.type IS '{"proto_type":"preapprovedloan.LoanActivitiesType", "comment":""}';
CREATE TABLE public.actor_vpa_name_map (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	vpa_name STRING NOT NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT actor_vpa_name_map_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX actor_vpa_name_map_actor_id_key (actor_id ASC),
	UNIQUE INDEX actor_vpa_name_map_vpa_name_key (vpa_name ASC),
	FAMILY frequently_updated (updated_at),
	FAMILY "primary" (id, actor_id, vpa_name, created_at, deleted_at)
);
COMMENT ON TABLE public.actor_vpa_name_map IS '{"proto_type":"upi.onboarding.ActorVpaNameMap", "comment": "Table to store the mapping between actor id and vpa name. For a vpa abcd@fbl, abcd is considered as vpa name", "ref": "api.upi.onboarding.actor_vpa_name_map.proto"}';
COMMENT ON COLUMN public.actor_vpa_name_map.actor_id IS 'column to store the actor id to which the vpa name belongs';
COMMENT ON COLUMN public.actor_vpa_name_map.vpa_name IS 'column to store the vpa name for actor. For a vpa abcd@fbl, abcd is considered as vpa name';
CREATE TABLE public.order_metadata (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	metadata JSONB NOT NULL,
	metadata_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT order_metadata_pkey PRIMARY KEY (id ASC),
	INDEX order_metadata_updated_at_idx (updated_at DESC),
	INDEX order_metadata_order_id_idx (order_id ASC),
	INDEX order_metadata_order_id_metadata_type_idx (order_id ASC, metadata_type ASC),
	FAMILY frequently_updated (updated_at),
	FAMILY "primary" (id, order_id, metadata, metadata_type, created_at, deleted_at)
);
COMMENT ON TABLE public.order_metadata IS 'stores the metadata of a particular type for an order id';
COMMENT ON COLUMN public.order_metadata.id IS 'stores the unique random id generated by default and acts as a primary key';
COMMENT ON COLUMN public.order_metadata.order_id IS 'stores the order_id from orders table (foreign key)';
COMMENT ON COLUMN public.order_metadata.metadata IS '{"proto_type":"order.Metadata", "comment":"stores the metadata for a given order id", "ref":"api.order.order_metadata.proto}';
COMMENT ON COLUMN public.order_metadata.metadata_type IS '{"proto_type":"order.MetadataType", "comment": "stores the type of metadata stored", "ref": "api.order.order_metadata.proto"}';
CREATE TABLE public.esign_requests (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	vendor_document_id STRING NULL,
	client_request_id STRING NULL,
	client STRING NULL,
	irn STRING NOT NULL,
	sign_url STRING NULL,
	status STRING NOT NULL,
	vendor STRING NULL,
	template_type STRING NULL,
	template_option JSONB NULL,
	signed_at TIMESTAMPTZ NULL,
	expiry_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT esign_requests_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX esign_requests_irn_key (irn ASC),
	UNIQUE INDEX esign_requests_unique_index_on_client_request_id_and_client (client_request_id ASC, client ASC),
	INDEX esign_requests_updated_at (updated_at DESC),
	FAMILY frequently_updated (status, sign_url, client_request_id, irn, updated_at, id, actor_id, client, template_type, template_option, signed_at, created_at),
	FAMILY seldom_updated (deleted_at, vendor_document_id, vendor, expiry_at)
);
COMMENT ON TABLE public.esign_requests IS 'Table to keep a track of any esign related requests';
COMMENT ON COLUMN public.esign_requests.vendor_document_id IS 'Document Id returned by vendor';
COMMENT ON COLUMN public.esign_requests.client_request_id IS 'Id generated from Client, sent to Esign';
COMMENT ON COLUMN public.esign_requests.client IS 'Client/Service/Caller which called Esign';
COMMENT ON COLUMN public.esign_requests.irn IS 'Internal Reference Number, generated and sent from Esign to Vendor';
COMMENT ON COLUMN public.esign_requests.sign_url IS 'document url to send to vendor';
COMMENT ON COLUMN public.esign_requests.status IS '{"proto_type":"esign.EsignRequestStatus", "comment":"Created/Pending/Success/Failed"}';
COMMENT ON COLUMN public.esign_requests.template_type IS '{"proto_type":"types.esign.TemplateType", "comment":""}';
COMMENT ON COLUMN public.esign_requests.template_option IS '{"proto_type":"types.esign.TemplateOptions", "comment":""}';
CREATE TABLE public.uss_investors (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	pan_number STRING NULL,
	disclosures JSONB NULL,
	agreements JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT uss_investors_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX uss_investor_actor_id_unique_idx (actor_id ASC)
);
COMMENT ON TABLE public.uss_investors IS '{"proto_type": "usstocks.account.Investor", "comment":"table to store all the investor data like consents, disclosures and agreements"}';
CREATE TABLE public.uss_accounts (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	account_status STRING NOT NULL,
	vendor STRING NOT NULL,
	vendor_account_id STRING NULL,
	external_account_id STRING NOT NULL,
	vendor_account_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT uss_accounts_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX uss_account_actor_vendor_unique_idx (actor_id ASC, vendor ASC),
	UNIQUE INDEX uss_account_vendor_acc_id_unique_idx (vendor_account_id ASC),
	UNIQUE INDEX uss_account_external_acc_id_unique_idx (external_account_id ASC)
);
COMMENT ON TABLE public.uss_accounts IS '{"proto_type": "usstocks.account.Account", "comment":"table to store all the us-stock broking accounts for a user"}';
COMMENT ON COLUMN public.uss_accounts.vendor_account_id IS 'vendor account id will be used for communications with vendor. vendor account id will be assigned by vendor on account creation';
COMMENT ON COLUMN public.uss_accounts.external_account_id IS 'external account id will be used as display id for user';
CREATE TABLE public.risk_bank_actions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	client_req_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	account_id STRING NOT NULL,
	account_type STRING NOT NULL,
	vendor STRING NOT NULL,
	action STRING NOT NULL,
	state STRING NOT NULL,
	request_reason JSONB NOT NULL,
	failure_reason STRING NULL,
	bank_action_reason JSONB NULL,
	provenance STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	comms_template STRING[] NULL,
	is_recon BOOL NULL,
	bank_action_date TIMESTAMPTZ NULL,
	lien_request_id VARCHAR NULL,
	CONSTRAINT risk_bank_actions_pkey PRIMARY KEY (id ASC),
	INDEX risk_bank_actions_client_req_id_idx (client_req_id ASC),
	INDEX risk_bank_actions_actor_id_idx (actor_id ASC),
	INDEX risk_bank_actions_updated_at_idx (updated_at ASC),
	UNIQUE INDEX risk_bank_actions_client_req_id_key (client_req_id ASC),
	FAMILY frequently_updated (updated_at, state, lien_request_id),
	FAMILY seldom_updated (bank_action_reason, failure_reason, bank_action_date),
	FAMILY "primary" (id, client_req_id, actor_id, account_id, account_type, action, vendor, request_reason, provenance, created_at, deleted_at, comms_template, is_recon)
);
COMMENT ON TABLE public.risk_bank_actions IS 'entity where we will keep track of all cases of block/unblock/freeze of users';
COMMENT ON COLUMN public.risk_bank_actions.client_req_id IS 'id corresponding to the celestial workflow';
COMMENT ON COLUMN public.risk_bank_actions.account_id IS 'id for orchestrating the request at celestial/orchestrator';
COMMENT ON COLUMN public.risk_bank_actions.account_type IS 'high level status of the request';
COMMENT ON COLUMN public.risk_bank_actions.vendor IS ' bank identifier/ action executing authority';
COMMENT ON COLUMN public.risk_bank_actions.action IS 'action needs to be taken on account';
COMMENT ON COLUMN public.risk_bank_actions.state IS 'state will determine whats the current user position is in the workflow for that Action';
COMMENT ON COLUMN public.risk_bank_actions.failure_reason IS 'states reason why overall state machine failed';
COMMENT ON COLUMN public.risk_bank_actions.bank_action_reason IS 'account status with reason received from bank api';
COMMENT ON COLUMN public.risk_bank_actions.provenance IS 'different entry origin of request in the table';
COMMENT ON COLUMN public.risk_bank_actions.comms_template IS 'comms to be triggered to users';
COMMENT ON COLUMN public.risk_bank_actions.is_recon IS 'is_recon flag to avoid pre-checks and force actor to flow, added to family primary as wont be updated';
COMMENT ON COLUMN public.risk_bank_actions.bank_action_date IS 'timestamp on when action is taken from bank, added to family seldom_updated as this may update for certain for certain cases';
CREATE TABLE public.auth_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	client_request_id STRING NOT NULL,
	flow_name STRING NOT NULL,
	orch_id STRING NULL,
	next_action JSONB NULL,
	status STRING NOT NULL,
	provenance STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	redirect_action JSONB NULL,
	preferences JSONB NOT NULL DEFAULT '{}':::JSONB,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX auth_requests_client_request_id_key (client_request_id ASC),
	INDEX auth_requests_updated_at_idx (updated_at ASC),
	INDEX auth_requests_orch_id_idx (orch_id ASC)
);
COMMENT ON TABLE public.auth_requests IS 'entity where we will keep track of all auth requests and will store the state machine for each request';
COMMENT ON COLUMN public.auth_requests.client_request_id IS 'id used by client for keeping track of request';
COMMENT ON COLUMN public.auth_requests.flow_name IS 'name of flow to be triggered which is defined on auth service';
COMMENT ON COLUMN public.auth_requests.orch_id IS 'id for orchestrating the request at celestial/orchestrator';
COMMENT ON COLUMN public.auth_requests.next_action IS 'deeplink to be sent after auth flow is complete';
COMMENT ON COLUMN public.auth_requests.status IS 'high level status of the request';
COMMENT ON COLUMN public.auth_requests.provenance IS 'enum denoting entry point for the request, CREDIT_CARD etc';
COMMENT ON COLUMN public.auth_requests.preferences IS '{"proto_type":"auth.orchestrator.Preferences", "comment":"stores client preferences for auth flows"}';
CREATE TABLE public.auth_request_stages (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	auth_request_id UUID NOT NULL,
	auth_stage STRING NOT NULL,
	auth_ref_id STRING NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	staled_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX auth_requests_updated_at_idx (updated_at ASC),
	INDEX auth_request_stages_auth_request_id_idx (auth_request_id ASC)
);
COMMENT ON TABLE public.auth_request_stages IS 'entity where we will keep track of all stages in an auth flow and store the state machine';
COMMENT ON COLUMN public.auth_request_stages.auth_request_id IS 'foreign key referencing auth_requests.id';
COMMENT ON COLUMN public.auth_request_stages.auth_stage IS 'stage of auth flow(e.g. liveness, secure pin etc.)';
COMMENT ON COLUMN public.auth_request_stages.auth_ref_id IS 'ref id used to keep track of individual auth mechanism(e.g. liveness attempt id)';
COMMENT ON COLUMN public.auth_request_stages.status IS 'high level status of the stage';
COMMENT ON COLUMN public.auth_request_stages.staled_at IS 'timestamp post which auth stage becomes stale';
COMMENT ON COLUMN public.auth_request_stages.completed_at IS 'timestamp when auth stage completes';
CREATE TABLE public.upi_request_logs (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	account_id STRING NULL,
	vendor STRING NOT NULL,
	status STRING NOT NULL,
	api_type STRING NOT NULL,
	detailed_status JSONB NULL,
	vendor_req_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	api_sub_type STRING NULL,
	CONSTRAINT upi_request_logs_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX upi_request_logs_vendor_req_id_key (vendor_req_id ASC),
	INDEX upi_request_logs_updated_at_idx (updated_at DESC),
	INDEX upi_request_logs_actor_id_account_id_idx (actor_id ASC, account_id ASC),
	INDEX upi_request_logs_account_id_api_type_idx (account_id ASC, api_type ASC),
	INDEX upi_request_logs_vendor_req_id_idx (vendor_req_id ASC),
	FAMILY frequently_updated (updated_at, status, detailed_status),
	FAMILY "primary" (id, actor_id, account_id, vendor, api_type, created_at, deleted_at, vendor_req_id, api_sub_type)
);
COMMENT ON TABLE public.upi_request_logs IS 'stores the response codes from the vendor for all the upi requests for an actor';
COMMENT ON COLUMN public.upi_request_logs.id IS 'stores the unique random id generated by default and acts as a primary key';
COMMENT ON COLUMN public.upi_request_logs.actor_id IS 'actor id corresponding to the user';
COMMENT ON COLUMN public.upi_request_logs.account_id IS 'account id of the upi account corresponding to which the request/response are logged';
COMMENT ON COLUMN public.upi_request_logs.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment":"stores the Tpap partner bank that epifi integrates with", "ref":"api.vendorgateway.vendor.proto"';
COMMENT ON COLUMN public.upi_request_logs.status IS '{"proto_type":"upi.onboarding.enums.UpiRequestLogApiStatus", "comment":"stores the status of the request initiated with vendor", "ref":"api.upi.onboarding.enums.upi_request_logs_enums.proto"}';
COMMENT ON COLUMN public.upi_request_logs.api_type IS '{"proto_type":"upi.onboarding.enums.UpiRequestLogApiType", "comment":"stores the api corresponding to the request initiated with vendor", "ref":"api.upi.onboarding.enums.upi_request_logs_enums.proto"}';
COMMENT ON COLUMN public.upi_request_logs.detailed_status IS '{"proto_type":"upi.onboarding.DetailedStatus", "comment":"stores the request codes and descriptions", "ref":"api.upi.onboarding.upi_request_logs.proto"}';
COMMENT ON COLUMN public.upi_request_logs.vendor_req_id IS 'stores the request id for the request initiated with vendor';
COMMENT ON COLUMN public.upi_request_logs.api_sub_type IS '{"proto_type":"upi.onboarding.enums.UpiRequestLogApiSubType", "comment":"stores the api sub type corresponding to the request initiated with vendor", "ref":"api.upi.onboarding.enums.upi_request_logs_enums.proto"}';
CREATE TABLE public.txn_risk_scores (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	vendor_response_time TIMESTAMPTZ NOT NULL,
	txn_id STRING NOT NULL,
	status STRING NOT NULL,
	reason STRING NULL,
	score FLOAT8 NOT NULL,
	vendor STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT txn_risk_scores_pkey PRIMARY KEY (txn_id ASC, id ASC),
	UNIQUE INDEX txn_risk_scores_id_key (id ASC),
	INDEX txn_risk_scores_txn_id_idx (txn_id ASC),
	INDEX txn_risk_scores_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.txn_risk_score_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	txn_risk_score_id STRING NOT NULL,
	txn_id STRING NOT NULL,
	rule_name STRING NOT NULL,
	remarks STRING NULL,
	score FLOAT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT txn_risk_score_details_pkey PRIMARY KEY (txn_id ASC, id ASC),
	UNIQUE INDEX txn_risk_score_details_id_key (id ASC),
	INDEX txn_risk_scores_details_txn_id_txn_risk_score_id_idx (txn_id ASC, txn_risk_score_id ASC),
	INDEX txn_risk_score_details_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.credit_report_downloads (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	request_id UUID NOT NULL,
	actor_id STRING NOT NULL,
	vendor STRING NOT NULL,
	fetch_type STRING NOT NULL,
	otp_info JSONB NULL,
	consent_info JSONB NULL,
	process_status STRING NOT NULL,
	process_sub_status STRING NOT NULL,
	redirect_deeplink JSONB NULL,
	downloaded_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	provenance STRING NULL,
	credit_report_id STRING NULL,
	orch_id STRING NULL,
	next_action JSONB NULL,
	vendor_error STRING NULL,
	details JSONB NULL,
	CONSTRAINT credit_report_downloads_pkey PRIMARY KEY (id ASC),
	INDEX credit_report_downloads_updated_at_idx (updated_at ASC),
	INDEX credit_report_downloads_actor_id_created_at_idx (actor_id ASC, downloaded_at DESC) STORING (vendor),
	UNIQUE INDEX uniq_credit_report_downloads_request_id_idx (request_id ASC)
);
COMMENT ON TABLE public.credit_report_downloads IS 'table to store credit report download process data';
COMMENT ON COLUMN public.credit_report_downloads.fetch_type IS 'specifies the kind of fetch that was done in order to download the report';
COMMENT ON COLUMN public.credit_report_downloads.otp_info IS 'info related to phone number otp verification';
COMMENT ON COLUMN public.credit_report_downloads.consent_info IS 'info related to report download consent given by user';
COMMENT ON COLUMN public.credit_report_downloads.process_status IS 'overall status of the credit report download process';
COMMENT ON COLUMN public.credit_report_downloads.process_sub_status IS 'a more granular status or reason for the process to be in a particular status';
CREATE TABLE public.credit_report_user_subscription_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	vendor STRING NOT NULL,
	subscription_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT credit_report_user_subscription_details_pkey PRIMARY KEY (id ASC),
	INDEX credit_report_user_subscription_details_updated_at_idx (updated_at ASC),
	UNIQUE INDEX uniq_credit_report_subscription_details_actor_id_vendor_idx (actor_id ASC, vendor ASC)
);
COMMENT ON COLUMN public.credit_report_user_subscription_details.subscription_info IS 'info related to credit report download subscription for an actor with a vendor';
CREATE TABLE public.liveness_fm_annotations (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	req_id STRING NOT NULL,
	annotation_type STRING NOT NULL,
	annotation JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT liveness_fm_annotations_pkey PRIMARY KEY (actor_id ASC, annotation_type ASC, req_id ASC, id ASC),
	UNIQUE INDEX liveness_fm_annotations_id_idx (id ASC),
	INDEX liveness_fm_annotations_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.liveness_fm_annotations IS 'collection of annotations made by ops team';
COMMENT ON COLUMN public.liveness_fm_annotations.req_id IS 'ID of the attempt which is being annotated. Eg. request id of liveness';
COMMENT ON COLUMN public.liveness_fm_annotations.annotation_type IS 'type of attempt which is being annotated. Eg. liveness, facematch';
COMMENT ON COLUMN public.liveness_fm_annotations.annotation IS 'annotation provided by ops team';
CREATE TABLE public.screener_attempts (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	result_info JSONB NULL,
	expiry TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	client STRING NOT NULL DEFAULT 'SCREENER_CHECK_CLIENT_UNSPECIFIED':::STRING,
	CONSTRAINT screener_attempts_pkey PRIMARY KEY (actor_id ASC, deleted_at_unix ASC),
	UNIQUE INDEX screener_attempts_id_key (id ASC),
	INDEX screener_attempts_updated_at_idx (updated_at ASC),
	INDEX screener_attempts_deleted_at_expiry_idx (deleted_at_unix ASC, expiry ASC)
);
COMMENT ON TABLE public.screener_attempts IS 'table to store screener attempts of a user';
COMMENT ON COLUMN public.screener_attempts.result_info IS 'stores screener attempt information like result(screener passed/failed), passing stage, remarks';
COMMENT ON COLUMN public.screener_attempts.expiry IS 'time after which screener is reset for a user';
CREATE TABLE public.screener_check_attempts (
	id STRING NOT NULL,
	screener_attempt_id STRING NOT NULL,
	check_type STRING NOT NULL,
	check_result STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT screener_check_attempts_pkey PRIMARY KEY (screener_attempt_id ASC, check_type ASC, deleted_at_unix ASC),
	UNIQUE INDEX screener_check_attempts_id_key (id ASC),
	INDEX screener_check_attempts_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.screener_check_attempts IS 'table to store individual screener check attempt details';
COMMENT ON COLUMN public.screener_check_attempts.screener_attempt_id IS 'foreign key to screener_attempts table';
CREATE TABLE public.loan_payment_requests (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	account_id STRING NOT NULL,
	amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	type STRING NOT NULL,
	orch_id STRING NOT NULL,
	details JSONB NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_payment_requests_pkey PRIMARY KEY (id ASC),
	INDEX loan_payment_requests_updated_at (updated_at DESC),
	INDEX loan_payment_requests_index_on_account_id (account_id ASC),
	UNIQUE INDEX loan_payment_requests_unique_index_on_orch_id (orch_id ASC),
	FAMILY frequently_updated (details, status, sub_status, updated_at, id, actor_id, account_id, orch_id, created_at),
	FAMILY seldom_updated (type, amount, deleted_at)
);
COMMENT ON TABLE public.loan_payment_requests IS 'Table used for orchestrating payments against a loan account, be it EMIs or Lump sum payments';
COMMENT ON COLUMN public.loan_payment_requests.account_id IS 'Loan account against which we are making a payment';
COMMENT ON COLUMN public.loan_payment_requests.amount IS 'Loan Payment Amount';
COMMENT ON COLUMN public.loan_payment_requests.type IS '{"proto_type":"preapprovedloan.LoanPaymentRequestType", "comment":"emi/lumpsum"}';
COMMENT ON COLUMN public.loan_payment_requests.orch_id IS 'Orchestration identifier which has started this execution';
COMMENT ON COLUMN public.loan_payment_requests.details IS '{"proto_type":"preapprovedloan.LoanPaymentRequestDetails", "comment":"Additional info in transactions. Will hold payment ref ids for both remitter and beneficiary"}';
COMMENT ON COLUMN public.loan_payment_requests.status IS '{"proto_type":"preapprovedloan.LoanPaymentRequestStatus", "comment":"status of the request"}';
COMMENT ON COLUMN public.loan_payment_requests.sub_status IS '{"proto_type":"preapprovedloan.LoanPaymentRequestSubStatus", "comment":"Granular info on status"}';
CREATE TABLE public.foreign_remittance_file_generation_attempts (
	id STRING NOT NULL,
	client_request_id STRING NOT NULL,
	file_name STRING NULL,
	file_status STRING NOT NULL,
	file_type STRING NOT NULL,
	vendor STRING NOT NULL,
	file_processing_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	acknowledged_at TIMESTAMPTZ NULL,
	CONSTRAINT foreign_remittance_file_generation_attempts_pkey PRIMARY KEY (id ASC),
	INDEX foreign_remittance_file_generation_updated_at_idx (updated_at ASC),
	INDEX foreign_remittance_file_generation_status_idx (file_status ASC),
	INDEX ift_fga_file_type_ack_at_idx (file_type ASC, acknowledged_at ASC),
	UNIQUE INDEX foreign_remittance_file_generation_attempts_client_request_id (client_request_id ASC) WHERE file_status != 'FILE_STATUS_INVALID':::STRING
);
COMMENT ON TABLE public.foreign_remittance_file_generation_attempts IS 'stores details of a file generated by the file generator service.';
COMMENT ON COLUMN public.foreign_remittance_file_generation_attempts.file_name IS 'name of the file. file name will be initially null and will be populated after the file contents gets generated';
COMMENT ON COLUMN public.foreign_remittance_file_generation_attempts.file_status IS 'denotes the status of the file';
COMMENT ON COLUMN public.foreign_remittance_file_generation_attempts.vendor IS 'denotes the name of the vendor for which the file is created.';
COMMENT ON COLUMN public.foreign_remittance_file_generation_attempts.file_processing_info IS 'denotes the file processing info for the generated file, will include file path, vendor action performed, etc.';
COMMENT ON COLUMN public.foreign_remittance_file_generation_attempts.created_at IS 'time at which the file is created';
CREATE TABLE public.deposit_interest_rates (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	start_date TIMESTAMPTZ NOT NULL,
	end_date TIMESTAMPTZ NULL,
	account_type STRING NOT NULL,
	vendor STRING NOT NULL,
	interest_details JSONB NOT NULL DEFAULT '{}':::JSONB,
	category STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	client_req_id STRING NULL,
	status STRING NULL DEFAULT 'INTEREST_RATE_STATUS_UNSPECIFIED':::STRING,
	CONSTRAINT deposit_interest_rates_pkey PRIMARY KEY (id ASC),
	INDEX deposit_interest_rates_start_date_idx (start_date DESC),
	INDEX deposit_interest_rates_updated_at_idx (updated_at DESC),
	UNIQUE INDEX deposit_interest_rates_unique_client_req_id_idx (client_req_id ASC),
	INDEX deposit_interest_rates_status_idx (status ASC) WHERE status = 'INTEREST_RATE_STATUS_ACTIVE':::STRING
);
COMMENT ON TABLE public.deposit_interest_rates IS 'stores deposit interest rate details';
COMMENT ON COLUMN public.deposit_interest_rates.start_date IS 'effective date of the interest rate';
COMMENT ON COLUMN public.deposit_interest_rates.end_date IS 'valid till date of the interest rate';
COMMENT ON COLUMN public.deposit_interest_rates.account_type IS 'SD or FD';
COMMENT ON COLUMN public.deposit_interest_rates.vendor IS 'banking partner';
COMMENT ON COLUMN public.deposit_interest_rates.interest_details IS 'interest rate details for different tenures';
COMMENT ON COLUMN public.deposit_interest_rates.category IS 'general public or senior citizen';
CREATE TABLE public.risk_evaluator_entities (
	entity_type STRING NOT NULL,
	entity_id STRING NOT NULL,
	vendor STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	CONSTRAINT risk_evaluator_entities_pkey PRIMARY KEY (entity_type ASC, entity_id ASC, id ASC),
	INDEX risk_evaluator_entities_entity_type_entity_id (entity_type ASC, entity_id ASC),
	INDEX risk_evaluator_entities_updated_at (updated_at ASC),
	INDEX risk_evaluator_entities_entity_vendor_idx (vendor ASC, entity_type ASC, entity_id ASC)
);
CREATE TABLE public.user_intel (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	client_req_id STRING NOT NULL,
	intel_type STRING NOT NULL,
	intel_data JSONB NOT NULL,
	status STRING NOT NULL,
	vendor STRING NOT NULL,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	sensitive_intel_data JSONB NULL,
	CONSTRAINT user_intel_pkey PRIMARY KEY (actor_id ASC, intel_type ASC, deleted_at_unix ASC),
	UNIQUE INDEX user_intel_id_idx (id ASC),
	INDEX user_intel_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.user_intel IS 'stores user related intelligence used for qualitative analysis of a user';
COMMENT ON COLUMN public.user_intel.intel_type IS 'stores the type of user intel stored eg. affluence, premium device intel etc.';
COMMENT ON COLUMN public.user_intel.sensitive_intel_data IS '{"proto_type":"userintel.SensitiveIntelData", "comment":"stores sensitive/pii intel data"}';
CREATE TABLE public.service_requests (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	request_type STRING NOT NULL,
	status STRING NOT NULL,
	details JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	vendor_request_id STRING NULL,
	CONSTRAINT service_requests_pkey PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX service_requests_actor_request_type_idx (actor_id ASC, request_type ASC, deleted_at_unix ASC),
	UNIQUE INDEX service_requests_id_idx (id ASC),
	INDEX service_requests_updated_at_idx (updated_at ASC),
	UNIQUE INDEX service_requests_request_type_vendor_request_id_unique_idx (request_type ASC, vendor_request_id ASC),
	INDEX service_requests_actor_request_type_status_created_at_idx (actor_id ASC, request_type ASC, status ASC, created_at DESC)
);
COMMENT ON TABLE public.service_requests IS 'Table to keep track of all requests made by the users';
COMMENT ON COLUMN public.service_requests.request_type IS 'Type of request made by the user';
CREATE TABLE public.aml_screening_attempts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	client_request_id STRING NOT NULL,
	product STRING NOT NULL,
	vendor STRING NOT NULL,
	customer_details JSONB NULL,
	status STRING NOT NULL,
	result STRING NOT NULL,
	rejection_code STRING NULL,
	rejection_message STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	last_screening_attempted_at TIMESTAMPTZ NULL,
	CONSTRAINT aml_screening_attempts_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX client_request_id_idx (client_request_id ASC),
	INDEX actor_id_entity_product_index (actor_id ASC, product ASC),
	INDEX updated_at (updated_at DESC),
	INDEX aml_screening_attempt_last_screening_attempted_at_idx (last_screening_attempted_at DESC)
);
COMMENT ON TABLE public.aml_screening_attempts IS 'stores details about the aml screening attempts raised';
COMMENT ON COLUMN public.aml_screening_attempts.product IS 'this is the product for which the attempt is raised';
COMMENT ON COLUMN public.aml_screening_attempts.customer_details IS 'details of the customer being passed to vendor';
COMMENT ON COLUMN public.aml_screening_attempts.status IS 'status of the screening attempt';
COMMENT ON COLUMN public.aml_screening_attempts.result IS 'result of the screening attempt';
COMMENT ON COLUMN public.aml_screening_attempts.rejection_code IS 'rejection code from vendor if attempt is rejected';
COMMENT ON COLUMN public.aml_screening_attempts.rejection_message IS 'rejection message from vendor if attempt is rejected';
CREATE TABLE public.aml_case_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	review_status STRING NOT NULL,
	vendor STRING NOT NULL,
	products JSONB NOT NULL,
	match_details JSONB NULL,
	vendor_case_id STRING NULL,
	attempt_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT aml_case_details_pkey PRIMARY KEY (id ASC),
	INDEX actor_id_index (actor_id ASC),
	UNIQUE INDEX unique_index_on_vendor_case_id (vendor_case_id ASC),
	INDEX attempt_id_index (attempt_id ASC),
	INDEX updated_at (updated_at DESC)
);
COMMENT ON TABLE public.aml_case_details IS 'stores details about the aml matches found';
COMMENT ON COLUMN public.aml_case_details.review_status IS 'status of the manual review by agent';
COMMENT ON COLUMN public.aml_case_details.products IS 'collection of products which the case is created';
COMMENT ON COLUMN public.aml_case_details.match_details IS 'details of the match found like case id, watchlist name etc.';
COMMENT ON COLUMN public.aml_case_details.vendor_case_id IS 'unique id for each case which is generated by vendor';
COMMENT ON COLUMN public.aml_case_details.attempt_id IS 'primary id of the screening_attempts table if the match is created for a screening attempt';
CREATE TABLE public.referrals_segmented_components (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	segment_id STRING NOT NULL,
	component STRING NOT NULL,
	component_details JSONB NOT NULL,
	active_since TIMESTAMPTZ NOT NULL,
	active_till TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	app_feature STRING NOT NULL DEFAULT 'SAVINGS_ACCOUNT':::STRING,
	variant STRING NOT NULL DEFAULT 'VARIANT_UNSPECIFIED':::STRING,
	CONSTRAINT referrals_segmented_components_pkey PRIMARY KEY (id ASC),
	INDEX rsc_updated_at_index (updated_at DESC),
	INDEX rsc_component_segment_id_index (component ASC, segment_id ASC),
	INDEX rsc_active_till_app_feature_component_idx (active_till ASC, app_feature ASC, component ASC)
);
COMMENT ON TABLE public.referrals_segmented_components IS 'stores the details of referral component details for different segments';
COMMENT ON COLUMN public.referrals_segmented_components.component IS 'stores ENUM corresponding to different components like OLD_HOME_SCREEN_WIDGET, NEW_HOME_SCREEN_WIDGET, etc.';
COMMENT ON COLUMN public.referrals_segmented_components.component_details IS 'stores the details of component corresponding to the segment, like {"how_to_win_widget_display_details": {"steps": "", "tncs": ""}}';
COMMENT ON COLUMN public.referrals_segmented_components.app_feature IS '{"proto_type":"inappreferral.enums.AppFeature", "comment": "field storing the app feature for which the component is shown"}';
COMMENT ON COLUMN public.referrals_segmented_components.variant IS '{"proto_type":"inappreferral.enums.Variant", "comment": "field storing the variant of the component. A variant represents another dimension to support multiple versions of the same (component, segment_id, app_feature) tuple."}';
CREATE TABLE public.aml_file_generation_attempts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	client_request_id STRING NOT NULL,
	file_type STRING NOT NULL,
	file_s3_paths JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT aml_file_generation_attempts_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX client_request_id_idx (client_request_id ASC),
	INDEX aml_file_generation_attempts_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.aml_file_generation_attempts IS 'stores details about the files generated for aml continuous screening';
COMMENT ON COLUMN public.aml_file_generation_attempts.client_request_id IS 'client request id to identify the screening attempts eligible for file generation';
COMMENT ON COLUMN public.aml_file_generation_attempts.file_type IS 'type of files generated';
COMMENT ON COLUMN public.aml_file_generation_attempts.file_s3_paths IS 's3 paths of the files generated';
CREATE TABLE public.aml_client_id_to_actor_id_mapping (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	client_request_id STRING NOT NULL,
	file_generation_status STRING NOT NULL,
	error STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	rowid INT8 NOT VISIBLE NOT NULL DEFAULT unique_rowid(),
	actor_id STRING NOT NULL,
	CONSTRAINT aml_attempt_id_client_id_mapper_pkey PRIMARY KEY (rowid ASC),
	UNIQUE INDEX aml_client_id_to_actor_id_mapping_actor_id_client_id_idx (actor_id ASC, client_request_id ASC),
	INDEX aml_client_id_to_actor_id_mapping_file_generation_status_idx (file_generation_status ASC),
	INDEX aml_client_id_to_actor_id_mapping_updated_at_idx (updated_at DESC),
	INDEX aml_client_id_to_actor_id_mapping_client_id_idx (client_request_id ASC)
);
COMMENT ON TABLE public.aml_client_id_to_actor_id_mapping IS 'stores the mapping of client request id to actor ids raised for file generation';
COMMENT ON COLUMN public.aml_client_id_to_actor_id_mapping.client_request_id IS 'client request id to identify the actors eligible for file generation';
COMMENT ON COLUMN public.aml_client_id_to_actor_id_mapping.file_generation_status IS 'status of the file generation for this actor';
COMMENT ON COLUMN public.aml_client_id_to_actor_id_mapping.error IS 'details of error in generating file';
CREATE TABLE public.loan_installment_payout (
	id STRING NOT NULL,
	loan_installment_info_id STRING NOT NULL,
	amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	due_date DATE NOT NULL,
	payout_date DATE NOT NULL,
	details JSONB NULL DEFAULT '{}':::JSONB,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT loan_installment_payout_pkey PRIMARY KEY (id ASC),
	INDEX loan_installment_payout_updated_at (updated_at DESC),
	INDEX loan_installment_payout_loan_installment_info_id (loan_installment_info_id ASC)
);
COMMENT ON TABLE public.loan_installment_payout IS 'Represents an installment that has been paid to the loan account or was supposed to be paid, but failed';
COMMENT ON COLUMN public.loan_installment_payout.loan_installment_info_id IS 'Reference to loan_installment_info table';
COMMENT ON COLUMN public.loan_installment_payout.amount IS 'Total installment amount paid';
COMMENT ON COLUMN public.loan_installment_payout.due_date IS 'Date on which installment needs to be paid';
COMMENT ON COLUMN public.loan_installment_payout.payout_date IS 'Date on which installment was actually paid';
COMMENT ON COLUMN public.loan_installment_payout.details IS '{"proto_type":"preapprovedloan.LoanInstallmentPayoutDetails", "comment":"Additional info like actual EMI amount, penalty etc"}';
COMMENT ON COLUMN public.loan_installment_payout.status IS '{"proto_type":"preapprovedloan.LoanInstallmentPayoutStatus", "comment":"pending/processing/success/failed"}';
CREATE TABLE public.p2p_investor_scheme_ledger (
	investor_id STRING NOT NULL,
	scheme_name STRING NOT NULL,
	effective_date DATE NOT NULL,
	current_value INT8 NULL,
	total_principal INT8 NULL,
	total_invested INT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT p2p_investor_scheme_ledger_pkey PRIMARY KEY (investor_id ASC, effective_date ASC, scheme_name ASC),
	INDEX p2p_investor_scheme_ledger_updated_at_idx (updated_at DESC)
);
COMMENT ON TABLE public.p2p_investor_scheme_ledger IS e'represents the chronological ledger of investor\'s investment details on a scheme level which includes details likes the total amount invested, current value etc. at a particular point in time.';
COMMENT ON COLUMN public.p2p_investor_scheme_ledger.investor_id IS 'foreign key from p2p_investors';
COMMENT ON COLUMN public.p2p_investor_scheme_ledger.scheme_name IS 'foreign key from p2p_investment_schemes';
COMMENT ON COLUMN public.p2p_investor_scheme_ledger.effective_date IS 'date at which the data was retrieved from the vendor';
COMMENT ON COLUMN public.p2p_investor_scheme_ledger.current_value IS 'current value of all the investments in the given scheme and investor (in paise)';
COMMENT ON COLUMN public.p2p_investor_scheme_ledger.total_principal IS 'total principal amount invested in the given scheme and investor (in paise)';
COMMENT ON COLUMN public.p2p_investor_scheme_ledger.total_invested IS 'total amount invested in the given scheme and investor (in paise)';
CREATE TABLE public.upi_number_pi_mapping (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pi_id STRING NOT NULL,
	upi_number STRING NOT NULL,
	state STRING NOT NULL,
	expire_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT upi_number_pi_mapping_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX upi_number_pi_mapping_upi_number_key (upi_number ASC),
	INDEX upi_number_pi_mapping_updated_at_idx (updated_at DESC),
	INDEX upi_number_pi_mapping_pi_id_idx (pi_id ASC),
	FAMILY frequently_updated (state, expire_at, updated_at),
	FAMILY "primary" (id, pi_id, upi_number, created_at, deleted_at)
);
COMMENT ON TABLE public.upi_number_pi_mapping IS 'maintains a mapping between upi number and pi id of the vpa';
COMMENT ON COLUMN public.upi_number_pi_mapping.id IS 'stores the unique random id generated by default and acts as a primary key';
COMMENT ON COLUMN public.upi_number_pi_mapping.pi_id IS 'stores the Pi id of the vpa to which upi number belongs to';
COMMENT ON COLUMN public.upi_number_pi_mapping.upi_number IS 'stores the upi number linked to the vpa';
COMMENT ON COLUMN public.upi_number_pi_mapping.state IS '{"proto_type":"upi.mapper.enums.UpiNumberPiMappingState", "comment":"stores the state of the upi_number linked to the vpa", "ref":"api.upi.mapper.enums.upi_number_pi_mapping_enums.proto"}';
COMMENT ON COLUMN public.upi_number_pi_mapping.expire_at IS 'stores the time till which the upi number can be relinked before it gets recycled after deregistering';
CREATE TABLE public.actor_upi_number_resolution (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	upi_number STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT actor_upi_number_resolution_pkey PRIMARY KEY (actor_id ASC, upi_number ASC),
	INDEX actor_upi_number_resolution_updated_at_idx (updated_at DESC),
	INDEX actor_upi_number_resolution_actor_id_idx (actor_id ASC),
	INDEX actor_upi_number_resolution_upi_number_idx (upi_number ASC),
	FAMILY frequently_updated (updated_at),
	FAMILY "primary" (id, actor_id, upi_number, created_at, deleted_at)
);
COMMENT ON TABLE public.actor_upi_number_resolution IS 'stores what all upi numbers is the actor aware of';
COMMENT ON COLUMN public.actor_upi_number_resolution.id IS 'stores the unique random id generated by default';
COMMENT ON COLUMN public.actor_upi_number_resolution.actor_id IS 'stores the actor id of the actor aware of the upi number';
COMMENT ON COLUMN public.actor_upi_number_resolution.upi_number IS 'stores the upi number of which the actor is aware of';
CREATE TABLE public.account_statement_request_metadata (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	client_req_id STRING NOT NULL,
	statement_document_url BYTES NOT NULL,
	statement_document_url_expiry_time TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT account_statement_request_metadata_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX client_req_id_idx (client_req_id ASC),
	INDEX account_statement_request_metadata_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (statement_document_url, statement_document_url_expiry_time),
	FAMILY "primary" (id, client_req_id, created_at, updated_at, deleted_at)
);
COMMENT ON TABLE public.account_statement_request_metadata IS '{"proto_type": "accounts.statement.AccountStatementRequestMetadata","comment": "table to store account statement request related metadata. Ex> URL."}';
COMMENT ON COLUMN public.account_statement_request_metadata.client_req_id IS '{"proto_type": "accounts.statement.AccountStatementRequestMetadata.ClientReqId", "comment": "client_req_id to be used internal service to get statement."}';
COMMENT ON COLUMN public.account_statement_request_metadata.statement_document_url IS '{"proto_type": "accounts.statement.AccountStatementRequestMetadata.StatementDocumentUrl", "comment": "Url of pdf generated for account statement."}';
COMMENT ON COLUMN public.account_statement_request_metadata.statement_document_url_expiry_time IS '{"proto_type":"accounts.statement.AccountStatementRequestMetadata.StatementDocumentUrlExpiryTime,"comment": "expiry time of account statement pdf url."}';
CREATE TABLE public.pin_code_seed (
	location_token STRING NOT NULL,
	pincode STRING NOT NULL,
	latitude FLOAT8 NOT NULL,
	longitude FLOAT8 NOT NULL,
	"geography" GEOGRAPHY NULL AS (st_setsrid(st_makepoint(longitude, latitude), 4326:::INT8)::GEOGRAPHY) STORED,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (pincode ASC, latitude ASC, longitude ASC),
	UNIQUE INDEX lat_long_uniq_idx (latitude ASC, longitude ASC),
	UNIQUE INDEX location_token_uniq_idx (location_token ASC),
	INVERTED INDEX geog_idx ("geography")
);
COMMENT ON TABLE public.pin_code_seed IS 'seed table to fetch address information around a lat long before hitting vendor for information';
CREATE TABLE public.international_fund_transfer_checks (
	actor_id STRING NOT NULL,
	lrs_consumed_limit JSONB NULL DEFAULT '{}':::JSONB,
	sof_document_name STRING NULL,
	sof_document_updated_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	lrs_limit_updated_at TIMESTAMPTZ NULL,
	is_pre_launch_interested_user BOOL NOT NULL DEFAULT false,
	sof_doc_type STRING NULL,
	last_fy_lrs_consumed_limit JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT international_fund_transfer_checks_pkey PRIMARY KEY (actor_id ASC),
	UNIQUE INDEX international_fund_transfer_checks_actor_id_key (actor_id ASC)
);
COMMENT ON COLUMN public.international_fund_transfer_checks.lrs_consumed_limit IS 'the amount in usd which user has already transferred abroad in current financial year';
COMMENT ON COLUMN public.international_fund_transfer_checks.sof_document_updated_at IS 'date on which the document was created or modified';
CREATE TABLE public.forex_rates (
	id STRING NOT NULL,
	currency_code STRING NOT NULL,
	exchange_rate INT8 NOT NULL,
	max_amount INT8 NOT NULL,
	amount_in_use INT8 NOT NULL,
	state STRING NOT NULL,
	start_at TIMESTAMP NOT NULL,
	end_at TIMESTAMP NOT NULL,
	vendor STRING NOT NULL,
	vendor_id STRING NOT NULL,
	provenance STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	remittance_type STRING NOT NULL DEFAULT 'FOREX_RATE_REMITTANCE_TYPE_OUTWARD':::STRING,
	CONSTRAINT forex_rates_pkey PRIMARY KEY (id ASC),
	INDEX forex_rates_updated_at (updated_at DESC),
	INDEX forex_rates_end_start_at_idx (end_at ASC, start_at ASC),
	INDEX forex_rates_vendor_and_vendor_id_idx (vendor ASC, vendor_id ASC),
	INDEX forex_rates_provenance_state_idx (provenance ASC, state ASC)
);
COMMENT ON TABLE public.forex_rates IS 'contains necessary data to determine the forex rate for a given currency';
COMMENT ON COLUMN public.forex_rates.currency_code IS 'currency code in ISO 4217 format';
COMMENT ON COLUMN public.forex_rates.exchange_rate IS 'the rate at which the currency is converted to the base currency (INR). the units are stored as INT8 multiplied by 1,000,000 to avoid floating point arithmetic';
COMMENT ON COLUMN public.forex_rates.max_amount IS 'max amount that can be used to buy at the given exchange_rate. the units are stored as INT8 multiplied by 1,000,000 to avoid floating point arithmetic';
COMMENT ON COLUMN public.forex_rates.amount_in_use IS 'amount used till now in the foreign currency (USD, EUR, GBP, etc.). the units are stored as INT8 multiplied by 1,000,000 to avoid floating point arithmetic';
COMMENT ON COLUMN public.forex_rates.state IS 'state of the forex rate. can be one of active, inactive';
COMMENT ON COLUMN public.forex_rates.start_at IS 'time from when the forex rate is valid';
COMMENT ON COLUMN public.forex_rates.end_at IS 'time till when the forex rate is valid';
COMMENT ON COLUMN public.forex_rates.vendor IS 'banking partner from whom we are buying the forex';
COMMENT ON COLUMN public.forex_rates.vendor_id IS 'unique id of the forex rate in the banking partner system';
COMMENT ON COLUMN public.forex_rates.provenance IS 'provenance of the forex rate. can be one of manual purchase, vendor api';
COMMENT ON COLUMN public.forex_rates.remittance_type IS 'inward/outward remittance';
CREATE TABLE public.contact_properties (
	hashed_phone_number STRING NOT NULL,
	actor_id STRING NULL,
	onboarding_completed_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	kyc_name JSONB NULL,
	CONSTRAINT contact_properties_pkey PRIMARY KEY (hashed_phone_number ASC),
	UNIQUE INDEX contact_properties_actor_id_unq_idx (actor_id ASC),
	INDEX contact_properties_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.contact_properties IS 'This table stores all the properties related to a hashed contact. This table works to supplement contacts table';
CREATE TABLE public.international_fund_transfer_users_blacklist (
	actor_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT international_fund_transfer_users_blacklist_pkey PRIMARY KEY (actor_id ASC),
	UNIQUE INDEX international_fund_transfer_users_blacklist_actor_id_key (actor_id ASC)
);
COMMENT ON TABLE public.international_fund_transfer_users_blacklist IS 'table containing list of actor ids blacklisted from international fund transfer';
CREATE TABLE public.epan_attempts (
	id STRING NOT NULL,
	client_request_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	state STRING NULL,
	sub_state STRING NULL,
	epan_data JSONB NULL,
	completed_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, client_request_id ASC),
	INDEX epan_data_updated_at_idx (updated_at ASC),
	UNIQUE INDEX epan_data_id_idx (id ASC),
	UNIQUE INDEX epan_data_client_request_id_idx (client_request_id ASC)
);
CREATE TABLE public.closed_accounts_balance_transfer (
	id STRING NOT NULL,
	savings_account_id STRING NOT NULL,
	last_known_balance JSONB NULL,
	last_known_balance_captured_at TIMESTAMPTZ NULL,
	bav_id UUID NULL,
	utr STRING NULL,
	date_of_transfer DATE NULL,
	amount_transferred JSONB NULL,
	transaction_status STRING NULL,
	failure_reason STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	reported_closure_balance JSONB NULL,
	balance_captured_from_statement JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX closed_accounts_balance_transfer_savings_account_id_idx (savings_account_id ASC),
	INDEX closed_accounts_balance_transfer_updated_at_idx (updated_at ASC),
	UNIQUE INDEX closed_accounts_balance_transfer_utr_idx (utr ASC)
);
COMMENT ON COLUMN public.closed_accounts_balance_transfer.savings_account_id IS 'foreign key to savings_accounts table';
COMMENT ON COLUMN public.closed_accounts_balance_transfer.last_known_balance IS 'balance captured at the time of account access revoke';
COMMENT ON COLUMN public.closed_accounts_balance_transfer.bav_id IS 'foreign key to bank_account_verifications table which contains alternate account details';
COMMENT ON COLUMN public.closed_accounts_balance_transfer.utr IS 'unique transaction reference number of the balance transfer transaction';
COMMENT ON COLUMN public.closed_accounts_balance_transfer.transaction_status IS 'enum that describes if transaction was successful/failed';
COMMENT ON COLUMN public.closed_accounts_balance_transfer.reported_closure_balance IS 'balance that was in account when account was closed by vendor as shared by them';
COMMENT ON COLUMN public.closed_accounts_balance_transfer.balance_captured_from_statement IS '{"proto_type":"moneyPkg.Money","comment": "balance that was in account when account was closed captured from account statement"}';
CREATE TABLE public.credit_report_derived_attributes (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	credit_report_id STRING NOT NULL,
	attribute_key STRING NULL,
	attribute_value STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT credit_report_derived_attributes_pkey PRIMARY KEY (id ASC),
	INDEX derived_attributes_actor_credit_report_id_attribute_key (actor_id ASC, credit_report_id ASC, attribute_key ASC),
	INDEX derived_attributes_actor_id (actor_id ASC)
);
CREATE TABLE public.waitlist_user_features (
	user_id UUID NOT NULL,
	feature STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (user_id ASC, feature ASC)
);
COMMENT ON COLUMN public.waitlist_user_features.feature IS '{proto_type:waitlist.WaitlistFeature}';
CREATE TABLE public.probable_known_merchants (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	merchant_id UUID NOT NULL,
	pi_id STRING NOT NULL,
	ds_merchant_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT probable_known_merchants_pkey PRIMARY KEY (id ASC),
	INDEX probable_known_merchant_updated_at (updated_at DESC),
	INDEX probable_known_merchant_merchant_id (merchant_id ASC),
	INDEX probable_known_merchant_pi_id (pi_id ASC),
	INDEX probable_known_merchant_ds_merchant_id (ds_merchant_id ASC)
);
COMMENT ON TABLE public.probable_known_merchants IS 'table to store details regarding the probable merchant info';
COMMENT ON COLUMN public.probable_known_merchants.merchant_id IS '{"proto_type":"probableKnownMerchant.MerchantId", "comment": "will store merchant_id, Primary key of the merchant model", "ref": "api.merchant.ProbableKnownMerchant.proto"}';
COMMENT ON COLUMN public.probable_known_merchants.pi_id IS '{"proto_type":"probableKnownMerchant.PiId", "comment": "will store pi_id, Primary key of the paymentInstrument model", "ref": "api.merchant.ProbableKnownMerchant.proto"}';
COMMENT ON COLUMN public.probable_known_merchants.ds_merchant_id IS '{"proto_type":"probableKnownMerchant.DsMerchantId", "comment": "will store ds_merchant_id", "ref": "api.merchant.ProbableKnownMerchant.proto"}';
CREATE TABLE public.investment_risk_survey (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	survey_data JSONB NOT NULL DEFAULT '{}':::JSONB,
	survey_status STRING NOT NULL DEFAULT 'SURVEY_STATUS_UNSPECIFIED':::STRING,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT investment_risk_survey_pkey PRIMARY KEY (id ASC),
	INDEX investment_risk_survey_updated_at_idx (updated_at DESC),
	INDEX investment_risk_survey_actor_id_idx (actor_id DESC),
	INDEX investment_risk_survey_survey_status_idx (survey_status ASC),
	UNIQUE INDEX investment_risk_survey_actor_id_deleted_at_unix_idx (actor_id ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.investment_risk_survey IS 'stores the investment risk survey questions and answers for each actor';
COMMENT ON COLUMN public.investment_risk_survey.survey_data IS 'questions and answers collected from the user';
COMMENT ON COLUMN public.investment_risk_survey.survey_status IS 'status of the survey data collection';
CREATE TABLE public.lrs_checks (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	provenance STRING NOT NULL,
	lrs_consumed_limit JSONB NULL DEFAULT '{}':::JSONB,
	lrs_checks_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT lrs_checks_pkey PRIMARY KEY (id ASC),
	INDEX lrs_checks_actor_id_status_idx (actor_id ASC, lrs_checks_status ASC),
	INDEX lrs_checks_updated_at_idx (updated_at ASC),
	INDEX lrs_checks_status_provenance_idx (lrs_checks_status ASC, provenance ASC)
);
COMMENT ON COLUMN public.lrs_checks.provenance IS 'entry point from where lrs checks is triggered, eg: pre-launch, account opening, buy flow';
COMMENT ON COLUMN public.lrs_checks.lrs_consumed_limit IS 'the amount in usd which user has already transferred abroad in current financial year';
CREATE TABLE public.temp_credit_report_manual_fetches (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	credit_report_data_raw JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT temp_credit_report_manual_fetches_pkey PRIMARY KEY (id ASC)
);
COMMENT ON TABLE public.temp_credit_report_manual_fetches IS 'table to temporarily store credit reports for a user manually fetched via fetch_credit_reports jenkins job';
CREATE TABLE public.ift_file_sequence_generator (
	id UUID NOT NULL,
	day STRING NOT NULL,
	file_type STRING NOT NULL,
	vendor_name STRING NOT NULL,
	sequence_number INT8 NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT ift_file_sequence_generator_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX ift_file_sequence_generator_day_file_type_vendor_name_key (day ASC, file_type ASC, vendor_name ASC),
	INDEX ift_file_sequence_generator_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.ift_file_sequence_generator IS 'table that would help to generate consistent sequential number per vendor, file_type and date for ift files';
COMMENT ON COLUMN public.ift_file_sequence_generator.day IS 'date in YYYY-MM-DD string format';
CREATE TABLE public.dc_forex_txn_refunds (
	id STRING NOT NULL,
	txn_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	total_txn_amount JSONB NOT NULL,
	refund_amount JSONB NULL,
	txn_time TIMESTAMPTZ NOT NULL,
	refund_status STRING NOT NULL,
	refund_process_identifier STRING NULL,
	txn_time_user_tier STRING NOT NULL,
	refund_processing_mode STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	orch_id STRING NULL,
	txn_type STRING NULL,
	refund_txn_id STRING NULL,
	refund_sub_status STRING NULL,
	forex_charges_info JSONB NULL,
	CONSTRAINT dc_forex_txn_refunds_pkey PRIMARY KEY (id ASC),
	INDEX dc_forex_refund_actor_id_idx (actor_id ASC),
	INDEX dc_forex_refund_txn_id_idx (txn_id ASC),
	INDEX dc_forex_refund_txn_time_idx (txn_time ASC, refund_status ASC),
	INDEX dc_forex_txn_refund_orch_id_idx (orch_id ASC),
	INDEX dc_forex_refund_refund_txn_id_idx (refund_txn_id ASC),
	FAMILY frequently_updated (updated_at, refund_status, orch_id, txn_type, refund_txn_id, refund_sub_status, forex_charges_info),
	FAMILY seldom_updated (refund_amount, deleted_at, refund_process_identifier),
	FAMILY "primary" (id, actor_id, txn_id, total_txn_amount, txn_time, txn_time_user_tier, refund_processing_mode, created_at)
);
COMMENT ON TABLE public.dc_forex_txn_refunds IS 'Table to store records of debit card forex transactions refunds based on the user tier';
COMMENT ON COLUMN public.dc_forex_txn_refunds.total_txn_amount IS 'Total amount of the international transactions including full forex charges';
COMMENT ON COLUMN public.dc_forex_txn_refunds.refund_status IS 'Status of the refund which can go from CREATED to APPROVED/REJECTED and finally PROCESSED if approved';
COMMENT ON COLUMN public.dc_forex_txn_refunds.refund_process_identifier IS '{"proto_type":"card.forex_txn_refunds.proto", "comment": "Identifier to check the status of refund processed"}';
COMMENT ON COLUMN public.dc_forex_txn_refunds.txn_time_user_tier IS 'Tier of the user at the time of the original transaction which will be taken into account for refund calculation';
COMMENT ON COLUMN public.dc_forex_txn_refunds.refund_processing_mode IS 'Manner in which the refund will be processed i.e whether it will be in real time or asynchronous';
CREATE TABLE public.epan_attempt_events (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	event_name STRING NOT NULL,
	client_req_id STRING NOT NULL,
	event_source STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT "primary" PRIMARY KEY (client_req_id ASC, id ASC),
	INDEX epan_attempt_events_updated_at_idx (updated_at ASC)
);
CREATE TABLE public.ift_file_entity_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	fga_attempt_id STRING NOT NULL,
	file_type STRING NOT NULL,
	entity_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT ift_file_entity_mappings_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX ift_file_entity_mappings_unq_non_deleted_entities_per_file_type_idx (file_type ASC, entity_id ASC, deleted_at_unix ASC),
	INDEX ift_file_entity_mappings_fga_idx (fga_attempt_id ASC)
);
COMMENT ON TABLE public.ift_file_entity_mappings IS 'maintains the list of entities that are used to create a file. An entity can be an order, actor, or any combination identifying a unique entry in a file';
COMMENT ON COLUMN public.ift_file_entity_mappings.entity_id IS 'unique id assigned for an entity like order id, actor id, etc.';
COMMENT ON COLUMN public.ift_file_entity_mappings.deleted_at_unix IS 'determines if the mapping is active or not';
CREATE TABLE public.referrals_notification_config_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	trigger STRING NOT NULL DEFAULT 'TRIGGER_UNSPECIFIED':::STRING,
	finite_code_channel STRING NOT NULL DEFAULT 'FINITE_CODE_CHANNEL_UNSPECIFIED':::STRING,
	finite_code_type STRING NOT NULL DEFAULT 'FINITE_CODE_TYPE_UNSPECIFIED':::STRING,
	variant STRING NOT NULL DEFAULT 'VARIANT_UNSPECIFIED':::STRING,
	segment_expression STRING NOT NULL DEFAULT 'DEFAULT_SEGMENT_ID':::STRING,
	content_info JSONB NOT NULL DEFAULT '{}':::JSONB,
	status STRING NOT NULL DEFAULT 'CONFIG_STATUS_UNSPECIFIED':::STRING,
	created_by STRING NOT NULL DEFAULT '':::STRING,
	active_from TIMESTAMPTZ NOT NULL,
	active_till TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT referrals_notification_config_infos_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	INDEX rnci_trigger_variant_status_active_from_active_till_index (trigger ASC, variant ASC, status ASC, active_from ASC, active_till ASC)
);
COMMENT ON TABLE public.referrals_notification_config_infos IS 'stores config details about referral related notifications';
COMMENT ON COLUMN public.referrals_notification_config_infos.trigger IS '{"proto_type": "inappreferral.notification.Trigger", "comment":"trigger for the notification"}';
COMMENT ON COLUMN public.referrals_notification_config_infos.finite_code_channel IS '{"proto_type": "inappreferral.FiniteCodeChannel", "comment":"channel to which the finite code belongs"}';
COMMENT ON COLUMN public.referrals_notification_config_infos.finite_code_type IS '{"proto_type": "inappreferral.FiniteCodeType", "comment":"type of the finite code"}';
COMMENT ON COLUMN public.referrals_notification_config_infos.variant IS '{"proto_type": "inappreferral.notification.Variant", "comment":"variant providing extra dimension for segregation of notifications based on some additional property"}';
COMMENT ON COLUMN public.referrals_notification_config_infos.segment_expression IS 'segment expression which is required to be matched for the particular notification config';
COMMENT ON COLUMN public.referrals_notification_config_infos.content_info IS '{"proto_type": "inappreferral.notification.ContentInfo", "comment":"content for each type of notification to be sent"}';
COMMENT ON COLUMN public.referrals_notification_config_infos.status IS '{"proto_type": "inappreferral.notification.ConfigStatus", "comment":"status of config"}';
COMMENT ON COLUMN public.referrals_notification_config_infos.created_by IS 'creator of the notification config';
COMMENT ON COLUMN public.referrals_notification_config_infos.active_from IS 'notification config active from [inclusive]';
COMMENT ON COLUMN public.referrals_notification_config_infos.active_till IS 'notification config active till (exclusive)';
CREATE TABLE public.account_operational_statuses (
	account_type STRING NOT NULL,
	account_identifier STRING NOT NULL,
	partner_bank STRING NOT NULL,
	account_opened_at DATE NULL,
	account_closed_at DATE NULL,
	operational_status STRING NULL,
	freeze_status STRING NULL,
	total_lien_marking JSONB NULL,
	latest_vendor_response JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	kyc_compliance_info JSONB NULL,
	CONSTRAINT "primary" PRIMARY KEY (account_type ASC, account_identifier ASC),
	INDEX account_operational_status_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (latest_vendor_response, updated_at),
	FAMILY seldom_updated (account_opened_at, account_closed_at, operational_status, freeze_status, total_lien_marking, kyc_compliance_info),
	FAMILY "primary" (account_type, account_identifier, partner_bank, created_at, deleted_at_unix)
);
COMMENT ON TABLE public.account_operational_statuses IS 'stores operational attributes of an account directly controlled by partner bank';
COMMENT ON COLUMN public.account_operational_statuses.account_type IS 'bank account type. ex: savings, FD, SD etc.';
COMMENT ON COLUMN public.account_operational_statuses.account_identifier IS 'id of an account, will have different pattern for different account type';
COMMENT ON COLUMN public.account_operational_statuses.operational_status IS 'tells if account is active, inactive, dormant or closed';
COMMENT ON COLUMN public.account_operational_statuses.freeze_status IS 'tells if what type of freeze is imposed on account if any';
COMMENT ON COLUMN public.account_operational_statuses.total_lien_marking IS 'total_lien_marking is the amount of money that is blocked by the bank on this account, which can not be access by the account right now';
COMMENT ON COLUMN public.account_operational_statuses.latest_vendor_response IS 'vendor response stores the response object of the api/callback that was used to set the operational states';
CREATE TABLE public.transaction_amount_breakup (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	transaction_id STRING NOT NULL,
	amount JSONB NOT NULL,
	breakup_type STRING NOT NULL,
	conversion_rate FLOAT8 NOT NULL,
	conversion_rate_source STRING NOT NULL,
	overhead_charge_percentage FLOAT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT transaction_amount_breakup_pkey PRIMARY KEY (id ASC),
	INDEX transaction_amount_breakup_transaction_id_idx (transaction_id ASC)
);
COMMENT ON TABLE public.transaction_amount_breakup IS 'table to store amount breakup eg. GST charges, conversion charges';
COMMENT ON COLUMN public.transaction_amount_breakup.transaction_id IS 'It corresponds to the transaction to which this amount breakup belongs';
COMMENT ON COLUMN public.transaction_amount_breakup.amount IS '{"proto_type":"types.Money", "comment": "stores the amount of breakup"}';
COMMENT ON COLUMN public.transaction_amount_breakup.breakup_type IS '{"proto_type":"order.payment.AmountBreakupType", "comment":"enum corresponding to the type this amount breakup belongs. Type can be a Markup or  any tax"}';
COMMENT ON COLUMN public.transaction_amount_breakup.conversion_rate IS 'It stores the rate at which the quote currency is converted to the base currency i.e. INR.';
COMMENT ON COLUMN public.transaction_amount_breakup.conversion_rate_source IS '{"proto_type":"order.payment.ConversionRateSourceType", "comment":"enum to denote the source from where the conversion rate was validated"}';
COMMENT ON COLUMN public.transaction_amount_breakup.overhead_charge_percentage IS 'It stores the raw information about the amount breakup, example markup percentage, gst percentage';
CREATE TABLE public.employer_pi_mapping (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	employer_id STRING NOT NULL,
	pi_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	source STRING NULL,
	CONSTRAINT employer_pi_mapping_pkey PRIMARY KEY (id ASC),
	INDEX employer_pi_mapping_updated_at (updated_at ASC),
	UNIQUE INDEX employer_pi_mapping_pi_id_idx (pi_id ASC)
);
COMMENT ON TABLE public.employer_pi_mapping IS 'table to store mapping of employer to details of payment instrument from which salary credits are done by employer';
COMMENT ON COLUMN public.employer_pi_mapping.employer_id IS 'denotes id of the employer in the internal DB';
COMMENT ON COLUMN public.employer_pi_mapping.pi_id IS 'denotes payment instrument id from which salary credits are performed by the employer';
COMMENT ON COLUMN public.employer_pi_mapping.source IS 'source of the mapping';
CREATE TABLE public.savings_account_closure_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	savings_account_id STRING NOT NULL,
	status STRING NOT NULL,
	status_reason STRING NOT NULL,
	entry_point STRING NOT NULL,
	user_feedback JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT savings_account_closure_requests_pkey PRIMARY KEY (id ASC),
	INDEX savings_account_closure_requests_updated_at (updated_at DESC),
	INDEX savings_account_closure_requests_actor_id (actor_id ASC),
	INDEX savings_account_closure_requests_sa_id (savings_account_id ASC),
	INDEX savings_account_closure_requests_status (status ASC),
	FAMILY frequently_updated (status, status_reason, updated_at),
	FAMILY seldom_updated (user_feedback),
	FAMILY "primary" (id, actor_id, savings_account_id, entry_point, created_at, deleted_at)
);
COMMENT ON TABLE public.savings_account_closure_requests IS 'Table used for bookkeeping the savings account closure requests raised by the user';
COMMENT ON COLUMN public.savings_account_closure_requests.savings_account_id IS 'savings account id for which the closure request is being raised for';
COMMENT ON COLUMN public.savings_account_closure_requests.status IS '{"proto_type":"savings.SAClosureRequestStatus", "comment":"stores terminal/non-terminal status of the closure request basis its lifecycle"}';
COMMENT ON COLUMN public.savings_account_closure_requests.status_reason IS '{"proto_type":"savings.SAClosureRequestStatusReason", "comment":"stores a reason when the closure request is updated to a specific status"}';
COMMENT ON COLUMN public.savings_account_closure_requests.entry_point IS '{"proto_type":"savings.SAClosureRequestEntryPoint", "comment":"stores the entry point from where the user entered the SA closure request flow"}';
COMMENT ON COLUMN public.savings_account_closure_requests.user_feedback IS '{"proto_type":"savings.SaClosureRequestUserFeedback", "comment":"stores the feedback received from the user during closure request flow"}';
CREATE TABLE public.sof_details (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	document_type STRING NOT NULL,
	sof_document_url STRING NULL,
	document_info JSONB NULL,
	valid_till TIMESTAMPTZ NULL,
	limit_strategies JSONB NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	sof_state STRING NOT NULL DEFAULT 'SOF_STATE_UNSPECIFIED':::STRING,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX sof_details_updated_at_idx (updated_at DESC),
	INDEX sof_details_actor_id_created_at_idx (actor_id ASC, created_at DESC) WHERE deleted_at IS NULL
);
COMMENT ON COLUMN public.sof_details.sof_state IS 'defines the state of sof e.g. is in processing for strategies compute, processing failed, etc';
CREATE TABLE public.order_vendor_order_map (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	vendor_order_id STRING NOT NULL,
	vendor STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	order_direction STRING NOT NULL DEFAULT 'ORDER_DIRECTION_FORWARD':::STRING,
	entity_ownership STRING NOT NULL DEFAULT 'EPIFI_TECH':::STRING,
	domain_reference_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX order_vendor_order_map_updated_at_idx (updated_at ASC),
	UNIQUE INDEX order_vendor_order_map_order_id_unique_idx (order_id ASC),
	UNIQUE INDEX ovom_vendor_order_id_order_type_partial_unique_idx (vendor_order_id ASC, order_direction ASC) WHERE order_direction = 'ORDER_DIRECTION_FORWARD':::STRING,
	UNIQUE INDEX order_vendor_order_map_domain_reference_id_index (domain_reference_id ASC)
);
COMMENT ON TABLE public.order_vendor_order_map IS 'Stores mapping of internal order id to vendor order id and vendor(in case of PG, eg:Razorpay)';
COMMENT ON COLUMN public.order_vendor_order_map.order_id IS 'Stores order Id of created at our end.';
COMMENT ON COLUMN public.order_vendor_order_map.vendor_order_id IS 'Stores order id created at vendor end(in case of PG, eg:Razorpay).';
COMMENT ON COLUMN public.order_vendor_order_map.vendor IS 'Stores vendor where we have initiate the order.';
COMMENT ON COLUMN public.order_vendor_order_map.order_direction IS 'enum to specify the direction of payment. ORDER_DIRECTION_FORWARD would mean that the payment is a debit w.r.t the user. ORDER_DIRECTION_REVERSE would mean that the payment is a reversal of an already done payment';
COMMENT ON COLUMN public.order_vendor_order_map.entity_ownership IS 'RE to which the order belongs. This will be used to identify the DB from which the data for a respective id has to be queried';
COMMENT ON COLUMN public.order_vendor_order_map.domain_reference_id IS 'Internal domain side identifier for the order. This will exist between domain, pay platform and the vendor. In the context of orders, this will be the same as client request id present in the orders entity ';
CREATE TABLE public.recurring_payments_vendor_details (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	recurring_payment_id STRING NOT NULL,
	vendor_customer_id STRING NULL,
	vendor STRING NOT NULL,
	vendor_payment_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT recurring_payments_vendor_details_pkey PRIMARY KEY (id ASC),
	INDEX recurring_payments_vendor_details_actor_id_idx (actor_id ASC),
	UNIQUE INDEX recurring_payments_vendor_details_recurring_payment_id_idx (recurring_payment_id ASC),
	INDEX recurring_payments_vendor_details_created_at_index (created_at ASC)
);
COMMENT ON TABLE public.recurring_payments_vendor_details IS 'Table to track vendor details for recurring payments that are managed externally, such as those processed through a payment gateway (PG).';
COMMENT ON COLUMN public.recurring_payments_vendor_details.actor_id IS 'Id of the actor associated with the recurring payment.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.recurring_payment_id IS 'Unique Id of the recurring payment.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.vendor_customer_id IS 'Customer Id received from the vendor, e.g., PG customer ID from Razorpay.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.vendor IS 'Vendor or partner that integrates with epifi, e.g., Razorpay.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.vendor_payment_id IS 'payment ID received from the vendor, e.g., from Razorpay.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.created_at IS 'Time of creation of the transaction.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.updated_at IS 'Last updated time of the transaction.';
COMMENT ON COLUMN public.recurring_payments_vendor_details.deleted_at IS 'Date of soft deletion of the transaction entry.';
CREATE TABLE public.pan_update_attempts (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	pan_number STRING NOT NULL,
	status STRING NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX pan_update_attempts_actor_id_deleted_at_unix_idx (actor_id ASC, deleted_at_unix ASC)
);
CREATE TABLE public.asset_histories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	asset_type STRING NOT NULL,
	data JSONB NOT NULL,
	actor_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL,
	deleted_at TIMESTAMPTZ NULL,
	history_date DATE NOT NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX asset_histories_updated_at_index (updated_at DESC),
	UNIQUE INDEX asset_histories_actor_id_asset_type_history_date_unique_idx (actor_id ASC, asset_type ASC, history_date ASC)
);
CREATE TABLE public.add_funds_transaction_callbacks (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	upi_transaction_id STRING NOT NULL,
	created_time TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	customer_account STRING NOT NULL,
	upi_cust_ref_id STRING NOT NULL,
	udir_transaction_id STRING NULL,
	udir_cust_ref_id STRING NULL,
	response_code STRING NULL,
	response_reason STRING NULL,
	response_action STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT add_funds_transaction_callbacks_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX add_funds_transactions_upi_transaction_id_unique (upi_transaction_id ASC),
	INDEX add_funds_transactions_customer_account_idx (customer_account ASC),
	INDEX add_funds_transactions_udir_transaction_id_idx (udir_transaction_id ASC),
	INDEX add_funds_transactions_upi_cust_ref_id_idx (upi_cust_ref_id ASC),
	INDEX add_funds_transactions_udir_cust_ref_id_idx (udir_cust_ref_id ASC)
);
COMMENT ON COLUMN public.add_funds_transaction_callbacks.id IS 'Unique identifier for the transaction';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.upi_transaction_id IS 'UPI Transaction ID associated with the Add Funds transaction';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.created_time IS 'Time of posting the callback';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.customer_account IS 'Customer Account Number';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.upi_cust_ref_id IS 'Original UPI transaction UTR';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.udir_transaction_id IS 'In case of Refund, Refund transaction Id';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.udir_cust_ref_id IS 'In case of Refund, Refund transaction UTR';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.response_code IS 'Response Code for the Transaction';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.response_reason IS 'Response Description for the Transaction';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.response_action IS 'High level response codes depicting the stage of the transaction';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.updated_at IS 'Timestamp when the record was last updated';
COMMENT ON COLUMN public.add_funds_transaction_callbacks.deleted_at IS 'Timestamp when the record was soft deleted, null if not deleted';
ALTER TABLE public.connected_accounts ADD CONSTRAINT fk_consent_handle_ref_aa_consents FOREIGN KEY (consent_handle) REFERENCES public.aa_consents(id);
ALTER TABLE public.account_pis ADD CONSTRAINT fk_pi_id_ref_payment_instruments FOREIGN KEY (pi_id) REFERENCES public.payment_instruments(id);
ALTER TABLE public.savings_accounts ADD CONSTRAINT fk_primary_account_holder_ref_users FOREIGN KEY (primary_account_holder) REFERENCES public.users(id);
ALTER TABLE public.savings_accounts ADD CONSTRAINT fk_secondary_account_holder_ref_users FOREIGN KEY (secondary_account_holder) REFERENCES public.users(id);
ALTER TABLE public.cards ADD CONSTRAINT fk_savings_acc_id_ref_savings FOREIGN KEY (savings_account_id) REFERENCES public.savings_accounts(id);
ALTER TABLE public.card_activation_requests ADD CONSTRAINT fk_card_id FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.card_creation_requests ADD CONSTRAINT fk_card_id FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.card_pins ADD CONSTRAINT fk_card_id FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.kyc_vendor_data ADD CONSTRAINT fk_kyc_attempt_id FOREIGN KEY (kyc_attempt_id) REFERENCES public.kyc_attempts(attempt_id);
ALTER TABLE public.order_attempts ADD CONSTRAINT fk_order_attempts_order_id FOREIGN KEY (order_id) REFERENCES public.orders(id);
ALTER TABLE public.transactions ADD CONSTRAINT transactions_order_ref_id_fkey FOREIGN KEY (order_ref_id) REFERENCES public.orders(id);
ALTER TABLE public.pi_state_logs ADD CONSTRAINT fk_pi_state_log_pi_id FOREIGN KEY (pi_id) REFERENCES public.payment_instruments(id);
ALTER TABLE public.golden_tickets ADD CONSTRAINT golden_tickets_referral_code_fkey FOREIGN KEY (referral_code) REFERENCES public.referrals(referral_code);
ALTER TABLE public.deposit_requests ADD CONSTRAINT deposit_requests_deposit_account_id_fkey FOREIGN KEY (deposit_account_id) REFERENCES public.deposit_accounts(id);
ALTER TABLE public.card_limits ADD CONSTRAINT fk_card_id_ref_cards FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.savings_ledger_recons ADD CONSTRAINT fk_account_recon_savings_accounts_id FOREIGN KEY (savings_account_id) REFERENCES public.savings_accounts(id);
ALTER TABLE public.card_delivery_trackings ADD CONSTRAINT fk_card_id_ref_cards FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.vkyc_attempts ADD CONSTRAINT fk_vkyc_summary_id_ref_vkyc_summaries FOREIGN KEY (vkyc_summary_id) REFERENCES public.vkyc_summaries(id);
ALTER TABLE public.vkyc_karza_call_infos ADD CONSTRAINT fk_vkyc_attempt_id_ref_vkyc_attempts FOREIGN KEY (vkyc_attempt_id) REFERENCES public.vkyc_attempts(id);
ALTER TABLE public.vkyc_karza_customer_infos ADD CONSTRAINT fk_vkyc_summary_id_ref_vkyc_summaries FOREIGN KEY (vkyc_summary_id) REFERENCES public.vkyc_summaries(id);
ALTER TABLE public.card_block_details ADD CONSTRAINT fk_card_id_ref_cards FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.shipping_preference_vendor_requests ADD CONSTRAINT shipping_preference_vendor_requests_preference_id_fkey FOREIGN KEY (preference_id) REFERENCES public.shipping_preferences(id);
ALTER TABLE public.vkyc_karza_call_histories ADD CONSTRAINT fk_call_info_id_ref_vkyc_karza_call_infos FOREIGN KEY (call_info_id) REFERENCES public.vkyc_karza_call_infos(id);
ALTER TABLE public.transaction_notification_map ADD CONSTRAINT fk_transaction_id_ref_transactions FOREIGN KEY (transaction_id) REFERENCES public.transactions(id);
ALTER TABLE public.merchant_pis ADD CONSTRAINT fk_merchant_id_ref_merchants FOREIGN KEY (merchant_id) REFERENCES public.merchants(id);
ALTER TABLE public.card_auth_attempts ADD CONSTRAINT fk_card_auth_attempts_ref_cards FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.disputed_transactions ADD CONSTRAINT fk_transaction_id_ref_transactions FOREIGN KEY (transaction_id) REFERENCES public.transactions(id);
ALTER TABLE public.finite_code_claims ADD CONSTRAINT finite_code_claims_finite_code_id_fkey FOREIGN KEY (finite_code_id) REFERENCES public.finite_codes(id);
ALTER TABLE public.card_tracking_requests ADD CONSTRAINT fk_card_tracking_requests_card_id FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.employment_verification_processes ADD CONSTRAINT fk_employment_data_id_ref_employment_data FOREIGN KEY (employment_data_id) REFERENCES public.employment_data(id);
ALTER TABLE public.employment_verification_checks ADD CONSTRAINT fk_verification_process_id_ref_employment_verification_process FOREIGN KEY (verification_process_id) REFERENCES public.employment_verification_processes(id);
ALTER TABLE public.deposit_account_bonus_payouts ADD CONSTRAINT deposit_account_bonus_payouts_deposit_account_id_fkey FOREIGN KEY (deposit_account_id) REFERENCES public.deposit_accounts(id);
ALTER TABLE public.card_action_attempts ADD CONSTRAINT fk_card_action_attempts_card_id FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.savings_account_aggregations ADD CONSTRAINT fk_savings_account_aggregations_savings_account_id FOREIGN KEY (savings_account_id) REFERENCES public.savings_accounts(id);
ALTER TABLE public.payment_instrument_purge_audits ADD CONSTRAINT payment_instrument_purge_audits_pi_id_fkey FOREIGN KEY (pi_id) REFERENCES public.payment_instruments(id);
ALTER TABLE public.deposit_transactions ADD CONSTRAINT deposit_transactions_deposit_account_id_fkey FOREIGN KEY (deposit_account_id) REFERENCES public.deposit_accounts(id);
ALTER TABLE public.p2p_investment_transactions ADD CONSTRAINT fk_investor_id_ref_investors FOREIGN KEY (investor_id) REFERENCES public.p2p_investors(id);
ALTER TABLE public.p2p_investment_transactions ADD CONSTRAINT fk_scheme_id_ref_schemes FOREIGN KEY (scheme_id) REFERENCES public.p2p_investment_schemes(id);
ALTER TABLE public.physical_card_dispatch_requests ADD CONSTRAINT fk_card_id FOREIGN KEY (card_id) REFERENCES public.cards(id);
ALTER TABLE public.workflow_histories ADD CONSTRAINT workflow_history_wf_req_id_fkey FOREIGN KEY (wf_req_id) REFERENCES public.workflow_requests(id);
ALTER TABLE public.p2p_dynamic_inventory ADD CONSTRAINT fk_investor_id_ref_p2p_investors FOREIGN KEY (investor_id) REFERENCES public.p2p_investors(id);
ALTER TABLE public.p2p_dynamic_inventory ADD CONSTRAINT fk_investment_transaction_id_ref_p2p_investment_transactions FOREIGN KEY (investment_transaction_id) REFERENCES public.p2p_investment_transactions(id);
ALTER TABLE public.goal_investment_instrument_mappings ADD CONSTRAINT fk_goal_id_ref_goals FOREIGN KEY (goal_id) REFERENCES public.goals(id);
ALTER TABLE public.credit_account_histories ADD CONSTRAINT credit_account_history_fk FOREIGN KEY (credit_account_details_id) REFERENCES public.credit_account_details(id);
ALTER TABLE public.caps_applicant_details ADD CONSTRAINT caps_applicant_details_fk FOREIGN KEY (caps_details_id) REFERENCES public.caps_details(id);
ALTER TABLE public.credit_account_holder_details ADD CONSTRAINT credit_account_holder_details_fk FOREIGN KEY (credit_account_details_id) REFERENCES public.credit_account_details(id);
ALTER TABLE public.loan_offer_eligibility_criteria ADD CONSTRAINT fk_loan_eligbility_criteria_ref_offer_id FOREIGN KEY (offer_id) REFERENCES public.loan_offers(id) ON DELETE SET NULL;
ALTER TABLE public.auth_request_stages ADD CONSTRAINT auth_request_stages_fk FOREIGN KEY (auth_request_id) REFERENCES public.auth_requests(id);
ALTER TABLE public.screener_check_attempts ADD CONSTRAINT fk_screener_attempt_id_ref_screener_attempts FOREIGN KEY (screener_attempt_id) REFERENCES public.screener_attempts(id);
ALTER TABLE public.loan_installment_payout ADD CONSTRAINT loan_installment_payout_loan_installment_info_id_fkey FOREIGN KEY (loan_installment_info_id) REFERENCES public.loan_installment_info(id);
ALTER TABLE public.p2p_investor_scheme_ledger ADD CONSTRAINT fk_investor_id_ref_p2p_investors FOREIGN KEY (investor_id) REFERENCES public.p2p_investors(id);
ALTER TABLE public.p2p_investor_scheme_ledger ADD CONSTRAINT fk_scheme_name_ref_p2p_investment_schemes FOREIGN KEY (scheme_name) REFERENCES public.p2p_investment_schemes(name);
ALTER TABLE public.closed_accounts_balance_transfer ADD CONSTRAINT fk_cabt_ref_savings_accounts FOREIGN KEY (savings_account_id) REFERENCES public.savings_accounts(id);
ALTER TABLE public.closed_accounts_balance_transfer ADD CONSTRAINT fk_cabt_ref_bank_account_verifications FOREIGN KEY (bav_id) REFERENCES public.bank_account_verifications(id);
ALTER TABLE public.epan_attempt_events ADD CONSTRAINT fk_epan_attempt_events_client_req_id FOREIGN KEY (client_req_id) REFERENCES public.epan_attempts(client_request_id);
ALTER TABLE public.ift_file_entity_mappings ADD CONSTRAINT fk_fga_attempt_id FOREIGN KEY (fga_attempt_id) REFERENCES public.foreign_remittance_file_generation_attempts(id);
ALTER TABLE public.employer_pi_mapping ADD CONSTRAINT fk_employer_pi_mapping_employer_id FOREIGN KEY (employer_id) REFERENCES public.employer(id);
-- Validate foreign key constraints. These can fail if there was unvalidated data during the SHOW CREATE ALL TABLES
ALTER TABLE public.connected_accounts VALIDATE CONSTRAINT fk_consent_handle_ref_aa_consents;
ALTER TABLE public.account_pis VALIDATE CONSTRAINT fk_pi_id_ref_payment_instruments;
ALTER TABLE public.savings_accounts VALIDATE CONSTRAINT fk_primary_account_holder_ref_users;
ALTER TABLE public.savings_accounts VALIDATE CONSTRAINT fk_secondary_account_holder_ref_users;
ALTER TABLE public.cards VALIDATE CONSTRAINT fk_savings_acc_id_ref_savings;
ALTER TABLE public.card_activation_requests VALIDATE CONSTRAINT fk_card_id;
ALTER TABLE public.card_creation_requests VALIDATE CONSTRAINT fk_card_id;
ALTER TABLE public.card_pins VALIDATE CONSTRAINT fk_card_id;
ALTER TABLE public.kyc_vendor_data VALIDATE CONSTRAINT fk_kyc_attempt_id;
ALTER TABLE public.order_attempts VALIDATE CONSTRAINT fk_order_attempts_order_id;
ALTER TABLE public.transactions VALIDATE CONSTRAINT transactions_order_ref_id_fkey;
ALTER TABLE public.pi_state_logs VALIDATE CONSTRAINT fk_pi_state_log_pi_id;
ALTER TABLE public.golden_tickets VALIDATE CONSTRAINT golden_tickets_referral_code_fkey;
ALTER TABLE public.deposit_requests VALIDATE CONSTRAINT deposit_requests_deposit_account_id_fkey;
ALTER TABLE public.card_limits VALIDATE CONSTRAINT fk_card_id_ref_cards;
ALTER TABLE public.savings_ledger_recons VALIDATE CONSTRAINT fk_account_recon_savings_accounts_id;
ALTER TABLE public.card_delivery_trackings VALIDATE CONSTRAINT fk_card_id_ref_cards;
ALTER TABLE public.vkyc_attempts VALIDATE CONSTRAINT fk_vkyc_summary_id_ref_vkyc_summaries;
ALTER TABLE public.vkyc_karza_call_infos VALIDATE CONSTRAINT fk_vkyc_attempt_id_ref_vkyc_attempts;
ALTER TABLE public.vkyc_karza_customer_infos VALIDATE CONSTRAINT fk_vkyc_summary_id_ref_vkyc_summaries;
ALTER TABLE public.card_block_details VALIDATE CONSTRAINT fk_card_id_ref_cards;
ALTER TABLE public.shipping_preference_vendor_requests VALIDATE CONSTRAINT shipping_preference_vendor_requests_preference_id_fkey;
ALTER TABLE public.vkyc_karza_call_histories VALIDATE CONSTRAINT fk_call_info_id_ref_vkyc_karza_call_infos;
ALTER TABLE public.transaction_notification_map VALIDATE CONSTRAINT fk_transaction_id_ref_transactions;
ALTER TABLE public.merchant_pis VALIDATE CONSTRAINT fk_merchant_id_ref_merchants;
ALTER TABLE public.card_auth_attempts VALIDATE CONSTRAINT fk_card_auth_attempts_ref_cards;
ALTER TABLE public.disputed_transactions VALIDATE CONSTRAINT fk_transaction_id_ref_transactions;
ALTER TABLE public.finite_code_claims VALIDATE CONSTRAINT finite_code_claims_finite_code_id_fkey;
ALTER TABLE public.card_tracking_requests VALIDATE CONSTRAINT fk_card_tracking_requests_card_id;
ALTER TABLE public.employment_verification_processes VALIDATE CONSTRAINT fk_employment_data_id_ref_employment_data;
ALTER TABLE public.employment_verification_checks VALIDATE CONSTRAINT fk_verification_process_id_ref_employment_verification_process;
ALTER TABLE public.deposit_account_bonus_payouts VALIDATE CONSTRAINT deposit_account_bonus_payouts_deposit_account_id_fkey;
ALTER TABLE public.card_action_attempts VALIDATE CONSTRAINT fk_card_action_attempts_card_id;
ALTER TABLE public.savings_account_aggregations VALIDATE CONSTRAINT fk_savings_account_aggregations_savings_account_id;
ALTER TABLE public.payment_instrument_purge_audits VALIDATE CONSTRAINT payment_instrument_purge_audits_pi_id_fkey;
ALTER TABLE public.deposit_transactions VALIDATE CONSTRAINT deposit_transactions_deposit_account_id_fkey;
ALTER TABLE public.p2p_investment_transactions VALIDATE CONSTRAINT fk_investor_id_ref_investors;
ALTER TABLE public.p2p_investment_transactions VALIDATE CONSTRAINT fk_scheme_id_ref_schemes;
ALTER TABLE public.physical_card_dispatch_requests VALIDATE CONSTRAINT fk_card_id;
ALTER TABLE public.workflow_histories VALIDATE CONSTRAINT workflow_history_wf_req_id_fkey;
ALTER TABLE public.p2p_dynamic_inventory VALIDATE CONSTRAINT fk_investor_id_ref_p2p_investors;
ALTER TABLE public.p2p_dynamic_inventory VALIDATE CONSTRAINT fk_investment_transaction_id_ref_p2p_investment_transactions;
ALTER TABLE public.goal_investment_instrument_mappings VALIDATE CONSTRAINT fk_goal_id_ref_goals;
ALTER TABLE public.credit_account_histories VALIDATE CONSTRAINT credit_account_history_fk;
ALTER TABLE public.caps_applicant_details VALIDATE CONSTRAINT caps_applicant_details_fk;
ALTER TABLE public.credit_account_holder_details VALIDATE CONSTRAINT credit_account_holder_details_fk;
ALTER TABLE public.loan_offer_eligibility_criteria VALIDATE CONSTRAINT fk_loan_eligbility_criteria_ref_offer_id;
ALTER TABLE public.auth_request_stages VALIDATE CONSTRAINT auth_request_stages_fk;
ALTER TABLE public.screener_check_attempts VALIDATE CONSTRAINT fk_screener_attempt_id_ref_screener_attempts;
ALTER TABLE public.loan_installment_payout VALIDATE CONSTRAINT loan_installment_payout_loan_installment_info_id_fkey;
ALTER TABLE public.p2p_investor_scheme_ledger VALIDATE CONSTRAINT fk_investor_id_ref_p2p_investors;
ALTER TABLE public.p2p_investor_scheme_ledger VALIDATE CONSTRAINT fk_scheme_name_ref_p2p_investment_schemes;
ALTER TABLE public.closed_accounts_balance_transfer VALIDATE CONSTRAINT fk_cabt_ref_savings_accounts;
ALTER TABLE public.closed_accounts_balance_transfer VALIDATE CONSTRAINT fk_cabt_ref_bank_account_verifications;
ALTER TABLE public.epan_attempt_events VALIDATE CONSTRAINT fk_epan_attempt_events_client_req_id;
ALTER TABLE public.ift_file_entity_mappings VALIDATE CONSTRAINT fk_fga_attempt_id;
ALTER TABLE public.employer_pi_mapping VALIDATE CONSTRAINT fk_employer_pi_mapping_employer_id;
