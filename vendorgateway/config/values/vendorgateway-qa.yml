Application:
  Environment: "qa"
  Name: "vendorgateway"
  IsSecureRedis: true
  SyncWrapperTimeout: 30
  VGAuthSvcSyncWrapperTimeout: 20
  IsStatementAPIEnabled: true
  IsListKeysSimulated: true
  CreateDisputeURL: "https://simulator.qa.pointz.in:8080/test/DMP/v1.0.0/createDispute"
  DisputeStatusCheckUrl: "https://simulator.qa.pointz.in:8080/DMP/v1.0.0/disputeStatusCheck"
  SendCorrespondenceUrl: "https://simulator.qa.pointz.in:8080/DMP/v1.0.0/disputeCorrespondence"
  ChannelQuestionnaireUrl: "https://simulator.qa.pointz.in:8080/DMP/v1.0.0/channelQuestionnaire"
  AccountTransactionsUrl: "https://simulator.qa.pointz.in:8080/DMP/v1.0.0/transactions"
  UploadDocumentUrl: "https://simulator.qa.pointz.in:8080/DMP/v1.0.0/disputeDocument"
  CreateCustomerURL: "https://simulator.qa.pointz.in:9091/createCustomerFederal"
  CreateLoanCustomerURL: "https://simulator.qa.pointz.in:8080/createLoanCustomerFederal"
  LoanCustomerCreationStatusURL: "https://simulator.qa.pointz.in:8080/loanCustomerCreationStatusFederal"
  CheckCustomerStatusURL: "https://simulator.qa.pointz.in:8080/checkCustomerStatusFederal"
  CreateAccountURL: "https://simulator.qa.pointz.in:9091/createAccountFederal"
  CheckAccountStatusURL: "https://simulator.qa.pointz.in:8080/checkAccountStatusFederal"
  DedupeCheckURL: "https://simulator.qa.pointz.in:8080/dedupeCheck"
  FetchCustomerDetailsUrl: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetCustomerDetails"
  EnquireVKYCStatusUrl: "https://simulator.qa.pointz.in:9091/openbanking/enquirevkyc"
  EnquireBalanceURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/getBalance"
  CkycSearchURL: "https://simulator.qa.pointz.in:9091/ckyc/search"
  GetKycDataURL: "https://simulator.qa.pointz.in:9091/ckyc/download"
  CreateVirtualIdURL: "https://simulator.qa.pointz.in:8080/createVirtualIdFederal"
  GetTokenURL: "https://simulator.qa.pointz.in:8080/listKeys"
  DeviceRegistrationURL: "https://simulator.qa.pointz.in:9091/registerDevice"
  SetPINURL: "https://simulator.qa.pointz.in:8080/SetCredFederal"
  UPIBalanceEnquiryURL: "https://simulator.qa.pointz.in:8080/BalEnq"
  ReqComplaintURL: "https://simulator.qa.pointz.in:8080/reqComplaint"
  ReqCheckComplaintStatusUrl: "https://simulator.qa.pointz.in:8080/checkComplaintStatus"
  ValidateAddressURL: "https://simulator.qa.pointz.in:8080/ValAddFederal"
  GenerateUpiOtpURL: "https://simulator.qa.pointz.in:8080/generateUpiOtp"
  RespAuthDetailsURL: "https://simulator.qa.pointz.in:8080/RespAuthDetails"
  ReqPayURL: "https://simulator.qa.pointz.in:8080/ReqPay"
  RegisterMobileURL: "https://simulator.qa.pointz.in:8080/registerMobile"
  ListUpiKeyUrl: "https://simulator.qa.pointz.in:8080/ListKeys"
  ListAccountURL: "https://simulator.qa.pointz.in:8080/ListAccount"
  ListAccountProviderURL: "https://simulator.qa.pointz.in:8080/ListAcctProvider"
  ListPspURL: "https://simulator.qa.pointz.in:8080/ListPsp"
  RespTxnConfirmationURL: "https://simulator.qa.pointz.in:8080/RespTxnConfirmationFederal"
  RespValidateAddressURL: "https://simulator.qa.pointz.in:8080/RespValAddFederal"
  ReqCheckTxnStatusURL: "https://simulator.qa.pointz.in:8080/ReqCheckTxnStatusFederal"
  ListVaeURL: "https://simulator.qa.pointz.in:8080/ListVaeFederal"
  ReqMandateURL: "https://simulator.qa.pointz.in:8080/ReqMandate"
  RespAuthMandateURL: "https://simulator.qa.pointz.in:8080/RespAuthMandate"
  RespMandateConfirmationURL: "https://simulator.qa.pointz.in:8080/RespMandateConfirmation"
  RespAuthValCustURL: "https://simulator.qa.pointz.in:8080/RespAuthValCust"
  ReqActivationUrl: "https://simulator.qa.pointz.in:8080/ActivateInternationalPayments"
  GetMapperInfoURL: "https://simulator.qa.pointz.in:8080/GetMapperInfo"
  GetUpiLiteURL: "https://simulator.qa.pointz.in:8080/GetUpiLite"
  SyncUpiLiteInfoURL: "https://simulator.qa.pointz.in:8080/SyncUpiLiteInfo"
  RegMapperURL: "https://simulator.qa.pointz.in:8080/RegMapper"
  ReqValQRUrl: "https://simulator.qa.pointz.in:8080/ValidateInternationalQR"

  PanProfileURL: "https://simulator.qa.pointz.in:9091/karza/panProfile"
  BureauIdUrl: "https://api.bureau.id/transactions"

  # send vkyc data to federal for inhouse vkyc service
  SendAgentDataURL: "https://simulator.qa.pointz.in:9091/vkyc/federal/send-agent-data"
  SendAuditorDataURL: "https://simulator.qa.pointz.in:9091/vkyc/federal/send-auditor-data"

  # EPAN Url
  GetEPANKarzaStatusURL: "https://simulator.qa.pointz.in:9091/karza/epan"
  InhouseGetAndValidateEPANURL: "https://delta-server.qa.pointz.in/verify/epan"

  # ITR
  InhouseVerifyAndGetITRIntimationDetailsURL: "https://simulator.qa.pointz.in:9091/inhouse/verify/itr-intimation"

  #Call back urls for account setup
  CreateCustomerCallBackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/customer/create"
  CreateAccountCallBackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/account/create"

  # Liveness Service
  Veri5CheckLivenessRequestURL: "https://simulator.qa.pointz.in:9091/video-id-kyc/api/1.0/liveness"
  Veri5MatchFaceRequestURL: "https://sandbox.veri5digital.com/video-id-kyc/api/1.0/faceCompare"
  KarzaCheckLivenessRequestURL: "https://simulator.qa.pointz.in:9091/v3/uat/video-liveness"
  KarzaLivenessCallbackURL: "https://vnotificationgw.qa.pointz.in/liveness/karza"
  KarzaMatchFaceRequestURL: "https://simulator.qa.pointz.in:9091/v3/facesimilarity"
  KarzaCheckPassiveLivenessRequestURL: "https://simulator.qa.pointz.in:9091/v3/image-liveness"
  KarzaCheckLivenessStatusURL: "https://simulator.qa.pointz.in:9091/v3/video-liveness-status"
  InhouseCheckLivenessRequestURL: "https://simulator.qa.pointz.in:9091/inhouse-liveness"
  InhouseMatchFaceRequestURL: "https://simulator.qa.pointz.in:9091/inhouse-facematch"
  InhouseMatchFaceRequestURLV2: "https://simulator.qa.pointz.in:9091/inhouse-facematch"
  UseFormMarshalForKarza: false
  UseFormMarshalForKarzaFM: false

  Razorpay:
    BaseUrl: "https://api.razorpay.com"

  # Employment Service
  Employment:
    KarzaPFOTPURL: "https://simulator.qa.pointz.in:9091/v2/epf-get-otp"
    KarzaPFPassbookURL: "https://simulator.qa.pointz.in:9091/v3/epf-get-passbook"
    KarzaEmploymentVerificationURL: "https://simulator.qa.pointz.in:9091/v2/employment-verification-advanced"
    KarzaSearchCompanyNameURL: "https://simulator.qa.pointz.in:9091/v2/employer-search-lite"
    KarzaUANLookupURL: "https://simulator.qa.pointz.in:9091/v2/uan-lookup"
    KarzaEPFAuthURL: "https://simulator.qa.pointz.in:9091/v2/epf-auth"
    KarzaEmployeeNameSearchURL: "https://simulator.qa.pointz.in:9091/v2/employee-search"
    KarzaCompanyMasterLLPDataURL: "https://testapi.karza.in/v2/mca"
    KarzaSearchGSTINBasisPAN: "https://simulator.qa.pointz.in:9091/uat/v1/search"
    KarzaGetForm16QuarterlyURL: "https://testapi.karza.in/v3/tdsq"
    KarzaGetEmployerDetailsByGstinURL: "https://api.karza.in/gst/uat/v2/gst-verification"
    KarzaGetUANFromPan: "https://simulator.qa.pointz.in:9091/v2/uan-by-pan"
    SignzyLoginURL: "https://simulator.qa.pointz.in:9091/v2/login"
    SignzyDomainNameVerificationURL: "https://simulator.qa.pointz.in:9091/v2/domainverifications"
    KarzaFindUanByPan: "https://simulator.qa.pointz.in:9091/v3/pan-uan"

  # CRM Service
  FreshdeskURI:
    Agent: "/agents"
    Ticket: "/tickets"
    FilterTicket: "/search/tickets"
    Solutions: "/solutions"
    Contacts: "/contacts"
    TicketField: "/admin/ticket_fields"
  FreshDeskAccountConfig:
    EPIFI_TECH:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"
    FEDERAL_BANK:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"

  # Freshdesk service
  FreshdeskAgentURL: "https://ficaretesting.freshdesk.com/api/v2/agents"
  FreshdeskTicketURL: "https://ficaretesting.freshdesk.com/api/v2/tickets"
  FreshdeskFilterTicketURL: "https://ficaretesting.freshdesk.com/api/v2/search/tickets"
  FreshdeskSolutionsURL: "https://ficaretesting.freshdesk.com/api/v2/solutions"
  FreshdeskContactsURL: "https://ficaretesting.freshdesk.com/api/v2/contacts"
  FreshdeskTicketFieldURL: "https://ficaretesting.freshdesk.com/api/v2/admin/ticket_fields"
  CxFreshdeskTicketAttachmentsBucketName: "epifi-qa-cx-ticket-attachments"

  #Freshchat service
  FreshchatConversationURL: "https://epifi6.freshchat.com/v2/conversations"
  FreshchatUserURL: "https://epifi6.freshchat.com/v2/users"
  FreshchatAgentURL: "https://epifi6.freshchat.com/v2/agents"


  InhouseNameCheckUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/namepairmatch"
  InhouseEmployerNameMatchUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/companymatch"
  InhouseEmployerNameCategoriserUrl: "http://text-semantics.data-dev.pointz.in/v1/name_categoriser"
  InhouseBreForCCUrl: "https://simulator.qa.pointz.in:9091/inhouseBre"

  DrivingLicenseValidationUrl: "https://simulator.qa.pointz.in:9091/v3/dl"

  VoterIdValidationUrl: "https://simulator.qa.pointz.in:9091/v2/voter"

  BankAccountVerificationUrl: "https://simulator.qa.pointz.in:9091/verify-bank-account"

  CAMS:
    OrderFeedFileURL: "https://simulator.qa.pointz.in:8080/cams/ProcessOrderFeedFile"
    FATCAFileURL: "https://simulator.qa.pointz.in:8080/cams/ProcessFATCAFeedFile"
    ElogFileURL: "https://simulator.qa.pointz.in:8080/cams/ProcessElogFile"
    OrderFeedFileStatusURL: "https://simulator.qa.pointz.in:8080/cams/GetOrderFeedFileStatus"
    OrderFeedFileSyncURL: "https://simulator.qa.pointz.in:8080/cams/ProcessOrderFeedFileSync"
    OrderFeedFileV2SyncURL: "https://simulator.qa.pointz.in:8080/cams/ProcessOrderFeedFileSync"
    S3Bucket: "epifi-qa-mutualfund"
    NFTFileURL: "https://simulator.qa.pointz.in:8080/cams/ProcessNFTFile"
    GetFolioDetailsURL: "https://simulator.qa.pointz.in:8080/cams/GetFolioDetails"

  Tiering:
    AddSchemeChangeURL: "https://simulator.qa.pointz.in:9091/tiering/schemeChangeAdd"
    EnquireSchemeChangeURL: "https://simulator.qa.pointz.in:9091/tiering/schemeChangeEnq"

  SmallCase:
    CreateTransactionURL: "https://simulator.qa.pointz.in:8080/smallcase/CreateTransaction"
    InitiateHoldingsImportURL: "https://simulator.qa.pointz.in:8080/smallcase/InitiateHoldingsImportURL"
    TriggerHoldingsImportFetchURL: "https://simulator.qa.pointz.in:8080/smallcase/TriggerHoldingsImportFetchURL"
    MFAnalyticsURL: "https://mf-api.smallcase.com/gateway/mf/analytics"
    SmallCaseGateway: "fimoney-stag"
  MFCentral:
    GenerateTokenURL: "https://simulator.qa.pointz.in:8080/mfcentral/GenerateToken"
    EncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
    UpdateFolioEmailURL: "https://simulator.qa.pointz.in:8080/mfcentral/api/client/v1/updateEmail"
    UpdateFolioMobileURL: "https://simulator.qa.pointz.in:8080/mfcentral/api/client/v1/updateMobile"
    InvestorConsentUrl: "https://simulator.qa.pointz.in:8080/mfcentral/api/client/v1/investorconsent"
    SubmitCasSummaryUrl: "https://simulator.qa.pointz.in:8080/mfcentral/api/client/v1/submitcassummaryrequest"
    GetCasDocumentUrl: "https://simulator.qa.pointz.in:8080/mfcentral/api/client/v1/getcasdocument"
    GetTransactionStatusUrl: "https://simulator.qa.pointz.in:8080/mfcentral/api/client/v1/getTransactionStatus"

  ## aa smart parser
  InHouseAAParserURL: "https://smart-parser.data-dev.pointz.in/parse"
  InHouseAABulkParserURL: "https://smart-parser.data-dev.pointz.in/bulk_parse"

  #SMS Service
  Exotel:
    URL: "https://%s:%<EMAIL>/v1/Accounts/%s/SMS"
    AccountSid: "epifi2"
    SenderId: "***********"
  Twilio:
    URL: "https://api.twilio.com/2010-04-01/Accounts/%s/Messages"
    SenderId: "***********"
  AclEpifi:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epiqalt"
    SenderId: "FiMony"
  AclFederal:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "fbefiqa"
    SenderId: "Fedfib"
  AclEpifiOtp:
    FallbackURL: "https://dmzotp.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    URL: "https://otp2.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    AppId: "epifiotp"
    SenderId: "FiMony"
  KaleyraFederal:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  KaleyraEpifi:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FiMony"
  KaleyraEpifiNR:
    URL: "https://api.in.kaleyra.io/v1/HXIN1778099997IN/messages"
    SenderId: "FIMONY"
    CallbackProfileId: "IN_27c7a617-6389-4e1f-8263-b590891440c7"
  KaleyraSmsCallbackURL: "https://vnotificationgw.qa.pointz.in/sms/callback/kaleyra/UrlListner/requestListener"
  AclWhatsapp:
    URL: "https://pushuat.aclwhatsapp.com/pull-platform-receiver/wa/messages"
    OptInURL: "http://115.113.127.155:8085/api/v1/addoptinpost"
  KaleyraFederalCreditCard:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  #Loylty Service
  Loylty:
    AuthTokenURL: "https://simulator.qa.pointz.in:8080/offers/loylty/auth"
    GiftCardBookingURL: "https://simulator.qa.pointz.in:8080/offers/loylty/egvbooking"
    CharityBookingURL: "https://simulator.qa.pointz.in:8080/offers/loylty/charitybooking"
    GiftCardProductListURL: "https://simulator.qa.pointz.in:8080/offers/loylty/giftcard/product"
    GiftCardProductDetailURL: ""
    CreateOrderURL: "https://simulator.qa.pointz.in:8080/offers/loylty/create-order"
    ConfirmOrderURL: "https://simulator.qa.pointz.in:8080/offers/loylty/confirm-order/%s"
    GetOrderDetailsURL: "https://simulator.qa.pointz.in:8080/offers/loylty/order-details/%s"

  Qwikcilver:
    GetAuthorizationCodeBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/authorization-code"
    GetAccessTokenBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/access-token"
    CreateOrderBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/create-order"
    GetActivatedCardDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/activated-card-details/%s"
    GetCategoryDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/category-details"
    GetOrderStatusBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/order-status/%s"
    GetProductDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-details/%s"
    GetProductListBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-list/%s"
    AccessTokenValidityDuration: "5s"
    MailOrderDetailsTo: "<EMAIL>"

  MoEngage:
    BaseUrl: "https://api-03.moengage.com/v1"

  Thriwe:
    BaseUrl: "https://staging-india-api-gateway.thriwe.com"

  Riskcovry:
    BaseUrl: "https://api.uat-riskcovry.com/api/partner"

  Onsurity:
    BaseUrl: "https://simulator.qa.pointz.in:8080"

  Karvy:
    KarviAppId: "RIA"
    AgentCode: "INA200015185"
    BranchCode: "R999"
    BucketName: "epifi-qa-mutualfund-karvy"
    FATCAFileURL: "https://simulator.qa.pointz.in:8080/karvy/ProcessFATCAFeedFile"
    OrderFeedFileSyncURL: "https://simulator.qa.pointz.in:8080/karvy/ProcessOrderFeedFileSync"
    NFTFileUploadURL: "https://simulator.qa.pointz.in:8080/karvy/NFTFileUploadURL"
    GetFolioDetailsURL: "https://simulator.qa.pointz.in:8080/karvy/GetFolioDetailsURL"

  #json file path
  PayFundTransferStatusCodeJson: "./mappingJson/fundTransferStatusCodes.json"
  PayUpiStatusCodeJson: "./mappingJson/upiStatusCodes.json"
  PayAckStatusCodeJson: "./mappingJson/ackStatusCode.json"

  DepositAckStatusCodeFilePath: "./mappingJson/depositAckStatusCode.json"
  DepositResponseStatusCodeFilePath: "./mappingJson/depositResponseStatusCodes.json"

  CardResponseStatusCodeFilePath: "./mappingJson/cardResponseStatusCodes.json"
  SIResponseStatusCodeFilePath: "./mappingJson/siResponseStatusCodes.json"

  Federal:
    CheckCustomerStatusForNonResidentURL: "https://simulator.qa.pointz.in:8080/checkCustomerStatusForNonResident"
    CreateCustomerForNonResidentURL: "https://simulator.qa.pointz.in:8080/createCustomerForNonResident"
    CustomerDetailsInsertURL: "https://simulator.qa.pointz.in:8080/upgradeKycLevel"
    PanValidationV2Url: "https://simulator.qa.pointz.in:9091/fedbnk/pan/v2.0.0/validate"
    PayIntraBankURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/intraBank"
    PayNEFTURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/neft"
    PayIMPSURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/imps"
    PayRTGSURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/rtgs"
    PayStatusURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/enquiry"
    PayIntraBankDepositURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/OwnDepositAccFT"
    RemitterDetailsFetchUrl: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/GetRemitterDetails"
    RemitterDetailsV1FetchUrl: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/GetRemitterDetailsV1"
    BeneficiaryNameLookupUrl: "https://simulator.qa.pointz.in:9091/openbanking/account_utility/v1.0.0/BenefiAcctNameLookup"
    GetCsisStatusUrl: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/federal/CSISStatusCheck"

    PayIntraBankCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/payment/federal"
    PayNEFTCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/payment/federal"
    PayIMPSCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/payment/federal"
    PayRTGSCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/payment/federal"

    InquireOrReportGSTCollectionURL: "https://simulator.qa.pointz.in:8080/test/federal/InquireOrReportGSTCollection"
    InquireOrReportGSTCollectionChannelId: "epifi"

    # B2C Payments
    PayB2CIntraBankURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/B2C/federal/intraBank"
    PayB2CIntraBankCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/payment/b2c/federal"
    PayB2CStatusURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/B2C/federal/enquiry"
    PayB2CImpsURL: "https://simulator.qa.pointz.in:9091/openbanking/fundtransfer/B2C/federal/imps"
    PayB2CImpsCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/payment/b2c/federal"

    # Shipping Preference Service
    ShippingAddressUpdateURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/ShipAddrModification"
    ShippingAddressUpdateCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/shipping_preference/federal"

    # Card Service
    DebitCardCreateURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardCreation"
    DebitCardCreateCallbackURL: "https://vnotificationgw.qa.pointz.in/openbanking/card/federal"
    DebitCardActivateURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardActivation"
    DebitCardEnquiryUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardEnqService"
    DebitCardPinSetUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinChangeUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinResetUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinValidationUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardBlockUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardSuspendOnOffUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLocationOnOffUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardECommerceOnOffUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLimitEnquiry: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
    DebitCardUpdateLimit: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/LimitUpdate"
    DebitCardDeliveryTracking: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
    DebitCardCVVEnquiryUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
    DebitCardConsolidatedCardControlUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"
    DebitCardPhysicalDispatchUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking-card/cardDispatch"
    DebitCardPhysicalDispatchCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/cardPhysicalDispatch/federal"
    CheckDebitCardIssuanceFeeStatusUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcationStatus"
    DebitCardCollectIssuanceFeeUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"

    # PAN Service
    PANValidationURL: "https://simulator.qa.pointz.in:8080/panValidation"
    PANAadhaarValidationURL: "https://simulator.qa.pointz.in:9091/panAadhaarValidation"

    EkycNameDobValidationURL: "https://simulator.qa.pointz.in:8080/ekyc/namedob/validation"
    AadharMobileValidationURL: "https://simulator.qa.pointz.in:9091/aadharmobilevalidate"
    ShareDocWithVendorURL: ""

    # UN Name Check Service
    UNNameCheckURL: "https://simulator.qa.pointz.in:8080/UNNameCheck"

    # Device Re-registration / Profile Update API
    DeviceReRegURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/device/re-registration"
    DeviceReRegCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/auth/federal/user-device/re-register"

    # Device De-registration / Deactivate User
    DeviceDeRegURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/user-device/deactivation"

    # Generate OTP
    GenerateOTPURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/auth/generate-otp"

    DeviceReactivationURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/user-device/reactivate"

    # Deposit service
    CreateFDURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/CreateFD"
    CreateSDURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/CreateSD"
    CreateRDURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/CreateRD"
    AutoRenewFdURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/AutoRenewFd"
    CloseDepositAccountURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/ClosingDepositAcc"
    CheckDepositAccountStatusURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/DepositEnq"
    GetDepositAccountDetailURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/GetAccDetails"
    GetPreClosureDetailURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/digitalCredit/v1.0.0/DepositAcctPreclosEnq"
    DepositListAccountURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/GetAccList"
    InterestRateInfoURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/interestRateInfo"
    CalculateInterestDetailsURL: "https://simulator.qa.pointz.in:9091/fedbnk/uat/neobanking/CalculateInterestDetailsURL"
    CreateDepositCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/deposit/federal/create"
    PreCloseDepositCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/deposit/federal/preclose"
    AutoRenewFdCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/deposit/federal/AutoRenewFd"

    # Standing Instruction service
    CreateSIUrl: "https://simulator.qa.pointz.in:9091/standinginstruction/federal/sicreate"
    ExecuteSIUrl: "https://simulator.qa.pointz.in:9091/standinginstruction/federal/siexecute"
    SICallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/payment/federal"
    ModifySIUrl: "https://simulator.qa.pointz.in:9091/standinginstruction/federal/simodify"
    RevokeSIUrl: "https://simulator.qa.pointz.in:9091/standinginstruction/federal/sirevoke"

    # csv file path
    CityCodesCsv: "./mappingCsv/cityCodes.csv"
    StateCodesCsv: "./mappingCsv/stateCodes.csv"
    CountryCodesCsv: "./mappingCsv/countryCodes.csv"

    #Account
    OpeningBalanceURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetAccStatement"
    ClosingBalanceURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetClosingBalance"
    AccountStatementURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetAccStatement"
    AccountStatementByDRApiUrl: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetAccStatementByDrApi"
    EnquireBalanceV1URL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetBalanceV1"
    MiniStatementURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/GetMiniStatement"
    AccountStatusURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/AccountStatusEnquiry"
    ThirdPartyAccountCollectionURL: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/tpAccountCollection"
    UpdateNomineeUrl: "https://simulator.qa.pointz.in:9091/openbanking/accounts/federal/nomineeUpdate"

    # Partner SDK
    GetSessionParamsUrl: "https://simulator.qa.pointz.in:9091/fedbnk/uat/partnersdk/GetSessionParams"

    # Enquiry Service Url
    CustomerCreationEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/CustomerCreationEnquiryStatus"
    AccountCreationEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/AccountCreationEnquiryStatus"
    CardCreationEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/CardCreationEnquiryStatus"
    DeviceReRegistrationDetailsEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/DeviceReRegistrationDetailsEnquiryStatus"
    MailingAddressModifyDetailsEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/MailingAddressModifyDetailsEnquiryStatus"
    ShippingAddressUpdateEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/ShippingAddressUpdateEnquiryStatus"
    DeviceRegistrationDetailsEnquiryStatusURL: "https://simulator.qa.pointz.in:9091/DeviceRegistrationDetailsEnquiryStatus"
    PhysicalCardDispatchDetailsEnquiryStatus: "https://simulator.qa.pointz.in:9091/PhysicalCardDispatchDetailsEnquiryStatus"

    # Chequebook Request and Track URLs
    OrderChequebookUrl: "https://simulator.qa.pointz.in:9091/fedbnk/neobanking/v1.0.0/chequeBookOrder"
    TrackChequebookUrl: "https://simulator.qa.pointz.in:9091/fedbnk/neobanking/v1.0.0/chequeBookTrack"
    IssueDigitalCancelledChequeUrl: "https://simulator.qa.pointz.in:9091/fedbnk/account_utility/v1.0.0/digitalChequeLeafIssuance"

    # Profile Update and enquiry URL
    ProfileUpdateUrl: "https://simulator.qa.pointz.in:9091/fedbnk/account_utility/v1.0.0/updateProfileAtBank"
    ProfileUpdateEnquiryUrl: "https://simulator.qa.pointz.in:9091/fedbnk/account_utility/v1.0.0/profileUpdateStatus"

    # lien service url
    LienUrl: "https://simulator.qa.pointz.in:9091/openbanking/lien/federal"
    TcsCalculationURL: "https://simulator.qa.pointz.in:8080/test/federal"
    TcsCalculationChannelId: "EPI"

    # e-nach service url
    ListEnachUrl: "https://simulator.qa.pointz.in:9091/NACHEnquiry_API/v1/enquiry"

    FetchEnachTransactionsUrl: "https://simulator.qa.pointz.in:9091/digitalCredit/v1.0.0/enachdata"

  LeadSquared:
    CreateOrUpdateLeadUrl: "https://simulator.qa.pointz.in:8080/salaryprogram/leadsquared/CreateOrUpdateLead%s%s"

  Karza:
    GenerateSessionTokenUrl: "https://simulator.qa.pointz.in:8080/v3/get-jwt"
    AddNewCustomerUrl: "https://app.karza.in/test/videokyc/api/v2/customers"
    GetSlotUrl: "https://app.karza.in/test/videokyc/api/v2/get-slot"
    BookSlotUrl: "https://app.karza.in/test/videokyc/api/v2/book-slot"
    SlotAgentsUrl: "https://app.karza.in/test/videokyc/api/v2/slot-agents"
    ReScheduleSlotUrl: "https://app.karza.in/test/videokyc/api/v2/reschedule-customer"
    TriggerCallback: "https://app.karza.in/test/videokyc/api/v2/trigger-callback"
    AgentDashboardUrl: "https://simulator.qa.pointz.in:8080/test/videokyc/api/agent-dashboard"
    AgentDashboardAuthUrl: "https://simulator.qa.pointz.in:8080/test/videokyc/api/agent-dashboard-auth"
    UpdateCustomerV3Url: "https://simulator.qa.pointz.in:8080/test/videokyc/api/v3/customers"
    AddNewCustomerV3Url: "https://simulator.qa.pointz.in:8080/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUrl: "https://simulator.qa.pointz.in:8080/test/videokyc/api/v2/generate-usertoken"
    GenerateWebLinkUrl: "https://simulator.qa.pointz.in:8080/test/videokyc/api/v2/link"
    TransactionStatusEnquiryUrl: "https://simulator.qa.pointz.in:8080/test/videokyc/api/v2/transaction-events"
    EmploymentVerificationAdvancedUrl: "https://simulator.qa.pointz.in:9091/karza/employmentVerificationAdvanced"
    KycOcrUrl: "https://testapi.karza.in/v3/ocr-plus/kyc"
    PassportVerificationURL: "https://testapi.karza.in/v3/passport-verification"

  Roanuz:
    GenerateCricketAccessTokenUrl: "https://simulator.qa.pointz.in:8080/test/rounaz/cricket"
    CricketURL: "https://simulator.qa.pointz.in:8080/test/rounaz/cricket"
    GenerateFootballAccessTokenUrl: "https://simulator.qa.pointz.in:8080/test/rounaz/football/auth/"
    FootballUrl: "https://simulator.qa.pointz.in:8080/test/rounaz/football"
    FootballAppId: "com.epififootball.www"
    FootballDeviceId: "developer"

  IPStack:
    GetLocationDetailFromIpUrl: "https://simulator.qa.pointz.in:8080/test/ipstack"

  CvlKra:
    SoapHost: "https://krapancheck.cvlindia.com"
    PanEnquiryURL: "https://simulator.qa.pointz.in:8080/CVLPanInquiry.svc"
    InsertUpdateKycURL: "https://simulator.qa.pointz.in:8080/CVLPanInquiry.svc"
    # CvlKra sftp config
    Host: "sftp.deploy.pointz.in"
    Port: 22

  NsdlKra:
    PanInquiryURL: "https://simulator.qa.pointz.in:8080/TIN/PanInquiryBackEnd"
    GenerateSignatureURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/nsdl/v1/generateSignature"
    DisableSigning: true
    PerformPanInquiryV4URL: "https://simulator.qa.pointz.in:8080/TIN/PanInquiryBackEnd"

  Manch:
    TransactionsURL: "https://simulator.qa.pointz.in:8080/app/api/transactions"
    DocumentsURL: "https://simulator.qa.pointz.in:8080/app/api/documents"
    OrgId: "TST00180"
    ReturnUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

  Digio:
    TransactionsURL: "https://ext.digio.in:444/v2/client/document"
    ExpiryInDays: 10
    ClientId: "dummy_client_id"
    SecretKey: "dummy_secret_key"

  WealthKarza:
    OcrURL: "https://testapi.karza.in/v3/kycocr"
    MaskAadhaar: true
    HideAadhaar: true
    Confidence: true
    CheckBlur: true
    CheckBlackAndWhite: true
    CheckCutCard: true
    CheckBrightness: true

  Experian:
    CheckCreditReportPresenceURL: "https://simulator.qa.pointz.in:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportURL: "https://simulator.qa.pointz.in:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportForExistingUserURL: "https://simulator.qa.pointz.in:8080/ECV-P2/content/onDemandRefresh.action"
    FetchExtendSubscriptionURL: "https://simulator.qa.pointz.in:8080/ECV-P2/content/consumerConsentReRegistration.action"

  Cibil:
    PingUrl: "https://simulator.qa.pointz.in:9091/cibil/consumer/dtc/ping"
    FulfillOfferUrl: "https://simulator.qa.pointz.in:9091/cibil/consumer/dtc/fulfilloffer"
    GetAuthQuestionsUrl: "https://simulator.qa.pointz.in:9091/cibil/consumer/dtc/GetAuthenticationQuestions"
    VerifyAuthAnswersUrl: "https://simulator.qa.pointz.in:9091/cibil/consumer/dtc/VerifyAuthenticationAnswers"
    GetCustomerAssetsUrl: "https://simulator.qa.pointz.in:9091/cibil/consumer/dtc/GetCustomerAssets"
    GetProductTokenUrl: "https://simulator.qa.pointz.in:9091/cibil/consumer/dtc/GetProductWebToken"
    ProductUrlPrefix: "https://atlasls-in-live.sd.demo.truelink.com/CreditView"

  Shipway:
    BulkUploadShipmentDataUrl: "https://simulator.qa.pointz.in:8080/shipway/BulkPushOrderData"
    GetShipmentDetailsUrl: "https://simulator.qa.pointz.in:9091/shipway/GetOrderShipmentDetails"
    AddOrUpdateWebhookUrl: "https://simulator.qa.pointz.in:9091/shipway/AddOrUpdateWebhook"
    UploadShipmentDataUrl: "https://simulator.qa.pointz.in:9091/shipway/PushOrderData"

  # AA service vendor URLs
  AA:
    BaseURL: "https://simulator.qa.pointz.in:8080"
    PostConsentURL: "/Consent"
    ConsentStatusURL: "/Consent/handle"
    ConsentArtefactURL: "/Consent"
    RequestDataURL: "/FI/request"
    FetchDataURL: "/FI/fetch"
    GetAccountLinkStatusURL: "/Account/link/status"
    GenerateAccessTokenURL: "https://uattokens.sahamati.org.in/auth/realms/sahamati/protocol/openid-connect/token"
    FetchCrEntityDetailURL: "https://uatcr.sahamati.org.in/entityInfo/AA"
    FetchCrEntityDetailURLV2: "https://uatcr.sahamati.org.in/v2/entityInfo/AA"
    ConsentUpdateURL: "/consent/update"
    AccountDeLinkURL: "/account/delink"
    UseSahamatiCrAndToken: false
    OneMoneyCrId: "onemoney-aa"
    FinvuCrId: "<EMAIL>"
    GetAccountLinkStatusBulkURL: "/Account/link/Status"
    GetHeartbeatStatusURL: "/Heartbeat"
    EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
    AAClientApiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.A-VX3lgu6T_r2FWIp2bsDAQK9vll6p4uQC_D5LwXmdo"
    SahamatiClientId: "EPIFIUAT"
    GenerateFinvuJwtTokenURL: "/web/token"
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    Ignosis:
      Url: "https://simulator.qa.pointz.in:8080"

  # Bouncy castle library URLs
  BouncyCastle:
    GenerateKeyPairURL: "https://simulator.qa.pointz.in:8080/ecc/v1/generateKey"
    GetSharedSecretURL: "https://simulator.qa.pointz.in:8080/ecc/v1/getSharedKey"
    DecryptDataURL: "https://simulator.qa.pointz.in:8080/ecc/v1/decrypt"
    CkycEncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/encryptAndSign"
    CkycVerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/ckyc/v1/verifyAndDecrypt"

  # Fennel Vendor URLs
  FennelFeatureStore:
    ExtractFeatureSetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/extract_features"
    ExtractFeatureSetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/query"
    ExtractFeatureSetsURLV3: "https://babel.data-dev.pointz.in/api/v1/query"
    LogDatasetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV3: "https://babel.data-dev.pointz.in/api/v1/log"
    PdScoreURL: "https://loan-default.data-dev.pointz.in/v2/loan_default"
    Simulator:
      Enable: false # turned off in QA by default
      ExtractFeatureSetsURL: "https://simulator.qa.pointz.in:8080/fennel/api/v1/extract_features"
      LogDatasetsURL: "https://simulator.qa.pointz.in:8080/fennel/api/v1/log"
      AllowedWorkflowsForSimulation: [ "acquisition" ]

  # Scienaptic Vendor Config
  Scienaptic:
    GenerateSmsFeaturesURL: "https://simulator.qa.pointz.in:9091/scienaptic/scraper/generate_feature"

  Ckyc:
    SearchURL: "https://simulator.qa.pointz.in:8080/Search/ckycverificationservice/verify"
    ApiVersion: "1.2"
    DownloadURL: "https://simulator.qa.pointz.in:8080/Search/ckycverificationservice/download"
    EnableCryptor: false

  InhouseOCR:
    MaskDocURL: "https://ocular.data-dev.pointz.in/v1/mask_doc"
    ExtractFieldsURL: "https://ocular.data-dev.pointz.in/v1/extract_fields"
    DetectDocumentURL: "https://ocular.data-dev.pointz.in/v1/detect_doc"
    ExtractFieldsURLV2: "https://simulator.qa.pointz.in:9091/inhouse/v1/extract_fields"

  InhousePopularFAQUrl: "http://popular-faqs.data-dev.pointz.in"

  Digilocker:
    GetAuthorizationCodeUrl: "https://simulator.qa.pointz.in:8080/public/oauth2/1/authorize"
    GetAccessTokenUrl: "https://simulator.qa.pointz.in:8080/public/oauth2/1/token"
    ClientId: "EBB0DE86"
    RedirectUri: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
    GetListOfIssuedDocumentsUrl: "https://simulator.qa.pointz.in:8080/public/oauth2/2/files/issued"
    GetRefreshTokenUrl: "https://simulator.qa.pointz.in:8080/public/oauth2/1/token"
    GetFileFromUriUrl: "https://simulator.qa.pointz.in:8080/public/oauth2/1/file/"
    GetAadhaarInXmlUrl: "https://simulator.qa.pointz.in:8080/public/oauth2/3/xml/eaadhaar"
    TokenCacheTtl: 10m

  Liquiloans:
    Host: "https://simulator.qa.pointz.in:9091"
    SupplyIntegrationHost: "https://simulator.qa.pointz.in:9091"
    SftpHost: "sftp.deploy.pointz.in"
    SftpPort: 22
    S3BucketP2PInvestmentLedger: "epifi-qa-p2p-investment-ledger"

  # TODO(@prasoon): update URL once available
  Lending:
    PreApprovedLoan:
      Federal:
        UrlLentra: "https://simulator.qa.pointz.in:8080/lending/federal/v1/enquiry"
        Url: "https://simulator.qa.pointz.in:9091"
        HttpUrl: "https://simulator.qa.pointz.in:8080"
        FetchDetailsUrl: "https://simulator.qa.pointz.in:9091/account_utility/v1.0.0/fetchLoanDetails"
        RespUrl: ""
        # TODO(@kantikumar): update sftp host once available
        SftpHost: ""
        SftpPort: 22
        PlAcntCrnNtbHttpURL: "https://simulator.qa.pointz.in:8080/loan/account/v2.0.0/creation"
        PlAcntCrnEnqNtbHttpURL: "https://simulator.qa.pointz.in:8080/loan/v1.0.0/enquiry"
      Liquiloans:
        Url: "https://simulator.qa.pointz.in:9091"
        # This URL points to HTTP port of simulator as few Liquiloans API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://simulator.qa.pointz.in:8080"
      Idfc:
        Url: "https://simulator.qa.pointz.in:9091"
        # URL to fetch the access token for IDFC APIs
        GetAccessTokenUrl: "https://app.uat-opt.idfcfirstbank.com/platform/oauth/oauth2/token"
        MandatePageUrl: "https://simulator.qa.pointz.in:8080/IDFCEMandate/EMandateB2BPaynimmo.aspx"
        Source: "FIMONEY"
        EnableEncryption: false
        SimulatorHttpURL: "https://simulator.qa.pointz.in:8080"
      Abfl:
        Url: "https://simulator.qa.pointz.in:9091"
        BreUrl: "https://simulator.qa.pointz.in:9091/v2/decisionEngineConfig"
        TxnDetailsUrl: "https://simulator.qa.pointz.in:9091"
        PwaJourneyUrl: "https://simulator.qa.pointz.in:9091/abfl/pwa"
      Lentra:
        Url: "https://ssguat.serviceurl.in/"
      Moneyview:
        BaseUrl: "https://simulator.qa.pointz.in:9091/moneyview"
        # This URL points to HTTP port of simulator as few MV API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "https://simulator.qa.pointz.in:8080/moneyview"
      Setu:
        BaseUrl: "https://simulator.qa.pointz.in:9091"
      Digitap:
        UanAdvancedUrl: "https://simulator.qa.pointz.in:9091/v3/uan_advanced/sync"
      Lenden:
        BaseUrl: "https://simulator.qa.pointz.in:9091/lenden/common/v1/EPF"
        ProductId: "EcoX-Loan-102"
        EnableCryptor: false

    CreditCard:
      M2P:
        RegisterCustomerHost: "https://simulator.qa.pointz.in:9091/"
        M2PHost: "https://simulator.qa.pointz.in:9091/"
        CreditCardRepaymentHost: "https://simulator.qa.pointz.in:9091/"
        M2PFallbackHost: "https://simulator.qa.pointz.in:9091/"
        M2PLMSHost: "https://simulator.qa.pointz.in:9091/"
        M2PPartnerSdkUrl: "https://simulator.qa.pointz.in:9091/gateway"
        M2PSetPinUrl: "https://simulator.qa.pointz.in:9091/"
        EnableEncryption: false
        M2PFederalHost: "https://simulator.qa.pointz.in:9091/"
      Federal:
        Url: "https://simulator.qa.pointz.in:9091/"
        UpdateCreditCardLimitDetailsUnsecuredChannel: "M2P-FDEPIFICR"
        UpdateCreditCardLimitDetailsMassUnsecuredChannel: "M2P-FDEPIFIMASSCR"
    CreditLine:
      Federal:
        Url: "https://simulator.qa.pointz.in:9091/limitFetch"
      M2P:
        Url: "https://simulator.qa.pointz.in:9091"
    Collateral:
      LendingMFCentralConfig:
        HttpUrl:
          AuthToken: "https://simulator.qa.pointz.in:9091/mfcentral/oauth/token"
          EncryptAndSign: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
          DecryptAndVerify: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
          SubmitCasSummary: "https://simulator.qa.pointz.in:9091/mfcentral/submitcassummary"
          InvestorConsent: "https://simulator.qa.pointz.in:9091/mfcentral/investorconsent"
          GetCasDocument: "https://simulator.qa.pointz.in:9091/mfcentral/getcasdocument"
          ValidateLien: "https://simulator.qa.pointz.in:9091/mfcentral/validatelien"
          SubmitLien: "https://simulator.qa.pointz.in:9091/mfcentral/submitlien"
          InvokeRevokeLien: "https://simulator.qa.pointz.in:9091/mfcentral/validateLienInvokeRevoke"
          CheckStatus: "https://simulator.qa.pointz.in:9091/mfcentral/lienCheckStatus"
          GetTransactionStatus: "https://simulator.qa.pointz.in:9091/mfcentral/getTransactionStatus"
    SecuredLoans:
      Url: "https://simulator.qa.pointz.in:8080/fiftyfin"

  Alpaca:
    BrokerApiHost: "https://simulator.qa.pointz.in:8080/test/alpaca"
    BrokerApiVersion: "v1"
    MarketApiHost: "https://data.sandbox.alpaca.markets"
    MarketApiVersion: "v2"
    StreamApiHost: "simulator.qa.pointz.in:8080"
    StreamApiPath: "test/v2/iex"
    StreamApiScheme: "wss"
    ShouldUseSimulatedEnvForWs: true
    OrderEventsApiPath: "/test/alpaca/v1/trade/events"
    AccountEventsApiPath: "/test/alpaca/v1/account/events"
    FundTransferEventsPath: "/test/alpaca/v1/events/transfers/status"
    JournalEventsPath: "/test/alpaca/v1/events/journals/status"
    BrokerEventsApiHost: "simulator.qa.pointz.in:8080"
    BrokerEventsApiScheme: "https"
    ShouldUseSimulatedEnvForEvents: true
    MarketDataBetaAPIPrefix: "v1beta1"

  MorningStar:
    TokenCacheTtl: 12h
    EquityAPIURL: "https://equityapi.morningstar.com"
    ApiUrl: "https://api.morningstar.com"
    ApiAccessTokenExpiryInDays: 90
    MorningStarObsoleteFundAPIUrl: "https://intools.morningstar.com/identifier/api/data"

  FederalInternationalFundTransfer:
    URL: "https://simulator.qa.pointz.in:8080/test/federal"
    CheckLRSEligibilityPrecision: 9


  Esign:
    Leegality:
      Url: "https://simulator.qa.pointz.in:9091/"

  ProfileValidation:
    Federal:
      Url: "https://simulator.qa.pointz.in:9091"

  #GPlace Vendor URLs
  GPlace:
    FindPlaceReqURL: "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    GetPlaceDetailsReqURL: "https://maps.googleapis.com/maps/api/place/details/json"

  GoogleReverseGeocodingUrl: "https://simulator.qa.pointz.in:9091/get-address-coordinate"
  GoogleGeocodingUrl: "https://simulator.qa.pointz.in:9091/get-coordinate-for-address"
  InhouseLocationServiceUrl: "https://geo.data-dev.pointz.in"
  MaxmindIp2CityUrlPrefix: "https://simulator.qa.pointz.in:9091/get-address-ip/"

  BureauPhoneNumberDetailsUrl: "https://api.overwatch.stg.bureau.id/v1/suppliers/phone-network"

  #DronaPay
  DronapayHostURL: "https://riskuat.dronapay.pointz.in/springapi"

  InhouseRiskServiceURL: "https://onboarding-risk-detection.data-dev.pointz.in/v3/onboarding_risk_detection"
  InhouseRiskServiceURLV1: "https://onboarding-risk-detection.data-dev.pointz.in/v1/shadow_onboarding_risk_detection"
  InhouseReonboardingRiskServiceURL: "https://onboarding-risk-detection.data-dev.pointz.in/v1/afu_risk_detection"
  CasePrioritisationModel:
    InHouseUrl: "https://ds-wise-prioritise.data-dev.pointz.in/prioritisation/v0"

  "InhouseMerchantResolutionServiceUrl": "https://merchant.data-dev.pointz.in/resolution"

  Aml:
    Tss:
      Epifi:
        ScreeningUrl: "https://simulator.qa.pointz.in:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "Epifi_tech"
        ParentCompany: "Epifi"
      StockGuardian:
        ScreeningUrl: "https://simulator.qa.pointz.in:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "LMS"
        ParentCompany: "SGIPL"

  IncomeEstimatorConf:
    InhouseIncomeEstimatorURL: "https://simulator.qa.pointz.in:9091/get-income-estimate"

  LocationModel:
    InHouseUrl: "http://onboarding-risk-detection.data-dev.pointz.in/v1/geo_score"
    RiskSeverityValToEnumMapping:
      "LOW": "RISK_SEVERITY_LOW"
      "MEDIUM": "RISK_SEVERITY_MEDIUM"
      "HIGH": "RISK_SEVERITY_HIGH"
      "CRITICAL": "RISK_SEVERITY_CRITICAL"

  EpanConfig:
    FederalConfig:
      SftpConn:
        Host: "sftp.deploy.pointz.in"
        Port: 22

  EnachConfig:
    FederalConfig:
      SftpConn:
        Host: "sftp.deploy.pointz.in"
        Port: 22
      FederalSFTPUploadPath: "/data/"
      FederalSFTPDownloadPath: "/data/"
      FileTypeToSFTPPathMap:
        FILE_TYPE_PRESENTATION_FILE: "/data/"
        FILE_TYPE_PRESENTATION_ACK_FILE: "/data/"
        FILE_TYPE_PRESENTATION_RESPONSE_FILE: "/data/"

  Dreamfolks:
    BaseUrl: "https://simulator.qa.pointz.in:9091/dreamfolks"
    ProgramId: "1000001632"
    ServiceId: "11"

  Visa:
    FindNearbyAtmTotalsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/totalsinquiry"
    FindNearbyAtmsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/atmsinquiry"
    FindGeocodesUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/geocodesinquiry"
    GetEnhancedForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates"
    GetEnhancedMarkupForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates/markup"

  Saven:
    CreditCardBaseUrl: "https://federal-fi.m2pfintech.dev"
    JwtExpiry: "3s"

  PerfiosDigilocker:
    ApiHost: "https://api-in-uat.perfios.com/kyc/api/v1/digilocker"

  Bridgewise:
    ApiHost: "https://rest.bridgewise.com"
    TokenCacheTtl: 12h


  FederalEscalation:
    BaseURL: "https://uatgateway.federalbank.co.in"
    CreateEscalationURL: "fedbnk/uat/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "fedbnk/uat/CRM/v1.0.0/cxsrbulkretrieval"
    S3BucketName: "epifi-qa-cx-ticket-attachments"

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9999

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    #M2P
    M2PSecrets: "qa/vendorgateway/m2p"
    M2PSecuredCardSecrets: "qa/vendorgateway/m2p/secured"
    # TODO(priyansh) : Get this secret added
    M2PMassUnsecuredCardSecrets: "qa/vendorgateway/m2p/mass-unsecured"
    EpifiM2pRsaPrivateKey: "qa/vendorgateway/epifi-m2p-private-key"
    EpifiM2pRsaPublicKey: "qa/vendorgateway/m2p-public-key"

    # In-house BRE
    InHouseBreBearer: "qa/vendorgateway/inhouse-bre-bearer"

    #Federal
    EpifiFederalPgpPrivateKey: "qa/pgp/pgp-epifi-key-fed-api"
    FederalPgpPublicKey: "qa/pgp/pgp-federal-dummy-key-for-sim"
    EpifiFederalPgpPassphrase: "qa/pgp/pgp-epifi-key-passphrase-fed-api"
    EpifiFederalUPIPrivateKey: "qa/vendorgateway/upi-xml-signature"
    EpifiFederalUPIFallbackPrivateKey: "qa/vendorgateway/upi-xml-signature"
    SenderCode: "qa/vg-vn-simulator-vgpci/federal-auth-sender-code"
    ServiceAccessId: "qa/vg-vn-vgpci/federal-auth-service-access-id"
    ServiceAccessCode: "qa/vg-vn-vgpci/federal-auth-service-access-code"
    ClientId: "qa/vg-vgpci/federal-auth-client-id"
    ClientSecretKey: "qa/vg-vgpci/federal-auth-client-secret-key"
    EpifiFederalCardDataPrivateKeyFallBack: "qa/vg-vngw-vgpci/rsa-federal-card-data"
    EpifiFederalCardDataPrivateKey: "qa/vg-vngw-vgpci/rsa-federal-card-data"

    #Closing Balance params
    ClosingBalanceCredentials: "qa/vendorgateway/federal-closing-balance-secrets"

    GetBalanceCredentialsV1: "qa/vendorgateway/federal-get-balance-v1-secrets"

    GetRemitterDetailsCredentials: "qa/vendorgateway/federal-get-remitter-details-secrets"
    GetRemitterDetailsV1Credentials: "qa/vendorgateway/federal-get-remitter-details-secrets-v1"
    GetBeneficiaryNameDetailsCredentials: "qa/vendorgateway/federal-get-beneficiary-name-details-secrets"
    GetCsisStatusCredentials: "qa/vendorgateway/federal-get-csis-status-secrets"

    #FCM
    FCMServiceAccountCredJson: "qa/vendorgateway/fcm-account-credentials"
    #Sendgrid
    SendGridAPIKey: "qa/vendorgateway/sendgrid-api-key"
    #TLS certs
    SimulatorCert: "qa/vg-vgpci/tls-client-cert-for-sim"
    EpiFiFederalClientSslCert: "qa/vg-vgpci/tls-client-cert-for-federal"
    EpiFiFederalClientSslKey: "qa/vg-vgpci/tls-client-priv-key-for-federal"
    #Freshdesk
    FreshdeskApiKey: "qa/vendorgateway/freshdesk-api-key"
    EpifiTechRiskFreshdeskApiKey: "qa/vendorgateway/epifitech-risk-freshdesk-api-key"
    #Ozonetel
    OzonetelApiKey: "qa/vendorgateway/ozonetel-api-key"
    #Freshchat
    FreshchatApiKey: "qa/vendorgateway/freshchat-api-key"
    # Pan Validation
    PanValidationAccessId: "qa/vendorgateway/federal-auth-pan-validation-access-id"
    PanValidationAccessCode: "qa/vendorgateway/federal-auth-pan-validation-access-code"

    #Loylty
    LoyltyClientId: "qa/vendorgateway/loylty-auth-client-id"
    LoyltyClientKey: "qa/vendorgateway/loylty-auth-client-key"
    LoyltyClientSecret: "qa/vendorgateway/loylty-auth-client-secret"
    LoyltyClientEncryptionKey: "qa/vendorgateway/loylty-auth-client-encryption-key"
    LoyltyEGVModuleId: "qa/vendorgateway/loylty-auth-egv-module-id"
    LoyltyCharityModuleId: "qa/vendorgateway/loylty-auth-charity-module-id"
    LoyltyApplicationId: "qa/vendorgateway/loylty-auth-application-id"
    LoyltyProgramId: "qa/vendorgateway/loylty-auth-program-id"

    #Qwikcilver
    QwikcilverSecrets: "qa/vendorgateway/qwikcilver-secrets"

    #Thriwe
    ThriweSecrets: "qa/vendorgateway/thriwe-secrets"

    #Riskcovry
    RiskcovrySecrets: "qa/vendorgateway/riskcovry-secrets"

    #Onsurity Secrets
    OnsuritySecrets: "qa/vendorgateway/onsurity-secrets"

    # UPI API
    UPISenderUserId: "qa/vendorgateway/federal-upi-sender-user-id"
    UPISenderPassword: "qa/vendorgateway/federal-upi-sender-password"
    UPISenderCode: "qa/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfede: "qa/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfede: "qa/vendorgateway/federal-upi-sender-password"
    UPISenderCodefede: "qa/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdIosAddFunds: "qa/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordIosAddFunds: "qa/vendorgateway/federal-upi-sender-password"
    UPISenderCodeIosAddFunds: "qa/vendorgateway/federal-upi-sender-code"
    UPISenderUserIdfifederal: "qa/vendorgateway/federal-upi-sender-user-id"
    UPISenderPasswordfifederal: "qa/vendorgateway/federal-upi-sender-password"
    UPISenderCodefifederal: "qa/vendorgateway/federal-upi-sender-code"

    # SMS API keys
    TwilioAccountSid: "qa/vendorgateway/twilio-account-sid"
    TwilioApiKey: "qa/vendorgateway/twilio-api-key"
    ExotelApiKey: "qa/vendorgateway/exotel-api-key"
    ExotelApiToken: "qa/vendorgateway/exotel-api-token"
    AclEpifiUserId: "qa/vendorgateway/acl-epifi-user-id"
    AclEpifiPassword: "qa/vendorgateway/acl-epifi-password"
    AclFederalUserId: "qa/vendorgateway/acl-federal-user-id"
    AclFederalPassword: "qa/vendorgateway/acl-federal-password"
    AclEpifiOtpUserId: "qa/vendorgateway/acl-epifi-otp-user-id"
    AclEpifiOtpPassword: "qa/vendorgateway/acl-epifi-otp-password"
    KaleyraFederalApiKey: "qa/vendorgateway/kaleyra-federal-api-key"
    KaleyraFederalCreditCardApiKey: "qa/vendorgateway/kaleyra-federal-cc-api-key"
    KaleyraEpifiApiKey: "qa/vendorgateway/kaleyra-epifi-api-key"
    # KaleyraEpifiNRApiKey: "qa/vendorgateway/kaleyra-epifi-nr-api-key" - uncomment when qa key is created.
    AclWhatsappUserId: "qa/vendorgateway/acl-whatsapp-user-id"
    AclWhatsappPassword: "qa/vendorgateway/acl-whatsapp-password"
    WhatsappEnterpriseId: "qa/vendorgateway/whatsapp-enterprise-id"
    WhatsappEnterpriseToken: "qa/vendorgateway/whatsapp-enterprise-token"
    # Video kyc KARZA api key
    KarzaVkycApiKey: "qa/vendorgateway/karza-vkyc-apikey"
    KarzaVkycPriorityApiKey: "qa/vendorgateway/karza-vkyc-priority-apikey"
    KarzaReVkycPriorityApiKey: "qa/vendorgateway/karza-re-vkyc-priority-apikey"
    KarzaSecrets: "qa/vendorgateway/karza"

    # Rounza cricket api's project and api key
    RounazCricketProjectKey: "qa/vendorgateway/roanuz-project-key"
    RounazCricketApiKey: "qa/vendorgateway/roanuz-api-key"

    # Rounaz football api's access and secret key
    RounazFootballAccessKey: "qa/vendorgateway/roanuz-football-access-key"
    RounazFootballSecretKey: "qa/vendorgateway/roanuz-football-secret-key"

    # B2C payments keys
    B2cUserId: "qa/vendorgateway/federal-auth-b2c-payment-user-id"
    B2cPassword: "qa/vendorgateway/federal-auth-b2c-payment-password"
    B2cSenderCodeKey: "qa/vendorgateway/federal-auth-b2c-payment-sender-code"

    # ipstack access key
    IpstackAccessKey: "qa/vendorgateway/ipstack-access-key"

    # Shipway username and password
    ShipwayUsername: "qa/vendorgateway/shipway-username"
    ShipwayPassword: "qa/vendorgateway/shipway-password"

    # client api key for aa
    AaVgSecretsV1: "qa/vendorgateway/aa-sahamati-secrets-v1"
    AaVgVnSecretsV1: "qa/vg-vn/aa-secrets-v1"

    # Manch secure key and template key
    ManchSecureKey: "qa/vendorgateway/manch-secure-key"
    ManchTemplateKey: "qa/vendorgateway/manch-template-key"

    # Experian Credit Report
    ExperianCreditReportPresenceClientName: "qa/vendorgateway/experian-credit-report-presence-client-name"
    ExperianCreditReportFetchClientName: "qa/vendorgateway/experian-credit-report-fetch-client-name"
    ExperianCreditReportForExistingUserClientName: "qa/vendorgateway/experian-credit-report-for-existing-user-client-name"
    ExperianExtendSubscriptionClientName: "qa/vendorgateway/experian-extend-subscription-client-name"
    ExperianVoucherCode: "qa/vendorgateway/experian-credit-report-voucher-code"

    SeonClientApiKey: "qa/vendorgateway/seon-api-key"

    FederalDepositSecrets: "qa/vendorgateway/federal-deposit-secrets"

    CAMSKey: "qa/investment-vendorgateway/cams-key"

    KarvyKey: "qa/investment-vendorgateway/karvy-key"

    SmallCaseKey: "qa/vendorgateway/smallcase-key"

    MFCentralKey: "qa/vendorgateway/mfcentral-key"

    LendingMFCentralSecrets: 'qa/vendorgateway/lamf-secrets'

    #ckyc
    CkycFiCode: "qa/vendorgateway/ckyc-fi-code"

    # cvl secrets
    CvlSftpUser: "qa/vendorgateway/cvl-sftp-user"
    CvlSftpPass: "qa/vendorgateway/cvl-sftp-pass"
    CvlSftpUploadUser: "qa/vendorgateway/cvl-sftp-upload-user"
    CvlSftpUploadPass: "qa/vendorgateway/cvl-sftp-upload-pass"
    CvlKraPassKey: "qa/vendorgateway/cvl-kra-pass-key"
    CvlKraPosCode: "qa/vendorgateway/cvl-kra-pos-code"
    CvlKraUserName: "qa/vendorgateway/cvl-kra-user-name"
    CvlKraPassword: "qa/vendorgateway/cvl-kra-password"
    CvlSftpSshKey: "qa/vendorgateway/cvl-sftp-ssh-key"
    CvlSecrets: "qa/vendorgateway/cvl-secrets"

    #digilocker
    DigilockerClientSecret: "qa/vendorgateway/digilocker-client-secret"

    # TODO(@prasoon): Add secret keys
    # Lending keys
    PreApprovedLoanFederalSecrets: "qa/vendorgateway/lending-preapprovedloans-secrets"
    FederalSftpSshKey: "qa/vendorgateway/federal-sftp-ssh-key"
    PreApprovedLoanSecrets: "qa/vendorgateway/lending-preapprovedloans-secrets"
    FiIdfcPreApprovedLoanPrivateKey: "qa/vendorgateway/fi-idfc-pre-approved-loan-private-key"

    # Leegality
    LeegalitySecret: "qa/vendorgateway/esign-leegality-secrets"

    #Liquiloans
    LiquiloansMid: "qa/vendorgateway/liquiloans-mid"
    LiquiloansKey: "qa/vendorgateway/liquiloans-key"
    LiquiloansSecrets: "qa/vendorgateway/liquiloans-secrets"

    #GPlace api key
    GPlaceApiKey: "qa/vendorgateway/gplace-api-key"

    # karza api keys
    KarzaKey: "qa/vendorgateway/karza-key"
    TartanKey: "qa/vendorgateway/tartan-key"

    VKYCAgentDashboardSecrets: "qa/vendorgateway/vkyc-agent-dash"

    # DronaPay
    DronaPayKey: "qa/vendorgateway/dronapay-key"

    GeolocationKey: "qa/vendorgateway/geolocation-key"

    # Secrets of payu
    PayuToken: "qa/vendorgateway/payu-token"
    PayuApiKey: "qa/vendorgateway/payu-key"

    MaxmindSecrets: "qa/vendorgateway/maxmind-secrets"

    BureauSecrets: "qa/vendorgateway/bureau-secrets"

    SignzySecrets: 'qa/vendorgateway/signzy-secrets'

    AlpacaSecrets: "qa/vendorgateway/alpaca-secrets"

    FederalInternationalFundTransferSecrets: "qa/vendorgateway/federal-internationalfundtransfer-secrets"

    FederalProfileValidationSecrets: 'qa/vendorgateway/hunter-secrets'

    # p2p Investment secrets
    p2pInvestmentLiquiloansSftpUser: "qa/vendorgateway/p2p-investment-liquiloans-sftp-user"
    p2pInvestmentLiquiloansSftpPassword: "qa/vendorgateway/p2p-investment-liquiloans-sftp-pass"

    MorningStarSecrets: "qa/vendorgateway/morningstar-secrets"
    MorningStarAccountSecrets: "qa/vendorgateway/morningstar-account-secrets"

    DepositInterestRateInfoSecrets: "qa/vendorgateway/deposit-interest-rate-info-secrets"

    TssApiToken: "qa/vendorgateway/tss-api-token"
    TSSAPITokenForSG: "qa/vendorgateway/tss-api-token-sg"

    VistaraSecrets: "qa/vendorgateway/vistara-secrets"

    #    SlackSecrets: "qa/vendorgateway/slack-bot-tokens"

    # Fennel Secrets
    FennelFeatureStoreSecrets: "qa/vendorgateway/fennel-secrets"

    DreamfolksSecrets: "qa/vendorgateway/dreamfolks-secrets"

    CommonSftpUser: "qa/vendorgateway-simulator/sftp-upload-user"
    CommonSftpPass: "qa/vendorgateway-simulator/sftp-upload-pass"

    #Lentra secrets
    LentraSecrets: "qa/vendorgateway/lentra-secrets"

    EpifiFederalEpanSftpSecrets: "qa/vendorgateway/epifi-federal-epan-sftp-secrets"

    EpifiFederalEnachSftpSecrets: "qa/vendorgateway/epifi-federal-enach-sftp-secrets"
    # Credgenics auth token
    CredgenicsAuthToken: "qa/vendorgateway/credgenics"

    CredgenicsAuthenticationKeyV2: "qa/vendorgateway/credgenics-v2"

    LeadSquaredSecrets: "qa/vendorgateway/leadsquared-keys"

    LendingFiftyFinLamfSecrets: "qa/vendorgateway/fiftyfin-lamf-secrets"

    KarzaPanProfileKey: "qa/vendorgateway/pan-profile-karza-key"

    PoshvineSecrets: "qa/vendorgateway/poshvine-secrets"

    CibilSecrets: "qa/vendorgateway/cibil"

    PanValidationSecretskey: "qa/vendorgateway/panvalidationsecrets"

    BureauIdSecrets: "qa/vendorgateway/bureauid-secrets"

    NetCoreEpifiSecrets: "qa/vendorgateway/netcore-epifi-secrets"

    VisaSecrets: "qa/vendorgateway/visa-secrets"

    MoEngageSecrets: "qa/vendorgateway/moengage-secrets"

    ScienapticSecrets: "qa/vendorgateway/openresty-scienaptic-sms"

    PerfiosDigilockerSecrets: "qa/vendorgateway/perfios-digilocker-secrets"

    AirtelFedSMSSecrets: "qa/vendorgateway/airtel-fed-sms-secrets"

    BridgewiseSecrets: "qa/vendorgateway/bridgewise-secrets"

    FederalEscalationSecrets: "qa/vendorgateway/federal-io-secrets"
    FederalEscalationClientSslCert: "qa/vendorgateway/federal-io-cert"
    FederalEscalationClientSslKey: "qa/vendorgateway/federal-io-key"

    SavenSecrets: "qa/vendorgateway/saven-cc-secrets"

  Nugget:
    BaseURL: "https://api.nugget.com/prod"
    Username: "<EMAIL>"
    Password: "59f4b22cbab233a46300dd9a972c4294-d15h11qcfvtc73bgssi0"
    ClientID: 18
    AccessTokenEndpoint: "/unified-support/auth/users/getAccessToken"

DisputeSFTP:
  user: ""
  password: ""
  host: ""
  port: 80
  S3BucketName: "epifi-federal-disputes"

Flags:
  TrimDebugMessageFromStatus: false
  TokenValidation: true
  AllowSpecialCharactersInAddress: true
  EnableTransactionEnquiryNewApi: true
  EnableUATForVKYC: true
  EnableFennelClusterV3: true
  EnableInstrumentBillingInterceptor: true
  EnableCibilV2Secrets: false
  UseNewFieldsInCifCreation: false

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/vendorgateway/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 15

TimeoutConfig:
  DefaultTimeout: 30s
  Vendors:
    QWIKCILVER:
      vendorgateway_offers_qwikcilver_qwikcilver_createorder:
        Timeout: 10s

FcmAnalyticsLabel: "qa-push-notification"

AwsSes:
  FiMoneyArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.money"
  FiCareArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.care"
  StockGuardianArn: "arn:aws:ses:ap-south-1:632884248997:identity/stockguardian.in"

# generic downtime handler for vg
# the vendor name is the enum value with config values like IsEnabled, StartTime, EndTime
DowntimeConfig:
  NSDL:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-03-15 15:04:05"
        EndTimestamp: "2022-03-16 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CVLKRA:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CKYC:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2022-04-07 21:00:00"
        EndTimestamp: "2022-04-07 22:00:00"
        Msg: "This is due to a downtime at our vendor partner. This should be up by %s. We will notify you once its up."
  DIGILOCKER:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  MANCH:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-08-26 23:00:00"
        EndTimestamp: "2022-08-27 09:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "qa/pgp/v1/pgp-epifi-fed-api"
  ExternalEntity:
    - Secret: "qa/pgp/v1/federal-simulator"

VideoSdk:
  Secrets:
    Path: "qa/vendorgateway/videosdk"

FederalAPICreds:
  REQUEST_SOURCE_UNSPECIFIED:
    Path: "qa/vg-vgpci/vendor-api-secrets-federal-default"
  REQUEST_SOURCE_LOANS:
    Path: "qa/vg-vgpci/vendor-api-secrets-federal-b2c-loans"

Uqudo:
  AccessTokenURL: "https://auth.uqudo.io/api/oauth/token"
  PublicKeyURL: "https://id.uqudo.io/api/.well-known/jwks.json"
  FetchImageURL: "https://id.uqudo.io/api/v1/info/img"
  EmiratesIdOcrUrl: "https://id.uqudo.io/api/v1/scan"
  Secrets:
    Path: "qa/vendorgateway/uqudo"
