Application:
  Nps:
    ApiHost: "https://npscra.nsdl.co.in/download"
  EnachTransactionStatusCodeJson: "./mappingJson/enachTransactionStatusCodes.json"

  # UPI config
  PspHandleToUpiOrgIdMap:
    default: "159049"
    fede: "400021"
    fbl: "159049"
    fedepsp: "159049"
    fifederal: "180031"
  SenseforthEventURL: "https://chatbotapi.uat.pointz.in/getmessages"

  #Ozonetel service
  OzonetelManualDialUrl: "https://in1-ccaas-api.ozonetel.com/CAServices/AgentManualDial.php"
  OzonetelUserName: "epifi_ccaas"
  OzonetelCampaignName: "Progressive_918047485490"

  PanProfileURL: "https://testapi.karza.in/v3/pan-profile"

  Seon:
    GetUserSocialMediaInformationUrl: "https://api.seon.io/SeonRestService/email-api/v2.1"

  Karvy:
    XMLTagReplaceFlag: true # This is for a bug at karvy side where xml tags are getting replaced by random symbols, this can be set to false once karvy fixes the bug.

  SmallCase:
    EnableNotFoundInInitHoldingsImport: true

  Karza:
    UpdateCustomerV3UATUrl: "https://app.karza.in/test/videokyc/api/v3/customers"
    AddNewCustomerV3UATUrl: "https://app.karza.in/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUATUrl: "https://app.karza.in/test/videokyc/api/v2/generate-usertoken"
    TransactionStatusEnquiryUATUrl: "https://app.karza.in/test/videokyc/api/v2/transaction-events"
    GenerateWebLinkUATUrl: "https://app.karza.in/test/videokyc/api/v2/link"
    GenerateSessionTokenUATUrl: "https://testapi.karza.in/v3/get-jwt"

  # Employment Service
  Employment:
    TartanUpdateEmployeeBankDetailsURL: "https://tnode.tartanhq.com/api/update_bank/"
    TartanGetHrmsDetailsURL: "https://tnode.tartanhq.com/api/hrms_list/"
    TartanGetEmployerDetailsURL: "https://tnode.tartanhq.com/api/org_details/"

  PAYUAffluenceURL: "https://mars-dev.payu.in/api/v3/daas/"

  AA:
    GetBulkConsentRequestURL: "/Consent/status/bulk"
    GenerateFinvuJwtTokenURL: "/web/token"
    ConsentArtefactV2URL: "/Consent/fetch"
    AaSecretsVersionToUse: "V1"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    IsOnemoneyV2Enabled: false
    IsFinvuV2Enabled: false

  Tiering:
    SchemeChangeURL: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/schemeChangeAddEnq"

  Alpaca:
    StreamApiHost: "stream.data.sandbox.alpaca.markets"
    StreamApiPath: "v2/iex"
    StreamApiScheme: "wss"
    ShouldUseSimulatedEnvForWs: false
    FundingConfig:
      FundTransferMode: "wire"

  Credgenics:
    BaseUrl: "https://apiprod.credgenics.com/recovery"

  Federal:
    CheckDebitCardIssuanceFeeStatusUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"
    DebitCardCollectIssuanceFeeUrl: "https://uatgateway.federalbank.co.in/fedbnk/uat/digitalCredit/v1.0.0/GSTbifurcation"

  Scienaptic:
    WhitelistedFeatures:
      totIncEver: true
      totIncL1M: true
      totIncL3M: true
      totIncL6M: true
      totIncL12M: true
      Income_current: true
      Income1M: true
      Income2M: true
      Income3M: true
      Income4M: true
      Income5M: true
      Income6M: true
      Income7M: true
      Income8M: true
      Income9M: true
      Income10M: true
      Income11M: true
      Income12M: true
      totSpendEver: true
      totSpendL1M: true
      totSpendL3M: true
      totSpendL6M: true
      totSpendL12M: true
      Spend_current: true
      Spend1M: true
      Spend2M: true
      Spend3M: true
      Spend4M: true
      Spend5M: true
      Spend6M: true
      Spend7M: true
      Spend8M: true
      Spend9M: true
      Spend10M: true
      Spend11M: true
      Spend12M: true
      totCashwithdrawlEver: true
      totCashwithdrawl_current: true
      totCashwithdrawlL1M: true
      totCashwithdrawlL3M: true
      totCashwithdrawlL6M: true
      totCashWithdrawlL12M: true
      totTimesCashwithdrawlever: true
      totTimesCashwithdrawl_current: true
      totTimesCashwithdrawlL1M: true
      totTimesCashwithdrawlL3M: true
      totTimesCashwithdrawlL6M: true
      totTimesCashwithdrawlL12M: true
      maxCashwithdrawlEver: true
      maxCashwithdrawl_current: true
      maxCashwithdrawlL1M: true
      maxCashwithdrawlL3M: true
      maxCashwithdrawlL6M: true
      maxCashwithdrawlL12M: true
      totOnlineTxnEver: true
      totOnlineTxn_current: true
      totOnlineTxnL1M: true
      totOnlineTxnL3M: true
      totOnlineTxnL6M: true
      totOnlineTxnL12M: true
      totTimesonlineTxnEver: true
      totTimesonlineTxn_current: true
      totTimesonlineTxnL1M: true
      TottimesonlineTxnL3M: true
      TottimesonlineTxnL6M: true
      totTimesonlineTxnL12M: true
      MaxonlineTxnEver: true
      MaxonlineTxn_current: true
      MaxonlineTxnL1M: true
      maxOnlinetxnL3M: true
      maxOnlinetxnL6M: true
      maxOnlinetxnL12M: true
      totOnlinedebitEver: true
      totOnlinedebit_current: true
      totOnlinedebitL1M: true
      totonlinedebitL3M: true
      totOnlinedebitL6M: true
      totOnlinedebitL12M: true
      totTimesonlinedebitever: true
      totTimesonlinedebit_current: true
      TotTimesonlinedebitL1M: true
      totTimesonlinedebitL3M: true
      TotTimesonlinedebitL6M: true
      totTimesonlinedebitL12M: true
      maxOnlinedebitever: true
      maxOnlinedebit_current: true
      maxOnlinedebitL1M: true
      maxOnlinedebitL3M: true
      maxOnlinedebitL6M: true
      maxOnlinedebitL12M: true
      totOnlinecreditEver: true
      totOnlinecredit_current: true
      totOnlinecreditL1M: true
      totOnlinecreditL3M: true
      totOnlinecreditL6M: true
      totonlinecreditL12M: true
      totTimesonlinecreditever: true
      totTimesonlinecredit_current: true
      totTimesonlinecreditL1M: true
      totTimesonlinecreditL3M: true
      totTimesonlinecreditL6M: true
      totTimesonlinecreditL12M: true
      maxOnlinecreditever: true
      maxOnlinecredit_current: true
      maxOnlinecreditL1M: true
      maxOnlinecreditL3M: true
      maxOnlinecreditL6M: true
      maxOnlinecreditL12M: true
      totCardTxnEver: true
      totCardTxn_current: true
      totCardTxnL1M: true
      totCardTxnL3M: true
      totCardTxnL6M: true
      totCardTxnL12M: true
      totTimesCardTxnEver: true
      totTimesCardTxn_current: true
      totTimesCardTxnL1M: true
      totTimesCardTxnL3M: true
      totTimesCardTxnL6M: true
      totTimescardtxnL12M: true
      maxCardTxnEver: true
      maxCardTxn_current: true
      maxCardTxnL1M: true
      maxCardTxnL3M: true
      maxCardTxnL6M: true
      maxCardTxnL12M: true
      totCardDebitEver: true
      totCardDebit_current: true
      totCardDebitL1M: true
      totCardDebitL3M: true
      totCardDebitL6M: true
      totCardDebitL12M: true
      totTimescardDebitEver: true
      totTimescardDebit_current: true
      totTimesCardDebitL1M: true
      totTimescardDebitL3M: true
      TotTimesCardDebitL6M: true
      totTimescarddebitL12M: true
      maxCardDebitEver: true
      maxCardDebit_current: true
      maxCardDebitL1M: true
      maxCardDebitL3M: true
      maxCardDebitL6M: true
      maxCardDebitL12M: true
      totCardCreditEver: true
      totCardCredit_current: true
      totCardCreditL1M: true
      totCardCreditL3M: true
      totCardCreditL6M: true
      totCardCreditL12M: true
      totTimescardcreditever: true
      totTimescardcredit_current: true
      totTimesCardCreditL1M: true
      totTimescardcreditL3M: true
      totTimesCardCreditL6M: true
      totTimescardcreditL12M: true
      maxCardCreditEver: true
      maxCardCredit_current: true
      maxCardCreditL1M: true
      maxCardCreditL3M: true
      maxCardCreditL6M: true
      maxCardCreditL12M: true
      avgSalary: true
      salary_current: true
      salary1M: true
      salary2M: true
      salary3M: true
      salary4M: true
      salary5M: true
      salary6M: true
      salary7M: true
      salary8M: true
      salary9M: true
      salary10M: true
      salary11M: true
      salary12M: true
      maxSpend: true
      totWalletdebitEver: true
      totWalletdebit_current: true
      totWalletdebitL1M: true
      totWalletdebitL3M: true
      totWalletdebitL6M: true
      totWalletdebitL12M: true
      totTimeswalletdebitever: true
      totTimeswalletdebit_current: true
      totTimeswalletdebitL1M: true
      totTimeswalletdebitL3M: true
      totTimeswalletdebitL6M: true
      totTimeswalletdebitL12M: true
      maxWalletdebitever: true
      maxWalletdebit_current: true
      maxWalletdebitL1M: true
      maxWalletdebitL3M: true
      maxWalletdebitL6M: true
      maxWalletdebitL12M: true
      totWalletCreditEver: true
      totWalletCredit_current: true
      totWalletcreditL1M: true
      totWalletcreditL3M: true
      totWalletcreditL6M: true
      totWalletcreditL12M: true
      totTimeswalletcreditEver: true
      totTimeswalletcredit_current: true
      totTimeswalletcreditL1M: true
      totTimeswalletcreditL3M: true
      totTimeswalletcreditL6M: true
      totTimeswalletcreditL12M: true
      maxWalletcreditever: true
      maxWalletcredit_current: true
      maxwalletcreditL1M: true
      maxWalletcreditL3M: true
      maxWalletcreditL6M: true
      maxWalletcreditL12M: true
      countSMSEMI: true
      activeEMIFlag: true
      totInvestmentDebitEver: true
      totInvestmentDebit_current: true
      totInvestmentdebitL1M: true
      totInvestmentdebitL3M: true
      totInvestmentdebitL6M: true
      totInvestmentdebitL12M: true
      totTimesinvestmentdebitever: true
      totTimesinvestmentdebit_current: true
      totTimesinvestmentdebitL1M: true
      totTimesinvestmentdebitL3M: true
      totTimesinvestmentdebitL6M: true
      totTimesinvestmentdebitL12M: true
      maxInvestmentdebitever: true
      maxInvestmentdebit_current: true
      maxInvestmentdebitL1M: true
      maxInvestmentdebitL3M: true
      maxInvestmentdebitL6M: true
      maxInvestmentdebitL12M: true
      totInvestmentcreditEver: true
      totInvestmentcredit_current: true
      totInvestmentcreditL1M: true
      totInvestmentcreditL3M: true
      totInvestmentcreditL6M: true
      totInvestmentcreditL12M: true
      totTimesinvestmentcreditever: true
      totTimesinvestmentcredit_current: true
      TotTimesinvestmentcreditL1M: true
      totTimesinvestmentcreditL3M: true
      TotTimesinvestmentcreditL6M: true
      totTimesinvestmentcreditL12M: true
      MaxInvestmentcreditever: true
      MaxInvestmentcredit_current: true
      maxInvestmentcreditL1M: true
      MaxInvestmentcreditL3M: true
      maxInvestmentcreditL6M: true
      MaxInvestmentcreditL12M: true
      timesAccountsBlocked: true
      timesAccountsBlocked_current: true
      timesAccountsBlockedL1M: true
      timesAccountsBlockedL3M: true
      timesAccountsBlockedL6M: true
      timesAccountsBlockedL12M: true
      timesInsuffBalSavAcct: true
      timesInsuffBalSavAcct_current: true
      timesInsuffBalSavAcctL1M: true
      timesInsuffBalSavAcctL3M: true
      timesInsuffBalSavAcctL6M: true
      timesInsuffBalSavAcctL12M: true
      EPFO_current: true
      EPFO_1M: true
      EPFO_2M: true
      EPFO_3M: true
      EPFO_4M: true
      EPFO_5M: true
      EPFO_6M: true
      timesOverdueLoanPmnt: true
      timesOverdueLoanPmnt_current: true
      timesOverdueLoanPmntL1M: true
      timesOverdueLoanPmntL3M: true
      timesOverdueLoanPmntL6M: true
      timesOverdueLoanPmntL12M: true
      timesCCDPmntMiss: true
      timesCCDPmntMiss_current: true
      timesCCDPmntMissL1M: true
      timesCCDPmntMissL3M: true
      timesCCDPmntMissL6M: true
      timesCCDPmntMissL12M: true
      timesChqDishonoured: true
      timesChqDishonoured_current: true
      timesChqDishonouredL1M: true
      timesChqDishonouredL3M: true
      timesChqDishonouredL6M: true
      timesChqDishonouredL12M: true
      maxCCDutilizationwarning: true
      maxCCDutilizationwarning_current: true
      maxCCDutilizationwarningL1M: true
      maxCCDutilizationwarningL3M: true
      maxCCDutilizationwarningL6M: true
      maxCCDutilizationwarningL12M: true
      numberOfTradelines: true
      numberOfCCDTradelines: true
      spendIncomeRatio_current: true
      spendIincomeRatioL1M: true
      spendIncomeRatioL3M: true
      spendIncomeRatioL6M: true
      spendIncomeRatioL12M: true
      cashWithdrawRatio_current: true
      cashWithdrawRatioL1M: true
      cashWithdrawRatioL3M: true
      cashWithdrawRatioL6M: true
      cashWithdrawRatioL12M: true
      avg_Inc_current: true
      avg_Inc_L1M: true
      avg_Inc_L3M: true
      avg_Inc_L6M: true
      avg_Inc_L12M: true
      avg_spend_current: true
      avg_spend_L1M: true
      avg_spend_L3M: true
      avg_spend_L6M: true
      avg_spend_L12M: true
      total_bank_accounts: true
      avg_balance_current: true
      avg_balance_1M: true
      avg_balance_2M: true
      avg_balance_3M: true
      avg_balance_4M: true
      avg_balance_5M: true
      avg_balance_6M: true
      avg_balance_7M: true
      avg_balance_8M: true
      avg_balance_9M: true
      avg_balance_10M: true
      avg_balance_11M: true
      avg_balance_12M: true
      tot_EMI_current: true
      tot_EMI_1M: true
      tot_EMI_2M: true
      tot_EMI_3M: true
      tot_EMI_4M: true
      tot_EMI_5M: true
      tot_EMI_6M: true
      tot_EMI_7M: true
      tot_EMI_8M: true
      tot_EMI_9M: true
      tot_EMI_10M: true
      tot_EMI_11M: true
      tot_EMI_12M: true
      Nach_ECS_Bounced_current: true
      Nach_ECS_Bounced_1M: true
      Nach_ECS_Bounced_2M: true
      Nach_ECS_Bounced_3M: true
      Nach_ECS_Bounced_4M: true
      Nach_ECS_Bounced_5M: true
      Nach_ECS_Bounced_6M: true
      Nach_ECS_Bounced_7M: true
      Nach_ECS_Bounced_8M: true
      Nach_ECS_Bounced_9M: true
      Nach_ECS_Bounced_10M: true
      Nach_ECS_Bounced_11M: true
      Nach_ECS_Bounced_12M: true
      avg_balance_L1M: true
      avg_balance_L3M: true
      avg_balance_L6M: true
      avg_balance_L12M: true
      count_balance_is_10000_current: true
      count_balance_is_10000_L1M: true
      count_balance_is_10000_L3M: true
      count_balance_is_10000_L6M: true
      count_balance_is_10000_L12M: true
      tot_EMI_L1M: true
      tot_EMI_L3M: true
      tot_EMI_L6M: true
      tot_EMI_L12M: true
      max_EMI_current: true
      max_EMI_L1M: true
      max_EMI_L3M: true
      max_EMI_L6M: true
      max_EMI_L12M: true
      Salary_flag_final: true
      salary1M_date: true
      salary2M_date: true
      salary3M_date: true
      salary4M_date: true
      salary5M_date: true
      salary6M_date: true
      salary7M_date: true
      salary8M_date: true
      salary9M_date: true
      salary10M_date: true
      salary11M_date: true
      salary12M_date: true
      salary_current_date: true
      bank_account_details: true
      # below metadata features are to be removed post cug testing
      salary_current_metadata: true
      salary1M_metadata: true
      salary2M_metadata: true
      salary3M_metadata: true
      salary4M_metadata: true
      salary5M_metadata: true
      salary6M_metadata: true
      salary7M_metadata: true
      salary8M_metadata: true
      salary9M_metadata: true
      salary10M_metadata: true
      salary11M_metadata: true
      salary12M_metadata: true
      EPFO_current_metadata: true
      EPFO_1M_metadata: true
      EPFO_2M_metadata: true
      EPFO_3M_metadata: true
      EPFO_4M_metadata: true
      EPFO_5M_metadata: true
      EPFO_6M_metadata: true
      tot_EMI_current_metadata: true
      tot_EMI_1M_metadata: true
      tot_EMI_2M_metadata: true
      tot_EMI_3M_metadata: true
      tot_EMI_4M_metadata: true
      tot_EMI_5M_metadata: true
      tot_EMI_6M_metadata: true
      tot_EMI_7M_metadata: true
      tot_EMI_8M_metadata: true
      tot_EMI_9M_metadata: true
      tot_EMI_10M_metadata: true
      tot_EMI_11M_metadata: true
      tot_EMI_12M_metadata: true
      Nach_ECS_Bounced_current_metadata: true
      Nach_ECS_Bounced_1M_metadata: true
      Nach_ECS_Bounced_2M_metadata: true
      Nach_ECS_Bounced_3M_metadata: true
      Nach_ECS_Bounced_4M_metadata: true
      Nach_ECS_Bounced_5M_metadata: true
      Nach_ECS_Bounced_6M_metadata: true
      Nach_ECS_Bounced_7M_metadata: true
      Nach_ECS_Bounced_8M_metadata: true
      Nach_ECS_Bounced_9M_metadata: true
      Nach_ECS_Bounced_10M_metadata: true
      Nach_ECS_Bounced_11M_metadata: true
      Nach_ECS_Bounced_12M_metadata: true

  InhouseOCR:
    ConfidenceThreshold: 0.5

  Lending:
    PreApprovedLoan:
      Lentra:
        Url: "https://ssguat.serviceurl.in/"
      Finflux:
        BaseUrl: "https://epifi.lms-uat.pointz.in"
        Auth:
          Username: "post"
          Password: "Test@123"
          IsPasswordEncrypted: false
          TokenValidityDuration: "14m"
        Charges:
          ProcessingFeeChargeId: 1
         #  This is a map against Vendor Id and Value is the stringified version of api.vendorgateway.lending.lms.finflux.types.LoanStatus
        LoanStatusVendorIdToEnumValue:
          100: "LOAN_STATUS_SUBMITTED_AND_AWAITING_APPROVAL"
          200: "LOAN_STATUS_APPROVED"
          300: "LOAN_STATUS_ACTIVE"
          303: "LOAN_STATUS_TRANSFER_IN_PROGRESS"
          304: "LOAN_STATUS_TRANSFER_ON_HOLD"
          400: "LOAN_STATUS_WITHDRAWN_BY_CLIENT"
          500: "LOAN_STATUS_REJECTED"
          600: "LOAN_STATUS_CLOSED"
          601: "LOAN_STATUS_WRITTEN_OFF"
          602: "LOAN_STATUS_RESCHEDULED"
          700: "LOAN_STATUS_OVERPAID"

  Poshvine:
    BaseUrl: "https://sandbox-api.poshvine.com"
    SSOUrl: "/cs/v1/sso/token_login"
    UpdateUrl: "/cs/external/users/update"
    FiClientId: "9d1207af-b3c1-4096-8cea-8858acf5e27b"
    ExpiresAt: "24h"
    RedirectionUrl: "https://sandbox-fi-money.poshvine.com/sso_login"

  NetCoreEpifi:
    URL: "https://bulkpush.mytoday.com/BulkSms/SingleMsgApi"
    FeedId: "393957"

  AirtelFedSMS:
    URL: "https://iqsms.airtel.in/api/v2/send-sms-cm"
    SenderId: "FedFiB"
    CustomerId: "EPIFI_TECH_Toj5Cm6v3x9qMYBCy3RR"
    RedirectSMSDlrConfigMap:
      prod:
        DlrUrl: "https://vnotificationgw.epifi.in/sms/callback/airtel/RealTimeDLR/requestListener"
      qa:
        DlrUrl: "https://vnotificationgw.qa.pointz.in/sms/callback/airtel/RealTimeDLR/requestListener"
      staging:
        DlrUrl: "https://vnotificationgw.staging.pointz.in/sms/callback/airtel/RealTimeDLR/requestListener"
      uat:
        DlrUrl: "https://vnotificationgw.uat.pointz.in/sms/callback/airtel/RealTimeDLR/requestListener"
  FederalEscalation:
    BaseURL: "https://uatgateway.federalbank.co.in"
    CreateEscalationURL: "fedbnk/uat/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "fedbnk/uat/CRM/v1.0.0/cxsrbulkretrieval"

Flags:
  TrimDebugMessageFromStatus: true
  UseAsyncNotificationCallback: true
  UseCustomTrustedCertPool: true
  EnableFederalCardDecryptionByFallbackKey: true
  UseNewSolID: false
  UseNewOccupationInCifCreation: true
  UseNewFieldsInCifCreation: false
  EnableTransactionEnquiryNewApi: true
  EnableUATForVKYC: true
  EnableFennelClusterV3: true
  EnableInstrumentBillingInterceptor: true
  EnableCibilV2Secrets: true

# HTTP client config inspired from DefaultTransport of http package
# https://golang.org/src/net/http/transport.go?h=DefaultTransport#L42
HttpClientConfig:
  Transport:
    DialContext:
      Timeout: 30s
      KeepAlive: 30s
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 500
    MaxIdleConnsPerHost: 50

# Need this special config for EPFGetPassbook api which can take upto 4m to respond
KarzaEPFPassbookHttpClientConfig:
  Transport:
    DialContext:
      Timeout: 4m
      KeepAlive: 4m
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 500
    MaxIdleConnsPerHost: 50

# Setting the timeout for all the dispute APIs post confirmation from federal where
# createDispute API in DMP system times out if the default time of 30 seconds is kept
# refer mail thread: DMP API Failure Scenarios
# increasing the timeout to 2minutes to check for timeouts
DisputeHTTPClientConfig:
  Transport:
    DialContext:
      Timeout: 120s
      KeepAlive: 120s
    TLSHandshakeTimeout: 10s
    MaxIdleConns: 100
    IdleConnTimeout: 90s
    MaxConnsPerHost: 500
    MaxIdleConnsPerHost: 50


Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/vendorgateway/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

HystrixConfig:
  DefaultServiceTemplate: "Q0"
  Commands:
    FEDERAL_BANK:
      - CommandName: "openbanking_upi_upi_check_txn_status"
        TemplateName: "Q0"
      - CommandName: "openbanking_accounts_accounts_get_account_statement"
        TemplateName: "Q0"
      - CommandName: "openbanking_savings_savings_get_opening_balance"
        TemplateName: "Q0"
      - CommandName: "openbanking_savings_savings_get_balance"
        TemplateName: "Q0"
      - CommandName: "openbanking_upi_upi_validate_address"
        TemplateName: "Q0"
      - CommandName: "namecheck_namecheck_un_name_check"
        TemplateName: "Q0"
      - CommandName: "openbanking_payment_payment_pay_deposit_add_funds"
        TemplateName: "Q0"
      - CommandName: "pan_pan_validate"
        TemplateName: "Q0"
        OverrideTemplateConfig:
          Disabled: true
      - CommandName: "openbanking_customer_customer_dedupe_check"
        TemplateName: "Q0"
        OverrideTemplateConfig:
          ForcedClosed: true
      - CommandName: "ekyc_ekyc_name_dob_validation_for_ekyc"
        TemplateName: "Q0"
    FRESHDESK:
      - CommandName: "cx_freshdesk_freshdesk_get_ticket_by_ticket_id"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_all_tickets"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_update_ticket"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_contacts"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_create_ticket"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_agent"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_add_private_note_in_ticket"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_fetch_ticket_conversations"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_update_ticket_raw"
        TemplateName: "Q0"
      - CommandName: "cx_freshdesk_freshdesk_get_ticket_field"
        TemplateName: "Q0"

TimeoutConfig:
  DefaultTimeout: 30s
  Vendors:
    FEDERAL_BANK:
      # Setting the timeout for all the dispute APIs post confirmation from federal where
      # createDispute API in DMP system times out if the default time of 30 seconds is kept
      # refer mail thread: DMP API Failure Scenarios
      vendorgateway_openbanking_dispute_dispute_createdispute:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_getdisputestatus:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_senddocument:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_channelquestionnaire:
        Timeout: 120s
      vendorgateway_openbanking_dispute_dispute_accounttransactions:
        Timeout: 120s
    FIFTYFIN:
      vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloansoastatement:
        Timeout: 120s
      vendorgateway_lending_securedloans_fiftyfin_fiftyfin_getloanforeclosurestatement:
        Timeout: 120s

XMLDigitalSignatureSigner:
  KeyParams:
    EpifiFederalUPIPrivateKey:
      ValidFrom: "02 Dec 22 10:00 +0530"
      ValidTill: "02 Dec 23 10:00 +0530"

Tracing:
  Enable: false

Freshdesk:
  GroupEnumToGroupIdMapping:
    CALLBACK: ***********
    EPIFI_ESCALATION: ***********
    ESCALATED_CASES_CLOSURE: ***********
    FEDERAL_ESCALATIONS: ***********
    L1_SUPPORT: ***********
    L2_SUPPORT: ***********
    NON_SFTP_ESCALATIONS: ***********
    SFTP_ESCALATIONS: ***********
    SFTP_PENDING_GROUP: ***********
    FEDERAL_UPDATES: ***********
    L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_ACCOUNT_CLOSURE_RISK_BLOCK: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    TRANSACTION: "Transactions"
    ACCOUNTS: "Accounts"
    ONBOARDING: "Onboarding"
    SAVE: "Save"
    WAITLIST: "Waitlist"
    RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General Enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_SAVE: "Save"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY: "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
  TransactionTypeEnumToValueMapping:
    DEBIT_CARD: "Debit Card"
    IMPS: "IMPS"
    NEFT: "NEFT"
    RTGS: "RTGS"
    UPI: "UPI"
    INTRA_BANK: "Intra Bank"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    ACCEPTED: "Accepted"
    REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    OPEN: 2
    PENDING: 3
    RESOLVED: 4
    CLOSED: 5
    WAITING_ON_THIRD_PARTY: 7
    ESCALATED_TO_L2: 8
    ESCALATED_TO_FEDERAL: 9
    SEND_TO_PRODUCT: 10
    WAITING_ON_PRODUCT: 11
    REOPEN: 12
    NEEDS_CLARIFICATION_FROM_CX: 13
    WAITING_ON_CUSTOMER: 6
  ProductCategoryDetailsEnumToValueMapping:
    MANUAL_WHITELISTING: "Manual Whitelisting"
    APP_DOWNLOAD_ISSUE: "App download issue"
    DEVICE_CHECK_FAILURE: "Device check failure"
    PHONE_NUMBER_OTP: "OTP"
    EMAIL_SELECTION_FAILURE: "Email selection failure"
    MOTHER_FATHER_NAME: ""
    PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    KYC: "Manual KYC"
    LIVENESS: "Liveness"
    FACEMATCH_FAIL: "Face-match fail"
    UN_NAME_CHECK: "UN Name blacklist"
    CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    UPI_CONSENT_FAILURE: "UPI Consent failure"
    DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    CARD_CREATION_FAILURE: "Card creation failure"
    CARD_PIN_SET_FAILURE: ""
    UPI_SETUP_FAILURE: "UPI setup failure"
    VKYC: "VKYC"
    REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT: "Fixed Deposit"
    PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT: "Smart Deposit"
  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE: "Pre-Closure FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY: "Mature FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE: "Pre-Closure SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY: "Mature SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"
  TicketVisibilityEnumToValueMapping:
    TICKET_VISIBILITY_ONLY_AGENT: "Agent"
    TICKET_VISIBILITY_ONLY_CUSTOMER: "Customer"
    TICKET_VISIBILITY_ALL: "All"
  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"
  DefaultPageSize: 100

CRM:
  Freshdesk:
    Risk:
      DefaultPageSize: 100
      VerdictEnumToValueMapping:
        VERDICT_UNSPECIFIED: ""
        VERDICT_PASS: "Pass"
        VERDICT_FAIL: "Fail"
      ReviewTypeEnumToValueMapping:
        REVIEW_TYPE_UNSPECIFIED: ""
        REVIEW_TYPE_USER_REVIEW: "User Review"
        REVIEW_TYPE_TRANSACTION_REVIEW: "Transaction Review"
        REVIEW_TYPE_AFU_REVIEW: "Afu Review"
        REVIEW_TYPE_LEA_COMPLAINT_REVIEW: "LEA Complaint Review"
        REVIEW_TYPE_ESCALATION_REVIEW: "Escalation Review"
      PriorityEnumToValueMapping:
        PRIORITY_UNSPECIFIED: 0
        PRIORITY_CRITICAL: 4
        PRIORITY_HIGH: 3
        PRIORITY_MEDIUM: 2
        PRIORITY_LOW: 1
      StatusEnumToValueMapping:
        STATUS_UNSPECIFIED: 0
        STATUS_CREATED: 6
        STATUS_ASSIGNED: 7
        STATUS_IN_REVIEW: 8
        STATUS_IN_QA_REVIEW: 9
        STATUS_DONE: 10
        STATUS_WONT_REVIEW: 11
        STATUS_MANUAL_INTERVENTION: 12
        STATUS_PENDING_USER_INFO: 13
        STATUS_REVIEW_ACTION_IN_PROGRESS: 14
        STATUS_MARKED_FOR_AUTO_ACTION: 15
        STATUS_PENDING_ON_USER: 16
      AgentGroupEnumToGroupIdMapping:
        AGENT_GROUP_UNSPECIFIED: 0
        AGENT_GROUP_USER_OUTBOUND_CALL: 88000111045
        AGENT_GROUP_TRANSACTION_REVIEW: 88000105522
        AGENT_GROUP_USER_REVIEW: 88000112250
        AGENT_GROUP_L1: 88000098850
        AGENT_GROUP_L2: 88000150196
        AGENT_GROUP_QA: 88000150196
        AGENT_GROUP_ESCALATION: 88000094206
        AGENT_GROUP_MULTI_REVIEW: 88000150740
      TicketScopeEnumToValueMapping:
        TICKET_SCOPE_UNSPECIFIED: 0
        GLOBAL_ACCESS: 1
        GROUP_ACCESS: 2
        RESTRICTED_ACCESS: 3

FederalLien:
  ReasonEnumToValueMap:
    REASON_UNSPECIFIED: ""
    REASON_UAT: "OTH"
  LienRequestTypeEnumToValueMap:
    LIEN_REQUEST_TYPE_ADD: "ADD"
    LIEN_REQUEST_TYPE_ENQUIRY: "ENQUIRY"
  LienTypeEnumToValueMap:
    LIEN_TYPE_ADD: "ADD"
  DateLayout: "2006-01-02"
  DateLayoutPostfix: "T00:00:00.000"
  Channel: "Fi"
  StatusCodeToEnumMap:
    S0000: "OK"
    S000: "OK"
    0000: "INTERNAL"
    F000: "INTERNAL"
    F001: "ALREADY_EXISTS"
  CBSStatusCodeToEnumMap:
    SUCCESS: "OK"
    FAILURE: "INTERNAL"

AwsSes:
  ContactListName: "fi"
  UnsubscribeOptionPlaceholder: "to unsubscribe <a href=\"{{amazonSESUnsubscribeUrl}}\">click here</a>"

MaxAllowedTimeIntervalForMiniStatement: 2160h

VendorAddresses:
  RegisteredAddresses:
    FEDERAL_BANK:
      RegionCode: "IN"
      PostalCode: "682031"
      AdministrativeArea: "KERALA"
      Locality: "KOCHI"
      AddressLines:
        - "EPIFI FEDERAL NEO BANKING"
        - "FEDERAL TOWERS,MARINE DRIVE"

DisputeConfig:
  FiRequesterId: "F768484C-9C9A-4023-B298-3BA45DA1F352"


PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: |
        {
         "key": "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",
         "cert": "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",
         "pass_phrase": "qwerty1234"
        }

  ExternalEntity:
    - Secret: |
        {
         "key": "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",
         "cert": "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",
         "pass_phrase": "qwerty1234"
        }

RedactedRawRequestLogExceptionList: [ ]
RedactedRawResponseLogExceptionList: [ ]

VideoSdk:
  ApiHost: "https://api.videosdk.live"
  ApiVersion: "v2"
  JwtExpiryDuration: "45m"

DCIssuance:
  ChargeType: "DC"
  PartnerId: "EPIFI"
  ApiName: "ENQUIRY"

SkipServerCertVerification: true
