package lenden

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/vendorgateway/config"
)

type Cryptor struct {
	Conf *config.Lenden
}

// SecureData is the encrypted and base64 encoded payload with a hash checksum
// of request sent to vendor or response received from vendor.
type SecureData struct {
	Payload  string `json:"payload"`
	Checksum string `json:"checksum"`
}

// Encrypt encrypts the input data using AES-CBC mode.
func (c *Cryptor) Encrypt(data []byte) ([]byte, error) {
	if !c.Conf.EnableCryptor {
		secureReqBody := &SecureData{Payload: string(data)}
		return json.Marshal(secureReqBody)
	}
	if len(data) == 0 {
		return nil, errors.New("no data to encrypt")
	}
	encryptedBody, err := encryptAESCBC(c.Conf.<PERSON>, string(data), c.Conf.Iv)
	if err != nil {
		return nil, errors.Wrap(err, "error encrypting the request body")
	}
	secureReqBody := &SecureData{
		Payload: string(encryptedBody),
		// Generate an SHA-256 checksum for the encrypted data
		Checksum: generateHash(string(encryptedBody)),
	}
	return json.Marshal(secureReqBody)
}

// Decrypt decrypts the input data using AES-CBC mode.
func (c *Cryptor) Decrypt(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, errors.New("no data to decrypt")
	}
	if !c.Conf.EnableCryptor {
		return data, nil
	}
	// Decode the base64 encoded data
	decodedData, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return nil, errors.Wrap(err, "error decoding base64 encoded encrypted data")
	}

	// Create a new AES cipher block
	block, err := aes.NewCipher([]byte(c.Conf.SecretKey))
	if err != nil {
		return nil, errors.Wrap(err, "error creating a new cipher")
	}

	// Decrypt the data using AES-CBC mode
	decryptor := cipher.NewCBCDecrypter(block, []byte(c.Conf.Iv))
	decryptedData := make([]byte, len(decodedData))
	decryptor.CryptBlocks(decryptedData, decodedData)

	// Remove padding from the decrypted data
	decryptedData = pkcs5UnPadding(decryptedData)

	return decryptedData, nil
}

// encryptAESCBC encrypts the plaintext using AES-CBC mode.
func encryptAESCBC(key, plainText, iv string) ([]byte, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, errors.Wrap(err, "error creating a new cipher")
	}
	if plainText == "" {
		return nil, errors.New("plaintext is empty")
	}
	encryptor := cipher.NewCBCEncrypter(block, []byte(iv))
	content := []byte(plainText)
	content = pkcs5Padding(content, block.BlockSize())
	cipherText := make([]byte, len(content))
	encryptor.CryptBlocks(cipherText, content)
	encodedCipherText := []byte(base64.StdEncoding.EncodeToString(cipherText))
	return encodedCipherText, nil
}

// generateHash generates an SHA-256 hash for the input string.
func generateHash(input string) string {
	hash := sha256.Sum256([]byte(input))
	return hex.EncodeToString(hash[:])
}

// pkcs5Padding pads the input data to a multiple of the block size.
func pkcs5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padText...)
}

// pkcs5UnPadding removes padding from the decrypted data.
func pkcs5UnPadding(src []byte) []byte {
	length := len(src)
	unPadding := int(src[length-1])
	return src[:(length - unPadding)]
}
