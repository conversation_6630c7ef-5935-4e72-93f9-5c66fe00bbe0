package lenden

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"go.temporal.io/sdk/testsuite"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	userPb "github.com/epifi/gamma/api/user"
	lendenVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	deeplinkMocks "github.com/epifi/gamma/preapprovedloan/deeplink/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	helperMocks "github.com/epifi/gamma/preapprovedloan/helper/mocks"
	userMocks "github.com/epifi/gamma/user/mocks"
	lendenVgMocks "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/lenden/mocks"
)

type mockedDependencies struct {
	loanStepExecutionDao *daoMocks.MockLoanStepExecutionsDao
	loanRequestDao       *daoMocks.MockLoanRequestsDao
	loanOfferDao         *daoMocks.MockLoanOffersDao
	loanApplicantDao     *daoMocks.MockLoanApplicantDao
	lendenVgClient       *lendenVgMocks.MockLendenClient
	rpcHelper            *helperMocks.MockRpcHelper
	deeplinkFactory      *deeplinkMocks.MockProviderFactory
	deeplinkProvider     *deeplinkMocks.MockIDeeplinkProvider
	userClient           *userMocks.MockUsersClient
}

func newLendenActProcessorWithMocks(t *testing.T) (*Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)

	mockLoanStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockLoanRequestDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockLoanOfferDao := daoMocks.NewMockLoanOffersDao(ctr)
	mockLoanApplicantDao := daoMocks.NewMockLoanApplicantDao(ctr)
	mockLendenVgClient := lendenVgMocks.NewMockLendenClient(ctr)
	mockRpcHelper := helperMocks.NewMockRpcHelper(ctr)
	mockDeeplinkFactory := deeplinkMocks.NewMockProviderFactory(ctr)
	mockDeeplinkProvider := deeplinkMocks.NewMockIDeeplinkProvider(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)

	processor := &Processor{
		loanStepExecutionDao: mockLoanStepExecutionDao,
		loanRequestDao:       mockLoanRequestDao,
		loanOfferDao:         mockLoanOfferDao,
		loanApplicantDao:     mockLoanApplicantDao,
		lendenVgClient:       mockLendenVgClient,
		rpcHelper:            mockRpcHelper,
		deeplinkFactory:      mockDeeplinkFactory,
		userClient:           mockUserClient,
	}

	md := &mockedDependencies{
		loanStepExecutionDao: mockLoanStepExecutionDao,
		loanRequestDao:       mockLoanRequestDao,
		loanOfferDao:         mockLoanOfferDao,
		loanApplicantDao:     mockLoanApplicantDao,
		lendenVgClient:       mockLendenVgClient,
		rpcHelper:            mockRpcHelper,
		deeplinkFactory:      mockDeeplinkFactory,
		deeplinkProvider:     mockDeeplinkProvider,
		userClient:           mockUserClient,
	}

	return processor, md, func() {
		ctr.Finish()
	}
}

// Helper functions to create test data
func createMockLSE(actorId, refId string, hasMandateData bool, hasTrackingId bool) *palPb.LoanStepExecution {
	lse := &palPb.LoanStepExecution{
		Id:      "lse-id-1",
		ActorId: actorId,
		RefId:   refId,
		Status:  palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
	}

	if hasMandateData {
		mandateData := &palPb.MandateData{}
		if hasTrackingId {
			mandateData.MerchantTxnId = "tracking-id-123"
		}
		lse.Details = &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_MandateData{
				MandateData: mandateData,
			},
		}
	}

	return lse
}

func createMockLoanRequest(refId, offerId string) *palPb.LoanRequest {
	return &palPb.LoanRequest{
		Id:      refId,
		OfferId: offerId,
	}
}

func createMockLoanOffer(offerId string, hasConstraint bool, constraintAccount *palPb.AaAnalysisBankDetails) *palPb.LoanOffer {
	offer := &palPb.LoanOffer{
		Id: offerId,
	}

	if hasConstraint {
		offer.OfferConstraints = &palPb.LoanOfferConstraints{
			LendenConstraintInfo: &palPb.LendenConstraintInfo{
				BankDetails: constraintAccount,
			},
		}
	}

	return offer
}

func createMockSavingsAccount() *account.Account {
	return &account.Account{
		AccountNo:  "**********",
		IfscCode:   "FDRL0001234",
		BankName:   "Federal Bank",
		CustomerId: "customer-123",
	}
}

func createMockUser() *userPb.User {
	return &userPb.User{
		Profile: &userPb.UserProfile{
			KycName: &userPb.UserName{
				FirstName: "John",
				LastName:  "Doe",
			},
		},
	}
}

func createMockLoanApplicant(actorId string) *palPb.LoanApplicant {
	return &palPb.LoanApplicant{
		ActorId:            actorId,
		VendorApplicantId:  "vendor-applicant-123",
	}
}

func TestProcessor_LdcCheckMandateStatus(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}

	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *mockedDependencies)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "Fi-Federal savings account matches loan offer constraint account - no add bank option",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					LoanStep: createMockLSE("actor-1", "ref-1", true, false),
					Vendor:   palPb.Vendor_LENDEN,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				},
			},
			mockFunc: func(md *mockedDependencies) {
				// Mock LSE update for ExecuteWork
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				
				// Mock loan applicant
				md.loanApplicantDao.EXPECT().GetByActorId(gomock.Any(), "actor-1").Return(
					createMockLoanApplicant("actor-1"), nil)
				
				// Mock initiate mandate - returns prerequisites pending
				md.lendenVgClient.EXPECT().InitMandate(gomock.Any(), gomock.Any()).Return(
					&lendenVgPb.InitMandateResponse{
						Status: &rpc.Status{Code: uint32(lendenVgPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND)},
					}, nil)
				
				// Mock loan request and offer with matching constraint
				constraintAccount := &palPb.AaAnalysisBankDetails{
					AccountNumber:     "**********",
					AccountHolderName: "John Doe",
					Ifsc:              "FDRL0001234",
					BankName:          "Federal Bank",
				}
				
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), "ref-1").Return(
					createMockLoanRequest("ref-1", "offer-1"), nil)
				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offer-1").Return(
					createMockLoanOffer("offer-1", true, constraintAccount), nil)
				
				// Mock savings account that matches constraint
				md.rpcHelper.EXPECT().GetSavingsAccountDetails(gomock.Any(), "actor-1", 
					commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR).Return(
					createMockSavingsAccount(), nil)
				
				md.rpcHelper.EXPECT().GetUserByActorId(gomock.Any(), "actor-1").Return(
					createMockUser(), nil)
				
				// Mock deeplink factory and provider
				md.deeplinkFactory.EXPECT().GetDeeplinkGenerator(gomock.Any(), gomock.Any()).Return(md.deeplinkProvider)
				md.deeplinkProvider.EXPECT().GetLoanHeader().Return(&palPb.LoanHeader{}).AnyTimes()
				
				// Expect mandate init screen with no add bank option (allowAddBankAccount = false)
				md.deeplinkProvider.EXPECT().GetPlMandateInitScreenV2(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, params *provider.PlMandateInitScreenV2Params) (*deeplinkPb.Deeplink, error) {
						// Verify that allowAddBankAccount is false when accounts match
						assert.False(t, params.AllowAddBankAccount, "Should not allow adding bank account when savings and constraint accounts match")
						assert.Nil(t, params.AddBankAccountDeeplink, "Add bank account deeplink should be nil when accounts match")
						return &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PL_MANDATE_INIT_SCREEN_V2}, nil
					})
			},
			wantErr: true, // Expecting transient error to continue polling
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
		},
