package fiftyfin

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	typesPb "github.com/epifi/gamma/api/typesv2"
	fiftyfinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

// FiftyfinGetAdditionalKycDetailScreen gets prefilled data from onboarding and set deeplink for additional detail screen
func (p *Processor) FiftyfinGetAdditionalKycDetailScreen(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}

		// handle loan offer expiry
		offerExpiredRes, err := p.handleLoanOfferExpiry(ctx, req.GetVendor(), req.GetLoanProgram(), lse, res)
		if err != nil {
			if errors.Is(err, ErrorLoanOfferExpired) {
				lg.Error("loan offer has expired")
				return offerExpiredRes, nil
			}
			lg.Error("error in handleLoanOfferExpiry", zap.Error(err))
			return nil, err
		}

		// Get user details to prefill data
		userDetail, udErr := p.rpcHelper.GetUserByActorId(ctx, req.GetLoanStep().GetActorId())
		if udErr != nil {
			lg.Error("error getting user by actor id", zap.Error(udErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, udErr.Error())
		}
		fatherName := userDetail.GetProfile().GetFatherName().ToString()
		motherName := userDetail.GetProfile().GetMotherName().ToString()

		nextAction, nextActionErr := p.deeplinkProvider.GetKycAdditionalDetailScreenDeeplink(p.deeplinkProvider.GetLoanHeader(), lse.GetRefId(), fatherName, motherName)
		if nextActionErr != nil {
			lg.Error("error getting deeplink for kyc additional detail screen")
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, nextActionErr.Error())
		}

		// update next action to kyc screen
		updateLrErr := palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, res.GetLoanStep().GetRefId(), nextAction)
		if updateLrErr != nil {
			lg.Error("error while updating next action to kyc additional detail screen", zap.Error(updateLrErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, updateLrErr.Error())
		}

		// set lse to in progress
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		lse.Details = &palPb.LoanStepExecutionDetails{Details: &palPb.LoanStepExecutionDetails_ApplicantData{ApplicantData: &palPb.ApplicantData{}}}
		updateLseErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		})
		if updateLseErr != nil {
			lg.Error("error while setting status for loan step execution", zap.Error(updateLseErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, updateLseErr.Error())
		}
		res.LoanStep = lse

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) CheckFiftyfinAdditionalKycDetailsInputStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	logger := activity.GetLogger(ctx)

	return palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{LoanStep: lse}

		// handle loan offer expiry
		offerExpiredRes, err := p.handleLoanOfferExpiry(ctx, req.GetVendor(), req.GetLoanProgram(), lse, res)
		if err != nil {
			if errors.Is(err, ErrorLoanOfferExpired) {
				logger.Error("offer has expired")
				return offerExpiredRes, nil
			}
			logger.Error("error in handleLoanOfferExpiry", zap.Error(err))
			return nil, err
		}

		lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			logger.Error("error in getting loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request: %s", err.Error()))
		}

		if lr.GetSubStatus() == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED {
			return res, nil
		}

		return nil, errors.Wrap(epifierrors.ErrTransient, "waiting for user to verify phone and email")
	})
}

// FiftyfinSubmitAdditionalKycDetail fetches additional kyc data to be submitted and performs the VG call
func (p *Processor) FiftyfinSubmitAdditionalKycDetail(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	return palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{LoanStep: lse}

		// handle loan offer expiry
		offerExpiredRes, err := p.handleLoanOfferExpiry(ctx, req.GetVendor(), req.GetLoanProgram(), lse, res)
		if err != nil {
			if errors.Is(err, ErrorLoanOfferExpired) {
				lg.Error("offer has expired")
				return offerExpiredRes, nil
			}
			lg.Error("error in handleLoanOfferExpiry", zap.Error(err))
			return nil, err
		}

		// Get user submitted details from lse and convert to vg compliant enum
		userData := lse.GetDetails().GetApplicantData()
		if userData == nil {
			lg.Error("got empty applicant data from from lse")
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, "got empty applicant data from from lse")
		}

		additionalKycDetReq := &fiftyfinPb.AddAdditionalKycDetailsRequest{
			RelationType:   fiftyfinPb.RelationType_RELATION_TYPE_FATHER,
			RelativeName:   (&commontypes.Name{}).Parse(userData.GetFatherName()),
			MotherName:     (&commontypes.Name{}).Parse(userData.GetMotherName()),
			EmploymentType: Enum(userData.GetEmploymentType(), typesPb.EmploymentType_value, typesPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED),
			MaritalStatus:  Enum(userData.GetMaritalStatus(), typesPb.MaritalStatus_value, typesPb.MaritalStatus_UNSPECIFIED),
			ResidenceType:  Enum(userData.GetResidenceType(), fiftyfinPb.ResidenceType_value, fiftyfinPb.ResidenceType_RESIDENCE_TYPE_UNSPECIFIED),
		}

		// Get vendor applicant id
		loanApplicantRes, loanApplicantErr := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
		if loanApplicantErr != nil {
			lg.Error("error while getting loan applicant for the user", zap.Error(loanApplicantErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, loanApplicantErr.Error())
		}
		additionalKycDetReq.UserId = loanApplicantRes.GetVendorApplicantId()

		// Perform vendor call
		additionalKycDetailResp, additionalKycDetailErr := p.ffVgClient.AddAdditionalKycDetails(ctx, additionalKycDetReq)
		if rpcErr := epifigrpc.RPCError(additionalKycDetailResp, additionalKycDetailErr); rpcErr != nil {
			lg.Error("failed to initiate kyc", zap.Error(rpcErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, rpcErr.Error())
		}

		return res, nil
	})
}
