//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/leads"
	"github.com/epifi/gamma/leads/consumer"
	"github.com/epifi/gamma/leads/dao/impl"
	"github.com/epifi/gamma/leads/dao/utils"
	"github.com/epifi/gamma/leads/developer"
)

func InitializeUserLeadService(userClient userPb.UsersClient, loansClient palPb.PreApprovedLoanClient, dbConnProvider *storageV2.DBResourceProvider[*gorm.DB], eventBroker events.Broker) *leads.Service {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		utils.NewDBProvider,
		impl.UserLeadWireSet,
		leads.NewService,
	)
	return &leads.Service{}
}

func InitializrDevLeadsService(dbConnProvider *storageV2.DBResourceProvider[*gorm.DB]) *developer.LeadsDbStateService {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		utils.NewDBProvider,
		impl.UserLeadWireSet,
		developer.NewDevFactory,
		developer.NewLeadsDbStateService,
	)
	return &developer.LeadsDbStateService{}
}

// InitializeDeleteUserEventConsumerService wires dependencies for the SQS consumer that handles
// delete-user events and clears actor_id in leads.
func InitializeDeleteUserEventConsumerService(dbConnProvider *storageV2.DBResourceProvider[*gorm.DB]) *consumer.ConsumerService {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		utils.NewDBProvider,
		impl.UserLeadWireSet,
		consumer.NewConsumerService,
	)
	return &consumer.ConsumerService{}
}
