// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/leads"
	"github.com/epifi/gamma/leads/consumer"
	"github.com/epifi/gamma/leads/dao/impl"
	"github.com/epifi/gamma/leads/dao/utils"
	"github.com/epifi/gamma/leads/developer"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeUserLeadService(userClient user.UsersClient, loansClient preapprovedloan.PreApprovedLoanClient, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], eventBroker events.Broker) *leads.Service {
	dbProvider := utils.NewDBProvider(dbConnProvider)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	userLeadDao := impl.NewUserLeadDao(dbProvider, domainIdGenerator)
	service := leads.NewService(userClient, loansClient, userLeadDao, eventBroker)
	return service
}

func InitializrDevLeadsService(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB]) *developer.LeadsDbStateService {
	dbProvider := utils.NewDBProvider(dbConnProvider)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	userLeadDao := impl.NewUserLeadDao(dbProvider, domainIdGenerator)
	devFactory := developer.NewDevFactory(userLeadDao)
	leadsDbStateService := developer.NewLeadsDbStateService(devFactory)
	return leadsDbStateService
}

// InitializeDeleteUserEventConsumerService wires dependencies for the SQS consumer that handles
// delete-user events and clears actor_id in leads.
func InitializeDeleteUserEventConsumerService(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB]) *consumer.ConsumerService {
	dbProvider := utils.NewDBProvider(dbConnProvider)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	userLeadDao := impl.NewUserLeadDao(dbProvider, domainIdGenerator)
	consumerService := consumer.NewConsumerService(userLeadDao)
	return consumerService
}
