// nolint:gosec
package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/knadh/koanf"

	"github.com/epifi/be-common/pkg/cfg"
)

//go:generate conf_gen github.com/epifi/gamma/leads/config Config

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, _, err = loadConfig()
	})
	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, *koanf.Koanf, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.LEADS_SERVICE)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}

	dbOwnershipMap := conf.DbConfigMap.GetOwnershipToDbConfigMap()

	if _, err = cfg.LoadAndUpdateSecretValues(dbOwnershipMap, nil, conf.Application.Environment, conf.AWS.Region); err != nil {
		return nil, nil, fmt.Errorf("failed to load and update secret values %w", err)
	}

	return conf, k, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type Config struct {
	DbConfigMap cfg.DbConfigMap
	Application *Application
	AWS         *cfg.AWS

	Secrets *cfg.Secrets

	// DeleteUserEventSqsSubscriber holds configuration for the delete user event SQS subscriber.
	DeleteUserEventSqsSubscriber *cfg.SqsSubscriber `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}
