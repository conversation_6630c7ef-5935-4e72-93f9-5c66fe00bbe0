//nolint:dupl
package impl

import (
	"context"
	"fmt"
	"time"

	pkgErrors "github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/leads/dao"
	"github.com/epifi/gamma/leads/dao/model"
	"github.com/epifi/gamma/leads/dao/utils"

	"github.com/google/wire"
)

type UserLeadDao struct {
	dbProvider *utils.DBProvider
	idGen      idgen.IdGenerator
}

var _ dao.UserLeadDao = &UserLeadDao{}

var UserLeadWireSet = wire.NewSet(NewUserLeadDao, wire.Bind(new(dao.UserLeadDao), new(*UserLeadDao)))

var userLeadColumnNames = map[leads.UserLeadFieldMask]string{
	leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID:           "actor_id",
	leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_PERSONAL_DETAILS:   "personal_details",
	leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_ADDITIONAL_DETAILS: "additional_details",
	leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS:        "lead_status",
	leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT:       "completed_at",
	leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EXPIRED_AT:         "expired_at",
}

func NewUserLeadDao(dbProvider *utils.DBProvider, idGen idgen.IdGenerator) *UserLeadDao {
	return &UserLeadDao{
		dbProvider: dbProvider,
		idGen:      idGen,
	}
}

func (c *UserLeadDao) Create(ctx context.Context, userLead *leads.UserLead) (*leads.UserLead, error) {
	defer metric_util.TrackDuration("leads/dao/impl", "UserLeadDao", "Create", time.Now())
	db, err := c.dbProvider.GetDBFromContext(ctx, common.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error in get db from context")
	}

	userLeadModel := model.NewUserLead(userLead)
	id, err := c.idGen.Get(idgen.UserLeadId)
	if err != nil {
		return nil, fmt.Errorf("id generation failed: %w", err)
	}
	userLeadModel.Id = id

	res := db.Create(userLeadModel)
	if res.Error != nil {
		// if trying to create more than one entry for same details return duplicate entry error
		if storage.IsDuplicateRowError(res.Error) {
			return nil, pkgErrors.Wrap(epifierrors.ErrDuplicateEntry, res.Error.Error())
		}
		return nil, res.Error
	}
	return userLeadModel.GetProto(), nil
}

func (c *UserLeadDao) Update(ctx context.Context, userLead *leads.UserLead, updateMasks []leads.UserLeadFieldMask) error {
	defer metric_util.TrackDuration("leads/dao/impl", "UserLeadDao", "Update", time.Now())

	if userLead.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}

	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	db, err := c.dbProvider.GetDBFromContext(ctx, common.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return pkgErrors.Wrap(err, "error in db provider")
	}

	userLeadModel := model.NewUserLead(userLead)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)

	res := db.Model(userLeadModel).Where("id = ?", userLead.Id).Select(updateColumns).Updates(userLeadModel)

	if res.Error != nil {
		return res.Error
	}

	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}

	return nil
}

func (c *UserLeadDao) GetById(ctx context.Context, id string) (*leads.UserLead, error) {
	defer metric_util.TrackDuration("leads/dao/impl", "UserLeadDao", "GetById", time.Now())
	if id == "" {
		return nil, pkgErrors.New("id can't be blank")
	}

	db, err := c.dbProvider.GetDBFromContext(ctx, common.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error in getConnFromContextOrProvider")
	}

	var userLeadModel model.UserLead
	if err := db.Where("id = ?", id).First(&userLeadModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return userLeadModel.GetProto(), nil
}

func (c *UserLeadDao) GetByClientReqIdAndClientId(ctx context.Context, clientReqId string, clientId string) (*leads.UserLead, error) {
	defer metric_util.TrackDuration("leads/dao/impl", "UserLeadDao", "GetByClientReqIdAndClientId", time.Now())

	if clientReqId == "" {
		return nil, pkgErrors.New("client request id can't be blank")
	}

	if clientId == "" {
		return nil, pkgErrors.New("client id can't be blank")
	}

	db, err := c.dbProvider.GetDBFromContext(ctx, common.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error in getConnFromContextOrProvider")
	}

	var userLeadModel model.UserLead
	if err := db.Where("client_request_id = ? AND client_id = ?", clientReqId, clientId).First(&userLeadModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return userLeadModel.GetProto(), nil
}

// GetActiveUserLeadsByFilter retrieves active (not expired, not completed) user leads based on filter criteria and product types.
func (c *UserLeadDao) GetActiveUserLeadsByFilter(ctx context.Context, filter *model.UserLeadFilter, productTypes []leads.ProductType) ([]*leads.UserLead, error) {
	defer metric_util.TrackDuration("leads/dao/impl", "UserLeadDao", "GetActiveUserLeadsByFilter", time.Now())

	if filter == nil {
		return nil, pkgErrors.New("filter cannot be nil or empty for GetActiveUserLeadsByFilter")
	}
	if filter.ActorID == "" && filter.PAN == "" && filter.MobileNumber == "" && filter.Email == "" {
		return nil, pkgErrors.New("at least one filter needs to be specified")
	}

	db, err := c.dbProvider.GetDBFromContext(ctx, common.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error getting db from context")
	}

	query := db.Model(&model.UserLead{})
	query = filter.BuildQuery(query)

	// Apply product type filter if provided
	if len(productTypes) > 0 {
		query = query.Where("product_type IN (?)", productTypes)
	}

	// Filter for active leads
	query = query.Where("completed_at IS NULL")

	var userLeadModels []*model.UserLead
	if err := query.Find(&userLeadModels).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			// Return empty slice if no records found, not an error
			return []*leads.UserLead{}, nil
		}
		return nil, pkgErrors.Wrap(err, "error fetching active user leads by filter")
	}

	// Convert model slice to proto slice
	userLeadProtos := make([]*leads.UserLead, 0, len(userLeadModels))
	for _, m := range userLeadModels {
		userLeadProtos = append(userLeadProtos, m.GetProto())
	}

	return userLeadProtos, nil
}

func (c *UserLeadDao) selectedColumnsForUpdate(updateMasks []leads.UserLeadFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, userLeadColumnNames[field])
	}
	return selectColumns
}

// RemoveActorId set the actor_id to NULL and sets status to LEAD_CREATED for incomplete leads (where completed_at is null) for a given actor_id
func (c *UserLeadDao) RemoveActorId(ctx context.Context, actorId string) error {
	defer metric_util.TrackDuration("leads/dao/impl", "UserLeadDao", "RemoveActorId", time.Now())

	if actorId == "" {
		return pkgErrors.New("actor_id can't be blank")
	}

	db, err := c.dbProvider.GetDBFromContext(ctx, common.Ownership_EPIFI_TECH_V2)
	if err != nil {
		return pkgErrors.Wrap(err, "error in get db from context")
	}

	// Update actor_id to NULL and status to LEAD_CREATED where completed_at is NULL and actor_id matches
	res := db.Model(&model.UserLead{}).
		Where("actor_id = ? AND completed_at IS NULL", actorId).
		Updates(map[string]interface{}{
			"actor_id":    nil,
			"lead_status": leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
		})

	if res.Error != nil {
		return pkgErrors.Wrap(res.Error, "error updating actor_id and status for incomplete leads")
	}

	return nil
}
