package impl

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/leads/dao/model"

	testPkg "github.com/epifi/be-common/pkg/test/v2"
)

var (
	lead1 = &leads.UserLead{
		ActorId:         "actor-id-1",
		ClientRequestId: "1",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "1",
		Pan:             "**********",
		MobileNumber:    "0000000000",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
	}
	lead2 = &leads.UserLead{
		ActorId:         "actor-id-2",
		ClientRequestId: "2",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "1",
		Pan:             "**********",
		MobileNumber:    "0000000001",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
	}
	lead3 = &leads.UserLead{
		ActorId:         "actor-id-3",
		ClientRequestId: "3",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "1",
		Pan:             "**********",
		MobileNumber:    "0000000002",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
	}
	lead4 = &leads.UserLead{
		ActorId:         "actor-id-4",
		ClientRequestId: "4",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "1",
		Pan:             "**********",
		MobileNumber:    "0000000003",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
	}
)

func TestUserLeadDao_Create(t *testing.T) {
	testPkg.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)
	type args struct {
		ctx  context.Context
		lead *leads.UserLead
	}
	tests := []struct {
		name    string
		args    args
		want    *leads.UserLead
		wantErr bool
	}{
		{
			name: "should create lead successfully",
			args: args{
				ctx:  context.Background(),
				lead: lead1,
			},
			want:    lead1,
			wantErr: false,
		},
		{
			name: "should fail to create lead",
			args: args{
				ctx: context.Background(),
				lead: &leads.UserLead{
					ActorId:         "actor-id-1",
					ClientRequestId: "1",
					ClientId:        "1",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := userLeadDao.Create(getTestContextWithOwnership(tt.args.ctx), tt.args.lead)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&leads.UserLead{}, "created_at", "updated_at", "id"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestUserLeadDao_Update(t *testing.T) {
	t.Parallel()
	testPkg.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)

	// Create a lead to update
	createdLead, err := userLeadDao.Create(getTestContextWithOwnership(context.Background()), lead2)
	if err != nil {
		t.Fatalf("Failed to create lead for update test: %v", err)
	}

	type args struct {
		ctx        context.Context
		userLead   *leads.UserLead
		updateMask []leads.UserLeadFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		// Add a verify function to check the updated state if needed
		verify func(t *testing.T, id string)
	}{
		{
			name: "should update lead status successfully",
			args: args{
				ctx: context.Background(),
				userLead: &leads.UserLead{
					Id:         createdLead.Id,
					LeadStatus: leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED,
				},
				updateMask: []leads.UserLeadFieldMask{leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS},
			},
			wantErr: false,
			verify: func(t *testing.T, id string) {
				updatedLead, err := userLeadDao.GetById(getTestContextWithOwnership(context.Background()), id)
				if err != nil {
					t.Fatalf("Failed to get updated lead: %v", err)
				}
				if updatedLead.LeadStatus != leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED {
					t.Errorf("LeadStatus mismatch: got %v, want %v", updatedLead.LeadStatus, leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED)
				}
				// Check other fields remain unchanged
				if updatedLead.Pan != lead2.Pan {
					t.Errorf("Pan mismatch: got %v, want %v", updatedLead.Pan, lead1.Pan)
				}
			},
		},
		{
			name: "should fail update with empty ID",
			args: args{
				ctx:        context.Background(),
				userLead:   &leads.UserLead{LeadStatus: leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED}, // No ID
				updateMask: []leads.UserLeadFieldMask{leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS},
			},
			wantErr: true,
		},
		{
			name: "should fail update with empty update mask",
			args: args{
				ctx: context.Background(),
				userLead: &leads.UserLead{
					Id:         createdLead.Id,
					LeadStatus: leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED,
				},
				updateMask: []leads.UserLeadFieldMask{}, // Empty mask
			},
			wantErr: true,
		},
		{
			name: "should return error for non-existent lead",
			args: args{
				ctx: context.Background(),
				userLead: &leads.UserLead{
					Id:         "non-existent-id",
					LeadStatus: leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED,
				},
				updateMask: []leads.UserLeadFieldMask{leads.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS},
			},
			wantErr: true, // Expects ErrRowNotUpdated
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := userLeadDao.Update(getTestContextWithOwnership(tt.args.ctx), tt.args.userLead, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				// TODO: Check for specific error types like ErrRowNotUpdated if needed
				return
			}
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.args.userLead.Id)
			}
		})
	}
}

func TestUserLeadDao_GetById(t *testing.T) {
	t.Parallel()
	testPkg.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)

	// Create a lead to get
	createdLead, err := userLeadDao.Create(getTestContextWithOwnership(context.Background()), lead3)
	if err != nil {
		t.Fatalf("Failed to create lead for get test: %v", err)
	}

	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *leads.UserLead
		wantErr bool
	}{
		{
			name: "should get lead successfully",
			args: args{
				ctx: context.Background(),
				id:  createdLead.GetId(),
			},
			want:    createdLead, // Use the created lead for comparison
			wantErr: false,
		},
		{
			name: "should fail get with empty ID",
			args: args{
				ctx: context.Background(),
				id:  "",
			},
			wantErr: true,
		},
		{
			name: "should return not found error for non-existent ID",
			args: args{
				ctx: context.Background(),
				id:  "non-existent-id",
			},
			want:    nil,
			wantErr: true, // Expects ErrRecordNotFound
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := userLeadDao.GetById(getTestContextWithOwnership(tt.args.ctx), tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&leads.UserLead{}, "created_at", "updated_at"), // Ignore potentially different timestamps
			}
			// Only compare if no error was wanted and we expected a result
			if !tt.wantErr && tt.want != nil {
				// Adjust the wanted lead's timestamps to match the fetched one before comparing,
				// or ignore them if they are not critical for the test logic.
				// Here, we ignore them using protocmp.IgnoreFields.
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("GetById() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestUserLeadDao_GetByClientReqIdAndClientId(t *testing.T) {
	t.Parallel()
	testPkg.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)

	createdLead, err := userLeadDao.Create(getTestContextWithOwnership(context.Background()), lead4)
	if err != nil {
		t.Fatalf("Failed to create lead for get test: %v", err)
	}

	type args struct {
		ctx         context.Context
		clientReqId string
		clientId    string
	}
	tests := []struct {
		name    string
		args    args
		want    *leads.UserLead
		wantErr bool
	}{
		{
			name: "should get lead successfully",
			args: args{
				ctx:         context.Background(),
				clientReqId: createdLead.GetClientRequestId(),
				clientId:    createdLead.GetClientId(),
			},
			want:    createdLead, // Use the created lead for comparison
			wantErr: false,
		},
		{
			name: "should fail get with empty client request ID",
			args: args{
				ctx:         context.Background(),
				clientReqId: "",
				clientId:    createdLead.GetClientId(),
			},
			wantErr: true,
		},
		{
			name: "should fail get with empty client ID",
			args: args{
				ctx:         context.Background(),
				clientReqId: createdLead.GetClientRequestId(),
				clientId:    "",
			},
			wantErr: true,
		},
		{
			name: "should return not found error for non-existent client request ID",
			args: args{
				ctx:         context.Background(),
				clientReqId: "non-existent-client-req-id",
				clientId:    createdLead.GetClientId(),
			},
			want:    nil,
			wantErr: true, // Expects ErrRecordNotFound
		},
		{
			name: "should return not found error for non-existent client ID",
			args: args{
				ctx:         context.Background(),
				clientReqId: createdLead.GetClientRequestId(),
				clientId:    "non-existent-client-id",
			},
			want:    nil,
			wantErr: true, // Expects ErrRecordNotFound
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := userLeadDao.GetByClientReqIdAndClientId(getTestContextWithOwnership(tt.args.ctx), tt.args.clientReqId, tt.args.clientId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientReqIdAndClientId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&leads.UserLead{}, "created_at", "updated_at"),
				}
				if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
					t.Errorf("GetByClientReqIdAndClientId() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestUserLeadDao_GetActiveUserLeadsByFilter(t *testing.T) {
	// Ensure clean state for this specific test function
	testPkg.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)

	// --- Setup Test Data ---
	activeLead1 := &leads.UserLead{
		ActorId:         "active-actor-1",
		ClientRequestId: "req-active-1",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "client-a",
		Pan:             "ACTIVEA111A",
		MobileNumber:    "1111111111",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
	}
	activeLead2 := &leads.UserLead{
		ActorId:         "active-actor-1", // Same actor, different product
		ClientRequestId: "req-active-2",
		ProductType:     leads.ProductType_PRODUCT_TYPE_UNSPECIFIED, // Using UNSPECIFIED to avoid unique constraint
		ClientId:        "client-a",
		Pan:             "ACTIVEA111A", // Same PAN
		MobileNumber:    "1111111111",
		Email:           "<EMAIL>",                              // Same email
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED, // Using LEAD_CREATED as IN_PROGRESS seems undefined
	}
	activeLead3 := &leads.UserLead{
		ActorId:         "active-actor-3",
		ClientRequestId: "req-active-3",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "client-a",
		Pan:             "ACTIVEA111B",
		MobileNumber:    "1111111112",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED,
	}
	inactiveLead1 := &leads.UserLead{
		ActorId:         "inactive-actor-1",
		ClientRequestId: "req-inactive-1",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "client-c",
		Pan:             "INACTIC333C",
		MobileNumber:    "3333333333",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED,
		CompletedAt:     timestampPb.Now(),
	}
	inactiveLead2 := &leads.UserLead{
		ActorId:         "inactive-actor-1",
		ClientRequestId: "req-inactive-2",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "client-a",
		Pan:             "INACTIVE111A",
		MobileNumber:    "4444444444",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_REJECTED,
		CompletedAt:     timestampPb.Now(),
	}

	createdActiveLead1, err := userLeadDao.Create(getTestContextWithOwnership(context.Background()), activeLead1)
	if err != nil {
		t.Fatalf("Failed to create activeLead1: %v", err)
	}
	createdActiveLead2, err := userLeadDao.Create(getTestContextWithOwnership(context.Background()), activeLead2)
	if err != nil {
		t.Fatalf("Failed to create activeLead2: %v", err)
	}
	createdActiveLead3, err := userLeadDao.Create(getTestContextWithOwnership(context.Background()), activeLead3)
	if err != nil {
		t.Fatalf("Failed to create activeLead3: %v", err)
	}
	_, err = userLeadDao.Create(getTestContextWithOwnership(context.Background()), inactiveLead1)
	if err != nil {
		t.Fatalf("Failed to create inactiveLead1: %v", err)
	}
	_, err = userLeadDao.Create(getTestContextWithOwnership(context.Background()), inactiveLead2)
	if err != nil {
		t.Fatalf("Failed to create inactiveLead2: %v", err)
	}

	// --- End Setup Test Data ---

	type args struct {
		ctx    context.Context
		filter *model.UserLeadFilter
	}
	tests := []struct {
		name         string
		args         args
		productTypes []leads.ProductType
		want         []*leads.UserLead
		wantErr      bool
	}{
		{
			name: "should get active leads by ActorId",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					ActorID: "active-actor-1",
				},
			},
			productTypes: nil,
			// Expect only leads created in this test with matching ActorID
			want:    []*leads.UserLead{createdActiveLead1, createdActiveLead2},
			wantErr: false,
		},
		{
			name: "should get active leads by Pan",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					PAN: "ACTIVEA111A",
				},
			},
			productTypes: nil,
			// Expect only the active leads created in this test
			want:    []*leads.UserLead{createdActiveLead1, createdActiveLead2},
			wantErr: false,
		},
		{
			name: "should get active leads by MobileNumber",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					MobileNumber: "1111111111",
				},
			},
			productTypes: nil,
			// Expect only leads created in this test with matching MobileNumber
			want:    []*leads.UserLead{createdActiveLead1, createdActiveLead2},
			wantErr: false,
		},
		{
			name: "should get active leads by Email",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					Email: "<EMAIL>",
				},
			},
			productTypes: nil,
			// Expect only leads created in this test with matching Email
			want:    []*leads.UserLead{createdActiveLead1, createdActiveLead2},
			wantErr: false,
		},
		{
			name: "should get active leads matching either pan or email",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					PAN:   "ACTIVEA111B",
					Email: "<EMAIL>",
				},
			},
			productTypes: nil,
			want:         []*leads.UserLead{createdActiveLead1, createdActiveLead2, createdActiveLead3},
			wantErr:      false,
		},
		{
			name: "should get active leads by ActorId and ProductType",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					ActorID: "active-actor-1",
				},
			},
			productTypes: []leads.ProductType{leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN},
			// Expect only leads created in this test matching both
			want:    []*leads.UserLead{createdActiveLead1},
			wantErr: false,
		},
		{
			name: "should return empty list if no matching active leads",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					ActorID: "non-existent-actor",
				},
			},
			productTypes: nil,
			want:         []*leads.UserLead{},
			wantErr:      false,
		},
		{
			name: "should return empty list if only inactive leads match",
			args: args{
				ctx: context.Background(),
				filter: &model.UserLeadFilter{
					ActorID: "inactive-actor-1",
				},
			},
			productTypes: nil,
			// Expect empty list as inactiveLead1 status (CONVERTED) should ideally be filtered by DAO
			want:    []*leads.UserLead{},
			wantErr: false,
		},
		{
			name: "should return error if filter is nil", // Keep this expectation
			args: args{
				ctx:    context.Background(),
				filter: nil,
			},
			productTypes: nil,
			want:         nil,
			wantErr:      true, // DAO returns error for nil filter
		},
		{
			name: "should return error if filter is empty", // Keep this expectation
			args: args{
				ctx:    context.Background(),
				filter: &model.UserLeadFilter{},
			},
			productTypes: nil,
			want:         nil,
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := userLeadDao.GetActiveUserLeadsByFilter(getTestContextWithOwnership(tt.args.ctx), tt.args.filter, tt.productTypes)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActiveUserLeadsByFilter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&leads.UserLead{}, "created_at", "updated_at"),
				protocmp.SortRepeated(func(a, b *leads.UserLead) bool { return a.Id < b.Id }),
			}

			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetActiveUserLeadsByFilter() mismatch (-want +got):\n%s \n Got: %s, \n Want: %s, ", diff, got, tt.want)
			}
		})
	}
}

func TestUserLeadDao_RemoveActorId(t *testing.T) {
	t.Parallel()
	testPkg.TruncateAndPopulateRdsFixturesForMultipleDBs(t, daoTestSuite.dbNameToDb, affectedTestTables)

	ctx := getTestContextWithOwnership(context.Background())

	// Create test leads - one completed and one incomplete
	incompleteLead := &leads.UserLead{
		ActorId:         "test-actor-id",
		ClientRequestId: "incomplete-1",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "test-client",
		Pan:             "**********",
		MobileNumber:    "9999999999",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED, // Set to CONVERTED initially
	}

	completedLead := &leads.UserLead{
		ActorId:         "test-actor-id",
		ClientRequestId: "complete-1",
		ProductType:     leads.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN,
		ClientId:        "test-client",
		Pan:             "**********",
		MobileNumber:    "9999999999",
		Email:           "<EMAIL>",
		LeadStatus:      leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED,
		CompletedAt:     timestampPb.Now(),
	}

	// Create the test leads
	createdIncompleteLead, err := userLeadDao.Create(ctx, incompleteLead)
	if err != nil {
		t.Fatalf("Failed to create incomplete lead: %v", err)
	}

	createdCompletedLead, err := userLeadDao.Create(ctx, completedLead)
	if err != nil {
		t.Fatalf("Failed to create completed lead: %v", err)
	}

	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(t *testing.T)
	}{
		{
			name: "should remove actor_id and set status to LEAD_CREATED for incomplete lead only",
			args: args{
				ctx:     ctx,
				actorId: "test-actor-id",
			},
			wantErr: false,
			verify: func(t *testing.T) {
				// Verify incomplete lead's actor_id is removed and status is updated
				updatedIncompleteLead, err := userLeadDao.GetById(ctx, createdIncompleteLead.Id)
				if err != nil {
					t.Errorf("Failed to get incomplete lead: %v", err)
					return
				}
				if updatedIncompleteLead.ActorId != "" {
					t.Errorf("Incomplete lead actor_id not removed, got %v", updatedIncompleteLead.ActorId)
				}
				if updatedIncompleteLead.LeadStatus != leads.UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED {
					t.Errorf("Incomplete lead status not updated to LEAD_CREATED, got %v", updatedIncompleteLead.LeadStatus)
				}

				// Verify completed lead's actor_id and status are unchanged
				updatedCompletedLead, err := userLeadDao.GetById(ctx, createdCompletedLead.Id)
				if err != nil {
					t.Errorf("Failed to get completed lead: %v", err)
					return
				}
				if updatedCompletedLead.ActorId != "test-actor-id" {
					t.Errorf("Completed lead actor_id changed, got %v, want test-actor-id", updatedCompletedLead.ActorId)
				}
				if updatedCompletedLead.LeadStatus != leads.UserLeadStatus_USER_LEAD_STATUS_CONVERTED {
					t.Errorf("Completed lead status changed, got %v, want CONVERTED", updatedCompletedLead.LeadStatus)
				}
			},
		},
		{
			name: "should fail with empty actor_id",
			args: args{
				ctx:     ctx,
				actorId: "",
			},
			wantErr: true,
		},
		{
			name: "should succeed with non-existent actor_id",
			args: args{
				ctx:     ctx,
				actorId: "non-existent-actor-id",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := userLeadDao.RemoveActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("RemoveActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t)
			}
		})
	}
}
