// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/jarvis/workflow/service.proto

package workflow

import (
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	rpc "github.com/epifi/be-common/api/rpc"
	jarvis "github.com/epifi/gamma/api/jarvis"
	frontend "github.com/epifi/gamma/api/jarvis/frontend"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Environment int32

const (
	Environment_ENV_UNSPECIFIED Environment = 0
	Environment_ENV_QA          Environment = 1
	Environment_ENV_PROD        Environment = 2
)

// Enum value maps for Environment.
var (
	Environment_name = map[int32]string{
		0: "ENV_UNSPECIFIED",
		1: "ENV_QA",
		2: "ENV_PROD",
	}
	Environment_value = map[string]int32{
		"ENV_UNSPECIFIED": 0,
		"ENV_QA":          1,
		"ENV_PROD":        2,
	}
)

func (x Environment) Enum() *Environment {
	p := new(Environment)
	*p = x
	return p
}

func (x Environment) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Environment) Descriptor() protoreflect.EnumDescriptor {
	return file_api_jarvis_workflow_service_proto_enumTypes[0].Descriptor()
}

func (Environment) Type() protoreflect.EnumType {
	return &file_api_jarvis_workflow_service_proto_enumTypes[0]
}

func (x Environment) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Environment.Descriptor instead.
func (Environment) EnumDescriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{0}
}

type CreateNewTicketWorkflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	JarvisHeader  *jarvis.Header          `protobuf:"bytes,2,opt,name=jarvis_header,json=jarvisHeader,proto3" json:"jarvis_header,omitempty"`
	TicketForm    *frontend.Ticket        `protobuf:"bytes,3,opt,name=ticket_form,json=ticketForm,proto3" json:"ticket_form,omitempty"`
	TicketType    string                  `protobuf:"bytes,4,opt,name=ticket_type,json=ticketType,proto3" json:"ticket_type,omitempty"`
	FormId        string                  `protobuf:"bytes,5,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
}

func (x *CreateNewTicketWorkflowRequest) Reset() {
	*x = CreateNewTicketWorkflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNewTicketWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNewTicketWorkflowRequest) ProtoMessage() {}

func (x *CreateNewTicketWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNewTicketWorkflowRequest.ProtoReflect.Descriptor instead.
func (*CreateNewTicketWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateNewTicketWorkflowRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateNewTicketWorkflowRequest) GetJarvisHeader() *jarvis.Header {
	if x != nil {
		return x.JarvisHeader
	}
	return nil
}

func (x *CreateNewTicketWorkflowRequest) GetTicketForm() *frontend.Ticket {
	if x != nil {
		return x.TicketForm
	}
	return nil
}

func (x *CreateNewTicketWorkflowRequest) GetTicketType() string {
	if x != nil {
		return x.TicketType
	}
	return ""
}

func (x *CreateNewTicketWorkflowRequest) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

type CreateNewTicketWorkflowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	TicketForm     *frontend.Ticket         `protobuf:"bytes,2,opt,name=ticket_form,json=ticketForm,proto3" json:"ticket_form,omitempty"`
}

func (x *CreateNewTicketWorkflowResponse) Reset() {
	*x = CreateNewTicketWorkflowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNewTicketWorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNewTicketWorkflowResponse) ProtoMessage() {}

func (x *CreateNewTicketWorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNewTicketWorkflowResponse.ProtoReflect.Descriptor instead.
func (*CreateNewTicketWorkflowResponse) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateNewTicketWorkflowResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CreateNewTicketWorkflowResponse) GetTicketForm() *frontend.Ticket {
	if x != nil {
		return x.TicketForm
	}
	return nil
}

// NEW: Structured Jenkins job paths
type JenkinsJobPaths struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Server job paths (existing)
	ServerJobPath []string `protobuf:"bytes,1,rep,name=server_job_path,json=serverJobPath,proto3" json:"server_job_path,omitempty"`
	// Worker job paths (new structured approach)
	WorkerJobPaths []*WorkerJobPath `protobuf:"bytes,2,rep,name=worker_job_paths,json=workerJobPaths,proto3" json:"worker_job_paths,omitempty"`
}

func (x *JenkinsJobPaths) Reset() {
	*x = JenkinsJobPaths{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JenkinsJobPaths) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JenkinsJobPaths) ProtoMessage() {}

func (x *JenkinsJobPaths) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JenkinsJobPaths.ProtoReflect.Descriptor instead.
func (*JenkinsJobPaths) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{2}
}

func (x *JenkinsJobPaths) GetServerJobPath() []string {
	if x != nil {
		return x.ServerJobPath
	}
	return nil
}

func (x *JenkinsJobPaths) GetWorkerJobPaths() []*WorkerJobPath {
	if x != nil {
		return x.WorkerJobPaths
	}
	return nil
}

type WorkerJobPath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkerName         string  `protobuf:"bytes,1,opt,name=worker_name,json=workerName,proto3" json:"worker_name,omitempty"`                                 // "card-worker"
	BinaryBuildPath    string  `protobuf:"bytes,2,opt,name=binary_build_path,json=binaryBuildPath,proto3" json:"binary_build_path,omitempty"`                // "K8s/job/deploy/job/build/job/card-worker/"
	DeployPath         string  `protobuf:"bytes,3,opt,name=deploy_path,json=deployPath,proto3" json:"deploy_path,omitempty"`                                 // "K8s/job/qa/job/card-worker/" (environment specific)
	ImagePromotionPath *string `protobuf:"bytes,4,opt,name=image_promotion_path,json=imagePromotionPath,proto3,oneof" json:"image_promotion_path,omitempty"` // "K8s/job/promote-image/" (only for prod)
}

func (x *WorkerJobPath) Reset() {
	*x = WorkerJobPath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkerJobPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerJobPath) ProtoMessage() {}

func (x *WorkerJobPath) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerJobPath.ProtoReflect.Descriptor instead.
func (*WorkerJobPath) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{3}
}

func (x *WorkerJobPath) GetWorkerName() string {
	if x != nil {
		return x.WorkerName
	}
	return ""
}

func (x *WorkerJobPath) GetBinaryBuildPath() string {
	if x != nil {
		return x.BinaryBuildPath
	}
	return ""
}

func (x *WorkerJobPath) GetDeployPath() string {
	if x != nil {
		return x.DeployPath
	}
	return ""
}

func (x *WorkerJobPath) GetImagePromotionPath() string {
	if x != nil && x.ImagePromotionPath != nil {
		return *x.ImagePromotionPath
	}
	return ""
}

type DeploymentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Environment      Environment      `protobuf:"varint,1,opt,name=environment,proto3,enum=api.jarvis.workflow.Environment" json:"environment,omitempty"`
	CherryPickBranch string           `protobuf:"bytes,2,opt,name=cherry_pick_branch,json=cherryPickBranch,proto3" json:"cherry_pick_branch,omitempty"`
	JenkinsJobPaths  *JenkinsJobPaths `protobuf:"bytes,3,opt,name=jenkins_job_paths,json=jenkinsJobPaths,proto3" json:"jenkins_job_paths,omitempty"` // NEW: Structured job paths
}

func (x *DeploymentParams) Reset() {
	*x = DeploymentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentParams) ProtoMessage() {}

func (x *DeploymentParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentParams.ProtoReflect.Descriptor instead.
func (*DeploymentParams) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeploymentParams) GetEnvironment() Environment {
	if x != nil {
		return x.Environment
	}
	return Environment_ENV_UNSPECIFIED
}

func (x *DeploymentParams) GetCherryPickBranch() string {
	if x != nil {
		return x.CherryPickBranch
	}
	return ""
}

func (x *DeploymentParams) GetJenkinsJobPaths() *JenkinsJobPaths {
	if x != nil {
		return x.JenkinsJobPaths
	}
	return nil
}

type PRDeploymentWorkflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader    *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	JarvisHeader     *jarvis.Header          `protobuf:"bytes,2,opt,name=jarvis_header,json=jarvisHeader,proto3" json:"jarvis_header,omitempty"`
	PrNumber         int64                   `protobuf:"varint,3,opt,name=pr_number,json=prNumber,proto3" json:"pr_number,omitempty"`
	RepoName         string                  `protobuf:"bytes,4,opt,name=repo_name,json=repoName,proto3" json:"repo_name,omitempty"`
	DeploymentParams []*DeploymentParams     `protobuf:"bytes,5,rep,name=deployment_params,json=deploymentParams,proto3" json:"deployment_params,omitempty"`
	SlackChannelId   string                  `protobuf:"bytes,6,opt,name=slack_channel_id,json=slackChannelId,proto3" json:"slack_channel_id,omitempty"`
	UserEmailId      string                  `protobuf:"bytes,7,opt,name=user_email_id,json=userEmailId,proto3" json:"user_email_id,omitempty"`
	// Deprecated: Marked as deprecated in api/jarvis/workflow/service.proto.
	PrTitle *string `protobuf:"bytes,8,opt,name=pr_title,json=prTitle,proto3,oneof" json:"pr_title,omitempty"`
	// Deprecated: Marked as deprecated in api/jarvis/workflow/service.proto.
	MonorailTicket     *string             `protobuf:"bytes,9,opt,name=monorail_ticket,json=monorailTicket,proto3,oneof" json:"monorail_ticket,omitempty"`
	DeploymentMetadata *DeploymentMetadata `protobuf:"bytes,10,opt,name=deployment_metadata,json=deploymentMetadata,proto3" json:"deployment_metadata,omitempty"`
	Servers            []string            `protobuf:"bytes,11,rep,name=servers,proto3" json:"servers,omitempty"`
	Workers            []string            `protobuf:"bytes,12,rep,name=workers,proto3" json:"workers,omitempty"` // NEW: Workers list
}

func (x *PRDeploymentWorkflowRequest) Reset() {
	*x = PRDeploymentWorkflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PRDeploymentWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PRDeploymentWorkflowRequest) ProtoMessage() {}

func (x *PRDeploymentWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PRDeploymentWorkflowRequest.ProtoReflect.Descriptor instead.
func (*PRDeploymentWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{5}
}

func (x *PRDeploymentWorkflowRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *PRDeploymentWorkflowRequest) GetJarvisHeader() *jarvis.Header {
	if x != nil {
		return x.JarvisHeader
	}
	return nil
}

func (x *PRDeploymentWorkflowRequest) GetPrNumber() int64 {
	if x != nil {
		return x.PrNumber
	}
	return 0
}

func (x *PRDeploymentWorkflowRequest) GetRepoName() string {
	if x != nil {
		return x.RepoName
	}
	return ""
}

func (x *PRDeploymentWorkflowRequest) GetDeploymentParams() []*DeploymentParams {
	if x != nil {
		return x.DeploymentParams
	}
	return nil
}

func (x *PRDeploymentWorkflowRequest) GetSlackChannelId() string {
	if x != nil {
		return x.SlackChannelId
	}
	return ""
}

func (x *PRDeploymentWorkflowRequest) GetUserEmailId() string {
	if x != nil {
		return x.UserEmailId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/jarvis/workflow/service.proto.
func (x *PRDeploymentWorkflowRequest) GetPrTitle() string {
	if x != nil && x.PrTitle != nil {
		return *x.PrTitle
	}
	return ""
}

// Deprecated: Marked as deprecated in api/jarvis/workflow/service.proto.
func (x *PRDeploymentWorkflowRequest) GetMonorailTicket() string {
	if x != nil && x.MonorailTicket != nil {
		return *x.MonorailTicket
	}
	return ""
}

func (x *PRDeploymentWorkflowRequest) GetDeploymentMetadata() *DeploymentMetadata {
	if x != nil {
		return x.DeploymentMetadata
	}
	return nil
}

func (x *PRDeploymentWorkflowRequest) GetServers() []string {
	if x != nil {
		return x.Servers
	}
	return nil
}

func (x *PRDeploymentWorkflowRequest) GetWorkers() []string {
	if x != nil {
		return x.Workers
	}
	return nil
}

type PRDeploymentWorkflowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	Status         *rpc.Status              `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	WorkflowId     string                   `protobuf:"bytes,3,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
}

func (x *PRDeploymentWorkflowResponse) Reset() {
	*x = PRDeploymentWorkflowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PRDeploymentWorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PRDeploymentWorkflowResponse) ProtoMessage() {}

func (x *PRDeploymentWorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PRDeploymentWorkflowResponse.ProtoReflect.Descriptor instead.
func (*PRDeploymentWorkflowResponse) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{6}
}

func (x *PRDeploymentWorkflowResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *PRDeploymentWorkflowResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *PRDeploymentWorkflowResponse) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type DeploymentMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrTitle           string `protobuf:"bytes,1,opt,name=pr_title,json=prTitle,proto3" json:"pr_title,omitempty"`
	MonorailTicket    string `protobuf:"bytes,2,opt,name=monorail_ticket,json=monorailTicket,proto3" json:"monorail_ticket,omitempty"`
	ChangeType        string `protobuf:"bytes,3,opt,name=change_type,json=changeType,proto3" json:"change_type,omitempty"`
	VisibilityType    string `protobuf:"bytes,4,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	QaTestingRequired bool   `protobuf:"varint,5,opt,name=qa_testing_required,json=qaTestingRequired,proto3" json:"qa_testing_required,omitempty"`
	DevTested         bool   `protobuf:"varint,6,opt,name=dev_tested,json=devTested,proto3" json:"dev_tested,omitempty"`
}

func (x *DeploymentMetadata) Reset() {
	*x = DeploymentMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_jarvis_workflow_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeploymentMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeploymentMetadata) ProtoMessage() {}

func (x *DeploymentMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_jarvis_workflow_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeploymentMetadata.ProtoReflect.Descriptor instead.
func (*DeploymentMetadata) Descriptor() ([]byte, []int) {
	return file_api_jarvis_workflow_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeploymentMetadata) GetPrTitle() string {
	if x != nil {
		return x.PrTitle
	}
	return ""
}

func (x *DeploymentMetadata) GetMonorailTicket() string {
	if x != nil {
		return x.MonorailTicket
	}
	return ""
}

func (x *DeploymentMetadata) GetChangeType() string {
	if x != nil {
		return x.ChangeType
	}
	return ""
}

func (x *DeploymentMetadata) GetVisibilityType() string {
	if x != nil {
		return x.VisibilityType
	}
	return ""
}

func (x *DeploymentMetadata) GetQaTestingRequired() bool {
	if x != nil {
		return x.QaTestingRequired
	}
	return false
}

func (x *DeploymentMetadata) GetDevTested() bool {
	if x != nil {
		return x.DevTested
	}
	return false
}

var File_api_jarvis_workflow_service_proto protoreflect.FileDescriptor

var file_api_jarvis_workflow_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61,
	0x70, 0x69, 0x2f, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73,
	0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x97, 0x02, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65,
	0x77, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x33, 0x0a, 0x0d, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0c, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46,
	0x6f, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x22, 0xac, 0x01,
	0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c,
	0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x22, 0x87, 0x01, 0x0a,
	0x0f, 0x4a, 0x65, 0x6e, 0x6b, 0x69, 0x6e, 0x73, 0x4a, 0x6f, 0x62, 0x50, 0x61, 0x74, 0x68, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4a, 0x6f, 0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x4c, 0x0a, 0x10, 0x77, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x4a,
	0x6f, 0x62, 0x50, 0x61, 0x74, 0x68, 0x52, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x4a, 0x6f,
	0x62, 0x50, 0x61, 0x74, 0x68, 0x73, 0x22, 0xcd, 0x01, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x4a, 0x6f, 0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x69, 0x6e,
	0x61, 0x72, 0x79, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x35, 0x0a, 0x14, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x22, 0xd6, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x65,
	0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x63, 0x68, 0x65, 0x72, 0x72, 0x79, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x65,
	0x72, 0x72, 0x79, 0x50, 0x69, 0x63, 0x6b, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x50, 0x0a,
	0x11, 0x6a, 0x65, 0x6e, 0x6b, 0x69, 0x6e, 0x73, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a,
	0x61, 0x72, 0x76, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x4a,
	0x65, 0x6e, 0x6b, 0x69, 0x6e, 0x73, 0x4a, 0x6f, 0x62, 0x50, 0x61, 0x74, 0x68, 0x73, 0x52, 0x0f,
	0x6a, 0x65, 0x6e, 0x6b, 0x69, 0x6e, 0x73, 0x4a, 0x6f, 0x62, 0x50, 0x61, 0x74, 0x68, 0x73, 0x22,
	0xfd, 0x04, 0x0a, 0x1b, 0x50, 0x52, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x0d, 0x6a, 0x61, 0x72,
	0x76, 0x69, 0x73, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0c, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x70, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x70, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x10, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x10,
	0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x70, 0x72,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30,
	0x0a, 0x0f, 0x6d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x0e, 0x6d,
	0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x58, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x12, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x77, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x70, 0x72, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x6d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22,
	0xb1, 0x01, 0x0a, 0x1c, 0x50, 0x52, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x22, 0xf1, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69,
	0x6c, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x27, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x71, 0x61, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x71, 0x61, 0x54, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x65,
	0x76, 0x54, 0x65, 0x73, 0x74, 0x65, 0x64, 0x2a, 0x3c, 0x0a, 0x0b, 0x45, 0x6e, 0x76, 0x69, 0x72,
	0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x4e, 0x56, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45,
	0x4e, 0x56, 0x5f, 0x51, 0x41, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x4e, 0x56, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x10, 0x02, 0x42, 0x2c, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x6a, 0x61, 0x72, 0x76, 0x69, 0x73, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_jarvis_workflow_service_proto_rawDescOnce sync.Once
	file_api_jarvis_workflow_service_proto_rawDescData = file_api_jarvis_workflow_service_proto_rawDesc
)

func file_api_jarvis_workflow_service_proto_rawDescGZIP() []byte {
	file_api_jarvis_workflow_service_proto_rawDescOnce.Do(func() {
		file_api_jarvis_workflow_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_jarvis_workflow_service_proto_rawDescData)
	})
	return file_api_jarvis_workflow_service_proto_rawDescData
}

var file_api_jarvis_workflow_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_jarvis_workflow_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_jarvis_workflow_service_proto_goTypes = []interface{}{
	(Environment)(0),                        // 0: api.jarvis.workflow.Environment
	(*CreateNewTicketWorkflowRequest)(nil),  // 1: api.jarvis.workflow.CreateNewTicketWorkflowRequest
	(*CreateNewTicketWorkflowResponse)(nil), // 2: api.jarvis.workflow.CreateNewTicketWorkflowResponse
	(*JenkinsJobPaths)(nil),                 // 3: api.jarvis.workflow.JenkinsJobPaths
	(*WorkerJobPath)(nil),                   // 4: api.jarvis.workflow.WorkerJobPath
	(*DeploymentParams)(nil),                // 5: api.jarvis.workflow.DeploymentParams
	(*PRDeploymentWorkflowRequest)(nil),     // 6: api.jarvis.workflow.PRDeploymentWorkflowRequest
	(*PRDeploymentWorkflowResponse)(nil),    // 7: api.jarvis.workflow.PRDeploymentWorkflowResponse
	(*DeploymentMetadata)(nil),              // 8: api.jarvis.workflow.DeploymentMetadata
	(*workflow.RequestHeader)(nil),          // 9: celestial.workflow.RequestHeader
	(*jarvis.Header)(nil),                   // 10: jarvis.Header
	(*frontend.Ticket)(nil),                 // 11: api.jarvis.frontend.Ticket
	(*workflow.ResponseHeader)(nil),         // 12: celestial.workflow.ResponseHeader
	(*rpc.Status)(nil),                      // 13: rpc.Status
}
var file_api_jarvis_workflow_service_proto_depIdxs = []int32{
	9,  // 0: api.jarvis.workflow.CreateNewTicketWorkflowRequest.request_header:type_name -> celestial.workflow.RequestHeader
	10, // 1: api.jarvis.workflow.CreateNewTicketWorkflowRequest.jarvis_header:type_name -> jarvis.Header
	11, // 2: api.jarvis.workflow.CreateNewTicketWorkflowRequest.ticket_form:type_name -> api.jarvis.frontend.Ticket
	12, // 3: api.jarvis.workflow.CreateNewTicketWorkflowResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	11, // 4: api.jarvis.workflow.CreateNewTicketWorkflowResponse.ticket_form:type_name -> api.jarvis.frontend.Ticket
	4,  // 5: api.jarvis.workflow.JenkinsJobPaths.worker_job_paths:type_name -> api.jarvis.workflow.WorkerJobPath
	0,  // 6: api.jarvis.workflow.DeploymentParams.environment:type_name -> api.jarvis.workflow.Environment
	3,  // 7: api.jarvis.workflow.DeploymentParams.jenkins_job_paths:type_name -> api.jarvis.workflow.JenkinsJobPaths
	9,  // 8: api.jarvis.workflow.PRDeploymentWorkflowRequest.request_header:type_name -> celestial.workflow.RequestHeader
	10, // 9: api.jarvis.workflow.PRDeploymentWorkflowRequest.jarvis_header:type_name -> jarvis.Header
	5,  // 10: api.jarvis.workflow.PRDeploymentWorkflowRequest.deployment_params:type_name -> api.jarvis.workflow.DeploymentParams
	8,  // 11: api.jarvis.workflow.PRDeploymentWorkflowRequest.deployment_metadata:type_name -> api.jarvis.workflow.DeploymentMetadata
	12, // 12: api.jarvis.workflow.PRDeploymentWorkflowResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	13, // 13: api.jarvis.workflow.PRDeploymentWorkflowResponse.status:type_name -> rpc.Status
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_jarvis_workflow_service_proto_init() }
func file_api_jarvis_workflow_service_proto_init() {
	if File_api_jarvis_workflow_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_jarvis_workflow_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNewTicketWorkflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNewTicketWorkflowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JenkinsJobPaths); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkerJobPath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PRDeploymentWorkflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PRDeploymentWorkflowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_jarvis_workflow_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeploymentMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_jarvis_workflow_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_api_jarvis_workflow_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_jarvis_workflow_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_jarvis_workflow_service_proto_goTypes,
		DependencyIndexes: file_api_jarvis_workflow_service_proto_depIdxs,
		EnumInfos:         file_api_jarvis_workflow_service_proto_enumTypes,
		MessageInfos:      file_api_jarvis_workflow_service_proto_msgTypes,
	}.Build()
	File_api_jarvis_workflow_service_proto = out.File
	file_api_jarvis_workflow_service_proto_rawDesc = nil
	file_api_jarvis_workflow_service_proto_goTypes = nil
	file_api_jarvis_workflow_service_proto_depIdxs = nil
}
