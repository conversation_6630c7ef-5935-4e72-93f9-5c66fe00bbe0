// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/jarvis/workflow/service.proto

package workflow

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateNewTicketWorkflowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNewTicketWorkflowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNewTicketWorkflowRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateNewTicketWorkflowRequestMultiError, or nil if none found.
func (m *CreateNewTicketWorkflowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNewTicketWorkflowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewTicketWorkflowRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJarvisHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowRequestValidationError{
					field:  "JarvisHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowRequestValidationError{
					field:  "JarvisHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJarvisHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewTicketWorkflowRequestValidationError{
				field:  "JarvisHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowRequestValidationError{
					field:  "TicketForm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowRequestValidationError{
					field:  "TicketForm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewTicketWorkflowRequestValidationError{
				field:  "TicketForm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TicketType

	// no validation rules for FormId

	if len(errors) > 0 {
		return CreateNewTicketWorkflowRequestMultiError(errors)
	}

	return nil
}

// CreateNewTicketWorkflowRequestMultiError is an error wrapping multiple
// validation errors returned by CreateNewTicketWorkflowRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateNewTicketWorkflowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNewTicketWorkflowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNewTicketWorkflowRequestMultiError) AllErrors() []error { return m }

// CreateNewTicketWorkflowRequestValidationError is the validation error
// returned by CreateNewTicketWorkflowRequest.Validate if the designated
// constraints aren't met.
type CreateNewTicketWorkflowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNewTicketWorkflowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNewTicketWorkflowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNewTicketWorkflowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNewTicketWorkflowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNewTicketWorkflowRequestValidationError) ErrorName() string {
	return "CreateNewTicketWorkflowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNewTicketWorkflowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNewTicketWorkflowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNewTicketWorkflowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNewTicketWorkflowRequestValidationError{}

// Validate checks the field values on CreateNewTicketWorkflowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNewTicketWorkflowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNewTicketWorkflowResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateNewTicketWorkflowResponseMultiError, or nil if none found.
func (m *CreateNewTicketWorkflowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNewTicketWorkflowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewTicketWorkflowResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketForm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowResponseValidationError{
					field:  "TicketForm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNewTicketWorkflowResponseValidationError{
					field:  "TicketForm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketForm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNewTicketWorkflowResponseValidationError{
				field:  "TicketForm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNewTicketWorkflowResponseMultiError(errors)
	}

	return nil
}

// CreateNewTicketWorkflowResponseMultiError is an error wrapping multiple
// validation errors returned by CreateNewTicketWorkflowResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateNewTicketWorkflowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNewTicketWorkflowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNewTicketWorkflowResponseMultiError) AllErrors() []error { return m }

// CreateNewTicketWorkflowResponseValidationError is the validation error
// returned by CreateNewTicketWorkflowResponse.Validate if the designated
// constraints aren't met.
type CreateNewTicketWorkflowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNewTicketWorkflowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNewTicketWorkflowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNewTicketWorkflowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNewTicketWorkflowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNewTicketWorkflowResponseValidationError) ErrorName() string {
	return "CreateNewTicketWorkflowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNewTicketWorkflowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNewTicketWorkflowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNewTicketWorkflowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNewTicketWorkflowResponseValidationError{}

// Validate checks the field values on JenkinsJobPaths with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *JenkinsJobPaths) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JenkinsJobPaths with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JenkinsJobPathsMultiError, or nil if none found.
func (m *JenkinsJobPaths) ValidateAll() error {
	return m.validate(true)
}

func (m *JenkinsJobPaths) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWorkerJobPaths() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JenkinsJobPathsValidationError{
						field:  fmt.Sprintf("WorkerJobPaths[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JenkinsJobPathsValidationError{
						field:  fmt.Sprintf("WorkerJobPaths[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JenkinsJobPathsValidationError{
					field:  fmt.Sprintf("WorkerJobPaths[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return JenkinsJobPathsMultiError(errors)
	}

	return nil
}

// JenkinsJobPathsMultiError is an error wrapping multiple validation errors
// returned by JenkinsJobPaths.ValidateAll() if the designated constraints
// aren't met.
type JenkinsJobPathsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JenkinsJobPathsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JenkinsJobPathsMultiError) AllErrors() []error { return m }

// JenkinsJobPathsValidationError is the validation error returned by
// JenkinsJobPaths.Validate if the designated constraints aren't met.
type JenkinsJobPathsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JenkinsJobPathsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JenkinsJobPathsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JenkinsJobPathsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JenkinsJobPathsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JenkinsJobPathsValidationError) ErrorName() string { return "JenkinsJobPathsValidationError" }

// Error satisfies the builtin error interface
func (e JenkinsJobPathsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJenkinsJobPaths.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JenkinsJobPathsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JenkinsJobPathsValidationError{}

// Validate checks the field values on WorkerJobPath with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WorkerJobPath) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WorkerJobPath with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WorkerJobPathMultiError, or
// nil if none found.
func (m *WorkerJobPath) ValidateAll() error {
	return m.validate(true)
}

func (m *WorkerJobPath) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkerName

	// no validation rules for BinaryBuildPath

	// no validation rules for DeployPath

	if m.ImagePromotionPath != nil {
		// no validation rules for ImagePromotionPath
	}

	if len(errors) > 0 {
		return WorkerJobPathMultiError(errors)
	}

	return nil
}

// WorkerJobPathMultiError is an error wrapping multiple validation errors
// returned by WorkerJobPath.ValidateAll() if the designated constraints
// aren't met.
type WorkerJobPathMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WorkerJobPathMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WorkerJobPathMultiError) AllErrors() []error { return m }

// WorkerJobPathValidationError is the validation error returned by
// WorkerJobPath.Validate if the designated constraints aren't met.
type WorkerJobPathValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WorkerJobPathValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WorkerJobPathValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WorkerJobPathValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WorkerJobPathValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WorkerJobPathValidationError) ErrorName() string { return "WorkerJobPathValidationError" }

// Error satisfies the builtin error interface
func (e WorkerJobPathValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWorkerJobPath.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WorkerJobPathValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WorkerJobPathValidationError{}

// Validate checks the field values on DeploymentParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeploymentParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeploymentParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeploymentParamsMultiError, or nil if none found.
func (m *DeploymentParams) ValidateAll() error {
	return m.validate(true)
}

func (m *DeploymentParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Environment

	// no validation rules for CherryPickBranch

	if all {
		switch v := interface{}(m.GetJenkinsJobPaths()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeploymentParamsValidationError{
					field:  "JenkinsJobPaths",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeploymentParamsValidationError{
					field:  "JenkinsJobPaths",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJenkinsJobPaths()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeploymentParamsValidationError{
				field:  "JenkinsJobPaths",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeploymentParamsMultiError(errors)
	}

	return nil
}

// DeploymentParamsMultiError is an error wrapping multiple validation errors
// returned by DeploymentParams.ValidateAll() if the designated constraints
// aren't met.
type DeploymentParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeploymentParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeploymentParamsMultiError) AllErrors() []error { return m }

// DeploymentParamsValidationError is the validation error returned by
// DeploymentParams.Validate if the designated constraints aren't met.
type DeploymentParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeploymentParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeploymentParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeploymentParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeploymentParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeploymentParamsValidationError) ErrorName() string { return "DeploymentParamsValidationError" }

// Error satisfies the builtin error interface
func (e DeploymentParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeploymentParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeploymentParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeploymentParamsValidationError{}

// Validate checks the field values on PRDeploymentWorkflowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PRDeploymentWorkflowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PRDeploymentWorkflowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PRDeploymentWorkflowRequestMultiError, or nil if none found.
func (m *PRDeploymentWorkflowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PRDeploymentWorkflowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PRDeploymentWorkflowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PRDeploymentWorkflowRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PRDeploymentWorkflowRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJarvisHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PRDeploymentWorkflowRequestValidationError{
					field:  "JarvisHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PRDeploymentWorkflowRequestValidationError{
					field:  "JarvisHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJarvisHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PRDeploymentWorkflowRequestValidationError{
				field:  "JarvisHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrNumber

	// no validation rules for RepoName

	for idx, item := range m.GetDeploymentParams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PRDeploymentWorkflowRequestValidationError{
						field:  fmt.Sprintf("DeploymentParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PRDeploymentWorkflowRequestValidationError{
						field:  fmt.Sprintf("DeploymentParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PRDeploymentWorkflowRequestValidationError{
					field:  fmt.Sprintf("DeploymentParams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SlackChannelId

	// no validation rules for UserEmailId

	if all {
		switch v := interface{}(m.GetDeploymentMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PRDeploymentWorkflowRequestValidationError{
					field:  "DeploymentMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PRDeploymentWorkflowRequestValidationError{
					field:  "DeploymentMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeploymentMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PRDeploymentWorkflowRequestValidationError{
				field:  "DeploymentMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrTitle != nil {
		// no validation rules for PrTitle
	}

	if m.MonorailTicket != nil {
		// no validation rules for MonorailTicket
	}

	if len(errors) > 0 {
		return PRDeploymentWorkflowRequestMultiError(errors)
	}

	return nil
}

// PRDeploymentWorkflowRequestMultiError is an error wrapping multiple
// validation errors returned by PRDeploymentWorkflowRequest.ValidateAll() if
// the designated constraints aren't met.
type PRDeploymentWorkflowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PRDeploymentWorkflowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PRDeploymentWorkflowRequestMultiError) AllErrors() []error { return m }

// PRDeploymentWorkflowRequestValidationError is the validation error returned
// by PRDeploymentWorkflowRequest.Validate if the designated constraints
// aren't met.
type PRDeploymentWorkflowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PRDeploymentWorkflowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PRDeploymentWorkflowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PRDeploymentWorkflowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PRDeploymentWorkflowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PRDeploymentWorkflowRequestValidationError) ErrorName() string {
	return "PRDeploymentWorkflowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PRDeploymentWorkflowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPRDeploymentWorkflowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PRDeploymentWorkflowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PRDeploymentWorkflowRequestValidationError{}

// Validate checks the field values on PRDeploymentWorkflowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PRDeploymentWorkflowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PRDeploymentWorkflowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PRDeploymentWorkflowResponseMultiError, or nil if none found.
func (m *PRDeploymentWorkflowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PRDeploymentWorkflowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PRDeploymentWorkflowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PRDeploymentWorkflowResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PRDeploymentWorkflowResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PRDeploymentWorkflowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PRDeploymentWorkflowResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PRDeploymentWorkflowResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkflowId

	if len(errors) > 0 {
		return PRDeploymentWorkflowResponseMultiError(errors)
	}

	return nil
}

// PRDeploymentWorkflowResponseMultiError is an error wrapping multiple
// validation errors returned by PRDeploymentWorkflowResponse.ValidateAll() if
// the designated constraints aren't met.
type PRDeploymentWorkflowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PRDeploymentWorkflowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PRDeploymentWorkflowResponseMultiError) AllErrors() []error { return m }

// PRDeploymentWorkflowResponseValidationError is the validation error returned
// by PRDeploymentWorkflowResponse.Validate if the designated constraints
// aren't met.
type PRDeploymentWorkflowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PRDeploymentWorkflowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PRDeploymentWorkflowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PRDeploymentWorkflowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PRDeploymentWorkflowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PRDeploymentWorkflowResponseValidationError) ErrorName() string {
	return "PRDeploymentWorkflowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PRDeploymentWorkflowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPRDeploymentWorkflowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PRDeploymentWorkflowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PRDeploymentWorkflowResponseValidationError{}

// Validate checks the field values on DeploymentMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeploymentMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeploymentMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeploymentMetadataMultiError, or nil if none found.
func (m *DeploymentMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DeploymentMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrTitle

	// no validation rules for MonorailTicket

	// no validation rules for ChangeType

	// no validation rules for VisibilityType

	// no validation rules for QaTestingRequired

	// no validation rules for DevTested

	if len(errors) > 0 {
		return DeploymentMetadataMultiError(errors)
	}

	return nil
}

// DeploymentMetadataMultiError is an error wrapping multiple validation errors
// returned by DeploymentMetadata.ValidateAll() if the designated constraints
// aren't met.
type DeploymentMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeploymentMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeploymentMetadataMultiError) AllErrors() []error { return m }

// DeploymentMetadataValidationError is the validation error returned by
// DeploymentMetadata.Validate if the designated constraints aren't met.
type DeploymentMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeploymentMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeploymentMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeploymentMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeploymentMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeploymentMetadataValidationError) ErrorName() string {
	return "DeploymentMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e DeploymentMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeploymentMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeploymentMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeploymentMetadataValidationError{}
