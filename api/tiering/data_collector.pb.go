// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/tiering/data_collector.proto

package tiering

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	kyc "github.com/epifi/gamma/api/kyc"
	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
	enums "github.com/epifi/gamma/api/salaryprogram/enums"
	enums1 "github.com/epifi/gamma/api/tiering/enums"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// For all new QC, include data response type
// And also a new value to be added in QualifyingCriteriaType enum
type CollectedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//	*CollectedData_BalanceData
	//	*CollectedData_KycData
	//	*CollectedData_SalaryData
	//	*CollectedData_BaseTierData
	//	*CollectedData_UsStocksData
	//	*CollectedData_DepositsData
	Data isCollectedData_Data `protobuf_oneof:"data"`
}

func (x *CollectedData) Reset() {
	*x = CollectedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectedData) ProtoMessage() {}

func (x *CollectedData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectedData.ProtoReflect.Descriptor instead.
func (*CollectedData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{0}
}

func (m *CollectedData) GetData() isCollectedData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *CollectedData) GetBalanceData() *BalanceData {
	if x, ok := x.GetData().(*CollectedData_BalanceData); ok {
		return x.BalanceData
	}
	return nil
}

func (x *CollectedData) GetKycData() *KycData {
	if x, ok := x.GetData().(*CollectedData_KycData); ok {
		return x.KycData
	}
	return nil
}

func (x *CollectedData) GetSalaryData() *SalaryData {
	if x, ok := x.GetData().(*CollectedData_SalaryData); ok {
		return x.SalaryData
	}
	return nil
}

func (x *CollectedData) GetBaseTierData() *BaseTierData {
	if x, ok := x.GetData().(*CollectedData_BaseTierData); ok {
		return x.BaseTierData
	}
	return nil
}

func (x *CollectedData) GetUsStocksData() *UsStocksData {
	if x, ok := x.GetData().(*CollectedData_UsStocksData); ok {
		return x.UsStocksData
	}
	return nil
}

func (x *CollectedData) GetDepositsData() *DepositsData {
	if x, ok := x.GetData().(*CollectedData_DepositsData); ok {
		return x.DepositsData
	}
	return nil
}

type isCollectedData_Data interface {
	isCollectedData_Data()
}

type CollectedData_BalanceData struct {
	BalanceData *BalanceData `protobuf:"bytes,1,opt,name=balance_data,json=balanceData,proto3,oneof"`
}

type CollectedData_KycData struct {
	KycData *KycData `protobuf:"bytes,2,opt,name=kyc_data,json=kycData,proto3,oneof"`
}

type CollectedData_SalaryData struct {
	SalaryData *SalaryData `protobuf:"bytes,3,opt,name=salary_data,json=salaryData,proto3,oneof"`
}

type CollectedData_BaseTierData struct {
	BaseTierData *BaseTierData `protobuf:"bytes,4,opt,name=base_tier_data,json=baseTierData,proto3,oneof"`
}

type CollectedData_UsStocksData struct {
	UsStocksData *UsStocksData `protobuf:"bytes,5,opt,name=us_stocks_data,json=usStocksData,proto3,oneof"`
}

type CollectedData_DepositsData struct {
	DepositsData *DepositsData `protobuf:"bytes,6,opt,name=deposits_data,json=depositsData,proto3,oneof"`
}

func (*CollectedData_BalanceData) isCollectedData_Data() {}

func (*CollectedData_KycData) isCollectedData_Data() {}

func (*CollectedData_SalaryData) isCollectedData_Data() {}

func (*CollectedData_BaseTierData) isCollectedData_Data() {}

func (*CollectedData_UsStocksData) isCollectedData_Data() {}

func (*CollectedData_DepositsData) isCollectedData_Data() {}

// Data collected from savings service
type BalanceData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// current balance of user
	AvailableBalance *money.Money `protobuf:"bytes,1,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// Ledger balance of the account (Opening balance on the given day)
	LedgerBalance *money.Money `protobuf:"bytes,2,opt,name=ledger_balance,json=ledgerBalance,proto3" json:"ledger_balance,omitempty"`
	// Timestamp for which the given balance was calculated
	BalanceAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=balance_at,json=balanceAt,proto3" json:"balance_at,omitempty"`
}

func (x *BalanceData) Reset() {
	*x = BalanceData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceData) ProtoMessage() {}

func (x *BalanceData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceData.ProtoReflect.Descriptor instead.
func (*BalanceData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{1}
}

func (x *BalanceData) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *BalanceData) GetLedgerBalance() *money.Money {
	if x != nil {
		return x.LedgerBalance
	}
	return nil
}

func (x *BalanceData) GetBalanceAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceAt
	}
	return nil
}

// Data collected from kyc service
type KycData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// KYC done by user can either be min kyc or full kyc.
	KycLevel    kyc.KYCLevel    `protobuf:"varint,1,opt,name=kyc_level,json=kycLevel,proto3,enum=kyc.KYCLevel" json:"kyc_level,omitempty"`
	KycProvider kyc.KycProvider `protobuf:"varint,2,opt,name=kyc_provider,json=kycProvider,proto3,enum=kyc.KycProvider" json:"kyc_provider,omitempty"`
	// To identify the type of KYC in progress
	KycType kyc.KycType `protobuf:"varint,3,opt,name=kyc_type,json=kycType,proto3,enum=kyc.KycType" json:"kyc_type,omitempty"`
	// This field is used by caller to evaluate the next action for the client.
	KycStatus kyc.KycStatus `protobuf:"varint,4,opt,name=kyc_status,json=kycStatus,proto3,enum=kyc.KycStatus" json:"kyc_status,omitempty"`
}

func (x *KycData) Reset() {
	*x = KycData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycData) ProtoMessage() {}

func (x *KycData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycData.ProtoReflect.Descriptor instead.
func (*KycData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{2}
}

func (x *KycData) GetKycLevel() kyc.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return kyc.KYCLevel(0)
}

func (x *KycData) GetKycProvider() kyc.KycProvider {
	if x != nil {
		return x.KycProvider
	}
	return kyc.KycProvider(0)
}

func (x *KycData) GetKycType() kyc.KycType {
	if x != nil {
		return x.KycType
	}
	return kyc.KycType(0)
}

func (x *KycData) GetKycStatus() kyc.KycStatus {
	if x != nil {
		return x.KycStatus
	}
	return kyc.KycStatus(0)
}

// Data collected from salary program service
type SalaryData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boolean field to indicate if the user is salaried or not
	//
	// Deprecated: Marked as deprecated in api/tiering/data_collector.proto.
	IsSalaried common.BooleanEnum `protobuf:"varint,1,opt,name=is_salaried,json=isSalaried,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_salaried,omitempty"`
	// min amount required for salary txn detection.
	//
	// Deprecated: Marked as deprecated in api/tiering/data_collector.proto.
	MinReqAmountForSalary *money.Money `protobuf:"bytes,2,opt,name=min_req_amount_for_salary,json=minReqAmountForSalary,proto3" json:"min_req_amount_for_salary,omitempty"`
	// salary activation type of the user
	SalaryActivationType salaryprogram.SalaryActivationType `protobuf:"varint,3,opt,name=salary_activation_type,json=salaryActivationType,proto3,enum=salaryprogram.SalaryActivationType" json:"salary_activation_type,omitempty"`
	// salary band of the user
	SalaryBand enums.SalaryBand `protobuf:"varint,4,opt,name=salary_band,json=salaryBand,proto3,enum=salaryprogram.enums.SalaryBand" json:"salary_band,omitempty"`
	// flag to indicate if the user is a b2b user
	B2BSalaryProgramVerificationStatus bool `protobuf:"varint,5,opt,name=b2b_salary_program_verification_status,json=b2bSalaryProgramVerificationStatus,proto3" json:"b2b_salary_program_verification_status,omitempty"`
	// b2b salary band of the user
	B2BSalaryBand enums.B2BSalaryBand `protobuf:"varint,6,opt,name=b2b_salary_band,json=b2bSalaryBand,proto3,enum=salaryprogram.enums.B2BSalaryBand" json:"b2b_salary_band,omitempty"`
}

func (x *SalaryData) Reset() {
	*x = SalaryData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalaryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalaryData) ProtoMessage() {}

func (x *SalaryData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalaryData.ProtoReflect.Descriptor instead.
func (*SalaryData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{3}
}

// Deprecated: Marked as deprecated in api/tiering/data_collector.proto.
func (x *SalaryData) GetIsSalaried() common.BooleanEnum {
	if x != nil {
		return x.IsSalaried
	}
	return common.BooleanEnum(0)
}

// Deprecated: Marked as deprecated in api/tiering/data_collector.proto.
func (x *SalaryData) GetMinReqAmountForSalary() *money.Money {
	if x != nil {
		return x.MinReqAmountForSalary
	}
	return nil
}

func (x *SalaryData) GetSalaryActivationType() salaryprogram.SalaryActivationType {
	if x != nil {
		return x.SalaryActivationType
	}
	return salaryprogram.SalaryActivationType(0)
}

func (x *SalaryData) GetSalaryBand() enums.SalaryBand {
	if x != nil {
		return x.SalaryBand
	}
	return enums.SalaryBand(0)
}

func (x *SalaryData) GetB2BSalaryProgramVerificationStatus() bool {
	if x != nil {
		return x.B2BSalaryProgramVerificationStatus
	}
	return false
}

func (x *SalaryData) GetB2BSalaryBand() enums.B2BSalaryBand {
	if x != nil {
		return x.B2BSalaryBand
	}
	return enums.B2BSalaryBand(0)
}

type BaseTierData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// base tier of the user
	ActorBaseTier enums1.Tier `protobuf:"varint,1,opt,name=actor_base_tier,json=actorBaseTier,proto3,enum=tiering.enums.Tier" json:"actor_base_tier,omitempty"`
}

func (x *BaseTierData) Reset() {
	*x = BaseTierData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseTierData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseTierData) ProtoMessage() {}

func (x *BaseTierData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseTierData.ProtoReflect.Descriptor instead.
func (*BaseTierData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{4}
}

func (x *BaseTierData) GetActorBaseTier() enums1.Tier {
	if x != nil {
		return x.ActorBaseTier
	}
	return enums1.Tier(0)
}

type UsStocksData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount added in us stocks in last 30 days
	WalletAddFundsValueInL30D *money.Money `protobuf:"bytes,1,opt,name=wallet_add_funds_value_in_l30d,json=walletAddFundsValueInL30d,proto3" json:"wallet_add_funds_value_in_l30d,omitempty"`
}

func (x *UsStocksData) Reset() {
	*x = UsStocksData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsStocksData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsStocksData) ProtoMessage() {}

func (x *UsStocksData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsStocksData.ProtoReflect.Descriptor instead.
func (*UsStocksData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{5}
}

func (x *UsStocksData) GetWalletAddFundsValueInL30D() *money.Money {
	if x != nil {
		return x.WalletAddFundsValueInL30D
	}
	return nil
}

type DepositsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total SD and FD amount in user's account
	TotalDeposits *money.Money `protobuf:"bytes,1,opt,name=total_deposits,json=totalDeposits,proto3" json:"total_deposits,omitempty"`
}

func (x *DepositsData) Reset() {
	*x = DepositsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositsData) ProtoMessage() {}

func (x *DepositsData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositsData.ProtoReflect.Descriptor instead.
func (*DepositsData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{6}
}

func (x *DepositsData) GetTotalDeposits() *money.Money {
	if x != nil {
		return x.TotalDeposits
	}
	return nil
}

type SegmentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShouldExcludeSalaryB2CCriteria bool `protobuf:"varint,1,opt,name=should_exclude_salary_b2c_criteria,json=shouldExcludeSalaryB2cCriteria,proto3" json:"should_exclude_salary_b2c_criteria,omitempty"`
	ShouldExcludeAaSalaryCriteria  bool `protobuf:"varint,2,opt,name=should_exclude_aa_salary_criteria,json=shouldExcludeAaSalaryCriteria,proto3" json:"should_exclude_aa_salary_criteria,omitempty"`
}

func (x *SegmentData) Reset() {
	*x = SegmentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SegmentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SegmentData) ProtoMessage() {}

func (x *SegmentData) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SegmentData.ProtoReflect.Descriptor instead.
func (*SegmentData) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{7}
}

func (x *SegmentData) GetShouldExcludeSalaryB2CCriteria() bool {
	if x != nil {
		return x.ShouldExcludeSalaryB2CCriteria
	}
	return false
}

func (x *SegmentData) GetShouldExcludeAaSalaryCriteria() bool {
	if x != nil {
		return x.ShouldExcludeAaSalaryCriteria
	}
	return false
}

type GatherDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BalanceData  *BalanceData  `protobuf:"bytes,1,opt,name=balance_data,json=balanceData,proto3" json:"balance_data,omitempty"`
	KycData      *KycData      `protobuf:"bytes,2,opt,name=kyc_data,json=kycData,proto3" json:"kyc_data,omitempty"`
	SalaryData   *SalaryData   `protobuf:"bytes,3,opt,name=salary_data,json=salaryData,proto3" json:"salary_data,omitempty"`
	BaseTierData *BaseTierData `protobuf:"bytes,4,opt,name=base_tier_data,json=baseTierData,proto3" json:"base_tier_data,omitempty"`
	UsStocksData *UsStocksData `protobuf:"bytes,5,opt,name=us_stocks_data,json=usStocksData,proto3" json:"us_stocks_data,omitempty"`
	DepositsData *DepositsData `protobuf:"bytes,6,opt,name=deposits_data,json=depositsData,proto3" json:"deposits_data,omitempty"`
	SegmentData  *SegmentData  `protobuf:"bytes,7,opt,name=segment_data,json=segmentData,proto3" json:"segment_data,omitempty"`
}

func (x *GatherDataResponse) Reset() {
	*x = GatherDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatherDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatherDataResponse) ProtoMessage() {}

func (x *GatherDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatherDataResponse.ProtoReflect.Descriptor instead.
func (*GatherDataResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{8}
}

func (x *GatherDataResponse) GetBalanceData() *BalanceData {
	if x != nil {
		return x.BalanceData
	}
	return nil
}

func (x *GatherDataResponse) GetKycData() *KycData {
	if x != nil {
		return x.KycData
	}
	return nil
}

func (x *GatherDataResponse) GetSalaryData() *SalaryData {
	if x != nil {
		return x.SalaryData
	}
	return nil
}

func (x *GatherDataResponse) GetBaseTierData() *BaseTierData {
	if x != nil {
		return x.BaseTierData
	}
	return nil
}

func (x *GatherDataResponse) GetUsStocksData() *UsStocksData {
	if x != nil {
		return x.UsStocksData
	}
	return nil
}

func (x *GatherDataResponse) GetDepositsData() *DepositsData {
	if x != nil {
		return x.DepositsData
	}
	return nil
}

func (x *GatherDataResponse) GetSegmentData() *SegmentData {
	if x != nil {
		return x.SegmentData
	}
	return nil
}

// Deprecated: in favor of EvaluatorMeta
type TierOptionType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tier               enums1.Tier               `protobuf:"varint,1,opt,name=tier,proto3,enum=tiering.enums.Tier" json:"tier,omitempty"`
	CriteriaOptionType enums1.CriteriaOptionType `protobuf:"varint,2,opt,name=criteria_option_type,json=criteriaOptionType,proto3,enum=tiering.enums.CriteriaOptionType" json:"criteria_option_type,omitempty"`
}

func (x *TierOptionType) Reset() {
	*x = TierOptionType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_data_collector_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TierOptionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierOptionType) ProtoMessage() {}

func (x *TierOptionType) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_data_collector_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierOptionType.ProtoReflect.Descriptor instead.
func (*TierOptionType) Descriptor() ([]byte, []int) {
	return file_api_tiering_data_collector_proto_rawDescGZIP(), []int{9}
}

func (x *TierOptionType) GetTier() enums1.Tier {
	if x != nil {
		return x.Tier
	}
	return enums1.Tier(0)
}

func (x *TierOptionType) GetCriteriaOptionType() enums1.CriteriaOptionType {
	if x != nil {
		return x.CriteriaOptionType
	}
	return enums1.CriteriaOptionType(0)
}

var File_api_tiering_data_collector_proto protoreflect.FileDescriptor

var file_api_tiering_data_collector_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x07, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x1a, 0x11, 0x61, 0x70, 0x69,
	0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xf5, 0x02, 0x0a, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x39, 0x0a, 0x0c, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x0b, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a,
	0x08, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4b, 0x79, 0x63, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x0b,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x65,
	0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x54, 0x69, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x54, 0x69, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0e, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x73, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x75, 0x73, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xc4, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x74, 0x22,
	0xc2, 0x01, 0x0a, 0x07, 0x4b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x09, 0x6b,
	0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d,
	0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b,
	0x79, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x0c, 0x6b, 0x79, 0x63, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x6b, 0x79, 0x63, 0x2e, 0x4b, 0x79, 0x63, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52,
	0x0b, 0x6b, 0x79, 0x63, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x08,
	0x6b, 0x79, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c,
	0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4b, 0x79, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6b, 0x79,
	0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x0a, 0x6b, 0x79, 0x63, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x6b, 0x79, 0x63, 0x2e,
	0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6b, 0x79, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xe1, 0x03, 0x0a, 0x0a, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x69,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x69,
	0x73, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x69, 0x65, 0x64, 0x12, 0x50, 0x0a, 0x19, 0x6d, 0x69, 0x6e,
	0x5f, 0x72, 0x65, 0x71, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x15, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x12, 0x59, 0x0a, 0x16, 0x73,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x53, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x14, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x5f, 0x62, 0x61, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x64, 0x52, 0x0a, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x64, 0x12, 0x52, 0x0a, 0x26, 0x62, 0x32, 0x62, 0x5f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x22, 0x62, 0x32, 0x62, 0x53, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0f,
	0x62, 0x32, 0x62, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x42, 0x32, 0x42, 0x53,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x64, 0x52, 0x0d, 0x62, 0x32, 0x62, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x64, 0x22, 0x4b, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65,
	0x54, 0x69, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x61, 0x73,
	0x65, 0x54, 0x69, 0x65, 0x72, 0x22, 0x65, 0x0a, 0x0c, 0x55, 0x73, 0x53, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x1e, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x69, 0x6e, 0x5f, 0x6c, 0x33, 0x30, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x19, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64,
	0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x4c, 0x33, 0x30, 0x64, 0x22, 0x49, 0x0a, 0x0c,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0e,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x22, 0x73, 0x68, 0x6f, 0x75, 0x6c,
	0x64, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x5f, 0x62, 0x32, 0x63, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1e, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x45, 0x78, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x42, 0x32, 0x63, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x12, 0x48, 0x0a, 0x21, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x61, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f,
	0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1d,
	0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x61, 0x53,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x22, 0x9f, 0x03,
	0x0a, 0x12, 0x47, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0c, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x69, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0b, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x0a,
	0x08, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x4b, 0x79, 0x63, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x07, 0x6b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x0b, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x3b, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x54, 0x69, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0c, 0x62, 0x61, 0x73, 0x65, 0x54, 0x69, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a,
	0x0e, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x55, 0x73, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x75, 0x73,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x0d, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x8e, 0x01, 0x0a, 0x0e, 0x54, 0x69, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x14, 0x63,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_tiering_data_collector_proto_rawDescOnce sync.Once
	file_api_tiering_data_collector_proto_rawDescData = file_api_tiering_data_collector_proto_rawDesc
)

func file_api_tiering_data_collector_proto_rawDescGZIP() []byte {
	file_api_tiering_data_collector_proto_rawDescOnce.Do(func() {
		file_api_tiering_data_collector_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_tiering_data_collector_proto_rawDescData)
	})
	return file_api_tiering_data_collector_proto_rawDescData
}

var file_api_tiering_data_collector_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_tiering_data_collector_proto_goTypes = []interface{}{
	(*CollectedData)(nil),                   // 0: tiering.CollectedData
	(*BalanceData)(nil),                     // 1: tiering.BalanceData
	(*KycData)(nil),                         // 2: tiering.KycData
	(*SalaryData)(nil),                      // 3: tiering.SalaryData
	(*BaseTierData)(nil),                    // 4: tiering.BaseTierData
	(*UsStocksData)(nil),                    // 5: tiering.UsStocksData
	(*DepositsData)(nil),                    // 6: tiering.DepositsData
	(*SegmentData)(nil),                     // 7: tiering.SegmentData
	(*GatherDataResponse)(nil),              // 8: tiering.GatherDataResponse
	(*TierOptionType)(nil),                  // 9: tiering.TierOptionType
	(*money.Money)(nil),                     // 10: google.type.Money
	(*timestamppb.Timestamp)(nil),           // 11: google.protobuf.Timestamp
	(kyc.KYCLevel)(0),                       // 12: kyc.KYCLevel
	(kyc.KycProvider)(0),                    // 13: kyc.KycProvider
	(kyc.KycType)(0),                        // 14: kyc.KycType
	(kyc.KycStatus)(0),                      // 15: kyc.KycStatus
	(common.BooleanEnum)(0),                 // 16: api.typesv2.common.BooleanEnum
	(salaryprogram.SalaryActivationType)(0), // 17: salaryprogram.SalaryActivationType
	(enums.SalaryBand)(0),                   // 18: salaryprogram.enums.SalaryBand
	(enums.B2BSalaryBand)(0),                // 19: salaryprogram.enums.B2BSalaryBand
	(enums1.Tier)(0),                        // 20: tiering.enums.Tier
	(enums1.CriteriaOptionType)(0),          // 21: tiering.enums.CriteriaOptionType
}
var file_api_tiering_data_collector_proto_depIdxs = []int32{
	1,  // 0: tiering.CollectedData.balance_data:type_name -> tiering.BalanceData
	2,  // 1: tiering.CollectedData.kyc_data:type_name -> tiering.KycData
	3,  // 2: tiering.CollectedData.salary_data:type_name -> tiering.SalaryData
	4,  // 3: tiering.CollectedData.base_tier_data:type_name -> tiering.BaseTierData
	5,  // 4: tiering.CollectedData.us_stocks_data:type_name -> tiering.UsStocksData
	6,  // 5: tiering.CollectedData.deposits_data:type_name -> tiering.DepositsData
	10, // 6: tiering.BalanceData.available_balance:type_name -> google.type.Money
	10, // 7: tiering.BalanceData.ledger_balance:type_name -> google.type.Money
	11, // 8: tiering.BalanceData.balance_at:type_name -> google.protobuf.Timestamp
	12, // 9: tiering.KycData.kyc_level:type_name -> kyc.KYCLevel
	13, // 10: tiering.KycData.kyc_provider:type_name -> kyc.KycProvider
	14, // 11: tiering.KycData.kyc_type:type_name -> kyc.KycType
	15, // 12: tiering.KycData.kyc_status:type_name -> kyc.KycStatus
	16, // 13: tiering.SalaryData.is_salaried:type_name -> api.typesv2.common.BooleanEnum
	10, // 14: tiering.SalaryData.min_req_amount_for_salary:type_name -> google.type.Money
	17, // 15: tiering.SalaryData.salary_activation_type:type_name -> salaryprogram.SalaryActivationType
	18, // 16: tiering.SalaryData.salary_band:type_name -> salaryprogram.enums.SalaryBand
	19, // 17: tiering.SalaryData.b2b_salary_band:type_name -> salaryprogram.enums.B2BSalaryBand
	20, // 18: tiering.BaseTierData.actor_base_tier:type_name -> tiering.enums.Tier
	10, // 19: tiering.UsStocksData.wallet_add_funds_value_in_l30d:type_name -> google.type.Money
	10, // 20: tiering.DepositsData.total_deposits:type_name -> google.type.Money
	1,  // 21: tiering.GatherDataResponse.balance_data:type_name -> tiering.BalanceData
	2,  // 22: tiering.GatherDataResponse.kyc_data:type_name -> tiering.KycData
	3,  // 23: tiering.GatherDataResponse.salary_data:type_name -> tiering.SalaryData
	4,  // 24: tiering.GatherDataResponse.base_tier_data:type_name -> tiering.BaseTierData
	5,  // 25: tiering.GatherDataResponse.us_stocks_data:type_name -> tiering.UsStocksData
	6,  // 26: tiering.GatherDataResponse.deposits_data:type_name -> tiering.DepositsData
	7,  // 27: tiering.GatherDataResponse.segment_data:type_name -> tiering.SegmentData
	20, // 28: tiering.TierOptionType.tier:type_name -> tiering.enums.Tier
	21, // 29: tiering.TierOptionType.criteria_option_type:type_name -> tiering.enums.CriteriaOptionType
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_api_tiering_data_collector_proto_init() }
func file_api_tiering_data_collector_proto_init() {
	if File_api_tiering_data_collector_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_tiering_data_collector_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalaryData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseTierData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsStocksData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SegmentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatherDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_data_collector_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TierOptionType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_tiering_data_collector_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CollectedData_BalanceData)(nil),
		(*CollectedData_KycData)(nil),
		(*CollectedData_SalaryData)(nil),
		(*CollectedData_BaseTierData)(nil),
		(*CollectedData_UsStocksData)(nil),
		(*CollectedData_DepositsData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_tiering_data_collector_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_tiering_data_collector_proto_goTypes,
		DependencyIndexes: file_api_tiering_data_collector_proto_depIdxs,
		MessageInfos:      file_api_tiering_data_collector_proto_msgTypes,
	}.Build()
	File_api_tiering_data_collector_proto = out.File
	file_api_tiering_data_collector_proto_rawDesc = nil
	file_api_tiering_data_collector_proto_goTypes = nil
	file_api_tiering_data_collector_proto_depIdxs = nil
}
