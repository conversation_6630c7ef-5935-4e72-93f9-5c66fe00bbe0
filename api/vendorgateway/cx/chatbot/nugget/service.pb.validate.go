// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/cx/chatbot/nugget/service.proto

package nugget

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)
)

// Validate checks the field values on FetchAccessTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAccessTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAccessTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAccessTokenRequestMultiError, or nil if none found.
func (m *FetchAccessTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAccessTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for ClientRequestId

	// no validation rules for Platform

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchAccessTokenRequestMultiError(errors)
	}

	return nil
}

// FetchAccessTokenRequestMultiError is an error wrapping multiple validation
// errors returned by FetchAccessTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchAccessTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAccessTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAccessTokenRequestMultiError) AllErrors() []error { return m }

// FetchAccessTokenRequestValidationError is the validation error returned by
// FetchAccessTokenRequest.Validate if the designated constraints aren't met.
type FetchAccessTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAccessTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAccessTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAccessTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAccessTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAccessTokenRequestValidationError) ErrorName() string {
	return "FetchAccessTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAccessTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAccessTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAccessTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAccessTokenRequestValidationError{}

// Validate checks the field values on FetchAccessTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAccessTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAccessTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAccessTokenResponseMultiError, or nil if none found.
func (m *FetchAccessTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAccessTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return FetchAccessTokenResponseMultiError(errors)
	}

	return nil
}

// FetchAccessTokenResponseMultiError is an error wrapping multiple validation
// errors returned by FetchAccessTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchAccessTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAccessTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAccessTokenResponseMultiError) AllErrors() []error { return m }

// FetchAccessTokenResponseValidationError is the validation error returned by
// FetchAccessTokenResponse.Validate if the designated constraints aren't met.
type FetchAccessTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAccessTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAccessTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAccessTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAccessTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAccessTokenResponseValidationError) ErrorName() string {
	return "FetchAccessTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAccessTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAccessTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAccessTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAccessTokenResponseValidationError{}
