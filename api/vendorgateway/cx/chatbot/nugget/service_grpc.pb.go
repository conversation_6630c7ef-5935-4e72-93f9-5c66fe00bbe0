// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/cx/chatbot/nugget/service.proto

package nugget

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NuggetChatbotService_FetchAccessToken_FullMethodName = "/cx.chatbot.nugget.NuggetChatbotService/FetchAccessToken"
)

// NuggetChatbotServiceClient is the client API for NuggetChatbotService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NuggetChatbotServiceClient interface {
	FetchAccessToken(ctx context.Context, in *FetchAccessTokenRequest, opts ...grpc.CallOption) (*FetchAccessTokenResponse, error)
}

type nuggetChatbotServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNuggetChatbotServiceClient(cc grpc.ClientConnInterface) NuggetChatbotServiceClient {
	return &nuggetChatbotServiceClient{cc}
}

func (c *nuggetChatbotServiceClient) FetchAccessToken(ctx context.Context, in *FetchAccessTokenRequest, opts ...grpc.CallOption) (*FetchAccessTokenResponse, error) {
	out := new(FetchAccessTokenResponse)
	err := c.cc.Invoke(ctx, NuggetChatbotService_FetchAccessToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NuggetChatbotServiceServer is the server API for NuggetChatbotService service.
// All implementations should embed UnimplementedNuggetChatbotServiceServer
// for forward compatibility
type NuggetChatbotServiceServer interface {
	FetchAccessToken(context.Context, *FetchAccessTokenRequest) (*FetchAccessTokenResponse, error)
}

// UnimplementedNuggetChatbotServiceServer should be embedded to have forward compatible implementations.
type UnimplementedNuggetChatbotServiceServer struct {
}

func (UnimplementedNuggetChatbotServiceServer) FetchAccessToken(context.Context, *FetchAccessTokenRequest) (*FetchAccessTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchAccessToken not implemented")
}

// UnsafeNuggetChatbotServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NuggetChatbotServiceServer will
// result in compilation errors.
type UnsafeNuggetChatbotServiceServer interface {
	mustEmbedUnimplementedNuggetChatbotServiceServer()
}

func RegisterNuggetChatbotServiceServer(s grpc.ServiceRegistrar, srv NuggetChatbotServiceServer) {
	s.RegisterService(&********************************, srv)
}

func _NuggetChatbotService_FetchAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAccessTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NuggetChatbotServiceServer).FetchAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NuggetChatbotService_FetchAccessToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NuggetChatbotServiceServer).FetchAccessToken(ctx, req.(*FetchAccessTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ******************************** is the grpc.ServiceDesc for NuggetChatbotService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ******************************** = grpc.ServiceDesc{
	ServiceName: "cx.chatbot.nugget.NuggetChatbotService",
	HandlerType: (*NuggetChatbotServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FetchAccessToken",
			Handler:    _NuggetChatbotService_FetchAccessToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/cx/chatbot/nugget/service.proto",
}
