// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/cx/chatbot/nugget/service.proto

package nugget

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FetchAccessTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header          *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId         string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientRequestId string                       `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	Platform        common.Platform              `protobuf:"varint,4,opt,name=platform,proto3,enum=api.typesv2.common.Platform" json:"platform,omitempty"`
	Name            *common.Name                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Email           string                       `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber     *common.PhoneNumber          `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *FetchAccessTokenRequest) Reset() {
	*x = FetchAccessTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAccessTokenRequest) ProtoMessage() {}

func (x *FetchAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*FetchAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{0}
}

func (x *FetchAccessTokenRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FetchAccessTokenRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *FetchAccessTokenRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *FetchAccessTokenRequest) GetPlatform() common.Platform {
	if x != nil {
		return x.Platform
	}
	return common.Platform(0)
}

func (x *FetchAccessTokenRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *FetchAccessTokenRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *FetchAccessTokenRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

type FetchAccessTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AccessToken string      `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *FetchAccessTokenResponse) Reset() {
	*x = FetchAccessTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAccessTokenResponse) ProtoMessage() {}

func (x *FetchAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*FetchAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescGZIP(), []int{1}
}

func (x *FetchAccessTokenResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchAccessTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

var File_api_vendorgateway_cx_chatbot_nugget_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2f, 0x6e,
	0x75, 0x67, 0x67, 0x65, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e,
	0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x02, 0x0a, 0x17,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2c,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x62, 0x0a, 0x18, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0x83, 0x01, 0x0a, 0x14, 0x4e,
	0x75, 0x67, 0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x6b, 0x0a, 0x10, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x62, 0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74,
	0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x78, 0x0a, 0x3a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x5a, 0x3a,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x2f, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescData = file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDesc
)

func file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescData)
	})
	return file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDescData
}

var file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendorgateway_cx_chatbot_nugget_service_proto_goTypes = []interface{}{
	(*FetchAccessTokenRequest)(nil),     // 0: cx.chatbot.nugget.FetchAccessTokenRequest
	(*FetchAccessTokenResponse)(nil),    // 1: cx.chatbot.nugget.FetchAccessTokenResponse
	(*vendorgateway.RequestHeader)(nil), // 2: vendorgateway.RequestHeader
	(common.Platform)(0),                // 3: api.typesv2.common.Platform
	(*common.Name)(nil),                 // 4: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),          // 5: api.typesv2.common.PhoneNumber
	(*rpc.Status)(nil),                  // 6: rpc.Status
}
var file_api_vendorgateway_cx_chatbot_nugget_service_proto_depIdxs = []int32{
	2, // 0: cx.chatbot.nugget.FetchAccessTokenRequest.header:type_name -> vendorgateway.RequestHeader
	3, // 1: cx.chatbot.nugget.FetchAccessTokenRequest.platform:type_name -> api.typesv2.common.Platform
	4, // 2: cx.chatbot.nugget.FetchAccessTokenRequest.name:type_name -> api.typesv2.common.Name
	5, // 3: cx.chatbot.nugget.FetchAccessTokenRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	6, // 4: cx.chatbot.nugget.FetchAccessTokenResponse.status:type_name -> rpc.Status
	0, // 5: cx.chatbot.nugget.NuggetChatbotService.FetchAccessToken:input_type -> cx.chatbot.nugget.FetchAccessTokenRequest
	1, // 6: cx.chatbot.nugget.NuggetChatbotService.FetchAccessToken:output_type -> cx.chatbot.nugget.FetchAccessTokenResponse
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_cx_chatbot_nugget_service_proto_init() }
func file_api_vendorgateway_cx_chatbot_nugget_service_proto_init() {
	if File_api_vendorgateway_cx_chatbot_nugget_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAccessTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAccessTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_cx_chatbot_nugget_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_cx_chatbot_nugget_service_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_cx_chatbot_nugget_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_cx_chatbot_nugget_service_proto = out.File
	file_api_vendorgateway_cx_chatbot_nugget_service_proto_rawDesc = nil
	file_api_vendorgateway_cx_chatbot_nugget_service_proto_goTypes = nil
	file_api_vendorgateway_cx_chatbot_nugget_service_proto_depIdxs = nil
}
