package tiering

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/jonboulle/clockwork"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/frontend/tiering/display_names"

	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
)

// GetAMBHistory fetches and formats AMB history data
func GetAMBHistory(ctx context.Context, actorID string, tieringPinotClient tieringPinotPb.EODBalanceClient, tierTimeRangesMap map[string]*tieringPb.TimeRanges) ([]*tieringPb.AmbDetails, error) {
	if len(tierTimeRangesMap) == 0 {
		return nil, nil
	}

	// Create segments from tier time ranges
	segments := createMonthlySegments(tierTimeRangesMap)

	// Sort and limit segments
	segments = sortAndLimitSegments(segments)

	if len(segments) == 0 {
		return nil, nil
	}

	// --- Fetch Historical Data in Parallel ---
	var wg sync.WaitGroup
	var mu sync.Mutex // Mutex to protect concurrent writes to ambHistory
	ambHistory := make([]*tieringPb.AmbDetails, len(segments))
	collectedErrors := make([]error, 0, len(segments))
	errChan := make(chan error, len(segments))

	// Process each segment
	for i, segment := range segments {
		wg.Add(1)

		// Create copies for goroutine closure
		index := i
		segStartDate := segment.startDate
		segEndDate := segment.endDate
		segTier := segment.tier

		// Launch goroutine to fetch data for this segment
		goroutine.Run(ctx, 2*time.Second, func(gctx context.Context) {
			defer wg.Done()

			ambDetails, err := fetchSegmentData(gctx, actorID, monthSegment{
				startDate: segStartDate,
				endDate:   segEndDate,
				tier:      segTier,
			}, tieringPinotClient)

			if err != nil {
				errChan <- err
				return
			}

			// Add to history entries (protected by mutex)
			mu.Lock()
			ambHistory[index] = ambDetails
			mu.Unlock()
		})
	}

	// Wait for all goroutines to complete, with a timeout
	fetchedAllData := waitgroup.SafeWait(&wg, 3*time.Second) // Wait up to 3 seconds total

	// Close the error channel now that all producers (goroutines) are done.
	close(errChan)

	// Collect errors from the channel
	for chErr := range errChan {
		collectedErrors = append(collectedErrors, chErr)
	}

	// Check if timeout occurred
	if !fetchedAllData {
		collectedErrors = append(collectedErrors, errors.New("timeout occurred while fetching AMB history segments"))
	}

	// Check if there were any errors collected during processing
	if len(collectedErrors) > 0 {
		var combinedError error
		for _, cerr := range collectedErrors {
			if combinedError == nil {
				combinedError = cerr
			} else {
				combinedError = errors.Wrap(combinedError, cerr.Error())
			}
		}
		return nil, combinedError
	}

	// Filter out any nil entries
	finalHistory := make([]*tieringPb.AmbDetails, 0, len(ambHistory))
	for _, entry := range ambHistory {
		if entry != nil {
			finalHistory = append(finalHistory, entry)
		}
	}

	return finalHistory, nil
}

// monthSegment represents a time period for AMB calculation with associated tier
type monthSegment struct {
	startDate time.Time
	endDate   time.Time
	tier      external.Tier
}

// createMonthlySegments processes tier time ranges into monthly segments
func createMonthlySegments(tierTimeRangesMap map[string]*tieringPb.TimeRanges) []monthSegment {
	return createMonthlySegmentsWithClock(tierTimeRangesMap, clockwork.NewRealClock())
}

// createMonthlySegmentsWithClock processes tier time ranges into monthly segments using the provided clock
func createMonthlySegmentsWithClock(tierTimeRangesMap map[string]*tieringPb.TimeRanges, clock clockwork.Clock) []monthSegment {
	var segments []monthSegment
	// Get current time for boundary checks and ensure it's in IST
	currentTime := clock.Now().In(datetime.IST)

	currentMonthStart := datetime.StartOfMonth(currentTime)

	// start date will be start of the 2nd last calendar month
	absStartDate := *(datetime.AddNMonths(&currentMonthStart, -2))

	// Iterate through each tier in the map
	for tierStr, timeRanges := range tierTimeRangesMap {
		tier := external.Tier(external.Tier_value[tierStr])

		// Skip processing for tiers we don't care about
		if tier == external.Tier_TIER_UNSPECIFIED {
			continue
		}

		// Process each time range for this tier
		for _, timeRange := range timeRanges.GetTimeRanges() {
			periodStartTime := timeRange.GetFromTime().AsTime().In(datetime.IST)
			periodEndTime := timeRange.GetToTime().AsTime().In(datetime.IST)

			// Adjust start time based on the 3-month filter
			if periodStartTime.Before(absStartDate) {
				periodStartTime = absStartDate
			}

			// Adjust end time to not go beyond current time
			if periodEndTime.After(currentTime) {
				periodEndTime = currentTime
			}

			// If the adjusted period is invalid or empty, skip
			if !periodStartTime.Before(periodEndTime) {
				continue
			}

			// Special handling for REGULAR tier - use whole calendar months within the period
			if tier == external.Tier_TIER_FI_REGULAR {
				// Find the start of the first relevant month for this period part
				currentSegmentStartMonth := datetime.StartOfMonth(periodStartTime)

				// Iterate through months covering the period
				for currentSegmentStartMonth.Before(periodEndTime) {
					// Calculate the end of the current month
					segmentEndForMonth := datetime.EndOfMonth(currentSegmentStartMonth)
					nextMonthStart := *(datetime.AddNMonths(&currentSegmentStartMonth, 1))

					// Determine actual start/end for this specific monthly segment, clamped by period start/end
					actualSegmentStart := periodStartTime
					if currentSegmentStartMonth.After(periodStartTime) {
						actualSegmentStart = currentSegmentStartMonth
					}
					actualSegmentEnd := periodEndTime
					if segmentEndForMonth.Before(periodEndTime) {
						actualSegmentEnd = segmentEndForMonth
					}

					// Add segment if it's valid and non-empty
					if actualSegmentStart.Before(actualSegmentEnd) {
						segments = append(segments, monthSegment{
							// Report the *full* month for REGULAR tier display, matching collector.go approach
							startDate: currentSegmentStartMonth,
							endDate:   segmentEndForMonth,
							tier:      tier,
						})
					}

					// Move to next month
					currentSegmentStartMonth = nextMonthStart

					// Break if we've moved past the period end time
					if !currentSegmentStartMonth.Before(periodEndTime) {
						break
					}
				}
			} else {
				// For other tiers, break down by calendar month using actual tier time range boundaries
				currentSegmentStart := periodStartTime
				for currentSegmentStart.Before(periodEndTime) {
					// Calculate the end of the current month
					segmentEndInMonth := datetime.EndOfMonth(currentSegmentStart)
					segmentStartInMonth := datetime.StartOfMonth(currentSegmentStart)
					nextMonthStart := *(datetime.AddNMonths(&segmentStartInMonth, 1))

					// Determine the actual end of this segment (earlier of month end or period end)
					actualSegmentEnd := periodEndTime
					if segmentEndInMonth.Before(periodEndTime) {
						actualSegmentEnd = segmentEndInMonth
					}

					// Add segment if valid
					if currentSegmentStart.Before(actualSegmentEnd) {
						segments = append(segments, monthSegment{
							startDate: currentSegmentStart,
							endDate:   actualSegmentEnd,
							tier:      tier,
						})
					}

					// Move start to the beginning of the next segment (start of next month)
					currentSegmentStart = nextMonthStart

					// Break loop if the next segment start is not before the period end
					if !currentSegmentStart.Before(periodEndTime) {
						break
					}
				}
			}
		}
	}

	return segments
}

// sortAndLimitSegments sorts segments by end date (most recent first) and limits to the most recent segments
func sortAndLimitSegments(segments []monthSegment) []monthSegment {
	if len(segments) == 0 {
		return segments
	}

	// Sort segments by end date (most recent first)
	sort.Slice(segments, func(i, j int) bool {
		// Primary sort: End date descending (most recent month first)
		if segments[i].endDate.Year() != segments[j].endDate.Year() {
			return segments[i].endDate.After(segments[j].endDate)
		}
		if segments[i].endDate.Month() != segments[j].endDate.Month() {
			return segments[i].endDate.After(segments[j].endDate)
		}
		// Secondary sort: Start date descending (if same end month)
		return segments[i].startDate.After(segments[j].startDate)
	})

	// Match the collector.go approach: remove the first segment (current month)
	// since this info is already captured in current AMB
	if len(segments) > 1 {
		segments = segments[1:]
	}

	if len(segments) == 0 {
		return segments
	}

	// Limit to the most recent N segments
	const maxHistorySegments = 5
	if len(segments) > maxHistorySegments {
		segments = segments[:maxHistorySegments]
	}

	return segments
}

// fetchSegmentData fetches AMB data for a single segment
func fetchSegmentData(ctx context.Context, actorID string, segment monthSegment, tieringPinotClient tieringPinotPb.EODBalanceClient) (*tieringPb.AmbDetails, error) {
	segStartDate := segment.startDate
	segEndDate := segment.endDate
	segTier := segment.tier

	// Format date range
	dateRange := fmt.Sprintf("%d %s – %d %s",
		segStartDate.Day(), segStartDate.Month().String()[:3],
		segEndDate.Day(), segEndDate.Month().String()[:3])

	// Get tier display name
	tierDisplayName, err := display_names.GetTitleCaseDisplayString(segTier)
	if err != nil {
		return nil, err
	}

	// Create timestamps directly from IST times - no UTC conversion needed
	// The service will handle timezone interpretation using the timezone field
	fromTimestamp := timestamppb.New(segStartDate)
	toTimestamp := timestamppb.New(segEndDate)

	ambMaintainedValue := 0.0
	var ambMaintainedStr string
	var fetchErr error

	// Get average EOD balance from pinot client with timezone information
	// Extract timezone from the segment dates automatically
	segmentTimezone := segStartDate.Location().String()
	eodBalanceResp, err := tieringPinotClient.GetAverageEODBalanceInDateRange(ctx, &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
		ActorId:       actorID,
		FromTimestamp: fromTimestamp,
		ToTimestamp:   toTimestamp,
		Timezone:      segmentTimezone, // Use timezone from the segment dates
	})
	if rpcErr := epifigrpc.RPCError(eodBalanceResp, err); rpcErr != nil {
		fetchErr = rpcErr
		if cfg.IsNonProdEnvBestEffort() {
			// Populate with dummy value for non prod
			ambMaintainedStr = "N/A" // Indicate data unavailable/error
		} else {
			return nil, fmt.Errorf("failed to get average EOD balance for %s to %s: %w", segStartDate, segEndDate, fetchErr)
		}
	} else {
		ambMaintainedValue = eodBalanceResp.GetAvgBalance()
		ambMaintainedStr = money.ToDisplayStringInIndianFormat(money.ParseFloat(ambMaintainedValue, money.RupeeCurrencyCode), 2, true)
	}

	return &tieringPb.AmbDetails{
		Dates:         dateRange,
		Plan:          tierDisplayName,
		AmbMaintained: ambMaintainedStr,
	}, nil
}
