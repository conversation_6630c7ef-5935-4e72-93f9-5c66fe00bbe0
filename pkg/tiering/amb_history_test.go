package tiering

import (
	"context"
	"sort"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jonboulle/clockwork"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/api/tiering/pinot/mocks"
)

// Helper function to create a TimeRange
func createTimeRange(from, to time.Time) *tieringPb.TimeRange {
	return &tieringPb.TimeRange{
		FromTime: timestamppb.New(from),
		ToTime:   timestamppb.New(to),
	}
}

func TestCreateMonthlySegments(t *testing.T) {
	// Reference dates for testing
	fixedNow := time.Date(2025, 5, 15, 12, 0, 0, 0, datetime.IST)
	fakeClock := clockwork.NewFakeClockAt(fixedNow)

	// Start of the current month and previous months
	currentMonthStart := datetime.StartOfMonth(fixedNow)
	prevMonthStart := *datetime.AddNMonths(&currentMonthStart, -1)
	prevPrevMonthStart := *datetime.AddNMonths(&currentMonthStart, -2)
	prevPrevPrevMonthStart := *datetime.AddNMonths(&currentMonthStart, -3)

	// End of months
	prevPrevMonthEnd := datetime.EndOfMonth(prevPrevMonthStart)
	prevMonthEnd := datetime.EndOfMonth(prevMonthStart)
	currentMonthEnd := datetime.EndOfMonth(currentMonthStart)

	// Test cases
	testCases := []struct {
		name              string
		tierTimeRangesMap map[string]*tieringPb.TimeRanges
		expectedSegments  []monthSegment
	}{
		{
			name:              "Empty input",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{},
			expectedSegments:  []monthSegment{},
		},
		{
			name: "Single tier, single time range",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_BASIC": {
					TimeRanges: []*tieringPb.TimeRange{
						createTimeRange(prevPrevMonthStart, fixedNow),
					},
				},
			},
			expectedSegments: []monthSegment{
				{
					startDate: prevPrevMonthStart,
					endDate:   prevPrevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: prevMonthStart,
					endDate:   prevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: currentMonthStart,
					endDate:   fixedNow,
					tier:      external.Tier_TIER_FI_BASIC,
				},
			},
		},
		{
			name: "Multiple tiers - sequential transition",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_BASIC": {
					TimeRanges: []*tieringPb.TimeRange{
						// User was on BASIC from 3 months ago until the end of previous month
						createTimeRange(prevPrevPrevMonthStart, prevPrevMonthEnd),
					},
				},
				"TIER_FI_PLUS": {
					TimeRanges: []*tieringPb.TimeRange{
						// User upgraded to PLUS from start of previous month until now
						createTimeRange(prevMonthStart, fixedNow),
					},
				},
			},
			expectedSegments: []monthSegment{
				{
					startDate: prevPrevMonthStart,
					endDate:   prevPrevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: prevMonthStart,
					endDate:   prevMonthEnd,
					tier:      external.Tier_TIER_FI_PLUS,
				},
				{
					startDate: currentMonthStart,
					endDate:   fixedNow,
					tier:      external.Tier_TIER_FI_PLUS,
				},
			},
		},
		{
			name: "Multiple tiers - mid-month transition",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_BASIC": {
					TimeRanges: []*tieringPb.TimeRange{
						// User was on BASIC until the 15th of previous month
						createTimeRange(
							prevPrevPrevMonthStart,
							time.Date(2025, 4, 15, 0, 0, 0, 0, datetime.IST),
						),
					},
				},
				"TIER_FI_PLUS": {
					TimeRanges: []*tieringPb.TimeRange{
						// User upgraded to PLUS from the 15th of previous month
						createTimeRange(
							time.Date(2025, 4, 15, 0, 0, 0, 0, datetime.IST),
							fixedNow,
						),
					},
				},
			},
			expectedSegments: []monthSegment{
				{
					startDate: prevPrevMonthStart,
					endDate:   prevPrevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: prevMonthStart,
					endDate:   time.Date(2025, 4, 15, 0, 0, 0, 0, datetime.IST),
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: time.Date(2025, 4, 15, 0, 0, 0, 0, datetime.IST),
					endDate:   prevMonthEnd,
					tier:      external.Tier_TIER_FI_PLUS,
				},
				{
					startDate: currentMonthStart,
					endDate:   fixedNow,
					tier:      external.Tier_TIER_FI_PLUS,
				},
			},
		},
		{
			name: "Regular tier handling",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_REGULAR": {
					TimeRanges: []*tieringPb.TimeRange{
						createTimeRange(
							time.Date(2025, 3, 15, 0, 0, 0, 0, datetime.IST),
							time.Date(2025, 5, 10, 0, 0, 0, 0, datetime.IST),
						),
					},
				},
			},
			expectedSegments: []monthSegment{
				{
					startDate: time.Date(2025, 3, 1, 0, 0, 0, 0, datetime.IST),
					endDate:   time.Date(2025, 3, 31, 23, 59, 59, 999999999, datetime.IST),
					tier:      external.Tier_TIER_FI_REGULAR,
				},
				{
					startDate: time.Date(2025, 4, 1, 0, 0, 0, 0, datetime.IST),
					endDate:   time.Date(2025, 4, 30, 23, 59, 59, 999999999, datetime.IST),
					tier:      external.Tier_TIER_FI_REGULAR,
				},
				{
					startDate: time.Date(2025, 5, 1, 0, 0, 0, 0, datetime.IST),
					endDate:   currentMonthEnd,
					tier:      external.Tier_TIER_FI_REGULAR,
				},
			},
		},
		{
			name: "Tier upgrade across multiple months with gap",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_BASIC": {
					TimeRanges: []*tieringPb.TimeRange{
						// User was on BASIC for one month
						createTimeRange(prevPrevPrevMonthStart, prevPrevMonthEnd),
					},
				},
				"TIER_FI_PLUS": {
					TimeRanges: []*tieringPb.TimeRange{
						// User upgraded to PLUS after a month gap
						createTimeRange(prevMonthStart, fixedNow),
					},
				},
			},
			expectedSegments: []monthSegment{
				{
					startDate: prevPrevMonthStart,
					endDate:   prevPrevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: prevMonthStart,
					endDate:   prevMonthEnd,
					tier:      external.Tier_TIER_FI_PLUS,
				},
				{
					startDate: currentMonthStart,
					endDate:   fixedNow,
					tier:      external.Tier_TIER_FI_PLUS,
				},
			},
		},
		{
			name: "Invalid time range",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_BASIC": {
					TimeRanges: []*tieringPb.TimeRange{
						createTimeRange(fixedNow, prevMonthStart), // End is before start
					},
				},
			},
			expectedSegments: []monthSegment{}, // Should produce no segments
		},
		{
			name: "Tier unspecified",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_UNSPECIFIED": {
					TimeRanges: []*tieringPb.TimeRange{
						createTimeRange(prevMonthStart, fixedNow),
					},
				},
			},
			expectedSegments: []monthSegment{}, // Should be skipped
		},
		{
			name: "Time range before absolute start date",
			tierTimeRangesMap: map[string]*tieringPb.TimeRanges{
				"TIER_FI_BASIC": {
					TimeRanges: []*tieringPb.TimeRange{
						createTimeRange(
							time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST), // Older date
							fixedNow,
						),
					},
				},
			},
			expectedSegments: []monthSegment{
				{
					startDate: prevPrevMonthStart,
					endDate:   prevPrevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: prevMonthStart,
					endDate:   prevMonthEnd,
					tier:      external.Tier_TIER_FI_BASIC,
				},
				{
					startDate: currentMonthStart,
					endDate:   fixedNow,
					tier:      external.Tier_TIER_FI_BASIC,
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Call the function with our fake clock
			segments := createMonthlySegmentsWithClock(tc.tierTimeRangesMap, fakeClock)

			// Validation
			if len(tc.expectedSegments) == 0 {
				assert.Empty(t, segments, "Expected no segments for case: %s", tc.name)
				return
			}

			assert.Len(t, segments, len(tc.expectedSegments))

			// If expected number of segments matches, verify each segment
			if len(tc.expectedSegments) == len(segments) {
				// Sort both expected and actual segments for comparison
				// This is needed since the order of processing tiers in a map is non-deterministic
				sortedExpected := sortSegmentsForTest(tc.expectedSegments)
				sortedActual := sortSegmentsForTest(segments)

				for i := 0; i < len(sortedExpected); i++ {
					expected := sortedExpected[i]
					actual := sortedActual[i]

					// Compare date components and tier
					assert.Equal(t, expected.tier, actual.tier,
						"Segment %d tier mismatch for case: %s", i, tc.name)

					// Compare dates with year, month, day precision (ignoring time)
					assertDateEqual(t, expected.startDate, actual.startDate,
						"Segment %d start date mismatch for case: %s", i, tc.name)
					assertDateEqual(t, expected.endDate, actual.endDate,
						"Segment %d end date mismatch for case: %s", i, tc.name)
				}
			}
		})
	}
}

// Helper function to sort segments for test comparison
func sortSegmentsForTest(segments []monthSegment) []monthSegment {
	result := make([]monthSegment, len(segments))
	copy(result, segments)

	// Sort by tier, then by start date, then by end date
	sort.Slice(result, func(i, j int) bool {
		if result[i].tier != result[j].tier {
			return result[i].tier < result[j].tier
		}
		if !result[i].startDate.Equal(result[j].startDate) {
			return result[i].startDate.Before(result[j].startDate)
		}
		return result[i].endDate.Before(result[j].endDate)
	})

	return result
}

// Helper function to compare dates with day precision
func assertDateEqual(t *testing.T, expected, actual time.Time, msgAndArgs ...interface{}) {
	assert.Equal(t, expected.Year(), actual.Year(), msgAndArgs...)
	assert.Equal(t, expected.Month(), actual.Month(), msgAndArgs...)
	assert.Equal(t, expected.Day(), actual.Day(), msgAndArgs...)
}

func TestSortAndLimitSegments(t *testing.T) {
	now := time.Now()
	monthStart := datetime.StartOfMonth(now)

	// Create test segments
	segments := []monthSegment{
		{
			startDate: *datetime.AddNMonths(&monthStart, -3),
			endDate:   datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -3)),
			tier:      external.Tier_TIER_FI_BASIC,
		},
		{
			startDate: *datetime.AddNMonths(&monthStart, -1),
			endDate:   datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -1)),
			tier:      external.Tier_TIER_FI_PLUS,
		},
		{
			startDate: monthStart,
			endDate:   datetime.EndOfMonth(monthStart),
			tier:      external.Tier_TIER_FI_REGULAR,
		},
		{
			startDate: *datetime.AddNMonths(&monthStart, -2),
			endDate:   datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -2)),
			tier:      external.Tier_TIER_FI_BASIC,
		},
		{
			startDate: *datetime.AddNMonths(&monthStart, -5),
			endDate:   datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -5)),
			tier:      external.Tier_TIER_FI_BASIC,
		},
		{
			startDate: *datetime.AddNMonths(&monthStart, -4),
			endDate:   datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -4)),
			tier:      external.Tier_TIER_FI_PLUS,
		},
		{
			startDate: *datetime.AddNMonths(&monthStart, -6),
			endDate:   datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -6)),
			tier:      external.Tier_TIER_FI_BASIC,
		},
	}

	// Call the function
	result := sortAndLimitSegments(segments)

	// Verify results
	assert.Len(t, result, 5)
	// First segment in result should be from previous month (-1), not current month (0)
	// because the function excludes the current month
	assert.Equal(t, datetime.EndOfMonth(*datetime.AddNMonths(&monthStart, -1)), result[0].endDate)

	// Verify sorting by checking the order of end dates
	for i := 0; i < len(result)-1; i++ {
		assert.True(t, result[i].endDate.After(result[i+1].endDate) ||
			(result[i].endDate.Equal(result[i+1].endDate) && result[i].startDate.After(result[i+1].startDate)),
			"Segments should be sorted by end date (most recent first)")
	}
}

func TestFetchSegmentData(t *testing.T) {
	ctx := context.Background()
	actorID := "test-actor-123"

	// This test validates the timezone-aware fix:
	// IST segment dates are sent with timezone information automatically extracted from timestamps
	// The service handles timezone conversion only when timezone field is provided
	testCases := []struct {
		name               string
		segment            monthSegment
		expectedFromIST    time.Time // Expected IST timestamp to be sent
		expectedToIST      time.Time // Expected IST timestamp to be sent
		mockResponse       *tieringPinotPb.GetAverageEODBalanceInDateRangeResponse
		mockError          error
		expectedAmbDetails *tieringPb.AmbDetails
		expectedError      bool
	}{
		{
			name: "should return formatted AMB details for complete month",
			segment: monthSegment{
				startDate: time.Date(2024, 12, 1, 0, 0, 0, 0, datetime.IST),
				endDate:   time.Date(2024, 12, 31, 23, 59, 59, 999999999, datetime.IST),
				tier:      external.Tier_TIER_FI_BASIC,
			},
			// IST timestamps are sent directly with timezone information
			expectedFromIST: time.Date(2024, 12, 1, 0, 0, 0, 0, datetime.IST),
			expectedToIST:   time.Date(2024, 12, 31, 23, 59, 59, 999999999, datetime.IST),
			mockResponse: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpc.StatusOk(),
				AvgBalance: 25000.50,
			},
			mockError: nil,
			expectedAmbDetails: &tieringPb.AmbDetails{
				Dates:         "1 Dec – 31 Dec",
				Plan:          "Standard",
				AmbMaintained: "₹25,000.50",
			},
			expectedError: false,
		},
		{
			name: "should send IST timestamps with optional timezone information",
			segment: monthSegment{
				startDate: time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
				endDate:   time.Date(2024, 1, 31, 23, 59, 59, 999999999, datetime.IST),
				tier:      external.Tier_TIER_FI_PLUS,
			},
			// IST timestamps sent with timezone automatically extracted from segment dates
			expectedFromIST: time.Date(2024, 1, 1, 0, 0, 0, 0, datetime.IST),
			expectedToIST:   time.Date(2024, 1, 31, 23, 59, 59, 999999999, datetime.IST),
			mockResponse: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpc.StatusOk(),
				AvgBalance: 75000.00,
			},
			mockError: nil,
			expectedAmbDetails: &tieringPb.AmbDetails{
				Dates:         "1 Jan – 31 Jan",
				Plan:          "Plus",
				AmbMaintained: "₹75,000.00",
			},
			expectedError: false,
		},
		{
			name: "should handle partial month segments correctly",
			segment: monthSegment{
				startDate: time.Date(2024, 6, 15, 10, 30, 0, 0, datetime.IST),
				endDate:   time.Date(2024, 6, 25, 18, 45, 30, 0, datetime.IST),
				tier:      external.Tier_TIER_FI_REGULAR,
			},
			// Partial month IST timestamps sent with timezone context
			expectedFromIST: time.Date(2024, 6, 15, 10, 30, 0, 0, datetime.IST),
			expectedToIST:   time.Date(2024, 6, 25, 18, 45, 30, 0, datetime.IST),
			mockResponse: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpc.StatusOk(),
				AvgBalance: 150000.75,
			},
			mockError: nil,
			expectedAmbDetails: &tieringPb.AmbDetails{
				Dates:         "15 Jun – 25 Jun",
				Plan:          "Regular",
				AmbMaintained: "₹1,50,000.75",
			},
			expectedError: false,
		},
		{
			name: "should maintain timezone context across year transitions",
			segment: monthSegment{
				startDate: time.Date(2023, 12, 1, 0, 0, 0, 0, datetime.IST),
				endDate:   time.Date(2023, 12, 31, 23, 59, 59, 999999999, datetime.IST),
				tier:      external.Tier_TIER_FI_BASIC,
			},
			// December IST stays as December IST with timezone information
			expectedFromIST: time.Date(2023, 12, 1, 0, 0, 0, 0, datetime.IST),
			expectedToIST:   time.Date(2023, 12, 31, 23, 59, 59, 999999999, datetime.IST),
			mockResponse: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpc.StatusOk(),
				AvgBalance: 50000.25,
			},
			mockError: nil,
			expectedAmbDetails: &tieringPb.AmbDetails{
				Dates:         "1 Dec – 31 Dec",
				Plan:          "Standard",
				AmbMaintained: "₹50,000.25",
			},
			expectedError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create GoMock controller and mock client
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockClient := mocks.NewMockEODBalanceClient(ctrl)

			// Create expected request with IST timestamps and optional timezone information
			expectedRequest := &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
				ActorId:       actorID,
				FromTimestamp: timestamppb.New(tc.expectedFromIST),
				ToTimestamp:   timestamppb.New(tc.expectedToIST),
				Timezone:      tc.expectedFromIST.Location().String(), // Timezone extracted from segment dates
			}

			// Set up mock expectation with direct request matching
			mockClient.EXPECT().
				GetAverageEODBalanceInDateRange(
					gomock.Any(),    // context
					expectedRequest, // Direct request matching
				).
				Return(tc.mockResponse, tc.mockError).
				Times(1)

			// Call the function
			result, err := fetchSegmentData(ctx, actorID, tc.segment, mockClient)

			// Assert error expectations
			if tc.expectedError {
				require.Error(t, err)
				require.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)

				// Verify the returned AMB details
				if tc.expectedAmbDetails != nil {
					assert.Equal(t, tc.expectedAmbDetails.Dates, result.Dates)
					assert.Equal(t, tc.expectedAmbDetails.Plan, result.Plan)
					assert.Equal(t, tc.expectedAmbDetails.AmbMaintained, result.AmbMaintained)
				}
			}
		})
	}
}
