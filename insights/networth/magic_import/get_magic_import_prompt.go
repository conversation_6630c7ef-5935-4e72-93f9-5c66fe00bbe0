package magicimport

import (
	"encoding/json"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2"
)

func GetMagicImportPrompt() string {
	prompt := MagicImportSystemPrompt
	samplePrompts := &MagicImportPromptResponse{
		AICommentary: "This is a sample AI commentary for all the assets",
		ConventionalAssets: []*model.InvestmentDeclaration{
			{
				InstrumentType: typesv2.InvestmentInstrumentType_ART_AND_ARTEFACTS,
				DeclarationDetails: &model.OtherDeclarationDetails{
					Details: &model.OtherDeclarationDetails_ArtAndArtefacts{
						ArtAndArtefacts: &model.ArtAndArtefacts{
							InvestmentName: "Phone",
							InvestedValue: &money.Money{
								CurrencyCode: "INR",
								Units:        1000,
							},
						},
					},
				},
			},
		},
		UnconventionalAssets: []*UnconventionalAssets{
			{
				Name:         "Cricket bat",
				Reason:       "This is a cricket bat which is not a conventional asset but has value.",
				AICommentary: "This cricket bat is a valuable item, possibly signed by a famous player.",
			},
		},
	}

	res, err := json.Marshal(samplePrompts)
	if err != nil {
		// ideally this must never fail, preferring method simplicity over error handling
		// This logic will be verified in non-prod via UT execution in CI
		panic(err)
	}
	prompt += string(res) + "\n"

	return prompt
}
