package llm

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common/file"
)

// todo: make it a factory later if required
type LLMClient interface {
	SendPromptWithFiles(ctx context.Context, req *SendPromptWithFilesRequest) (string, error)
}

type SendPromptWithFilesRequest struct {
	Files         []*commontypes.File
	SystemContext string
	Model         string
}

func (r *SendPromptWithFilesRequest) GetFiles() []*commontypes.File {
	if r != nil {
		return r.Files
	}
	return nil
}

func (r *SendPromptWithFilesRequest) GetSystemContext() string {
	if r != nil {
		return r.SystemContext
	}
	return ""
}

func (r *SendPromptWithFilesRequest) GetModel() string {
	if r != nil {
		return r.Model
	}
	return ""
}
