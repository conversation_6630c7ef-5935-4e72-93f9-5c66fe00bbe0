package feedback_subscription

import (
	"context"
	"strconv"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	event "github.com/epifi/be-common/pkg/events"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/cx/events"
	dao "github.com/epifi/gamma/cx/ticket/dao"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	feedbackEnginePb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
)

type FeedbackSubscriptionClient struct {
	inAppCsatResponseDao dao.IInAppCsatResponseDao
	supportTicketDao     dao.ISupportTicketDao
	eventBroker          event.Broker
}

func NewFeedbackSubscriptionClient(inAppCsatResponseDao dao.IInAppCsatResponseDao, supportTicketDao dao.ISupportTicketDao,
	eventBroker event.Broker) *FeedbackSubscriptionClient {
	return &FeedbackSubscriptionClient{
		inAppCsatResponseDao: inAppCsatResponseDao,
		supportTicketDao:     supportTicketDao,
		eventBroker:          eventBroker,
	}
}

func (q *FeedbackSubscriptionClient) GetFeedbackInfo(ctx context.Context, request *feedbackEnginePb.GetFeedbackInfoRequest) (*feedbackEnginePb.GetFeedbackInfoResponse, error) {
	logger.Info(ctx, "received question response", zap.String(logger.QUESTION_ID, request.GetFeedbackInfo().GetQuestionId()), zap.String("attemptId", request.GetFeedbackInfo().GetAttemptId()), zap.String("questionResponse", request.GetFeedbackInfo().GetFeedbackAnswer().String()), zap.String("flowIdMeta", request.GetFeedbackInfo().GetFlowIdMeta()))
	ticketId, ticketIdErr := strconv.Atoi(request.GetFeedbackInfo().GetFlowIdMeta())
	// we are expecting ticket id in string format populated in flow id meta field to make this whole flow work
	if ticketIdErr != nil {
		logger.Error(ctx, "error while converting flow id meta to ticket id", zap.String("flowIdMeta", request.GetFeedbackInfo().GetFlowIdMeta()), zap.Error(ticketIdErr))
		return &feedbackEnginePb.GetFeedbackInfoResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	currentSupportTicket, getSupportTicketErr := q.supportTicketDao.GetById(ctx, int64(ticketId))
	if getSupportTicketErr != nil {
		return &feedbackEnginePb.GetFeedbackInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get support ticket in GetFeedbackInfo: " + getSupportTicketErr.Error()),
		}, nil
	}

	csatSurveysLen := len(currentSupportTicket.GetTicketMeta().GetSurveyDetails())
	if csatSurveysLen == 0 {
		logger.Error(ctx, "no triggered surveys found for ticket", zap.Int64("ticket_id", currentSupportTicket.GetId()))
		return &feedbackEnginePb.GetFeedbackInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("no triggered surveys found for ticket"),
		}, nil
	}

	latestSurvey := currentSupportTicket.GetTicketMeta().GetSurveyDetails()[csatSurveysLen-1]
	updatedSurvey := latestSurvey

	// We are creating csat entry for 1st question response only and marking it as partially submitted in support_tickets against that ticket.
	// If user submits last question in the same survey then we will update the status as submitted.

	switch latestSurvey.GetStatus() {
	case ticketPb.SurveyStatus_SURVEY_STATUS_WAITING_ON_USER:
		updatedSurvey.Status = ticketPb.SurveyStatus_SURVEY_STATUS_PARTIALLY_SUBMITTED
	case ticketPb.SurveyStatus_SURVEY_STATUS_PARTIALLY_SUBMITTED:
		updatedSurvey.Status = ticketPb.SurveyStatus_SURVEY_STATUS_SUBMITTED
		updatedSurvey.CompletedAt = timestamppb.New(time.Now())
	case ticketPb.SurveyStatus_SURVEY_STATUS_SUBMITTED:
		logger.Error(ctx, "survey already submitted for ticket", zap.Int64("ticket_id", currentSupportTicket.GetId()))
		return &feedbackEnginePb.GetFeedbackInfoResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	default:
		logger.Error(ctx, "unexpected survey status", zap.Int64("ticket_id", currentSupportTicket.GetId()), zap.String("survey_status", latestSurvey.GetStatus().String()))
		return &feedbackEnginePb.GetFeedbackInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("unexpected survey status: " + latestSurvey.GetStatus().String()),
		}, nil
	}

	currentSupportTicket.TicketMeta.SurveyDetails[csatSurveysLen-1] = updatedSurvey

	if latestSurvey.GetStatus() != ticketPb.SurveyStatus_SURVEY_STATUS_SUBMITTED {
		// create the record in "in app csat responses" table for 1st question response
		_, createErr := q.inAppCsatResponseDao.Create(ctx, &ticketPb.InAppCsatResponse{
			ActorId:    request.GetFeedbackInfo().GetActorId(),
			TicketId:   int64(ticketId),
			QuestionId: request.GetFeedbackInfo().GetQuestionId(),
			AttemptId:  request.GetFeedbackInfo().GetAttemptId(),
			CsatScore:  int64(request.GetFeedbackInfo().GetFeedbackAnswer().GetScaleAnswer().GetSelectedItem()),
		})

		if createErr != nil {
			logger.Error(ctx, "error while creating csat response record", zap.String("flowIdMeta", request.GetFeedbackInfo().GetFlowIdMeta()), zap.Int(logger.TICKET_ID, ticketId), zap.String(logger.ACTOR_ID_V2, request.GetFeedbackInfo().GetActorId()), zap.Error(createErr))
			return &feedbackEnginePb.GetFeedbackInfoResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to create csat response record: " + createErr.Error()),
			}, nil
		}
		q.eventBroker.AddToBatch(ctx, events.NewCsatFeedbackReceivedEvent(request.GetFeedbackInfo().GetActorId(),
			int64(ticketId), int64(request.GetFeedbackInfo().GetFeedbackAnswer().GetScaleAnswer().GetSelectedItem()), 0))
	}

	if _, err := q.supportTicketDao.Update(ctx, currentSupportTicket, []ticketPb.SupportTicketFieldMask{ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_META}); err != nil {
		logger.Error(ctx, "error while updating ticket meta", zap.Int64("ticket_id", currentSupportTicket.GetId()), zap.Error(err))
	}
	return &feedbackEnginePb.GetFeedbackInfoResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}
