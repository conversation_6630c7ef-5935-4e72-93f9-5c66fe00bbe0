Application:
  Environment: "development"

Aws:
  Region: "ap-south-1"

SalaryDb:
  AppName: "salaryprogram"
  DbType: "PGDB"
  StatementTimeout: 1s
  Name: "salaryprogram"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

SalaryProgramStatusUpdateEventPublisher:
  TopicName: "salaryprogram-status-update-topic"

Secrets:
  Ids:
    DbCredentials: "{\"username\": \"root\", \"password\": \"\"}"

RedisClusters:
  TieringConnectedAccountRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0
TieringInfoCache:
  Prefix: "TIERING:ATI:"
