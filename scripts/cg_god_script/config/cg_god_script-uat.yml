Application:
  Environment: "uat"

Aws:
  Region: "ap-south-1"
  S3:
    BucketName: "epifi-uat-onsurity-backfill"

SalaryDb:
  AppName: "salaryprogram"
  DbType: "PGDB"
  StatementTimeout: 1s
  Name: "salaryprogram"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "uat/rds/postgres"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

SalaryProgramStatusUpdateEventPublisher:
  TopicName: "uat-salaryprogram-status-update-topic"

SavingsTierUpdateEventPublisher:
  QueueName: "uat-savings-tier-update-event-consumer-queue"

Secrets:
  Ids:
    DbCredentials: "uat/rds/postgres"

RedisClusters:
  TieringConnectedAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 11
  TieringActorRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 10
    HystrixCommand:
      CommandName: "actor_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
TieringInfoCache:
  Prefix: "TIERING:ATI:"
