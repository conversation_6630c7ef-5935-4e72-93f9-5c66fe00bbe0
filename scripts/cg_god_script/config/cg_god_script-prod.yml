Application:
  Environment: "prod"

Aws:
  Region: "ap-south-1"
  S3:
    BucketName: "epifi-prod-onsurity-backfill"

SalaryDb:
  DbType: "PGDB"
  AppName: "salaryprogram"
  StatementTimeout: 10s
  Name: "salaryprogram"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 50
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/salaryprogram"
  GormV2:
    LogLevelGormV2: "SILENT"
    UseInsecureLog: false

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 10s
  Name: "tiering"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  SecretName: "prod/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "SILENT"
    UseInsecureLog: false

SalaryProgramStatusUpdateEventPublisher:
  TopicName: "prod-salaryprogram-status-update-topic"

SavingsTierUpdateEventPublisher:
  QueueName: "prod-savings-tier-update-event-consumer-queue"

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/salaryprogram"
    TieringDbCredentials: "prod/rds/epifimetis/tiering_dev_user"

RedisClusters:
  TieringConnectedAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: tiering
  TieringActorRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
      Password: "" ## empty string for no password
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: tiering
    HystrixCommand:
      CommandName: "tiering_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 1500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 30
TieringInfoCache:
  Prefix: "TIERING:ATI:"
