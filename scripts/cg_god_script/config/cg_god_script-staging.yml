Application:
  Environment: "staging"

Aws:
  Region: "ap-south-1"
  S3:
    BucketName: "epifi-staging-onsurity-backfill"

SalaryDb:
  AppName: "salaryprogram"
  DbType: "PGDB"
  StatementTimeout: 1s
  Name: "salaryprogram"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/postgres14"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

SalaryProgramStatusUpdateEventPublisher:
  TopicName: "staging-salaryprogram-status-update-topic"

SavingsTierUpdateEventPublisher:
  QueueName: "staging-savings-tier-update-event-consumer-queue"

Secrets:
  Ids:
    DbCredentials: "staging/rds/postgres14"
    TieringDbCredentials: "staging/rds/epifimetis/tiering_dev_user"

RedisClusters:
  TieringConnectedAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 10
  TieringActorRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 10
    HystrixCommand:
      CommandName: "actor_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
TieringInfoCache:
  Prefix: "TIERING:ATI:"
