package reports

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/logger"
	fgPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	fileGeneratorDaoImpl "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/dao/impl"
	orderDaoImpl "github.com/epifi/gamma/usstocks/order/dao/impl"
)

const (
	AccountId      = "4f53eb9f-b13d-3748-912b-a14b6fa4ed9e"
	netAmountIdx   = 6 // NetAmount is always at index 6
	descriptionIdx = 7 // Description is always at index 7
)

// SwiftReader handles reading and parsing SWIFT files
type SwiftReader struct {
	s3Client s3.S3Client
}

// NewSwiftReader creates a new SwiftReader instance
func NewSwiftReader(s3Client s3.S3Client) *SwiftReader {
	return &SwiftReader{
		s3Client: s3Client,
	}
}

type AlpacaOutwardReconAuditor struct {
	// alpaca_statement: Compares total debit and credit transaction amounts from Alpaca statements. If totals match, returns early with success; otherwise, reports discrepancy.
	// alpaca_outward_recon: Checks each outward transaction across Alpaca, internal records, and swift data. Returns detailed match/mismatch results for all transactions.

	auditorType              string
	vgStocksClient           vgStocksPb.StocksClient
	fileGenerationAttemptDao *fileGeneratorDaoImpl.FileGenerationAttemptCrdb
	orderManagerClient       orderPb.OrderManagerClient
	orderDao                 orderDaoImpl.WalletOrderPgdb
	swiftReader              *SwiftReader
}

func NewAlpacaOutwardReconAuditor(
	auditorType string,
	vgStocksClient vgStocksPb.StocksClient,
	fileGenerationAttemptDao *fileGeneratorDaoImpl.FileGenerationAttemptCrdb,
	orderManagerClient orderPb.OrderManagerClient,
	orderDao orderDaoImpl.WalletOrderPgdb,
	s3Client s3.S3Client,
) *AlpacaOutwardReconAuditor {
	return &AlpacaOutwardReconAuditor{
		auditorType:              auditorType,
		vgStocksClient:           vgStocksClient,
		fileGenerationAttemptDao: fileGenerationAttemptDao,
		orderManagerClient:       orderManagerClient,
		orderDao:                 orderDao,
		swiftReader:              NewSwiftReader(s3Client),
	}
}

type SwiftTransferResult struct {
	Transfers   map[string]float64
	TotalAmount float64
}

type FundTransferResult struct {
	TargetAccountSummary *TransactionSummary
	OtherAccountSummary  *TransactionSummary
}

type TransactionSummary struct {
	Transfers   []*FundTransfer
	TotalAmount float64
	Count       int
}

type FundTransfer struct {
	TransactionID string
	Amount        float64
}

type WalletOrderResult struct {
	Orders      []*WalletOrderDetail
	TotalAmount float64
}

type WalletOrderDetail struct {
	ClientOrderId string
	Id            string
	PoolTxnId     string
	SwiftTxnId    string
	AmountInUSD   float64
}

type AmountMismatch struct {
	Id                 string
	ClientOrderId      string
	SwiftTxnId         string
	SwiftAmount        float64
	FundTransferAmount float64
	AmountInUSD        float64
	Reason             string
}

func (a *AlpacaOutwardReconAuditor) Audit(ctx context.Context, targetDate time.Time) (bool, *ReportData, error) {

	logger.Info(ctx, "Running Alpaca outward reconciliation audit",
		zap.Time("target_date", targetDate),
		zap.String("day", targetDate.Weekday().String()))

	// Get SWIFT files for the target date
	startOfDay := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, time.UTC)
	endOfDay := startOfDay.AddDate(0, 0, 1)

	// Get fund transfer details
	fundTransfers, err := a.getFundTransferDetails(ctx, startOfDay, endOfDay)
	if err != nil {
		return false, nil, errors.Wrap(err, "failed to get fund transfer details")
	}

	// If auditor type is alpaca_statement, check if total amounts match
	// and return early
	// If auditor type is alpaca_outward_recon, check each transaction amount from all 3 sources and return the result
	if a.auditorType == "alpaca_statement" {
		if fundTransfers.TargetAccountSummary.TotalAmount != fundTransfers.OtherAccountSummary.TotalAmount {
			diff := math.Abs(fundTransfers.TargetAccountSummary.TotalAmount - fundTransfers.OtherAccountSummary.TotalAmount)
			return false, &ReportData{
				Title: fmt.Sprintf("❌ Alpaca Outward Reconciliation Report - %s 📒", targetDate.Format("2006-01-02")),
				Text: fmt.Sprintf("Total Amount Credited to Pool Account: $%g\nTotal Amount Debited from Pool Account: $%g\nDifference: $%g",
					fundTransfers.TargetAccountSummary.TotalAmount,
					fundTransfers.OtherAccountSummary.TotalAmount,
					diff),
			}, nil
		}
		return true, &ReportData{
			Title: fmt.Sprintf("✅ Alpaca Outward Reconciliation Report - %s 📒", targetDate.Format("2006-01-02")),
			Text: fmt.Sprintf("No Discrepancy Found!\n\nTotal Amount Credited to Pool Account: $%.2f\nTotal Amount Debited from Pool Account: $%.2f\n\nAll fund transfers are balanced.",
				fundTransfers.TargetAccountSummary.TotalAmount,
				fundTransfers.OtherAccountSummary.TotalAmount),
		}, nil
	}

	// Get SWIFT transfers
	swiftTransfers, err := a.getFileGenerationAttempts(ctx, startOfDay, endOfDay)
	if err != nil {
		return false, nil, errors.Wrap(err, "failed to get SWIFT transfers")
	}

	// Get wallet orders data
	walletOrders, err := a.getWalletOrdersData(ctx, fundTransfers.OtherAccountSummary.Transfers)
	if err != nil {
		return false, nil, errors.Wrap(err, "failed to get wallet orders data")
	}

	// Compare amounts and find mismatches
	mismatches := a.compareAmounts(swiftTransfers, fundTransfers, walletOrders)
	csvRows := [][]string{{
		"Wallet Order ID", "Wallet Client Order ID", "Swift Transaction ID", "Wallet Amount", "Swift Amount", "Alpaca Amount", "Reason",
	}}
	for _, m := range mismatches {
		csvRows = append(csvRows, []string{
			m.Id,
			m.ClientOrderId,
			m.SwiftTxnId,
			fmt.Sprintf("%.2f", m.AmountInUSD),
			fmt.Sprintf("%.2f", m.SwiftAmount),
			fmt.Sprintf("%.2f", m.FundTransferAmount),
			m.Reason,
		})
	}
	reportData := &ReportData{
		Title: fmt.Sprintf("📒Alpaca Outward Reconciliation Report - %s", targetDate.Format("2006-01-02")),
		Text: fmt.Sprintf("Total SWIFT Amount: $%.2f, Total Fund Transfer Amount: $%.2f, Total Wallet Orders Amount: $%.2f",
			swiftTransfers.TotalAmount, fundTransfers.OtherAccountSummary.TotalAmount, walletOrders.TotalAmount),
		CsvRows:  csvRows,
		filename: fmt.Sprintf("alpaca_outward_recon_%s.csv", targetDate.Format("********")),
	}

	success := false
	if len(mismatches) == 0 {
		success = true
		return success, reportData, nil
	}
	return success, reportData, nil
}

func (a *AlpacaOutwardReconAuditor) getFileGenerationAttempts(ctx context.Context, startTime, endTime time.Time) (*SwiftTransferResult, error) {
	startTs := timestamppb.New(startTime)
	endTs := timestamppb.New(endTime)

	fileGenAttempts, err := a.fileGenerationAttemptDao.GetFileGenerationAttemptsByAcknowledgementTimePeriod(
		ctx,
		fgPb.FileType_FILE_TYPE_SWIFT_TRANSFER,
		startTs,
		endTs,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get file generation attempts")
	}

	logger.Info(ctx, "SWIFT fileGenAttempts count", zap.Int("count", len(fileGenAttempts)), zap.Time("start", startTime), zap.Time("end", endTime))
	if len(fileGenAttempts) == 0 {
		logger.Warn("No SWIFT file generation attempts found for date", zap.Time("date", startTime))
	}

	result := &SwiftTransferResult{
		Transfers: make(map[string]float64),
	}

	for _, attempt := range fileGenAttempts {
		logger.Info(ctx, "SWIFT file attempt", zap.String("id", attempt.GetId()), zap.String("status", attempt.GetFileStatus().String()))
		if attempt.GetFileStatus() == fgPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED {
			fileInfo := attempt.GetFileProcessingInfo()
			if fileInfo != nil {
				if swiftInfo := fileInfo.GetSwiftTransferFileProcessingInfo(); swiftInfo != nil {
					logger.Info(ctx, "Processing SWIFT file", zap.String("file_path", swiftInfo.GetGeneratedFilePath()))
					swiftTransfer, err := a.swiftReader.ReadFile(ctx, swiftInfo.GetGeneratedFilePath())
					if err != nil {
						logger.Error(ctx, "failed to read SWIFT file",
							zap.String("file_id", attempt.GetId()),
							zap.String("file_path", swiftInfo.GetGeneratedFilePath()),
							zap.Error(err))
						return result, err
					}
					// Add all transactions from this file to our result
					for txnID, amount := range swiftTransfer.Transfers {
						result.Transfers[txnID] = amount
						result.TotalAmount += amount
					}
				}
			}
		}
	}
	return result, nil
}

// ReadFile reads and parses a SWIFT file from S3
// Sample file format after line 13:
// SR. NO,ACCOUNT NUMBER,TRANSACTION ID,TRANSACTION TIME,AMOUNT (INR),AMOUNT (USD),EXCHANGE RATE,GST,TCS,LRS STATUS,LRS CONSUMED,SOF DOCUMENT TYPE,SOF DOCUMENT URL,A2 FORM URL,SOF VERIFICATION STATE,SOF ID
// 1,55550XX56XXXX,TXNS93GWefsHaqa250521_XXXXXXXX,21 May 25 20:57 IST,10079.24,116.00,86.89,45.00,0.00,PASSED,136.09,Federal Bank Statement,https://federal.epifi.in/api/v1/us-stocks/download-file?path=/********/FILE_TYPE_SOF_DOCUMENT/sof_USSWOXXXXXXX.pdf,https://federal.epifi.in/api/v1/us-stocks/download-file?path=%2F********%2FFILE_TYPE_A2_FORM%2Fa2form_USSWO2rTt1HVXNn250521_202XXXXXXX.pdf,SOF_VERIFICATION_STATE_VERIFIED,SOFU6t+GtHLTGywO4cdY+Hh+XXXXX==

func (r *SwiftReader) ReadFile(ctx context.Context, filePath string) (*SwiftTransferResult, error) {
	logger.Info(ctx, "Reading SWIFT file from S3", zap.String("filePath", filePath))
	// Read file from S3
	fileContent, err := r.s3Client.Read(ctx, filePath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read SWIFT file from S3")
	}

	// Create CSV reader with comma separator
	csvReader := csv.NewReader(bytes.NewReader(fileContent))
	csvReader.TrimLeadingSpace = true
	csvReader.LazyQuotes = true    // Handle quotes more flexibly
	csvReader.FieldsPerRecord = -1 // Allow variable number of fields

	// Read all records
	records, err := csvReader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse CSV file")
	}

	logger.Info(ctx, "SWIFT file records read", zap.String("filePath", filePath), zap.Int("num_records", len(records)))

	if len(records) < 14 { // 12 metadata lines + 1 header + at least 1 data row
		logger.Warn("Invalid SWIFT file format: insufficient records", zap.String("filePath", filePath), zap.Int("num_records", len(records)))
		return nil, errors.New("invalid file format: insufficient records")
	}

	result := &SwiftTransferResult{
		Transfers: make(map[string]float64),
	}

	// Process transactions starting from line 14 (index 13)
	for i, record := range records[13:] {
		// Extract transaction ID (index 2) and USD amount (index 5)
		transactionID := strings.TrimSpace(record[2])
		usdAmountStr := strings.TrimSpace(record[5])

		// Convert USD amount to float
		usdAmount, err := strconv.ParseFloat(usdAmountStr, 64)
		if err != nil {
			logger.Error(ctx, "failed to parse USD amount",
				zap.String("transaction_id", transactionID),
				zap.String("amount_str", usdAmountStr),
				zap.Error(err),
				zap.Int("row_index", i+14))
			continue
		}

		// Store the transaction
		result.Transfers[transactionID] = usdAmount
		result.TotalAmount += usdAmount
	}

	if len(result.Transfers) == 0 {
		logger.Warn("No valid transactions found in SWIFT file", zap.String("filePath", filePath))
	} else {
		// Log first 2 transactions for debug
		count := 0
		for txnID, amount := range result.Transfers {
			logger.Info(ctx, "Sample SWIFT transaction", zap.String("transactionID", txnID), zap.Float64("amount", amount))
			count++
			if count >= 2 {
				break
			}
		}
	}

	return result, nil
}

func (a *AlpacaOutwardReconAuditor) getFundTransferDetails(ctx context.Context, startTime, endTime time.Time) (*FundTransferResult, error) {
	// GetVendorAccountActivitiesFile API returns T-2 data for the requested range.
	// So, we always add 2 day from the requested time window.
	adjustedStartTime := startTime.AddDate(0, 0, +2)
	adjustedEndTime := endTime.AddDate(0, 0, +2)

	res, err := a.orderManagerClient.GetVendorAccountActivitiesFile(ctx, &orderPb.GetVendorAccountActivitiesFileRequest{
		AccountId: AccountId,
		FromTime:  timestamppb.New(adjustedStartTime),
		ToTime:    timestamppb.New(adjustedEndTime),
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get vendor account activities")
	}

	fileContent, err := downloadFileContent(res.GetFileDownloadUrl())
	if err != nil {
		return nil, errors.Wrap(err, "failed to download activities file")
	}

	result := &FundTransferResult{
		TargetAccountSummary: &TransactionSummary{},
		OtherAccountSummary:  &TransactionSummary{},
	}

	csvReader := csv.NewReader(bytes.NewReader(fileContent))
	csvReader.TrimLeadingSpace = true
	csvReader.LazyQuotes = true

	// Skip header
	if _, err := csvReader.Read(); err != nil {
		return nil, errors.Wrap(err, "failed to read CSV header")
	}

	for {
		record, err := csvReader.Read()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, errors.Wrap(err, "failed to read CSV record")
		}

		amount, err := strconv.ParseFloat(record[netAmountIdx], 64)
		if err != nil {
			logger.Warn("failed to parse amount", zap.String("amount", record[netAmountIdx]))
			continue
		}

		// In debit transactions, the description will be a UUID and less than 50 chars in length ex:c4bce4fa-e5a08-4632-b0o8-105299xxxx38
		// In credit transactions, the description will be more than 50 chars, and it will include all the details ex: type: wire, subtype: none, statement_id: XXX, direction: INCOMING, supplimentary_details: Incoming Money Transfer ORG=ACC-****0044|EPIFI T BNF=ACC-****7028|ALPACA SECURITIES LLC, USA| DBT=CITIBANK NA|***GL028| AMOUNT=XX.00USD, REF=XXX,of the credit transaction. So we are hardcoding it to OUTWARD_POOL_CREDIT
		var transfer *FundTransfer
		if len(record[descriptionIdx]) < 50 {
			transfer = &FundTransfer{
				TransactionID: record[descriptionIdx],
				Amount:        math.Abs(amount),
			}
		} else {
			transfer = &FundTransfer{
				TransactionID: "OUTWARD_POOL_CREDIT",
				Amount:        math.Abs(amount),
			}
		}

		if amount < 0 {
			result.OtherAccountSummary.Transfers = append(result.OtherAccountSummary.Transfers, transfer)
			result.OtherAccountSummary.TotalAmount += math.Abs(amount)
			result.OtherAccountSummary.Count++
		} else {
			result.TargetAccountSummary.Transfers = append(result.TargetAccountSummary.Transfers, transfer)
			result.TargetAccountSummary.TotalAmount += amount
			result.TargetAccountSummary.Count++
		}
	}
	return result, nil
}

func (a *AlpacaOutwardReconAuditor) getWalletOrdersData(ctx context.Context, transfers []*FundTransfer) (*WalletOrderResult, error) {
	result := &WalletOrderResult{
		Orders: make([]*WalletOrderDetail, 0),
	}

	for _, transfer := range transfers {
		walletOrder, err := a.orderDao.GetByClientOrderId(ctx, transfer.TransactionID)
		if err != nil {
			logger.Error(ctx, "Failed to find wallet order",
				zap.String("client_order_id", transfer.TransactionID),
				zap.Error(err))
			return nil, errors.Wrap(err, "failed to get wallet order")
		}
		amountInUSD := float64(walletOrder.InvoiceDetails.GetAmountIn_USD().GetUnits()) + float64(walletOrder.InvoiceDetails.GetAmountIn_USD().GetNanos())/1e9
		detail := &WalletOrderDetail{
			ClientOrderId: walletOrder.GetClientOrderId(),
			Id:            walletOrder.GetId(),
			PoolTxnId:     walletOrder.GetPoolTxnId(),
			SwiftTxnId:    walletOrder.GetSwiftTxnId(),
			AmountInUSD:   amountInUSD,
		}
		result.Orders = append(result.Orders, detail)
		result.TotalAmount += amountInUSD
	}

	return result, nil
}

// Refactored: Unified comparison for SWIFT, wallet order, and fund transfer
func (a *AlpacaOutwardReconAuditor) compareAmounts(swiftTransfers *SwiftTransferResult, fundTransfers *FundTransferResult, walletOrders *WalletOrderResult) []*AmountMismatch {
	mismatches := make([]*AmountMismatch, 0)

	// Create maps for easier lookup
	swiftAmounts := make(map[string]float64)
	for txnID, amount := range swiftTransfers.Transfers {
		swiftAmounts[txnID] = amount
	}

	fundTransferAmounts := make(map[string]float64)
	for _, ft := range fundTransfers.OtherAccountSummary.Transfers {
		fundTransferAmounts[ft.TransactionID] = ft.Amount
	}

	// Track processed IDs
	processedSwiftIds := make(map[string]bool)
	processedFundIds := make(map[string]bool)

	// Check wallet orders against both SWIFT and Fund transfers
	for _, wo := range walletOrders.Orders {
		var reasons []string
		swiftAmount, hasSwift := swiftAmounts[wo.PoolTxnId]
		fundAmount, hasFund := fundTransferAmounts[wo.ClientOrderId]

		if hasSwift {
			processedSwiftIds[wo.PoolTxnId] = true
			if swiftAmount != wo.AmountInUSD {
				reasons = append(reasons, fmt.Sprintf("Swift amount mismatch (%.2f vs %.2f)", swiftAmount, wo.AmountInUSD))
			}
		}
		if hasFund {
			processedFundIds[wo.ClientOrderId] = true
			if fundAmount != wo.AmountInUSD {
				reasons = append(reasons, fmt.Sprintf("Fund transfer amount mismatch (%.2f vs %.2f)", fundAmount, wo.AmountInUSD))
			}
		}
		if len(reasons) > 0 {
			mismatches = append(mismatches, &AmountMismatch{
				Id:                 wo.Id,
				ClientOrderId:      wo.ClientOrderId,
				SwiftTxnId:         wo.PoolTxnId,
				SwiftAmount:        swiftAmount,
				FundTransferAmount: fundAmount,
				AmountInUSD:        wo.AmountInUSD,
				Reason:             strings.Join(reasons, "; "),
			})
		}
	}
	// Check for unmatched SWIFT transfers
	for swiftTxnID, swiftAmount := range swiftAmounts {
		if !processedSwiftIds[swiftTxnID] {
			mismatches = append(mismatches, &AmountMismatch{
				SwiftTxnId:  swiftTxnID,
				SwiftAmount: swiftAmount,
				Reason:      fmt.Sprintf("Unmatched SWIFT transfer of amount %.2f", swiftAmount),
			})
		}
	}

	// Check for unmatched Fund transfers
	for fundTxnID, fundAmount := range fundTransferAmounts {
		if !processedFundIds[fundTxnID] {
			mismatches = append(mismatches, &AmountMismatch{
				ClientOrderId:      fundTxnID,
				FundTransferAmount: fundAmount,
				Reason:             fmt.Sprintf("Unmatched Fund transfer of amount %.2f", fundAmount),
			})
		}
	}
	return mismatches
}

// downloadFileContent downloads a file from the given URL and returns its content as a byte slice.
func downloadFileContent(url string) ([]byte, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create request")
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to download file")
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			err = errors.Wrap(closeErr, "failed to close response body")
		}
	}()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download file: status code %d", resp.StatusCode)
	}
	return io.ReadAll(resp.Body)
}
