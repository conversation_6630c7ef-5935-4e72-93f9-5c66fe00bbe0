package reports

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

type MFSIPMonitorAuditor struct {
	ruleSubscriptionsDB *sql.DB
	actionExecutionsDB  *sql.DB
}

func MutualFundSIPAuditor(ruleSubscriptionsDB, actionExecutionsDB *sql.DB) *MFSIPMonitorAuditor {
	return &MFSIPMonitorAuditor{
		ruleSubscriptionsDB: ruleSubscriptionsDB,
		actionExecutionsDB:  actionExecutionsDB,
	}
}

func (a *MFSIPMonitorAuditor) Audit(ctx context.Context, now time.Time) (bool, *ReportData, error) {
	scheduleSIPQuery := `
		SELECT id
		FROM rule_subscriptions
		WHERE state = 'ACTIVE'
		  AND version_state = 'CURRENT'
		  AND json_extract_scalar(rule_param_values, '$.ruleParamValues.mutualFundVal.mutualFundVal.orderSubType') = 'BUY_SIP'
		  AND (
		    -- Daily SIPs
		    rule_id = 'd157d5c9-8b32-4f1b-9185-a60a8263ea25'
		    -- Monthly SIPs
		    OR (rule_id = '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0'
		      AND CAST(json_extract_scalar(rule_param_values, '$.ruleParamValues.configuredDateOfMonth.intVal') AS INTEGER) = EXTRACT(DAY FROM CURRENT_DATE))
		    -- Weekly SIPs
		    OR (rule_id = '42286516-f4d1-421f-9c1c-1a7f65612e66'
		      AND json_extract_scalar(rule_param_values, '$.ruleParamValues.configuredDayOfWeek.strVal') = format_datetime(CURRENT_DATE, 'EEEE'))
		  )
		  AND CAST(created_at AS DATE) <> CURRENT_DATE
	`

	scheduledRows, err := a.ruleSubscriptionsDB.QueryContext(ctx, scheduleSIPQuery)
	if err != nil {
		logger.Error(ctx, "failed to query scheduled Mutual Fund SIPs", zap.Error(err))
		return false, nil, err
	}
	defer func(scheduledRows *sql.Rows) {
		err := scheduledRows.Close()
		if err != nil {
			logger.Error(ctx, "failed to close scheduled SIP rows", zap.Error(err))
		}
	}(scheduledRows)

	scheduledIDs := make(map[string]struct{})
	for scheduledRows.Next() {
		var id string
		if err := scheduledRows.Scan(&id); err != nil {
			logger.Error(ctx, "failed to scan scheduled SIP row", zap.Error(err))
			return false, nil, err
		}
		scheduledIDs[id] = struct{}{}
	}

	executedSIPQuery := `
		SELECT subscription_id
		FROM action_executions
		WHERE action_type = 'PURCHASE_MUTUAL_FUND'
		  AND CAST(created_at AS DATE) = current_date
	`

	executedRows, err := a.actionExecutionsDB.QueryContext(ctx, executedSIPQuery)
	if err != nil {
		logger.Error(ctx, "failed to query executed Mutual Fund SIPs", zap.Error(err))
		return false, nil, err
	}
	defer func(executedRows *sql.Rows) {
		err := executedRows.Close()
		if err != nil {
			logger.Error(ctx, "failed to close executed SIP rows", zap.Error(err))
		}
	}(executedRows)

	executedIDs := make(map[string]struct{})
	for executedRows.Next() {
		var id string
		if err := executedRows.Scan(&id); err != nil {
			logger.Error(ctx, "failed to scan executed SIP row", zap.Error(err))
			return false, nil, err
		}
		executedIDs[id] = struct{}{}
	}

	var missingIDs []string
	for id := range scheduledIDs {
		if _, ok := executedIDs[id]; !ok {
			missingIDs = append(missingIDs, id)
		}
	}

	reportTitle := "Mutual Fund SIP Execution Report"
	if len(missingIDs) == 0 {
		reportText := fmt.Sprintf("✅ Mutual Fund SIP Execution Report for %s:\nAll scheduled SIPs (Daily, Weekly, Monthly) were executed successfully.\nScheduled: %d, Executed: %d", now.Format("2006-01-02"), len(scheduledIDs), len(executedIDs))
		return true, &ReportData{
			Title: reportTitle,
			Text:  reportText,
		}, nil
	}

	csvRows := [][]string{{"Subscription ID"}}
	for _, id := range missingIDs {
		csvRows = append(csvRows, []string{id})
	}

	reportText := fmt.Sprintf("⚠️ Mutual Fund SIP Execution Report for %s:\n• Scheduled: %d\n• Executed: %d\n• Missing Executions: %d\n\n*Action Required:* The attached CSV contains SIPs that were scheduled but not executed.",
		now.Format("2006-01-02"), len(scheduledIDs), len(executedIDs), len(missingIDs))

	reportData := &ReportData{
		Title:    reportTitle + " - Missing Executions",
		Text:     reportText,
		CsvRows:  csvRows,
		filename: "missing_mutual_fund_sip_executions",
	}
	return false, reportData, nil
}
