Application:
  Environment: "staging"

AWS:
  Region: "ap-south-1"

EpifiDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "staging/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

SimulatorDb:
  AppName: "simulator"
  StatementTimeout: 1s
  Username: "simulator_dev_user"
  Password: ""
  DbType: "CRDB"
  Name: "simulator"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.simulator_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.simulator_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardPGDB:
  StatementTimeout: 5m
  Name: "credit_card_pgdb"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  DbType: "PGDB"
  SecretName: "staging/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 1ms
    UseInsecureLog: true

Secrets:
  Ids:
    ClientEncryptionKey: "staging/card/card-qr-code-aes-cbc-encryption-key"
    ClientEncryptionInitialisationVector: "staging/card/card-qr-code-aes-cbc-encryption-iv"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80
