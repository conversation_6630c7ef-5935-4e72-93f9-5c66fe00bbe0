package main

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jszwec/csvutil"
	errorsPkg "github.com/pkg/errors"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/vendorgateway"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/typesv2/account"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	savingsPkg "github.com/epifi/gamma/pkg/savings"
	"github.com/epifi/gamma/savings/extacct/dao/model"
	"github.com/epifi/gamma/scripts/onboarding_god_script/config"
)

type AccountClosureBalTransferEnquiry struct {
	config           *config.Config
	db               *gorm.DB
	savClient        savingsPb.SavingsClient
	exacctClient     extacct.ExternalAccountsClient
	slack            *slack.Client
	accountsVgClient accounts.AccountsClient
	bankCustClient   bankcust.BankCustomerServiceClient
	userClient       userPb.UsersClient
}

const (
	// duration window from the timeTill to fetch the alternate account detail submissions.
	durationWindowToFetchAltAccDetailSubmissions = 21 * 24 * time.Hour
	// we send slack alert if submission is older than 7 days and enquiry status is failure
	durationWindowToAlertAsManualIntervention = 20 * 24 * time.Hour
)

// DoJob - AccountClosureBalTransferEnquiry
// This script tracks the balance transfer status for closed accounts and manages the fund transfer process to users' alternate accounts.
// This script is to fetch balance transfer status & UTR for closed accounts and store it in our db.
//
// Process Overview
// When users close accounts, they submit alternate account details via web form.
// These details are shared with Federal Bank, which processes fund transfers asynchronously.
// The script monitors transfer progress using Federal's ThirdPartyAccountCollection API.
//
// Execution Steps
// Data Retrieval: Collects alternate account details from users submitted within the past 21 days from the specified timeTill parameter
// Status Check: Verifies if balance transfer enquiry status exists in database and filters accounts missing enquiry status or UTR
// Vendor Enquiry: Calls Federal's ThirdPartyAccountCollection API with ENQUIRY parameter to retrieve transfer status and UTR for the filtered accounts from above step.
// Database Update: Records successful enquiry status and UTR information in the database (closed_account_balance_transfers table)
// Retry Management: Continues retry attempts for submissions within 21 days; escalates to manual intervention for submissions older than 20 days
// Alert Generation: Sends Slack notifications when balance transfers require manual handling after 20-day retry period
//
// Scheduling Configuration
// Daily automated execution through Jenkins cron job,
// processing data from the last 21 days and sending alerts for transfers that haven't completed after 20 days of retry attempts,
// ensuring comprehensive monitoring of all account closure balance transfers.
func (j *AccountClosureBalTransferEnquiry) DoJob(ctx context.Context, request *JobRequest) error {
	// script accepts two arguments,
	// - (optional) timeTill in unix format - if timeTill timestamp is missing, defaults to previous day's EOD. 11:59:59PM
	// - (optional) numberOfDaysToFetch, uses 8 days if empty

	oauthToken := j.config.SlackOAuthToken
	j.slack = slack.New(oauthToken, slack.OptionDebug(true))

	timeTillStr := request.Args1
	if timeTillStr == "" {
		timeTillStr = fmt.Sprintf("%d", datetimePkg.EndOfDay(time.Now().Add(-24*time.Hour)).Unix())
	}

	unixTimeTill, parseErr := strconv.ParseInt(timeTillStr, 10, 64)
	if parseErr != nil {
		return j.sendSlackTrigger(ctx, errorsPkg.Wrapf(parseErr, "failed to parse time till, arg 1 - %s, timeTillStr %s", request.Args1, timeTillStr))
	}

	var numberOfDaysToFetch time.Duration
	if request.Args2 != "" {
		numberOfDaysToFetchInt, parseErr := strconv.ParseInt(request.Args2, 10, 64)
		if parseErr != nil {
			return j.sendSlackTrigger(ctx, errorsPkg.Wrapf(parseErr, "failed to parse args2, %s", request.Args1))
		}
		numberOfDaysToFetch = time.Duration(numberOfDaysToFetchInt) * 24 * time.Hour
	} else {
		numberOfDaysToFetch = durationWindowToFetchAltAccDetailSubmissions
	}

	timeTill := datetimePkg.EndOfDayWithLocation(time.Unix(unixTimeTill, 0).In(datetimePkg.IST))
	fromTime := datetimePkg.StartOfDayWithLocation(timeTill.Add(-numberOfDaysToFetch).In(datetimePkg.IST))
	toTime := timeTill

	bankAccVerifications, getBavErr := j.fetchAlternateAccountDetailSubmissions(ctx, fromTime, toTime)
	if getBavErr != nil {
		return j.sendSlackTrigger(ctx, errorsPkg.Wrapf(getBavErr, "failed to fetch bav details from db"))
	}

	var processErr error
	// alerts are sent only for transactions which are failed even after 7 days.
	var (
		successfulFundTransferCount = 0
		alertSummary                []*accClosureBalTransferEnquirySummary
	)

	for _, bav := range bankAccVerifications {
		actorId := bav.GetActorId()
		actorCtx := epificontext.WithTraceId(ctx, metadata.MD{})
		actorCtx = epificontext.CtxWithActorId(actorCtx, actorId)

		var err error
		successfulFundTransferCount, err = j.processBalanceTransferEnquiry(actorCtx, bav, &alertSummary, successfulFundTransferCount)
		if err != nil {
			processErr = errors.Join(processErr, fmt.Errorf("process bav failed, actor: %s, bavId: %s, %w", bav.GetActorId(), bav.GetId(), err))
		}
	}

	if processErr != nil {
		logger.Error(ctx, "process balance transfer enquiry failed", zap.Error(j.sendSlackTrigger(ctx, processErr)))
	}

	logger.Info(ctx, "SUCCESSFUL FUND TRANSFERS COUNT", zap.Int("count", successfulFundTransferCount))

	if len(alertSummary) > 0 {
		return j.sendAlert(ctx, alertSummary)
	}

	return nil
}

func (j *AccountClosureBalTransferEnquiry) processBalanceTransferEnquiry(ctx context.Context, bav *extacct.BankAccountVerification, alertSummary *[]*accClosureBalTransferEnquirySummary, successfulFundTransferCount int) (int, error) {
	savAccounts, err := j.getAccountsForActor(ctx, bav.GetActorId())
	if err != nil {
		return successfulFundTransferCount, err
	}

	for _, savAcc := range savAccounts {
		success, processErr := j.processSingleSavingsAccount(ctx, bav, savAcc, alertSummary)
		if processErr != nil {
			return successfulFundTransferCount, processErr
		}
		if success {
			successfulFundTransferCount++
		}
	}

	return successfulFundTransferCount, nil
}

// getAccountsForActor fetches the list of accounts for the given actorId.
func (j *AccountClosureBalTransferEnquiry) getAccountsForActor(ctx context.Context, actorId string) ([]*savingsPb.Account, error) {
	savResp, savErr := j.savClient.GetAccountsList(ctx, &savingsPb.GetAccountsListRequest{Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
		BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
			ActorIds:                []string{actorId},
			AccountProductOfferings: account.AllAccountProductOffering(),
			PartnerBanks:            []vendorgateway.Vendor{vendorgateway.Vendor_FEDERAL_BANK},
		},
	}})
	if rpcErr := epifigrpc.RPCError(savResp, savErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to fetch accounts for actor, %w", rpcErr)
	}
	return savResp.GetAccounts(), nil
}

// processSingleSavingsAccount processes a single savings account for balance transfer enquiry.
// Returns true if the transfer was successful in this enquiry attempt, false otherwise.
// nolint: funlen,gosec
func (j *AccountClosureBalTransferEnquiry) processSingleSavingsAccount(ctx context.Context, bav *extacct.BankAccountVerification, savAcc *savingsPb.Account, alertSummary *[]*accClosureBalTransferEnquirySummary) (bool, error) {
	cbtResp, cbtErr := j.savClient.GetClosedAccountBalTransferData(ctx, &savingsPb.GetClosedAccountBalTransferDataRequest{SavingsAccountId: savAcc.GetId()})
	if err := epifigrpc.RPCError(cbtResp, cbtErr); err != nil && !cbtResp.GetStatus().IsRecordNotFound() {
		return false, fmt.Errorf("failed to get closed acc bal transfer data, %w", err)
	}

	for _, cbt := range cbtResp.GetEntries() {
		if cbt.IsBalanceTransferCompleted() {
			logger.Info(ctx, "proceeding to process next account since balance transfer is already successful",
				zap.String(logger.ACTOR_ID_V2, bav.GetActorId()), zap.String("savingsAccountId", savAcc.GetId()),
				zap.String("balanceTransferUtr", cbt.GetUtr()), zap.String("balanceTransferStatus", cbt.GetTransactionStatus().String()))
			return false, nil // continue to process next savings account for the user since balance transfer is already completed
		}
	}

	// enquire if balance transfer is not completed:
	latestCbt := savingsPkg.GetLatestClosedAccountInfo(cbtResp.GetEntries())
	bankCustResp, bankCustErr := j.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: vendorgateway.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: bav.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(bankCustResp, bankCustErr); rpcErr != nil {
		return false, fmt.Errorf("failed to get bank customer details for actor %s, %w", bav.GetActorId(), rpcErr)
	}

	userResp, userRespErr := j.userClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: bav.GetActorId()}})
	if rpcErr := epifigrpc.RPCError(userResp, userRespErr); rpcErr != nil {
		return false, fmt.Errorf("failed to get user details for actor %s, %w", bav.GetActorId(), rpcErr)
	}
	// users not eligible for third party account sharing automated flow are processed using scripts/onboarding_god_script/job_account_closure_mail.go job.
	// it follows a manual process where we share a csv file to federal
	if !savingsPkg.IsAccountEligibleForThirdPartyAccountSharingAutomatedFlow(userResp.GetUser().GetAccessRevokeDetails()) {
		logger.Info(ctx, "user not eligible for automated fund transfer flow", zap.String(logger.ACTOR_ID_V2, bav.GetActorId()), zap.String("savingsAccountId", savAcc.GetId()))
		return false, nil
	}

	tpacResp, tpacErr := j.accountsVgClient.ThirdPartyAccountCollection(ctx, &accounts.ThirdPartyAccountCollectionRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_FEDERAL_BANK,
		},
		SbAccount:       savAcc.GetAccountNo(),
		CustomerId:      bankCustResp.GetBankCustomer().GetVendorCustomerId(),
		MobileNumber:    savAcc.GetPhoneNumber().ToString(),
		PennyDropId:     bav.GetVendorReqId(),
		TpAccountNumber: bav.GetAccountNumber(),
		TpIfsc:          bav.GetIfsc(),
		RequestType:     accounts.ThirdPartyAccountCollectionRequest_REQUEST_TYPE_ENQ,
	})
	if rpcErr := epifigrpc.RPCError(tpacResp, tpacErr); rpcErr != nil {
		return false, fmt.Errorf("ThirdPartyAccountCollection enquiry failed for account: %s, penny_drop_id: %s, %w", savAcc.GetId(), bav.GetVendorReqId(), rpcErr)
	}

	year, month, day := time.Now().Date()
	if tpacResp.IsBalanceTransferSuccessful() {
		latestCbt.TransactionStatus = savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS
		latestCbt.Utr = tpacResp.GetUtr()
		latestCbt.TransactionFailureReason = tpacResp.GetErrorCode().String()
		latestCbt.BavId = bav.GetId()
		// using current timestamp for date of transfer since it is not coming from fed api
		latestCbt.DateOfTransfer = &date.Date{
			Year:  int32(year),
			Month: int32(month),
			Day:   int32(day),
		}
	} else {
		latestCbt.TransactionStatus = savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_FAILED
		latestCbt.Utr = tpacResp.GetUtr()
		latestCbt.TransactionFailureReason = tpacResp.GetErrorCode().String()
		latestCbt.BavId = bav.GetId()

		formSubmissionTimestamp := bav.GetUpdatedAt().AsTime()
		if time.Since(formSubmissionTimestamp) > durationWindowToAlertAsManualIntervention {
			*alertSummary = append(*alertSummary, &accClosureBalTransferEnquirySummary{
				ActorId:            bav.GetActorId(),
				SavingsAccId:       savAcc.GetId(),
				PennyDropId:        bav.GetVendorReqId(),
				TransactionStatus:  latestCbt.GetTransactionStatus().String(),
				FailureReason:      tpacResp.GetErrorCode().String(),
				FormSubmissionDate: formSubmissionTimestamp.Format(time.DateTime),
			},
			)
		}
	}

	updResp, updErr := j.savClient.UpdateClosedAccountBalTransferData(ctx, &savingsPb.UpdateClosedAccountBalTransferDataRequest{
		Data: latestCbt,
		FieldMasks: []savingsPb.CbtFieldMask{
			savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_DETAILS,
			savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_STATUS,
			savingsPb.CbtFieldMask_CBT_FIELD_MASK_BAV_ID,
		},
	})
	if rpcErr := epifigrpc.RPCError(updResp, updErr); rpcErr != nil {
		return false, fmt.Errorf("cbt update failed for account: %s, actor_id: %s, %w", savAcc.GetId(), bav.GetActorId(), rpcErr)
	}

	return tpacResp.IsBalanceTransferSuccessful(), nil
}

func (j *AccountClosureBalTransferEnquiry) fetchAlternateAccountDetailSubmissions(ctx context.Context, fromTime, toTime time.Time) ([]*extacct.BankAccountVerification, error) {
	var (
		bankAccountVerifications []*extacct.BankAccountVerification
		mod                      []*model.BankAccountVerification
	)

	fromTimeStr := fromTime.Format(time.RFC3339)
	toTimeStr := toTime.Format(time.RFC3339)
	query := fmt.Sprintf("SELECT * FROM bank_account_verifications WHERE bank_account_verifications.updated_at > '%v' AND bank_account_verifications.updated_at < '%v' AND bank_account_verifications.overall_status = '%v' AND (bank_account_verifications.caller->'source' = '\"%v\"' OR bank_account_verifications.caller->'source' = '\"%v\"')", fromTimeStr, toTimeStr, extacct.OverallStatus_OVERALL_STATUS_SUCCESS.String(), extacct.Source_SOURCE_USER.String(), extacct.Source_SOURCE_WEB.String())
	logger.Info(ctx, query)
	if err := j.db.Raw(query).Scan(&mod).Error; err != nil {
		logger.Error(ctx, "error while fetching BAV details from DB", zap.Error(err))
		return nil, j.sendSlackTrigger(ctx, err)
	}

	for _, m := range mod {
		bankAccountVerifications = append(bankAccountVerifications, m.ConvertToProto())
	}

	return bankAccountVerifications, nil
}

const accClosureBalTransferSlackMessage = "<@S08BA216HQQ> <@U08G4LZ0RGQ> <@U032YMJQ3E0> Account closure balance transfer enquiry job report."

func (j *AccountClosureBalTransferEnquiry) sendSlackTrigger(ctx context.Context, err error) error {
	var (
		channelID = "C020HNXMSGH"
	)
	slackAttachment := slack.Attachment{
		Pretext: accClosureBalTransferSlackMessage,
		Text:    err.Error(),
	}
	_, timestamp, errSlack := j.slack.PostMessage(channelID, slack.MsgOptionAttachments(slackAttachment))
	if errSlack != nil {
		logger.Error(ctx, "error posting message on slack", zap.Error(errSlack))
		return err
	}

	logger.Info(ctx, "slack message posted", zap.String("timestamp", timestamp))
	return err
}

type accClosureBalTransferEnquirySummary struct {
	ActorId            string `csv:"ActorId"`
	SavingsAccId       string `csv:"SavingsAccId"`
	PennyDropId        string `csv:"PennyDropId"`
	TransactionStatus  string `csv:"TransactionStatus"`
	FailureReason      string `csv:"FailureReason"`
	FormSubmissionDate string `csv:"FormSubmissionDate"`
}

func (j *AccountClosureBalTransferEnquiry) sendAlert(ctx context.Context, alertSummary []*accClosureBalTransferEnquirySummary) error {
	var (
		channelID = "C020HNXMSGH"
	)

	dataBytes, err := csvutil.Marshal(alertSummary)
	if err != nil {
		return fmt.Errorf("error in marshalling alert summary %w", err)
	}
	logger.Info(ctx, "alert summary data", zap.String("data", string(dataBytes)))

	dtString := timestamppb.Now().AsTime().In(datetimePkg.IST).Format("2006-01-02_15-04-05")
	fileName := fmt.Sprintf("balance-transfer-enquiry-report-%v.csv", dtString)
	csvReader := bytes.NewReader(dataBytes)
	params := slack.UploadFileV2Parameters{
		Reader:         csvReader,
		Filename:       fileName,
		FileSize:       csvReader.Len(),
		InitialComment: accClosureBalTransferSlackMessage,
		Title:          "report",
		Channel:        channelID,
	}

	_, err = j.slack.UploadFileV2(params)
	if err != nil {
		return fmt.Errorf("error in slackClient.UploadFile %w", err)
	}
	return nil
}
