package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	balanceEnumsPb "github.com/epifi/gamma/api/accounts/balance/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	feSaClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	beSavingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	"github.com/epifi/gamma/scripts/onboarding_god_script/config"
)

const (
	pageSize = 100
)

// JobMailSavingsAccountClosureRequests script gets two arguments, from time and to time
// sends email to ops the closure request that are submitted T-10 days back (config driven - see savings service config for exact value)
// so, if from and to timestamp points to today, we will list requests that became eligible for submission to ops today
// i.e., requests that are submitted T-10 days back to the requested timestamp are sent to ops
// the closure requests are validated on criteria and freeze checks before sending it to ops
type JobMailSavingsAccountClosureRequests struct {
	saClosureFeClient    feSaClosurePb.SavingsAccountClosureClient
	savingsClient        beSavingsPb.SavingsClient
	beCommsClient        commsPb.CommsClient
	exacctClient         extacct.ExternalAccountsClient
	config               *config.Config
	slack                *slack.Client
	accountBalanceClient accountBalancePb.BalanceClient
}

func (j *JobMailSavingsAccountClosureRequests) DoJob(ctx context.Context, request *JobRequest) error {
	// script gets two arguments, from time and to time in unix format
	// if from and to timestamp are missing, defaults to previous day's timestamps
	// todo: add another argument to force query submitted request - to forcefully fetch requests in SENT_TO_OPS status also
	oauthToken := j.config.SlackOAuthToken
	j.slack = slack.New(oauthToken, slack.OptionDebug(true))

	if request.Args1 == "" {
		request.Args1 = fmt.Sprintf("%d", datetimePkg.StartOfDay(time.Now().Add(-24*time.Hour)).Unix())
	}

	if request.Args2 == "" {
		request.Args2 = fmt.Sprintf("%d", datetimePkg.EndOfDay(time.Now().Add(-24*time.Hour)).Unix())
	}

	unixFromTime, parseErr1 := strconv.ParseInt(request.Args1, 10, 64)
	if parseErr1 != nil {
		return j.sendSlackTrigger(ctx, errors.Wrapf(parseErr1, "failed to parse from-timestamp, %s", request.Args1))
	}

	unixToTime, parseErr2 := strconv.ParseInt(request.Args2, 10, 64)
	if parseErr2 != nil {
		return j.sendSlackTrigger(ctx, errors.Wrapf(parseErr2, "failed to parse to-timestamp, %s", request.Args2))
	}

	fromTime := time.Unix(unixFromTime, 0)
	toTime := time.Unix(unixToTime, 0)

	submittedClosureRequests, collectErr := j.collectSubmittedClosureRequests(ctx, fromTime, toTime)
	if collectErr != nil {
		return j.sendSlackTrigger(ctx, errors.Wrapf(collectErr, "failed to collect submitted closure requests, from-timestamp: %s, to-timestamp %s", fromTime, toTime))
	}

	filteredClosureRequests, filterErr := j.filterEligibleRequests(ctx, submittedClosureRequests)
	if filterErr != nil {
		return j.sendSlackTrigger(ctx, errors.Wrapf(filterErr, "failed to filter eligible requests"))
	}

	logger.Info(ctx, "count of dropped closure request after filtering",
		zap.Int("submitted closure request", len(submittedClosureRequests)),
		zap.Int("filtered closure requests", len(filteredClosureRequests)),
		zap.Int("dropped closure requests", len(submittedClosureRequests)-len(filteredClosureRequests)),
	)

	csvRows, getCsvRowsErr := j.getSaClosureCsvRows(ctx, filteredClosureRequests)
	if getCsvRowsErr != nil {
		return j.sendSlackTrigger(ctx, errors.Wrap(getCsvRowsErr, "failed to get csv rows"))
	}

	csvFile, csvBytes, err := createAndConvertToBytesCsvFile(ctx, csvRows, "submitted_closure_requests_")
	if err != nil {
		return j.sendSlackTrigger(ctx, errors.Wrap(err, "failed to convert to bytes and csv file"))
	}

	mailErr := j.sendMail(ctx, csvFile, csvBytes, len(filteredClosureRequests), fromTime, toTime)
	if mailErr != nil {
		return j.sendSlackTrigger(ctx, errors.Wrap(mailErr, "failed to send mail"))
	}

	for _, closureRequest := range filteredClosureRequests {
		updateResp, updateErr := j.savingsClient.UpdateSaClosureRequestStatus(ctx, &beSavingsPb.UpdateSaClosureRequestStatusRequest{
			ClosureRequestId: closureRequest.GetId(),
			Status:           beSavingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SENT_TO_OPS,
		})
		if rpcErr := epifigrpc.RPCError(updateResp, updateErr); rpcErr != nil {
			return j.sendSlackTrigger(ctx, errors.Wrap(rpcErr, "failed to update closure request status"))
		}
	}

	return nil
}

func (j *JobMailSavingsAccountClosureRequests) sendMail(
	ctx context.Context,
	csvFile string,
	csvBytes []byte,
	count int,
	fromTime, toTime time.Time,
) error {
	// DD-MM-YYYY
	currentDate := time.Now().Format("02-01-2006")
	additionalMsg := fmt.Sprintf("Savings account closure requests for %s to %s\n, Expected rows in the file: %v",
		fromTime.In(datetimePkg.IST).Format(time.RFC822),
		toTime.In(datetimePkg.IST).Format(time.RFC822),
		count)
	var attachments []*commsPb.EmailMessage_Attachment

	attachments = append(attachments, &commsPb.EmailMessage_Attachment{
		FileContent:    csvBytes,
		FileName:       csvFile,
		Disposition:    commsPb.Disposition_ATTACHMENT,
		AttachmentType: "text/comma-separated-values",
	})

	resp, err := j.beCommsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: j.config.SaClosureEmailConfig.ToEmailId,
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   j.config.SaClosureEmailConfig.FromEmailId,
				FromEmailName: j.config.SaClosureEmailConfig.FromEmailName,
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_SaClosureRequestEmailOption{
						SaClosureRequestEmailOption: &commsPb.SaClosureRequestEmailOption{
							EmailType: commsPb.EmailType_SAVINGS_ACCOUNT_CLOSURE_REQUEST,
							Option: &commsPb.SaClosureRequestEmailOption_SaClosureSubmitToCxEmailV1{
								SaClosureSubmitToCxEmailV1: &commsPb.SaClosureSubmitToCxEmailV1{
									CurrentDate:       currentDate,
									TemplateVersion:   commsPb.TemplateVersion_VERSION_V1,
									AdditionalMessage: additionalMsg,
								},
							},
						},
					},
				},
				Attachment: attachments,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		return fmt.Errorf("error while emailing, %w", grpcErr)
	}
	return nil
}

func (j *JobMailSavingsAccountClosureRequests) getSaClosureCsvRows(ctx context.Context, closureRequests []*beSavingsPb.SavingsAccountClosureRequest,
) ([]*FederalCloseAccountRequestFormat, error) {

	var csvRows []*FederalCloseAccountRequestFormat

	for idx, closureRequest := range closureRequests {
		ctx = epificontext.CtxWithActorId(ctx, closureRequest.GetActorId())

		getAccResp, getAccErr := j.savingsClient.GetAccount(ctx, &beSavingsPb.GetAccountRequest{Identifier: &beSavingsPb.GetAccountRequest_Id{
			Id: closureRequest.GetSavingsAccountId()},
		})
		switch {
		case getAccErr != nil:
			return nil, j.sendSlackTrigger(ctx, fmt.Errorf("error fetching account details, %w", getAccErr))
		case getAccResp.GetAccount() == nil:
			return nil, j.sendSlackTrigger(ctx, fmt.Errorf("got nil account from GetAccount rpc"))
		}

		getBalResp, getBalErr := j.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
			Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: closureRequest.GetSavingsAccountId()},
			ActorId:       closureRequest.GetActorId(),
			DataFreshness: balanceEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
		})
		if rpcErr := epifigrpc.RPCError(getBalResp, getBalErr); rpcErr != nil {
			return nil, j.sendSlackTrigger(ctx, errors.Wrap(rpcErr, "error fetching current balance for the user"))
		}

		csvRow := convertToFederalCloseAccFormat(idx+1, nil, getAccResp.GetAccount().GetAccountNo(),
			j.config.Secrets.Ids[config.FederalPoolAccountNo], "Account Open",
			money.ToDisplayStringWithINRSymbol(getBalResp.GetAvailableBalance()),
			getBalResp.GetBalanceAt().AsTime().Format(time.DateTime))

		csvRows = append(csvRows, csvRow)
	}

	return csvRows, nil
}

func (j *JobMailSavingsAccountClosureRequests) collectSubmittedClosureRequests(ctx context.Context,
	fromTimestamp, toTimestamp time.Time) ([]*beSavingsPb.SavingsAccountClosureRequest, error) {

	var submittedClosureRequests []*beSavingsPb.SavingsAccountClosureRequest
	pageContext := &rpc.PageContextRequest{
		Token:    nil,
		PageSize: pageSize,
	}

	for {
		getSaClosureReqResp, getErr := j.savingsClient.GetSubmittedSaClosureRequests(ctx, &beSavingsPb.GetSubmittedSaClosureRequestsRequest{
			PageContext:   pageContext,
			FromTimestamp: timestamppb.New(fromTimestamp),
			ToTimestamp:   timestamppb.New(toTimestamp),
		})
		if rpcErr := epifigrpc.RPCError(getSaClosureReqResp, getErr); rpcErr != nil && !getSaClosureReqResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(rpcErr, "failed to get submitted closure request from savings client")
		}

		submittedClosureRequests = append(submittedClosureRequests, getSaClosureReqResp.GetSavingsAccountClosureRequests()...)

		if !getSaClosureReqResp.GetPageContext().GetHasAfter() {
			break
		}

		pageContext.Token = &rpc.PageContextRequest_AfterToken{AfterToken: getSaClosureReqResp.GetPageContext().GetAfterToken()}
	}

	return submittedClosureRequests, nil
}

func (j *JobMailSavingsAccountClosureRequests) filterEligibleRequests(ctx context.Context,
	submittedClosureRequests []*beSavingsPb.SavingsAccountClosureRequest,
) ([]*beSavingsPb.SavingsAccountClosureRequest, error) {

	var filteredClosureRequests []*beSavingsPb.SavingsAccountClosureRequest

	for _, submittedReq := range submittedClosureRequests {
		if submittedReq.GetStatus().IsCancelledStatus() {
			logger.Info(ctx, "skipping since request is already in cancelled status", zap.String(logger.REQUEST_ID, submittedReq.GetId()), zap.String(logger.STATUS, submittedReq.GetStatus().String()))
			continue
		}
		evalResp, evalErr := j.saClosureFeClient.EvaluateUserForClosureEligibility(ctx, &feSaClosurePb.EvaluateUserForClosureEligibilityRequest{
			ActorId: submittedReq.GetActorId(),
		})
		rpcErr := fepkg.FeRPCError(evalResp, evalErr)
		if rpcErr != nil {
			if evalResp.GetRespHeader().GetStatus().GetCode() == uint32(feSaClosurePb.EvaluateUserForClosureEligibilityResponse_FETCH_OPERATIONAL_STATUS_FAILURE.Number()) {
				// skip evaluating if operation status api fails and proceed with the request for closure without dropping
				logger.Info(ctx, "operational status api failed for actor, unable to check eligibility", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, submittedReq.GetActorId()))
			} else {
				return nil, fmt.Errorf("EvaluateUserOnCriterias rpc failed for actor: %s, %w", submittedReq.GetActorId(), rpcErr)
			}
		}

		if rpcErr == nil && (evalResp.GetHasFreeze() || evalResp.GetFailedCriteriaCheck() || evalResp.GetHasLien() || evalResp.GetHasPendingCharges()) {
			updateResp, updateErr := j.savingsClient.UpdateSaClosureRequestStatus(ctx, &beSavingsPb.UpdateSaClosureRequestStatusRequest{
				ClosureRequestId: submittedReq.GetId(),
				Status:           beSavingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_CANCELLED_ON_VALIDATION,
				StatusReason:     j.getStatusReason(evalResp),
			})
			if rpcErr := epifigrpc.RPCError(updateResp, updateErr); rpcErr != nil {
				if updateResp.GetStatus().GetCode() == uint32(beSavingsPb.UpdateSaClosureRequestStatusResponse_TERMINAL_STATUS_UPDATE_NOT_ALLOWED.Number()) {
					logger.Error(ctx, "terminal status update not allowed", zap.String(logger.REQUEST_ID, submittedReq.GetId()), zap.String(logger.STATUS, submittedReq.GetStatus().String()))
					continue
				}
				return nil, errors.Wrap(rpcErr, "UpdateSaClosureRequestStatus rpc failed")
			}

			continue
		}

		filteredClosureRequests = append(filteredClosureRequests, submittedReq)
	}

	return filteredClosureRequests, nil
}

func (j *JobMailSavingsAccountClosureRequests) getStatusReason(evalResp *feSaClosurePb.EvaluateUserForClosureEligibilityResponse) beSavingsPb.SAClosureRequestStatusReason {
	switch {
	case evalResp.GetHasFreeze():
		return beSavingsPb.SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_HAS_FREEZE
	case evalResp.GetFailedCriteriaCheck():
		return beSavingsPb.SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_FAILED_CRITERIA_CHECK
	case evalResp.GetHasLien():
		return beSavingsPb.SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_HAS_LIEN
	case evalResp.GetHasPendingCharges():
		return beSavingsPb.SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_HAS_PENDING_CHARGES
	default:
		return beSavingsPb.SAClosureRequestStatusReason_SA_CLOSURE_REQUEST_STATUS_REASON_UNSPECIFIED
	}
}

func (j *JobMailSavingsAccountClosureRequests) sendSlackTrigger(ctx context.Context, err error) error {
	var (
		channelID = "C020HNXMSGH"
	)
	slackAttachment := slack.Attachment{
		Pretext: "<@S05UWQU2HGA> <@U05781B95BL> job for sending SA Closure requests failed",
		Text:    err.Error(),
	}
	_, timestamp, errSlack := j.slack.PostMessage(channelID, slack.MsgOptionAttachments(slackAttachment))
	if errSlack != nil {
		logger.Error(ctx, "error posting message on slack", zap.Error(errSlack))
		return err
	}

	logger.Info(ctx, "slack message posted", zap.String("timestamp", timestamp))
	return err
}
