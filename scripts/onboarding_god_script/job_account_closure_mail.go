package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	savingsPkg "github.com/epifi/gamma/pkg/savings"

	"context"
	"fmt"
	"io/ioutil"
	"strconv"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/slack-go/slack"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	userPb "github.com/epifi/gamma/api/user"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/savings/extacct/dao/model"
	"github.com/epifi/gamma/scripts/onboarding_god_script/config"
)

type JobFetchBankAccountVerification struct {
	db              *gorm.DB
	savClient       savings.SavingsClient
	actorClient     actor.ActorClient
	exacctClient    extacct.ExternalAccountsClient
	commsClient     commsPb.CommsClient
	authClient      authPb.AuthClient
	userClient      userPb.UsersClient
	vgDepositClient vgDepositPb.DepositClient
	config          *config.Config
	slack           *slack.Client
	bcClient        bankcust.BankCustomerServiceClient
}

// FederalCloseAccountRequestFormat is the csv format in which Federal Bank has requested data for account closures
type FederalCloseAccountRequestFormat struct {
	SLNo               string `csv:"SL No"`                      // Bank fills
	NEFT               string `csv:"NEFT"`                       // NEFT for all
	FederalBank        string `csv:"NEFT"`                       // Federal Bank for all
	PoolAccountNo      string `csv:"DEBIT ACCNT NO"`             // Federal's Pool Account for closed accounts
	Amnt               string `csv:"AMNT"`                       // Bank fills
	Date               string `csv:"DATE"`                       // Today's date in DDMMYYYY format
	AccHoldersName     string `csv:"ACCTNAME"`                   // Account holder's name of user given account
	AccIfsc            string `csv:"IFSC"`                       // IFSC Code of external account
	AccIfsc2           string `csv:"IFSC"`                       // Account holder's name of user given account
	NEFT2              string `csv:"NEFT"`                       // NEFT for all
	Code               string `csv:"CODE"`                       // 10 for all
	AccNumber          string `csv:"Account No"`                 // Account number of user given account
	Remarks            string `csv:"Remarks"`                    // "FiAccountNumber CLOSURE" format ex. "555501XXXXXXXX CLOSURE"
	AccountStatus      string `csv:"Account Status"`             // AccountStatus denotes if account is closed/not from federals end
	AccountBalance     string `csv:"Last Known Account Balance"` // AccountBalance is the last known account balance
	BalanceCaptureTime string `csv:"Balance Captured At"`        // BalanceCaptureTime is the time at which AccountBalance was last captured
}

func convertToFederalCloseAccFormat(slNo int, verifiedAccount *extacct.BankAccount, fiAcctNo, federalPoolNo, accountStatus, balance, balanceTime string) *FederalCloseAccountRequestFormat {
	return &FederalCloseAccountRequestFormat{
		SLNo:               fmt.Sprintf("%d", slNo),
		NEFT:               "NEFT",
		FederalBank:        "Federal Bank",
		PoolAccountNo:      federalPoolNo,
		Amnt:               "",
		Date:               fmt.Sprintf("%v%v%v", time.Now().Day(), time.Now().Month(), time.Now().Year()),
		AccHoldersName:     verifiedAccount.GetName(),
		AccIfsc:            verifiedAccount.GetIfsc(),
		AccIfsc2:           verifiedAccount.GetIfsc(),
		NEFT2:              "NEFT",
		Code:               "10",
		AccNumber:          verifiedAccount.GetAccountNumber(),
		Remarks:            fiAcctNo + " CLOSURE",
		AccountStatus:      accountStatus,
		AccountBalance:     balance,
		BalanceCaptureTime: balanceTime,
	}
}

func createAndConvertToBytesCsvFile(ctx context.Context, list interface{}, fileName string) (string, []byte, error) {
	csvFile, err := ioutil.TempFile("", fileName+"*-"+time.Now().Format("**************"))
	if err != nil {
		logger.Error(ctx, "error creating a csv file", zap.Error(err))
		return "", nil, err
	}
	defer func() {
		if csvErr := csvFile.Close(); csvErr != nil {
			logger.Error(ctx, "error while closing the csv file", zap.Error(csvErr))
		}
	}()

	switch ls := list.(type) {
	case []*FederalCloseAccountRequestFormat:
		if err = gocsv.MarshalFile(ls, csvFile); err != nil {
			logger.Error(ctx, "failed to write contents to a csv file in federal format", zap.Error(err))
			return "", nil, err
		}
	case []*ActorBAVs:
		if err = gocsv.MarshalFile(ls, csvFile); err != nil {
			logger.Error(ctx, "failed to write contents to a csv file in actor bav format", zap.Error(err))
			return "", nil, err
		}
	case []*NrOnboardingLog:
		if err = gocsv.MarshalFile(ls, csvFile); err != nil {
			logger.Error(ctx, "failed to write contents to a csv file for nr onboarding logs", zap.Error(err))
			return "", nil, err
		}
	case []*DobReconciliationActorDetail:
		if err = gocsv.MarshalFile(ls, csvFile); err != nil {
			logger.Error(ctx, "failed to write contents to a csv file for dob reconciliation details", zap.Error(err))
			return "", nil, err
		}
	default:
		return "", nil, epifierrors.ErrInvalidArgument
	}
	logger.Info(ctx, "csv file generated successfully")
	csvBytes, err := ioutil.ReadFile(csvFile.Name())
	if err != nil {
		logger.Error(ctx, "failed to read contents from csv file", zap.Error(err))
		return "", nil, err
	}
	return csvFile.Name(), csvBytes, nil
}

func (j *JobFetchBankAccountVerification) DoJob(ctx context.Context, request *JobRequest) error {
	// This script expects 2 optional arguments, Args1 = From time and Args2 = To time. It expects time in Unix format.
	// In case these arguments are missing, it picks last 1405 minutes as a time range.
	var (
		from       = time.Now().Add(-24*time.Hour - 5*time.Minute).Format(time.RFC3339)
		to         = time.Now().Format(time.RFC3339)
		mod        []model.BankAccountVerification
		csvRows    []*FederalCloseAccountRequestFormat
		oauthToken = j.config.SlackOAuthToken
	)
	j.slack = slack.New(oauthToken, slack.OptionDebug(true))
	if request.Args1 != "" {
		utime, err := strconv.ParseInt(request.Args1, 10, 64)
		if err != nil {
			logger.Error(ctx, "error in parsing from time", zap.Error(err))
			return j.sendSlackTrigger(ctx, err)
		}
		from = time.Unix(utime, 0).Format(time.RFC3339)
	}
	if request.Args2 != "" {
		utime, err := strconv.ParseInt(request.Args2, 10, 64)
		if err != nil {
			logger.Error(ctx, "error in parsing to time", zap.Error(err))
			return j.sendSlackTrigger(ctx, err)
		}
		to = time.Unix(utime, 0).Format(time.RFC3339)
	}
	query := fmt.Sprintf("SELECT * FROM bank_account_verifications WHERE bank_account_verifications.updated_at > '%v' AND bank_account_verifications.updated_at < '%v' AND bank_account_verifications.overall_status = '%v' AND (bank_account_verifications.caller->'source' = '\"%v\"' OR bank_account_verifications.caller->'source' = '\"%v\"')", from, to, extacct.OverallStatus_OVERALL_STATUS_SUCCESS.String(), extacct.Source_SOURCE_USER.String(), extacct.Source_SOURCE_WEB.String())
	logger.Info(ctx, query)
	if err := j.db.Raw(query).Scan(&mod).Error; err != nil {
		logger.Error(ctx, "error while fetching BAV details from DB", zap.Error(err))
		return j.sendSlackTrigger(ctx, err)
	}
	logger.Info(ctx, "records fetched", zap.Int("number_of_records", len(mod)))
	if err := j.processBankAccountVerification(ctx, mod, &csvRows); err != nil {
		logger.Error(ctx, "error while generating csv rows", zap.Error(err))
		return j.sendSlackTrigger(ctx, err)
	}
	federalFile, federalBytes, err := createAndConvertToBytesCsvFile(ctx, csvRows, "successful_validations")
	if err != nil {
		logger.Error(ctx, "error creating csv for successful account validation", zap.Error(err))
		return j.sendSlackTrigger(ctx, err)
	}
	// return fmt.Errorf("this is a test error")
	return j.sendMail(ctx, federalFile, federalBytes, len(mod))
}

func (j *JobFetchBankAccountVerification) sendMail(ctx context.Context, file string, bytes []byte, count int) error {
	// https://stackoverflow.com/questions/********/convert-time-time-to-string/********#********
	// DD-MM-YYYY
	currentDate := time.Now().Format("02-01-2006")
	additionalMsg := fmt.Sprintf("Expected rows in the file: %v", count)
	attachments := []*commsPb.EmailMessage_Attachment{
		{
			FileContent:    bytes,
			FileName:       file,
			Disposition:    commsPb.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		},
	}
	resp, err := j.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: j.config.BulkAccValidationViaEmailConfig.ToEmailId,
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   j.config.BulkAccValidationViaEmailConfig.FromEmailId,
				FromEmailName: j.config.BulkAccValidationViaEmailConfig.FromEmailName,
				ToEmailName:   j.config.BulkAccValidationViaEmailConfig.ToEmailName,
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_MinKycAccountClosureUserVerifiedCxEmailOption{
						MinKycAccountClosureUserVerifiedCxEmailOption: &commsPb.MinKycAccountClosureUserVerifiedEmailOption{
							EmailType: commsPb.EmailType_MIN_KYC_ACCOUNT_CLOSURE_USER_VERIFIED,
							Option: &commsPb.MinKycAccountClosureUserVerifiedEmailOption_MinKycAccountClosureUserVerifiedMailToCxV1{
								MinKycAccountClosureUserVerifiedMailToCxV1: &commsPb.MinKycAccountClosureUserVerifiedEmailOptionV1{
									CurrentDate:     currentDate,
									AdditionalMsg:   additionalMsg,
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
								},
							},
						},
					},
				},
				Attachment: attachments,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error while emailing bulk user details CSV", zap.Error(grpcErr))
		return j.sendSlackTrigger(ctx, grpcErr)
	}
	return nil
}

func (j *JobFetchBankAccountVerification) processBankAccountVerification(ctx context.Context, mod []model.BankAccountVerification, csvRows *[]*FederalCloseAccountRequestFormat) error {
	count := 0
	skippedDueToAutomatedFundTransferCount := 0
	var skippedDueToAutomatedFundTransferActors []string

	for idx, bav := range mod {
		ctx = epificontext.CtxWithActorId(ctx, bav.ActorId)
		actorResp, errResp := j.actorClient.GetEntityDetailsByActorId(ctx, &actor.GetEntityDetailsByActorIdRequest{
			ActorId: bav.ActorId,
		})
		if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
			logger.Error(ctx, "error while fetching entity details by actor id", zap.Error(err))
			return j.sendSlackTrigger(ctx, err)
		}
		savingsResp, errResp := j.savClient.GetAccount(ctx, &savings.GetAccountRequest{
			Identifier: &savings.GetAccountRequest_PrimaryUserId{
				PrimaryUserId: actorResp.GetEntityId(),
			},
		})
		if errResp != nil {
			logger.Error(ctx, "error while fetching savings account details by user id", zap.Error(errResp))
			return j.sendSlackTrigger(ctx, errResp)
		}

		userResp, userErr := j.userClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{
				ActorId: bav.ActorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(userResp, userErr); rpcErr != nil {
			logger.Error(ctx, "error while fetching user details by actor id", zap.Error(rpcErr))
			return j.sendSlackTrigger(ctx, rpcErr)
		}

		accessRevokeDetails := userResp.GetUser().GetAccessRevokeDetails()
		if savingsPkg.IsAccountEligibleForThirdPartyAccountSharingAutomatedFlow(accessRevokeDetails) {
			// Skip the sending to ops if the account was blocked after the automated fund transfer start date
			skippedDueToAutomatedFundTransferCount++
			skippedDueToAutomatedFundTransferActors = append(skippedDueToAutomatedFundTransferActors, bav.ActorId)
			continue
		}

		verifiedAccts, err := j.exacctClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
			ActorId: bav.ActorId,
		})
		if grpcErr := epifigrpc.RPCError(verifiedAccts, err); grpcErr != nil {
			logger.Error(ctx, "error fetching successfully verified external account of the user", zap.Error(grpcErr))
			return j.sendSlackTrigger(ctx, grpcErr)
		}
		bal, balTime := j.getAccountBalanceDuringClosure(ctx, savingsResp.GetAccount().GetId())
		// verifiedAccts are in descending order of update time, hence first record is the latest verified account details
		if len(verifiedAccts.GetBankAccounts()) > 0 {
			*csvRows = append(*csvRows, convertToFederalCloseAccFormat(idx+1, verifiedAccts.GetBankAccounts()[0],
				savingsResp.GetAccount().GetAccountNo(), j.config.Secrets.Ids[config.FederalPoolAccountNo],
				j.getClosureStatusFromVendor(ctx, bav.ActorId, savingsResp.GetAccount()), bal, balTime))
			count += 1
		}
	}
	logger.Info(ctx, "rows written to file", zap.Int("row_count", count))
	logger.Info(ctx, "rows skipped due to automated fund transfer",
		zap.Int("row_count", skippedDueToAutomatedFundTransferCount),
		zap.Strings(logger.ACTOR_ID_V2, skippedDueToAutomatedFundTransferActors),
	)
	return nil
}

func (j *JobFetchBankAccountVerification) sendSlackTrigger(ctx context.Context, err error) error {
	var (
		channelID = "C020HNXMSGH"
	)
	slackAttachment := slack.Attachment{
		Pretext: "<@S05UWQU2HGA> <@S061N28Q3D5> EOD job for sending User verified account details failed",
	}
	_, timestamp, errSlack := j.slack.PostMessage(channelID, slack.MsgOptionAttachments(slackAttachment))
	if err != nil {
		logger.Error(ctx, "error posting message on slack", zap.Error(err))
		return errSlack
	}
	logger.Info(ctx, "slack message posted", zap.String("timestamp", timestamp))
	return err
}

func (j *JobFetchBankAccountVerification) getClosureStatusFromVendor(ctx context.Context, actorId string, account *savings.Account) string {
	deviceAuthResp, err := j.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(deviceAuthResp, err); te != nil {
		logger.Error(ctx, "error while getting device auth", zap.Error(te))
		return ""
	}

	getUserRes, err := j.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(getUserRes, err); te != nil {
		logger.Error(ctx, "error while getting user by actorId", zap.Error(te))
		return ""
	}

	bcResp, bcErr := j.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, bcErr); rpcErr != nil {
		logger.Error(ctx, "error in get bc", zap.Error(rpcErr))
		return ""
	}

	accountDetailsRes, err := j.vgDepositClient.GetAccountDetail(ctx, &vgDepositPb.GetAccountDetailRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: account.GetPartnerBank(),
		},
		Auth: &header.Auth{
			DeviceId:      deviceAuthResp.GetDevice().GetDeviceId(),
			DeviceToken:   deviceAuthResp.GetDeviceToken(),
			UserProfileId: deviceAuthResp.GetUserProfileId(),
		},
		RequestId:            idgen.FederalRandomSequence(idgen.FederalGetDepositAccountDetailsPrefix, 5),
		CustomerId:           bcResp.GetBankCustomer().GetVendorCustomerId(),
		AccountType:          accounts.Type_SAVINGS,
		DepositAccountNumber: account.GetAccountNo(),
		PhoneNumber:          getUserRes.GetUser().GetProfile().GetPhoneNumber(),
	})
	if te := epifigrpc.RPCError(accountDetailsRes, err); te != nil {
		logger.Error(ctx, "error while getting accounts details from vg deposit client", zap.Error(te))
		return ""
	}

	var accountStatusMap = map[bool]string{
		true:  "Account Closed",
		false: "Account Open",
	}
	return accountStatusMap[accountDetailsRes.GetAccountDetails().GetIsClosed()]
}

// getAccountBalanceDuringClosure returns list of (balance, balance captured at) from closed accounts balance transfer table
func (j *JobFetchBankAccountVerification) getAccountBalanceDuringClosure(ctx context.Context, savingsId string) (string, string) {
	resp, err := j.savClient.GetClosedAccountBalTransferData(ctx, &savings.GetClosedAccountBalTransferDataRequest{SavingsAccountId: savingsId})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil && !resp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while getting closed account balance", zap.Error(grpcErr))
		return "", ""
	}
	var balances, times string
	for _, cbt := range resp.GetEntries() {
		if cbt.GetLastKnownBalance().GetAvailableBalance() == nil || cbt.GetLastKnownBalance().GetLastUpdatedAt() == nil {
			continue
		}
		balances, times = appendBalanceDtls(cbt.GetLastKnownBalance().GetAvailableBalance(), cbt.GetLastKnownBalance().GetLastUpdatedAt(), balances, times)
	}
	return balances, times
}

func appendBalanceDtls(bal *gmoney.Money, capturedAt *timestampPb.Timestamp, balances, times string) (string, string) {
	balance := float64(bal.GetUnits()) + float64(bal.GetNanos())/1e9
	if balances == "" {
		balances = strconv.FormatFloat(balance, 'G', -1, 64)
		times = capturedAt.AsTime().String()
		return balances, times
	}
	balances = balances + ", " + strconv.FormatFloat(balance, 'G', -1, 64)
	times = times + ", " + capturedAt.AsTime().String()
	return balances, times
}
