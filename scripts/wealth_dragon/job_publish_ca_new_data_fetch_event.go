package main

import (
	"context"
	"fmt"
	"math"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
)

const (
	publishBatchSize = 1
)

type jobPublishCaNewDataFetchEvent struct {
	caNewDataFetchEventPublisher queue.Publisher
}

func (j *jobPublishCaNewDataFetchEvent) PerformJob(ctx context.Context, req *JobRequest) error {
	var actorIds []string
	var err error

	// Get actor IDs from Args1 (comma-separated) or from CSV file
	if req.Args1 != "" {
		// Parse comma-separated actor IDs from Args1 using utils function
		actorIds = splitByChar(req.Args1, ",")
	} else {
		// Read from CSV file using utils function
		actorIds, err = j.readActorIdsFromCSV()
		if err != nil {
			return fmt.Errorf("error reading actor IDs from CSV: %v", err)
		}
	}

	if len(actorIds) == 0 {
		return fmt.Errorf("no actor IDs provided")
	}

	totalActors := len(actorIds)
	logger.Info(ctx, "Publishing CA new data fetch events", zap.Int("total_actors", totalActors))

	// Split actors into batches
	numBatches := int(math.Ceil(float64(totalActors) / float64(publishBatchSize)))
	var allSuccess, allFailure []string

	// Process each batch
	for i := 0; i < numBatches; i++ {
		startIdx := i * publishBatchSize
		endIdx := (i + 1) * publishBatchSize
		if endIdx > totalActors {
			endIdx = totalActors
		}

		currentBatch := actorIds[startIdx:endIdx]
		logger.Info(ctx, "processing batch",
			zap.Int("batch", i+1),
			zap.Int("of", numBatches),
			zap.Int("size", len(currentBatch)),
			zap.Int("processed_so_far", startIdx))

		batchSuccess, batchFailure := j.processBatch(ctx, currentBatch)

		allSuccess = append(allSuccess, batchSuccess...)
		allFailure = append(allFailure, batchFailure...)

		logger.Info(ctx, "batch completed",
			zap.Int("batch", i+1),
			zap.Int("successful", len(batchSuccess)),
			zap.Int("failed", len(batchFailure)))
	}

	// Report final results
	logger.Info(ctx, "completed publishing CA new data fetch events",
		zap.Int("total_actors", totalActors),
		zap.Int("successful", len(allSuccess)),
		zap.Int("failed", len(allFailure)))

	if len(allFailure) > 0 {
		logger.Info(ctx, "failed actor IDs", zap.Strings("actorIds", allFailure))
	}

	return nil
}

// processBatch processes a batch of actors in parallel
func (j *jobPublishCaNewDataFetchEvent) processBatch(ctx context.Context, batch []string) ([]string, []string) {
	var wg sync.WaitGroup
	mutex := &sync.Mutex{}
	var success, failure []string

	for _, actorId := range batch {
		if actorId == "" {
			continue // Skip empty actor IDs
		}

		wg.Add(1)
		aid := actorId // Create a local copy for the goroutine

		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			defer logger.RecoverPanicAndError(ctx)

			if err := j.publishEventForActor(ctx, aid); err != nil {
				logger.Error(ctx, "error publishing ca new data fetch event", zap.Error(err), zap.String("actorId", aid))
				mutex.Lock()
				failure = append(failure, aid)
				mutex.Unlock()
			} else {
				mutex.Lock()
				success = append(success, aid)
				mutex.Unlock()
			}
		})
	}

	waitgroup.SafeWait(&wg, time.Hour)
	return success, failure
}

// publishEventForActor publishes event for a single actor
func (j *jobPublishCaNewDataFetchEvent) publishEventForActor(ctx context.Context, actorId string) error {
	msgId, pubErr := j.caNewDataFetchEventPublisher.Publish(ctx, &caExtPb.AccountDataSyncEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
		ActorId:           actorId,
		AccountId:         "dummy_account_id",
		EventTimestamp:    timestamppb.Now(),
		DataSyncType:      caExtPb.AccountDataSyncType_ACCOUNT_DATA_SYNC_TYPE_FULL_IMPORT,
	})
	if pubErr != nil {
		return fmt.Errorf("error publishing ca new data fetch event for actor %s: %v", actorId, pubErr)
	}

	logger.Debug(ctx, "successfully published ca new data fetch event",
		zap.String(logger.QUEUE_MESSAGE_ID, msgId),
		zap.String("actor_id", actorId))

	return nil
}

// readActorIdsFromCSV reads actor IDs from the CSV file in testdata using utils function
func (j *jobPublishCaNewDataFetchEvent) readActorIdsFromCSV() ([]string, error) {
	csvFilePath := filepath.Join("testdata", "publish_ca_new_data_fetch_actors.csv")

	records, err := readCSVFile(csvFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV file %s: %v", csvFilePath, err)
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("CSV file is empty")
	}

	// Skip header row and extract actor IDs
	actorIds := make([]string, 0, len(records)-1)
	for i := 1; i < len(records); i++ {
		if len(records[i]) > 0 && records[i][0] != "" {
			actorIds = append(actorIds, records[i][0])
		}
	}

	return actorIds, nil
}
