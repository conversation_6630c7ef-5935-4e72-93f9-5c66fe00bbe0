// nolint:gosimple,ineffassign,funlen,staticcheck,depguard
package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"strings"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"

	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/pkg/errors"
	"github.com/rudderlabs/analytics-go"
	"github.com/samber/lo"
	"github.com/slack-go/slack"
	temporalclient "go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/aws/v2/sns"
	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	cardControlPb "github.com/epifi/gamma/api/card/control"
	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	tokenizerProxyPb "github.com/epifi/gamma/api/card/tokenizer_proxy"
	commsPb "github.com/epifi/gamma/api/comms"
	clePb "github.com/epifi/gamma/api/credit_limit_estimator"
	depositPb "github.com/epifi/gamma/api/deposit"
	docsPb "github.com/epifi/gamma/api/docs"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	kycPb "github.com/epifi/gamma/api/kyc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pan"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/simulator/lending/creditcard"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tokenizerPb "github.com/epifi/gamma/api/tokenizer"
	userPb "github.com/epifi/gamma/api/user"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	ccVgV2Pb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	currencyInsightsVgPb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	accountVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgcardProvPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	b2cPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	panPb "github.com/epifi/gamma/api/vendorgateway/pan"
	authDaoImpl "github.com/epifi/gamma/auth/orchestrator/dao"
	ffConfig "github.com/epifi/gamma/firefly/config"
	"github.com/epifi/gamma/scripts/cc_firefly/config"
	"github.com/epifi/gamma/scripts/cc_firefly/job"
	"github.com/epifi/gamma/scripts/cc_firefly/wire"
	"github.com/epifi/gamma/user/onboarding/dao"
)

// jobName and jobArguments to be given as input
// refer the particular job.go file for the sample input format
var (
	inputJob        = flag.String("JobName", "", "refer job_names.go for the jobName input")
	Arguments       = flag.String("Arguments", "", "refer the particular job file for the sample input format")
	slackOauthToken = flag.String("oauth", "", "slack oauth token")
	filePath        = flag.String("file", "", "file path for file")
)

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}

	db, err := storageV2.NewPostgresDBWithConfig(conf.CreditCardPgDb, true)
	if err != nil {
		logger.ErrorNoCtx("Failed to load CC PGDB", zap.Error(err))
		return
	}

	sqlDb, _ := db.DB()
	defer func() { _ = sqlDb.Close() }()

	epifiDb, err := storageV2.NewPostgresDBWithConfig(conf.EpifiPgDb, true)
	if err != nil {
		logger.ErrorNoCtx("Failed to load epifi PGDB", zap.Error(err))
		return
	}
	epifiSqlDb, _ := epifiDb.DB()
	defer func() { _ = epifiSqlDb.Close() }()

	dcTxnExecutor := debitCardTxnExecutorProvider(epifiDb)

	ccPgDb, err := storageV2.NewPostgresDBWithConfig(conf.CreditCardPgDb, true)
	if err != nil {
		logger.ErrorNoCtx("Failed to load cc PGDB", zap.Error(err))
		return
	}

	epifiDbv2, err := storageV2.NewCRDBWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.ErrorNoCtx("Failed to load Epifi DB", zap.Error(err))
		return
	}
	ctx := context.Background()
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	s3Client := s3.NewClient(awsConf, conf.Aws.S3.EligibleUsersBucketName)
	dcDocsS3Client := s3.NewClient(awsConf, conf.Aws.S3.DcDocsBucketName)

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		conf.Secrets.Ids[ffConfig.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
		},
	)
	if err != nil {
		panic(err)
	}

	cryptorStoreMap, err := initCryptors(conf)
	if err != nil {
		panic(err)
	}

	var ffConn *grpc.ClientConn
	if conf.UseLendingConnectionForFireflyClients {
		ffConn = epifigrpc.NewSecureConn("lending.epifi.in:9528", "", false)
	} else {
		ffConn = epifigrpc.NewConnByService(cfg.FIREFLY_SERVICE)
	}
	creditLimitEstimatorConn := epifigrpc.NewConnByService(cfg.LIMIT_ESTIMATOR_SERVICE)

	defer epifigrpc.CloseConn(ffConn)
	fireflyClient := ffPb.NewFireflyClient(ffConn)
	ffBillingClient := ffBillingPb.NewBillingClient(ffConn)
	ffPinotClient := ffPinotPb.NewTxnAggregatesClient(ffConn)
	cleClient := clePb.NewCreditLimitEstimatorClient(creditLimitEstimatorConn)
	palClient := palPb.NewPreApprovedLoanClient(ffConn)
	fireflyLmsClient := ffLmsPb.NewLoanManagementSystemClient(ffConn)

	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	userClient := userPb.NewUsersClient(userConn)

	bankCustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bankCustConn)
	bankCustClient := bankCustPb.NewBankCustomerServiceClient(bankCustConn)

	ccVgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(ccVgConn)
	ccVgClient := ccVgPb.NewCreditCardClient(ccVgConn)
	savingsVgClient := savings.NewSavingsClient(ccVgConn)
	b2cPaymentClient := b2cPaymentPb.NewPaymentClient(ccVgConn)
	creditLineVgClient := creditline.NewCreditLineClient(ccVgConn)
	ekycVgClient := ekycPb.NewEKYCClient(ccVgConn)
	panVgClient := panPb.NewPANClient(ccVgConn)
	vgCardProvisionClient := vgcardProvPb.NewCardProvisioningClient(ccVgConn)
	currencyInsightsClient := currencyInsightsVgPb.NewServiceClient(ccVgConn)
	accountsVgClient := accountVgPb.NewAccountsClient(ccVgConn)
	ccVgClientV2 := ccVgV2Pb.NewCreditCardClient(ccVgConn)
	vgCustomerClient := vgCustomerPb.NewCustomerClient(ccVgConn)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actorPb.NewActorClient(actorConn)

	piConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	defer epifigrpc.CloseConn(piConn)
	piClient := piPb.NewPiClient(piConn)

	paymentConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	defer epifigrpc.CloseConn(paymentConn)
	paymentClient := paymentPb.NewPaymentClient(paymentConn)
	payClient := payPb.NewPayClient(paymentConn)

	orderConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	defer epifigrpc.CloseConn(orderConn)
	orderClient := orderPb.NewOrderServiceClient(orderConn)

	panConn := epifigrpc.NewConnByService(cfg.PAN_SERVICE)
	defer epifigrpc.CloseConn(panConn)
	panClient := pan.NewPanClient(panConn)

	sqsClient := sqs.InitSQSClient(awsConf)
	forexRefundOrderUpdateSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, conf.ForexRefundOrderUpdatePublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("error in initialising the sqs publisher", zap.Error(err))
	}

	ccStageUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, conf.CCStageUpdateEventPublisher, awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialize cc stage update event publisher", zap.Error(err))
	}

	rewardsManualGiveawayEventPublisher, err := sqs.NewPublisherWithConfig(ctx, conf.RewardsManualGiveawayEventPublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to create sqs publisher", zap.Error(err))
	}

	creditCardDocsS3Client := s3.NewClient(awsConf, conf.CreditCardS3BucketConfig.CcDocsBucketName)

	debitCardDataS3Client := s3.NewClient(awsConf, conf.Aws.S3.DcDataBucketName)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingsClientPb.NewSavingsClient(savingsConn)

	accountsConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	defer epifigrpc.CloseConn(accountsConn)
	operationalServiceClient := operationalStatusPb.NewOperationalStatusServiceClient(accountsConn)

	accountingClient := ffAccountsPb.NewAccountingClient(ffConn)

	doOnceManager := onceV2.NewDoOnce(db)

	rewardsConn := epifigrpc.NewConnByService(cfg.REWARD_SERVICE)
	defer epifigrpc.CloseConn(rewardsConn)
	rewardsClient := rewardsPb.NewRewardsGeneratorClient(rewardsConn)
	rewardsProjectionClient := rewardsProjectionPb.NewProjectorServiceClient(rewardsConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	defer epifigrpc.CloseConn(celestialConn)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	cardConn := epifigrpc.NewConnByService(cfg.CARD_SERVICE)
	defer epifigrpc.CloseConn(cardConn)
	cardProvClient := cardProvPb.NewCardProvisioningClient(cardConn)
	cardControlClient := cardControlPb.NewCardControlClient(cardConn)
	tokenizerProxyClient := tokenizerProxyPb.NewTokenizerProxyClient(cardConn)

	tieringConn := epifigrpc.NewConnByService(cfg.TIERING_SERVICE)
	defer epifigrpc.CloseConn(tieringConn)
	tieringClient := tieringPb.NewTieringClient(tieringConn)

	slackClient := slack.New(conf.Secrets.Ids[config.SlackOauthToken], slack.OptionDebug(false))
	pgpCryptor, err := cryptorStoreMap.GetCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP)
	if err != nil {
		panic(err)
	}

	depositConn := epifigrpc.NewConnByService(cfg.DEPOSIT_SERVICE)
	defer epifigrpc.CloseConn(depositConn)
	depositClient := depositPb.NewDepositClient(depositConn)

	riskConn := epifigrpc.NewConnByService(cfg.RISK_SERVICE)
	defer epifigrpc.CloseConn(riskConn)
	profileClient := profilePb.NewProfileClient(riskConn)

	simulatorConn := epifigrpc.NewConnByService(cfg.SIMULATOR_GRPC_SERVICE)
	defer epifigrpc.CloseConn(simulatorConn)
	simulatorCcClient := creditcard.NewCreditCardClient(simulatorConn)

	docsConn := epifigrpc.NewConnByService(cfg.DOCS_SERVICE)
	defer epifigrpc.CloseConn(docsConn)
	docsClient := docsPb.NewDocsClient(docsConn)

	kycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	defer epifigrpc.CloseConn(kycConn)
	kycClient := kycPb.NewKycClient(kycConn)

	onboardingConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(onboardingConn)
	onboardingClient := onboardingPb.NewOnboardingClient(onboardingConn)

	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	authClient := authPb.NewAuthClient(authConn)

	tokenizerConn := epifigrpc.NewConnByService(cfg.TOKENIZER_SERVICE)
	defer epifigrpc.CloseConn(tokenizerConn)
	tokenizerClient := tokenizerPb.NewTokenizerClient(tokenizerConn)

	segmentConn := epifigrpc.NewConnByService(cfg.SEGMENT_SERVICE)
	defer epifigrpc.CloseConn(segmentConn)
	segmentClient := segmentPb.NewSegmentationServiceClient(segmentConn)

	dynamodbClient := dynamodb.NewFromConfig(awsConf)

	timeClient := datetime.NewDefaultTime()

	var temporalClient, authTemporalClient, debitCardTemporalClient temporalclient.Client

	if conf.EnableTemporalCodec {
		envNamespace := getEnvNamespaceClient(namespace.Firefly, env)
		temporalClient, err = temporalcl.NewWorkflowClient(envNamespace, true, conf.Secrets.Ids["TemporalCodecAesKey"])
		if err != nil {
			logger.Panic("error creating new wf client", zap.Error(err), zap.String(logger.NAMESPACE, envNamespace))
		}

		envAuthNamespace := getEnvNamespaceClient(namespace.Auth, env)
		authTemporalClient, err = temporalcl.NewWorkflowClient(envAuthNamespace, true, conf.Secrets.Ids["TemporalCodecAesKey"])
		if err != nil {
			logger.Panic("error creating new wf client", zap.Error(err), zap.String(logger.NAMESPACE, envAuthNamespace))
		}

		envDebitCardNamespace := getEnvNamespaceClient(namespace.Card, env)
		debitCardTemporalClient, err = temporalcl.NewWorkflowClient(envDebitCardNamespace, true, conf.Secrets.Ids["TemporalCodecAesKey"])
		if err != nil {
			logger.Panic("error creating new wf client", zap.Error(err), zap.String(logger.NAMESPACE, envDebitCardNamespace))
		}
	}

	onbDao := dao.NewOnboardingCrdb(epifiDbv2, timeClient)
	authReqDao := authDaoImpl.NewAuthRequestsCrdb(epifiDbv2)
	authReqStagesDao := authDaoImpl.NewAuthRequestStagesCrdb(epifiDbv2)

	fireflyRedisStore := storage.NewRedisClientFromConfig(conf.FireflyRedisStore, true)
	defer func() { _ = fireflyRedisStore.Close() }()

	cardsRedisStore := storage.NewRedisClientFromConfig(conf.DcRedisStore, true)
	defer func() { _ = cardsRedisStore.Close() }()

	jobRegistry, err := wire.InitialiseJobRegistry(context.Background(), db, epifiDb, s3Client, rudderClient, conf, fireflyClient, slackClient,
		commsClient, userClient, ccVgClient, accountingClient, doOnceManager, piClient, epifiDb, ccPgDb, ffBillingClient,
		actorClient, paymentClient, savingsClient, dcDocsS3Client, orderClient, bankCustClient, forexRefundOrderUpdateSqsPublisher,
		rewardsClient, ffPinotClient, ccStageUpdateEventPublisher, creditCardDocsS3Client, conf.CcWorkflowReportingJobConfig,
		debitCardDataS3Client, pgpCryptor, panClient, operationalServiceClient, celestialClient, cardProvClient, tieringClient,
		payClient, cardControlClient, depositClient, b2cPaymentClient, creditLineVgClient, cleClient, palClient, profileClient,
		simulatorCcClient, awsConf, cryptorStoreMap, fireflyLmsClient, rewardsProjectionClient, docsClient, ekycVgClient,
		panVgClient, kycClient, onboardingClient, authClient, savingsVgClient, onbDao, temporalClient, authReqDao, authReqStagesDao,
		authTemporalClient, epifiDbv2, tokenizerClient, fireflyRedisStore, rewardsManualGiveawayEventPublisher,
		dcTxnExecutor, dynamodbClient, segmentClient, debitCardTemporalClient, cardsRedisStore, tokenizerProxyClient, currencyInsightsClient, vgCardProvisionClient,
		accountsVgClient, ccVgClientV2, vgCustomerClient)
	if err != nil {
		logger.Panic("error in initialising job registry", zap.Error(err))
	}

	logger.InfoNoCtx("Job type 2", zap.String(logger.JOB_ID, *inputJob))
	jobType := job.JobNames[*inputJob]

	err = injectFilePathToJobArguments(conf, *inputJob)
	if err != nil {
		logger.ErrorNoCtx("error injecting filePath into job arguments", zap.Error(err))
		return
	}
	if err := jobRegistry.RunJob(jobType, *Arguments); err != nil {
		panic(err)
	}

	return
}

func initCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	// create a pgp cryptor to be used for vendor communication encryption
	pgpCryptor := pgp.New(conf.Secrets.Ids[config.FederalPgpPublicKey],
		conf.Secrets.Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets.Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, fmt.Errorf("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)

	ffPgpCryptor := pgp.New(conf.Secrets.Ids[config.SeshaasaiPgpPublicKey],
		conf.Secrets.Ids[config.EpifiSeshaasaiPgpPrivateKey], conf.Secrets.Ids[config.EpifiSeshaasaiPgpPassphrase])
	cryptorStore.AddCryptor(commonvgpb.Vendor_SESHAASAI, commonvgpb.CryptorType_PGP, ffPgpCryptor)

	return cryptorStore, nil
}

func injectFilePathToJobArguments(conf *config.Config, jobType string) error {
	if !lo.Contains(conf.WhiteListedJobsForInjectingFilePathParamIntoArgs, jobType) {
		return nil
	}

	jsonArg := make(map[string]string)
	err := json.Unmarshal([]byte(*Arguments), &jsonArg)
	if err != nil {
		return errors.Wrap(err, "error unmarshalling arguments")
	}
	jsonArg["FilePath"] = *filePath
	finalArgs, err := json.Marshal(jsonArg)
	if err != nil {
		return errors.Wrap(err, "error marshalling jsonArgs back to arguments")
	}
	*Arguments = string(finalArgs)
	return nil
}

func getEnvNamespaceClient(ns epifitemporal.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

// This is provider is for specific use-case only for this scripts
func debitCardTxnExecutorProvider(dcDb *gorm.DB) job.DcIdempotentTxnExecutor {
	return storageV2.NewGormTxnExecutor(dcDb)
}
