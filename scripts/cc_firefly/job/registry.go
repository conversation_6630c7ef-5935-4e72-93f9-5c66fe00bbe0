// nolint:dupl,funlen
package job

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
)

type Registry struct {
	processFiEligibleBaseJob              *ProcessFiEligibleBaseJob
	generateCcQrJob                       *GenerateCcQrJob
	initiateCardRequestJob                *InitiateCardRequestJob
	physicalCardReconJob                  *PhysicalCardReconJob
	ccCommsTestJob                        *CcCommsTestJob
	dCExpiryUpdateJob                     *DCExpiryUpdateJob
	dcTxnDataFetchJob                     *DcTxnDataFetchJob
	dcTxnPublishJob                       *DcTxnPublishJob
	dcForexRefundJob                      *ProcessCardForexTxnRefundJob
	dcForexRefundFiPlusReplay             *DcFiPlusForexRefundNotificationReplayJob
	ccCardTrackingCardRequestBackfillJob  *CardTrackingCardRequestBackfillJob
	ccStatementJob                        *CcStatementJob
	ccTriggerWelcomeOfferJob              *TriggerWelcomeOfferJob
	maskCardNumberJob                     *MaskCardNumberJob
	dcTxnDataFetchWithCountryCodeJob      *DcTxnDataFetchWithCountryCodeJob
	cCWorkflowReportingJob                *CcWorkflowReportingJob
	disableOffersJob                      *DisableOffersJob
	addFiRejectedUsersJob                 *AddFiRejectedUsersJob
	extendOfferExpiryJob                  *ExtendOfferExpiryJob
	dcDeclineDataDump                     *ProcessCardDeclineDataJob
	cardReqsUpdate                        *UpdateCardRequestsJob
	creditCardClosureIntimationJob        *CreditCardClosureIntimationJob
	uploadFileToS3BucketJob               *UploadFileToS3BucketJob
	getPanAadhaarLinkStatusJob            *GetPanAadhaarLinkStatusJob
	dcVendorIdToAccNumberMapJob           *DcVendorIdToAccNumberMapJob
	dcForexReconJob                       *ForexRefundReconJob
	cbsIdPopulationForTxnIdJob            *CbsIdPopulationForTxnIdJob
	dcCardCreationRetryJob                *DcCardCreationRetryJob
	statementAlertingJob                  *StatementAlertingJob
	reconUnsentRefunds                    *ReconUnsentRefunds
	dcPhysicalCardDispatchReconJob        *DcPhysicalCardDispatchReconJob
	dcContactlessSwitchOffJob             *DcContactlessSwitchOffJob
	processedRefundsRecon                 *ProcessedRefundsRecon
	dcForexRefundAlertingJob              *ForexRefundAlertingJob
	updatePaymentInstrumentJob            *UpdatePaymentInstrumentJob
	ccCardRequestRewardJob                *CcCardRequestRewardJob
	dcPhysicalCardDispatchFailureAlertJob *DcPhysicalCardDispatchFailureAlertJob
	triggerStatementGenerationJob         *TriggerStatementGenerationWorkflowJob
	addCCTransactionJob                   *AddCCTransactionJob
	triggerReconcileWorkflowJob           *TriggerReconcileWorkflowsJob
	statementDueDateMismatchJob           *StatementDueDateMismatchIssueJob
	updateCardRequestAndStageStatusJob    *UpdateCardRequestAndStageStatusJob
	manualStatementGenerationJob          *ManualStatementGenerationJob
	decryptVgFilesJob                     *DecryptVgFilesJob
	fetchWelcomeOfferRewardIdJob          *FetchWelcomeOfferRewardIdJob
	offerInvalidationJob                  *OfferInvalidationJob
	billEraserReconJob                    *BillEraserReconJob
	ccSecuredDepositIdJob                 *CcSecuredDepositIdJob
	updateAtmLimitJob                     *UpdateAtmLimitJob
	updateRewardInfoInBillJob             *UpdateRewardInfoInBillJob
	filiteRestJob                         *FiliteResetJob
	dedupeCheckJob                        *DedupeCheckJob
	terminateAuthWorkflowsJob             *TerminateAuthWorkflowsJob
	ccOfferAddJob                         *CcOfferAddJob
	invalidOnboardingDetectionJob         *InvalidOnboardingDetectionJob
	userOnboardingDataPopulationJob       *UserOnboardingDataPopulationJob
	ccUpdateCustomerDetailsAtM2PJob       *CcUpdateCustomerDetailsAtM2PJob
	BlockAndReissueNewCCJob               *BlockAndReissueNewCCJob
	registerCustomerForCCJob              *RegisterCustomerForCCJob
	updateCardDetailsAtBankJob            *UpdateCardDetailsAtBankJob
	dcRenewCardJob                        *DcRenewCardJob
	dcBlockCardJob                        *DcBlockCardJob
	imitateBlockAndReissueNewCCJob        *ImitateBlockAndReissueNewCCJob
	processCardTransaction                *ProcessCardTransaction
	deleteDcDynamoDataJob                 *DeleteDcDynamoDataJob
	changeDcPinValidationParamsJob        *ChangeDcPinValidationParamsJob
	getActorCcShippingAddressJob          *GetActorCcShippingAddressJob
	triggerWelcomeRewardsJob              *TriggerWelcomeRewardsJob
	dcPhysicalCardChargesReversalJob      *DcPhysicalCardChargesReversalJob
	cCReconJob                            *CCReconJob
	rotateTokenizerKeysJob                *RotateTokenizerKeysJob
	generateStatementJob                  *GenerateStatementJob
	activateCreditCardJob                 *ActivateCreditCardJob
	triggerFeeWaiverJob                   *TriggerFeeWaiverJob
	bypassCcOnboardingJob                 *BypassCcOnboardingJob
	deleteCardRequestJob                  *DeleteCardRequestJob
	triggerProcessCcTxnWfJob              *TriggerProcessCcTxnWfJob
	sendInAppNotificationJob              *SendInAppNotificationJob
	sendDpdEventsJob                      *SendDpdEventsJob
	initiateAmcReportGeneration           *InitiateAmcReportGenerationJob
	correctForexRefundDbJob               *CorrectForexRefundDbJob
	deleteAmcCardRequestJob               *DeleteAmcCardRequestJob
	visaApiTestJob                        *VisaApiTestJob
	investigateAwbMismatchJob             *InvestigateAwbMismatchJob
	dcOrderPhysicalCardWithChargesJob     *DcOrderPhysicalCardWithChargesJob
	fetchDueDataJob                       *FetchDueJob
	creatVendorRepaymentRecordJob         *CreatVendorRepaymentRecordJob
	ccFedFibSmsScriptJob                  *CcSmsScriptJob
	correctForexTcsClashJob               *CorrectForexTcsClashJob
	collectDCIssuanceApiTestJob           *CollectDCIssuanceApiTestJob
	ccProcessKycExpiryUpdate              *ProcessKycExpiryForCcJob
	dcChargesCollectionEnquiryJob         *EnquireDcChargesCollectionStsJob
	publishCcDelStateUpdateEventJob       *PublishCcDelStateUpdateEventJob
	ccCxMigrationSmsJob                   *CcCxMigrationSmsJob
	fetchCustJob                          *FetchCustJob
}

func NewRegistry(
	processFiEligibleBaseJob *ProcessFiEligibleBaseJob,
	generateCcQrJob *GenerateCcQrJob,
	initiateCardRequestJob *InitiateCardRequestJob,
	physicalCardReconJob *PhysicalCardReconJob,
	ccCommsTestJob *CcCommsTestJob,
	dCExpiryUpdateJob *DCExpiryUpdateJob,
	dcTxnDataFetchJob *DcTxnDataFetchJob,
	dcTxnPublishJob *DcTxnPublishJob,
	dcForexRefundJob *ProcessCardForexTxnRefundJob,
	dcForexRefundFiPlusReplay *DcFiPlusForexRefundNotificationReplayJob,
	ccCardTrackingCardRequestBackfillJob *CardTrackingCardRequestBackfillJob,
	ccStatementJob *CcStatementJob,
	ccTriggerWelcomeOfferJob *TriggerWelcomeOfferJob,
	maskCardNumberJob *MaskCardNumberJob,
	dcTxnDataFetchWithCountryCodeJob *DcTxnDataFetchWithCountryCodeJob,
	cCWorkflowReportingJob *CcWorkflowReportingJob,
	disableOffersJob *DisableOffersJob,
	addFiRejectedUsersJob *AddFiRejectedUsersJob,
	extendOfferExpiryJob *ExtendOfferExpiryJob,
	dcDeclineDataDump *ProcessCardDeclineDataJob,
	cardReqsUpdate *UpdateCardRequestsJob,
	creditCardClosureIntimationJob *CreditCardClosureIntimationJob,
	uploadFileToS3BucketJob *UploadFileToS3BucketJob,
	getPanAadhaarLinkStatusJob *GetPanAadhaarLinkStatusJob,
	dcVendorIdToAccNumberMapJob *DcVendorIdToAccNumberMapJob,
	dcForexRecon *ForexRefundReconJob,
	cbsIdPopulationForTxnIdJob *CbsIdPopulationForTxnIdJob,
	dcCardCreationRetryJob *DcCardCreationRetryJob,
	statementAlertingJob *StatementAlertingJob,
	reconUnsentRefunds *ReconUnsentRefunds,
	dcPhysicalCardDispatchReconJob *DcPhysicalCardDispatchReconJob,
	dcContactlessSwitchOffJob *DcContactlessSwitchOffJob,
	processedRefundsRecon *ProcessedRefundsRecon,
	dcForexRefundAlertingJob *ForexRefundAlertingJob,
	updatePaymentInstrumentJob *UpdatePaymentInstrumentJob,
	ccCardRequestRewardJob *CcCardRequestRewardJob,
	dcPhysicalCardDispatchFailureAlertJob *DcPhysicalCardDispatchFailureAlertJob,
	triggerStatementGenerationJob *TriggerStatementGenerationWorkflowJob,
	addCCTransactionJob *AddCCTransactionJob,
	triggerReconcileWorkflowJob *TriggerReconcileWorkflowsJob,
	statementDueDateMismatchJob *StatementDueDateMismatchIssueJob,
	updateCardRequestAndStageStatusJob *UpdateCardRequestAndStageStatusJob,
	manualStatementGenerationJob *ManualStatementGenerationJob,
	decryptVgFilesJob *DecryptVgFilesJob,
	fetchWelcomeOfferRewardIdJob *FetchWelcomeOfferRewardIdJob,
	offerInvalidationJob *OfferInvalidationJob,
	billEraserReconJob *BillEraserReconJob,
	ccSecuredDepositIdJob *CcSecuredDepositIdJob,
	updateAtmLimitJob *UpdateAtmLimitJob,
	updateRewardInfoInBillJob *UpdateRewardInfoInBillJob,
	filiteRestJob *FiliteResetJob,
	dedupeCheckJob *DedupeCheckJob,
	terminateAuthWorkflowsJob *TerminateAuthWorkflowsJob,
	ccOfferAddJob *CcOfferAddJob,
	invalidOnboardingDetectionJob *InvalidOnboardingDetectionJob,
	userOnboardingDataPopulationJob *UserOnboardingDataPopulationJob,
	ccUpdateCustomerDetailsAtM2PJob *CcUpdateCustomerDetailsAtM2PJob,
	blockAndReissueNewCCJob *BlockAndReissueNewCCJob,
	registerCustomerForCCJob *RegisterCustomerForCCJob,
	updateCardDetailsAtBankJob *UpdateCardDetailsAtBankJob,
	dcRenewCardJob *DcRenewCardJob,
	dcBlockCardJob *DcBlockCardJob,
	imitateBlockAndReissueNewCCJob *ImitateBlockAndReissueNewCCJob,
	processCardTransaction *ProcessCardTransaction,
	deleteDcDynamoDataJob *DeleteDcDynamoDataJob,
	changeDcPinValidationParamsJob *ChangeDcPinValidationParamsJob,
	getActorCcShippingAddressJob *GetActorCcShippingAddressJob,
	triggerWelcomeRewardsJob *TriggerWelcomeRewardsJob,
	dcPhysicalCardChargesReversalJob *DcPhysicalCardChargesReversalJob,
	cCReconJob *CCReconJob,
	rotateTokenizerKeysJob *RotateTokenizerKeysJob,
	generateStatementJob *GenerateStatementJob,
	activateCreditCardJob *ActivateCreditCardJob,
	triggerFeeWaiverJob *TriggerFeeWaiverJob,
	bypassCcOnboardingJob *BypassCcOnboardingJob,
	deleteCardRequestJob *DeleteCardRequestJob,
	triggerProcessCcTxnWfJob *TriggerProcessCcTxnWfJob,
	sendInAppNotificationJob *SendInAppNotificationJob,
	sendDpdEventsJob *SendDpdEventsJob,
	initiateAmcReportGeneration *InitiateAmcReportGenerationJob,
	correctForexRefundDbJob *CorrectForexRefundDbJob,
	deleteAmcCardRequestJob *DeleteAmcCardRequestJob,
	visaApiTestJob *VisaApiTestJob,
	investigateAwbMismatchJob *InvestigateAwbMismatchJob,
	dcOrderPhysicalCardWithChargesJob *DcOrderPhysicalCardWithChargesJob,
	fetchDueDataJob *FetchDueJob,
	creatVendorRepaymentRecordJob *CreatVendorRepaymentRecordJob,
	ccFedFibSmsScriptJob *CcSmsScriptJob,
	correctForexTcsClashJob *CorrectForexTcsClashJob,
	collectDCIssuanceApiTestJob *CollectDCIssuanceApiTestJob,
	ccProcessKycExpiryUpdate *ProcessKycExpiryForCcJob,
	dcChargesCollectionEnquiryJob *EnquireDcChargesCollectionStsJob,
	publishCcDelStateUpdateEventJob *PublishCcDelStateUpdateEventJob,
	ccCxMigrationSmsJob *CcCxMigrationSmsJob,
	fetchCustJob *FetchCustJob,
) *Registry {
	return &Registry{
		processFiEligibleBaseJob:              processFiEligibleBaseJob,
		generateCcQrJob:                       generateCcQrJob,
		initiateCardRequestJob:                initiateCardRequestJob,
		physicalCardReconJob:                  physicalCardReconJob,
		ccCommsTestJob:                        ccCommsTestJob,
		dCExpiryUpdateJob:                     dCExpiryUpdateJob,
		dcTxnDataFetchJob:                     dcTxnDataFetchJob,
		dcTxnPublishJob:                       dcTxnPublishJob,
		dcForexRefundJob:                      dcForexRefundJob,
		dcForexRefundFiPlusReplay:             dcForexRefundFiPlusReplay,
		ccCardTrackingCardRequestBackfillJob:  ccCardTrackingCardRequestBackfillJob,
		ccStatementJob:                        ccStatementJob,
		ccTriggerWelcomeOfferJob:              ccTriggerWelcomeOfferJob,
		maskCardNumberJob:                     maskCardNumberJob,
		dcTxnDataFetchWithCountryCodeJob:      dcTxnDataFetchWithCountryCodeJob,
		cCWorkflowReportingJob:                cCWorkflowReportingJob,
		disableOffersJob:                      disableOffersJob,
		addFiRejectedUsersJob:                 addFiRejectedUsersJob,
		extendOfferExpiryJob:                  extendOfferExpiryJob,
		dcDeclineDataDump:                     dcDeclineDataDump,
		cardReqsUpdate:                        cardReqsUpdate,
		creditCardClosureIntimationJob:        creditCardClosureIntimationJob,
		uploadFileToS3BucketJob:               uploadFileToS3BucketJob,
		getPanAadhaarLinkStatusJob:            getPanAadhaarLinkStatusJob,
		dcVendorIdToAccNumberMapJob:           dcVendorIdToAccNumberMapJob,
		dcForexReconJob:                       dcForexRecon,
		cbsIdPopulationForTxnIdJob:            cbsIdPopulationForTxnIdJob,
		dcCardCreationRetryJob:                dcCardCreationRetryJob,
		statementAlertingJob:                  statementAlertingJob,
		reconUnsentRefunds:                    reconUnsentRefunds,
		dcPhysicalCardDispatchReconJob:        dcPhysicalCardDispatchReconJob,
		dcContactlessSwitchOffJob:             dcContactlessSwitchOffJob,
		processedRefundsRecon:                 processedRefundsRecon,
		dcForexRefundAlertingJob:              dcForexRefundAlertingJob,
		updatePaymentInstrumentJob:            updatePaymentInstrumentJob,
		ccCardRequestRewardJob:                ccCardRequestRewardJob,
		dcPhysicalCardDispatchFailureAlertJob: dcPhysicalCardDispatchFailureAlertJob,
		triggerStatementGenerationJob:         triggerStatementGenerationJob,
		addCCTransactionJob:                   addCCTransactionJob,
		triggerReconcileWorkflowJob:           triggerReconcileWorkflowJob,
		statementDueDateMismatchJob:           statementDueDateMismatchJob,
		updateCardRequestAndStageStatusJob:    updateCardRequestAndStageStatusJob,
		manualStatementGenerationJob:          manualStatementGenerationJob,
		decryptVgFilesJob:                     decryptVgFilesJob,
		fetchWelcomeOfferRewardIdJob:          fetchWelcomeOfferRewardIdJob,
		offerInvalidationJob:                  offerInvalidationJob,
		billEraserReconJob:                    billEraserReconJob,
		ccSecuredDepositIdJob:                 ccSecuredDepositIdJob,
		updateAtmLimitJob:                     updateAtmLimitJob,
		updateRewardInfoInBillJob:             updateRewardInfoInBillJob,
		filiteRestJob:                         filiteRestJob,
		dedupeCheckJob:                        dedupeCheckJob,
		terminateAuthWorkflowsJob:             terminateAuthWorkflowsJob,
		ccOfferAddJob:                         ccOfferAddJob,
		invalidOnboardingDetectionJob:         invalidOnboardingDetectionJob,
		userOnboardingDataPopulationJob:       userOnboardingDataPopulationJob,
		ccUpdateCustomerDetailsAtM2PJob:       ccUpdateCustomerDetailsAtM2PJob,
		BlockAndReissueNewCCJob:               blockAndReissueNewCCJob,
		registerCustomerForCCJob:              registerCustomerForCCJob,
		updateCardDetailsAtBankJob:            updateCardDetailsAtBankJob,
		dcRenewCardJob:                        dcRenewCardJob,
		dcBlockCardJob:                        dcBlockCardJob,
		imitateBlockAndReissueNewCCJob:        imitateBlockAndReissueNewCCJob,
		processCardTransaction:                processCardTransaction,
		deleteDcDynamoDataJob:                 deleteDcDynamoDataJob,
		changeDcPinValidationParamsJob:        changeDcPinValidationParamsJob,
		getActorCcShippingAddressJob:          getActorCcShippingAddressJob,
		triggerWelcomeRewardsJob:              triggerWelcomeRewardsJob,
		dcPhysicalCardChargesReversalJob:      dcPhysicalCardChargesReversalJob,
		cCReconJob:                            cCReconJob,
		rotateTokenizerKeysJob:                rotateTokenizerKeysJob,
		generateStatementJob:                  generateStatementJob,
		activateCreditCardJob:                 activateCreditCardJob,
		triggerFeeWaiverJob:                   triggerFeeWaiverJob,
		bypassCcOnboardingJob:                 bypassCcOnboardingJob,
		deleteCardRequestJob:                  deleteCardRequestJob,
		triggerProcessCcTxnWfJob:              triggerProcessCcTxnWfJob,
		sendInAppNotificationJob:              sendInAppNotificationJob,
		sendDpdEventsJob:                      sendDpdEventsJob,
		initiateAmcReportGeneration:           initiateAmcReportGeneration,
		correctForexRefundDbJob:               correctForexRefundDbJob,
		deleteAmcCardRequestJob:               deleteAmcCardRequestJob,
		visaApiTestJob:                        visaApiTestJob,
		investigateAwbMismatchJob:             investigateAwbMismatchJob,
		dcOrderPhysicalCardWithChargesJob:     dcOrderPhysicalCardWithChargesJob,
		fetchDueDataJob:                       fetchDueDataJob,
		creatVendorRepaymentRecordJob:         creatVendorRepaymentRecordJob,
		ccFedFibSmsScriptJob:                  ccFedFibSmsScriptJob,
		correctForexTcsClashJob:               correctForexTcsClashJob,
		collectDCIssuanceApiTestJob:           collectDCIssuanceApiTestJob,
		ccProcessKycExpiryUpdate:              ccProcessKycExpiryUpdate,
		dcChargesCollectionEnquiryJob:         dcChargesCollectionEnquiryJob,
		publishCcDelStateUpdateEventJob:       publishCcDelStateUpdateEventJob,
		ccCxMigrationSmsJob:                   ccCxMigrationSmsJob,
		fetchCustJob:                          fetchCustJob,
	}
}

func (r *Registry) getJob(t Type) (Job, error) {
	switch t {
	case ProcessFiEligibleBase:
		return r.processFiEligibleBaseJob, nil
	case GenerateCcQr:
		return r.generateCcQrJob, nil
	case InitiateCardRequest:
		return r.initiateCardRequestJob, nil
	case PhysicalCardRecon:
		return r.physicalCardReconJob, nil
	case CcCommsTest:
		return r.ccCommsTestJob, nil
	case DcExpiryUpdate:
		return r.dCExpiryUpdateJob, nil
	case DcTxnDataFetch:
		return r.dcTxnDataFetchJob, nil
	case DcTxnPublish:
		return r.dcTxnPublishJob, nil
	case DcForexRefund:
		return r.dcForexRefundJob, nil
	case DcForexRefundFiPlusReplay:
		return r.dcForexRefundFiPlusReplay, nil
	case CcCardTrackingRequestBackfill:
		return r.ccCardTrackingCardRequestBackfillJob, nil
	case CcStatement:
		return r.ccStatementJob, nil
	case TriggerWelcomeOffer:
		return r.ccTriggerWelcomeOfferJob, nil
	case MaskingCardNumber:
		return r.maskCardNumberJob, nil
	case DcTxnDataFetchWithCountryCode:
		return r.dcTxnDataFetchWithCountryCodeJob, nil
	case CreditCardWorkflowsReporting:
		return r.cCWorkflowReportingJob, nil
	case DisableOffers:
		return r.disableOffersJob, nil
	case AddFiRejectedUsers:
		return r.addFiRejectedUsersJob, nil
	case ExtendOfferExpiry:
		return r.extendOfferExpiryJob, nil
	case ProcessDcDeclineData:
		return r.dcDeclineDataDump, nil
	case UpdateCardRequestsOfferId:
		return r.cardReqsUpdate, nil
	case CreditCardClosureIntimation:
		return r.creditCardClosureIntimationJob, nil
	case UploadFilesToS3Bucket:
		return r.uploadFileToS3BucketJob, nil
	case GetPanAadhaarLinkStatus:
		return r.getPanAadhaarLinkStatusJob, nil
	case DcVendorIdToAccNumber:
		return r.dcVendorIdToAccNumberMapJob, nil
	case ForexRefundRecon:
		return r.dcForexReconJob, nil
	case CbsIdPopulationForTxnId:
		return r.cbsIdPopulationForTxnIdJob, nil
	case DcCardCreationRetry:
		return r.dcCardCreationRetryJob, nil
	case StatementGenerationAlerting:
		return r.statementAlertingJob, nil
	case DcReconUnsentRefunds:
		return r.reconUnsentRefunds, nil
	case DcPhysicalCardDispatchRecon:
		return r.dcPhysicalCardDispatchReconJob, nil
	case DcContactlessSwitchOff:
		return r.dcContactlessSwitchOffJob, nil
	case ReconProcessedRefunds:
		return r.processedRefundsRecon, nil
	case ForexRefundAlerting:
		return r.dcForexRefundAlertingJob, nil
	case UpdatePaymentInstrument:
		return r.updatePaymentInstrumentJob, nil
	case CcCardRequestReward:
		return r.ccCardRequestRewardJob, nil
	case DcPhysicalCardDispatchFailureAlert:
		return r.dcPhysicalCardDispatchFailureAlertJob, nil
	case TriggerStatementGeneration:
		return r.triggerStatementGenerationJob, nil
	case AddCCTransaction:
		return r.addCCTransactionJob, nil
	case TriggerReconcileWorkflows:
		return r.triggerReconcileWorkflowJob, nil
	case StatementDueDateMismatch:
		return r.statementDueDateMismatchJob, nil
	case UpdateCardReqAndStageStatus:
		return r.updateCardRequestAndStageStatusJob, nil
	case ManualStatementGeneration:
		return r.manualStatementGenerationJob, nil
	case DecryptVgFiles:
		return r.decryptVgFilesJob, nil
	case WelcomeOfferRewardIdJob:
		return r.fetchWelcomeOfferRewardIdJob, nil
	case OfferInvalidation:
		return r.offerInvalidationJob, nil
	case BillEraserRecon:
		return r.billEraserReconJob, nil
	case CreditCardSecuredDepositIdJob:
		return r.ccSecuredDepositIdJob, nil
	case CcUpdateAtmLimitJob:
		return r.updateAtmLimitJob, nil
	case CcUpdateRewardInfoInBill:
		return r.updateRewardInfoInBillJob, nil
	case FiliteOnbResetJob:
		return r.filiteRestJob, nil
	case DedupeCheckingJob:
		return r.dedupeCheckJob, nil
	case TerminateAuthWfs:
		return r.terminateAuthWorkflowsJob, nil
	case CreditCcOfferAddJob:
		return r.ccOfferAddJob, nil
	case InvalidOnboardingDetection:
		return r.invalidOnboardingDetectionJob, nil
	case UserOnboardingDataPopulation:
		return r.userOnboardingDataPopulationJob, nil
	case CcUpdateCustomerDetailsAtM2pJob:
		return r.ccUpdateCustomerDetailsAtM2PJob, nil
	case BlockAndReissueNewCC:
		return r.BlockAndReissueNewCCJob, nil
	case RegisterCustomerForCC:
		return r.registerCustomerForCCJob, nil
	case UpdateCardDetailsAtBank:
		return r.updateCardDetailsAtBankJob, nil
	case DcRenewCard:
		return r.dcRenewCardJob, nil
	case BlockDebitCard:
		return r.dcBlockCardJob, nil
	case ImitateReissueCard:
		return r.imitateBlockAndReissueNewCCJob, nil
	case ProcessCardTransactionJob:
		return r.processCardTransaction, nil
	case DeleteDcDynamoData:
		return r.deleteDcDynamoDataJob, nil
	case ChangeDcPinValidationParams:
		return r.changeDcPinValidationParamsJob, nil
	case GetActorCcShippingAddress:
		return r.getActorCcShippingAddressJob, nil
	case TriggerWelcomeRewards:
		return r.triggerWelcomeRewardsJob, nil
	case DcPhysicalCardChargesReversal:
		return r.dcPhysicalCardChargesReversalJob, nil
	case CCRecon:
		return r.cCReconJob, nil
	case RotateTokenizerKeys:
		return r.rotateTokenizerKeysJob, nil
	case GenerateStatement:
		return r.generateStatementJob, nil
	case ActivateCreditCard:
		return r.activateCreditCardJob, nil
	case TriggerFeeWaiver:
		return r.triggerFeeWaiverJob, nil
	case BypassCcOnboarding:
		return r.bypassCcOnboardingJob, nil
	case DeleteCardRequest:
		return r.deleteCardRequestJob, nil
	case TriggerProcessCcTxnWf:
		return r.triggerProcessCcTxnWfJob, nil
	case SendInAppNotification:
		return r.sendInAppNotificationJob, nil
	case SendDpdEvent:
		return r.sendDpdEventsJob, nil
	case InitiateAmcReportGeneration:
		return r.initiateAmcReportGeneration, nil
	case DCCorrectForexRefundDbJob:
		return r.correctForexRefundDbJob, nil
	case DCDeleteAmcCardRequestJob:
		return r.deleteAmcCardRequestJob, nil
	case DCInvestigateAwbMismatchJob:
		return r.investigateAwbMismatchJob, nil
	case VisaApisTest:
		return r.visaApiTestJob, nil
	case DCOrderPhysicalCardWithChargesJob:
		return r.dcOrderPhysicalCardWithChargesJob, nil
	case FetchDueData:
		return r.fetchDueDataJob, nil
	case CreateVendorRepaymentRecord:
		return r.creatVendorRepaymentRecordJob, nil
	case CreditCardFedFibSmsScriptJob:
		return r.ccFedFibSmsScriptJob, nil
	case DcCorrectForexTcsClashJob:
		return r.correctForexTcsClashJob, nil
	case CollectDCIssuanceApiTest:
		return r.collectDCIssuanceApiTestJob, nil
	case CcProcessKycExpiryUpdate:
		return r.ccProcessKycExpiryUpdate, nil
	case DcEnquireChargesCollectionStatus:
		return r.dcChargesCollectionEnquiryJob, nil
	case SendCcDeliveryStateUpdateEvent:
		return r.publishCcDelStateUpdateEventJob, nil
	case CcCxMigrationSms:
		return r.ccCxMigrationSmsJob, nil
	case FetchCustomerDetails:
		return r.fetchCustJob, nil

	default:
		return nil, fmt.Errorf("unable to find job for job type: %v", t)
	}
}

func (r *Registry) RunJob(t Type, jobArguments string) error {
	job, err := r.getJob(t)
	if err != nil {
		return errors.Wrap(err, "failure in getJob")
	}

	return job.Run(context.Background(), jobArguments)
}
