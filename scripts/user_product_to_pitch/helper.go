package main

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	commsPb "github.com/epifi/gamma/api/comms"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	productPb "github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	userIntelPb "github.com/epifi/gamma/api/userintel"

	"github.com/epifi/gamma/pkg/decisionmodel"
	"github.com/epifi/gamma/screener/checks"
	"github.com/epifi/gamma/scripts/user_product_to_pitch/config"
	"github.com/epifi/gamma/userintel/inteltypeparam"
)

const (
	actorId              = "actor_id"
	screener             = "screener"
	loanAffinityCategory = "loan_affinity_category"
	pdCategory           = "pd_category"
	productToPitch       = "product_to_pitch"
	affluence            = "affluence"
)

type InputCsvRow struct {
	FirehoseId, ActorId string
}

type ProductToPitchRow struct {
	ActorId, Screener, LoanAffinityCategory, PdCategory, ProductToPitch string
}

// ToOutputCsvRow converts ProductToPitchRow to OutputCsvRow
func (p *ProductToPitchRow) ToOutputCsvRow() *OutputCsvRow {
	return &OutputCsvRow{
		ActorId:              p.ActorId,
		Screener:             p.Screener,
		LoanAffinityCategory: p.LoanAffinityCategory,
		PdCategory:           p.PdCategory,
		ProductToPitch:       p.ProductToPitch,
		Affluence:            "", // Empty for product-to-pitch only
	}
}

type AffluenceRow struct {
	ActorId, Affluence string
}

// ToOutputCsvRow converts AffluenceRow to OutputCsvRow
func (a *AffluenceRow) ToOutputCsvRow() *OutputCsvRow {
	return &OutputCsvRow{
		ActorId:              a.ActorId,
		Screener:             "", // Empty for affluence only
		LoanAffinityCategory: "", // Empty for affluence only
		PdCategory:           "", // Empty for affluence only
		ProductToPitch:       "", // Empty for affluence only
		Affluence:            a.Affluence,
	}
}

type OutputCsvRow struct {
	ActorId, Screener, LoanAffinityCategory, PdCategory, ProductToPitch, Affluence string
}

// ToProductToPitchRow converts OutputCsvRow to ProductToPitchRow
func (o *OutputCsvRow) ToProductToPitchRow() *ProductToPitchRow {
	return &ProductToPitchRow{
		ActorId:              o.ActorId,
		Screener:             o.Screener,
		LoanAffinityCategory: o.LoanAffinityCategory,
		PdCategory:           o.PdCategory,
		ProductToPitch:       o.ProductToPitch,
	}
}

// ToAffluenceRow converts OutputCsvRow to AffluenceRow
func (o *OutputCsvRow) ToAffluenceRow() *AffluenceRow {
	return &AffluenceRow{
		ActorId:   o.ActorId,
		Affluence: o.Affluence,
	}
}

// CombineToOutputCsvRow combines ProductToPitchRow and AffluenceRow into a single OutputCsvRow
func CombineToOutputCsvRow(productRow *ProductToPitchRow, affluenceRow *AffluenceRow) *OutputCsvRow {
	return &OutputCsvRow{
		ActorId:              productRow.ActorId,
		Screener:             productRow.Screener,
		LoanAffinityCategory: productRow.LoanAffinityCategory,
		PdCategory:           productRow.PdCategory,
		ProductToPitch:       productRow.ProductToPitch,
		Affluence:            affluenceRow.Affluence,
	}
}

// GetCsvHeaders returns headers based on output type
func getCsvHeaders(outputType OutputType) []string {
	switch outputType {
	case PRODUCT_TO_PITCH:
		return []string{actorId, screener, loanAffinityCategory, pdCategory, productToPitch}
	case AFFLUENCE:
		return []string{actorId, affluence}
	case BOTH:
		return []string{actorId, screener, loanAffinityCategory, pdCategory, productToPitch, affluence}
	default:
		return []string{actorId, screener, loanAffinityCategory, pdCategory, productToPitch, affluence}
	}
}

// GetCsvRowData returns row data based on output type
func (o *OutputCsvRow) GetCsvRowData(outputType OutputType) []string {
	switch outputType {
	case PRODUCT_TO_PITCH:
		return []string{o.ActorId, o.Screener, o.LoanAffinityCategory, o.PdCategory, o.ProductToPitch}
	case AFFLUENCE:
		return []string{o.ActorId, o.Affluence}
	case BOTH:
		return []string{o.ActorId, o.Screener, o.LoanAffinityCategory, o.PdCategory, o.ProductToPitch, o.Affluence}
	default:
		return []string{o.ActorId, o.Screener, o.LoanAffinityCategory, o.PdCategory, o.ProductToPitch, o.Affluence}
	}
}

// GenerateCsvInBinary creates CSV binary data for any output type
func generateCsvInBinary(rows []*OutputCsvRow, outputType OutputType) ([]byte, error) {
	var finalCsvRows [][]string

	// Add headers
	finalCsvRows = append(finalCsvRows, getCsvHeaders(outputType))

	// Add data rows
	for _, row := range rows {
		finalCsvRows = append(finalCsvRows, row.GetCsvRowData(outputType))
	}

	buf := new(bytes.Buffer)
	csvWriter := csv.NewWriter(buf)
	if csvWriterError := csvWriter.WriteAll(finalCsvRows); csvWriterError != nil {
		return nil, fmt.Errorf("error writing %s csv to buffer: %w", outputType.String(), csvWriterError)
	}

	return buf.Bytes(), nil
}

// GetTypeIdentifier returns the type identifier based on output type for use in file names and paths
func GetTypeIdentifier(outputType OutputType) string {
	switch outputType {
	case PRODUCT_TO_PITCH:
		return "product_to_pitch_only"
	case AFFLUENCE:
		return "affluence_only"
	case BOTH:
		return "product_to_pitch"
	default:
		return "product_to_pitch"
	}
}

// GetOutputFileName generates the output file name based on output type and input file
func GetOutputFileName(outputType OutputType, inputFileName string) string {
	timestamp := time.Now().Unix()
	baseFileName := GetTypeIdentifier(outputType)

	if inputFileName != "" {
		return fmt.Sprintf("%s_%s_%d.csv", baseFileName, inputFileName, timestamp)
	}
	return fmt.Sprintf("%s_%d.csv", baseFileName, timestamp)
}

// GetReportTitle returns the report title based on output type
func GetReportTitle(outputType OutputType) string {
	return GetReportTitleShort(outputType) + " Report"
}

// GetReportTitleShort returns the short report title for email subjects
func GetReportTitleShort(outputType OutputType) string {
	switch outputType {
	case PRODUCT_TO_PITCH:
		return "Product To Pitch"
	case AFFLUENCE:
		return "Affluence"
	case BOTH:
		return "Product To Pitch & Affluence"
	default:
		return "Product To Pitch"
	}
}

type Service struct {
	userClient        usersPb.UsersClient
	userIntelClient   userIntelPb.UserIntelServiceClient
	lendabilityClient lendabilityPb.LendabilityClient
	commsClient       commsPb.CommsClient
	s3Client          s3.S3Client
	conf              *config.Config
}

func NewService(
	userClient usersPb.UsersClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	lendabilityClient lendabilityPb.LendabilityClient,
	commsClient commsPb.CommsClient,
	s3Client s3.S3Client,
	conf *config.Config,
) *Service {
	return &Service{
		userClient:        userClient,
		userIntelClient:   userIntelClient,
		lendabilityClient: lendabilityClient,
		commsClient:       commsClient,
		s3Client:          s3Client,
		conf:              conf,
	}
}

// ReadCsvFromS3 reads a rpc request csv from s3
func (s *Service) ReadCsvFromS3(s3RecordPath string) ([]*InputCsvRow, error) {
	csvFileContents, readError := s.s3Client.Read(s3RecordPath)
	if readError != nil {
		return nil, fmt.Errorf("error reading csv from s3: %w", readError)
	}

	csvReader := csv.NewReader(bytes.NewReader(csvFileContents))
	csvRecords, csvReadError := csvReader.ReadAll()
	if csvReadError != nil {
		return nil, fmt.Errorf("error reading csv from s3: %w", csvReadError)
	}

	var inputCsvRowsRequest []*InputCsvRow
	if len(csvRecords) == 0 {
		return inputCsvRowsRequest, nil
	}
	for _, row := range (csvRecords)[1:] { // Skip the header of csv
		if len(row) < 2 {
			continue
		}
		inputCsvRowsRequest = append(inputCsvRowsRequest, &InputCsvRow{
			FirehoseId: row[0],
			ActorId:    row[1],
		})
	}

	return inputCsvRowsRequest, nil
}

// WriteCsvToS3 writes a rpc response csv to s3
func (s *Service) WriteCsvToS3(filePath string, data []byte) error {
	signedUrl, writeToS3error := s.s3Client.WriteAndGetPreSignedUrl(filePath, data, 3600)
	if writeToS3error != nil {
		return fmt.Errorf("error writing csv to s3: %w", writeToS3error)
	}

	logger.InfoNoCtx("successfully wrote csv to s3", zap.String("signed_url", signedUrl))

	return nil
}

// WriteCsvToMail writes a csv to mail
func (s *Service) SendCsvToMail(ctx context.Context, data []byte, outputType OutputType, outputFileName string) error {
	msgId, err := s.mailReport(ctx, data, outputType, outputFileName)
	if err != nil {
		return fmt.Errorf("error mailing %s: %w", GetReportTitleShort(outputType), err)
	}

	logger.Info(ctx, fmt.Sprintf("successfully mailed %s", GetReportTitleShort(outputType)), zap.String("message_id", msgId))

	return nil
}

func (s *Service) mailReport(ctx context.Context, fileContent []byte, outputType OutputType, outputFileName string) (string, error) {
	var (
		reportDate = time.Now().Format("2006-01-02")
		ccEmails   = []*commsPb.EmailMessage_Destination_EmailAddress{
			{
				EmailId: "<EMAIL>",
				Name:    "Sahil Rathi",
			},
		}
	)

	if s.conf.Application.Environment == "prod" {
		ccEmails = append(ccEmails, []*commsPb.EmailMessage_Destination_EmailAddress{
			{
				EmailId: "<EMAIL>",
				Name:    "Nikhil Peruri",
			},
			{
				EmailId: "<EMAIL>",
				Name:    "Kushal",
			},
			{
				EmailId: "<EMAIL>",
				Name:    "Rinsy Roshan",
			},
			{
				EmailId: "<EMAIL>",
				Name:    "Rachit Chaddha",
			},
			{
				EmailId: "<EMAIL>",
				Name:    "Mohit Patni",
			},
		}...)
	}

	request := &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: "<EMAIL>",
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   "<EMAIL>",
				FromEmailName: GetReportTitle(outputType),
				ToEmailId:     "<EMAIL>",
				ToEmailName:   GetReportTitle(outputType),
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_InternalReportEmailOption{
						InternalReportEmailOption: &commsPb.InternalReportEmailOption{
							EmailType: commsPb.EmailType_INTERNAL_REPORT_EMAIL,
							Option: &commsPb.InternalReportEmailOption_InternalReportEmailOptionV1{
								InternalReportEmailOptionV1: &commsPb.InternalReportEmailOptionsV1{
									ReportTitle:     GetReportTitle(outputType),
									ReportDate:      reportDate,
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
								},
							},
						},
					},
				},
				Attachment: []*commsPb.EmailMessage_Attachment{
					{
						FileContent:    fileContent,
						FileName:       outputFileName,
						Disposition:    commsPb.Disposition_ATTACHMENT,
						AttachmentType: "text/comma-separated-values",
					},
				},
				Destination: &commsPb.EmailMessage_Destination{
					CcAddresses: ccEmails,
				},
			}},
	}
	response, err := s.commsClient.SendMessage(ctx, request)
	if rpcErr := epifigrpc.RPCError(response, err); rpcErr != nil {
		logger.Error(ctx, fmt.Sprintf("error mailing %s", GetReportTitleShort(outputType)), zap.Any("response", response), zap.Error(rpcErr))
		return "", fmt.Errorf("error mailing %s: %w", GetReportTitleShort(outputType), rpcErr)
	}

	return response.GetMessageId(), nil
}

func (s *Service) GetAffluenceForActor(ctx context.Context, actorId string) (*AffluenceRow, error) {
	// Fetch income estimate data to calculate affluence class
	incomeEstimateIntel, err := s.userIntelClient.GetUserIntel(ctx, &userIntelPb.GetUserIntelRequest{
		ActorId:   actorId,
		IntelType: userIntelPb.IntelType_INTEL_TYPE_INCOME_ESTIMATE,
	})
	if rpcErr := epifigrpc.RPCError(incomeEstimateIntel, err); rpcErr != nil {
		logger.Error(ctx, "error fetching income estimate intel", zap.String("actor_id", actorId), zap.Error(rpcErr))
		return &AffluenceRow{
			ActorId:   actorId,
			Affluence: "NA",
		}, fmt.Errorf("error fetching income estimate intel: %w", rpcErr)
	}

	affluenceClass := decisionmodel.GetAffluenceClassFromIncomeEstimate(incomeEstimateIntel.GetUserIntel().GetIntelData().GetIncomeEstimateData().GetPredictedIncome())
	return &AffluenceRow{
		ActorId:   actorId,
		Affluence: strings.TrimPrefix(affluenceClass.String(), "AFFLUENCE_CLASS_"),
	}, nil
}

func (s *Service) GetProductToPitchForActor(ctx context.Context, actorId string) (*ProductToPitchRow, error) {
	var (
		incomeScreenerCheck, lendableUserScreenerCheck, highConfidenceAppsScreenerCheck, finalScreenerCheck bool
		finalScreenerCheckText                                                                              = "NA"
		loanAffinityCat                                                                                     lendabilityPb.LoanAffinityCategory
		pdCat                                                                                               lendabilityPb.PDCategory
		productToPitchForActor                                                                              = productPb.ProductType_PRODUCT_TYPE_UNSPECIFIED
	)

	errGroup, gctx := errgroup.WithContext(ctx)

	if shouldCheckScreener {
		errGroup.Go(func() error {
			// Pass the screener check for the actor if any of the one is true
			// 1. check if user income can pass screener
			// 2. check if user is a lendable user
			// 3. check if user has more than 2 high confidence apps
			incomeEstimateIntel, err := s.userIntelClient.GetUserIntel(gctx, &userIntelPb.GetUserIntelRequest{
				ActorId:   actorId,
				IntelType: userIntelPb.IntelType_INTEL_TYPE_INCOME_ESTIMATE,
			})
			if rpcErr := epifigrpc.RPCError(incomeEstimateIntel, err); rpcErr != nil {
				logger.Error(gctx, "error fetching income estimate intel", zap.String("actor_id", actorId), zap.Error(rpcErr))
				return nil
			}

			incomeScreenerCheck = decisionmodel.CanPassScreenerForIncome(incomeEstimateIntel.GetUserIntel().GetIntelData().GetIncomeEstimateData().GetPredictedIncome())
			return nil
		})

		errGroup.Go(func() error {
			// fetching lendability intel
			lendabilityIntel, err := s.userIntelClient.GetUserIntel(gctx, &userIntelPb.GetUserIntelRequest{
				ActorId:     actorId,
				ClientReqId: inteltypeparam.InvalidClientReqIdForWbFlow,
				IntelType:   userIntelPb.IntelType_INTEL_TYPE_LENDABILITY,
			})
			if rpcErr := epifigrpc.RPCError(lendabilityIntel, err); rpcErr != nil {
				logger.Error(gctx, "error fetching lendability intel", zap.String("actor_id", actorId), zap.Error(rpcErr))
				return nil
			}

			lendableUserScreenerCheck = lendabilityIntel.GetUserIntel().GetIntelData().GetLendabilityData().GetIsUserLendable() == common.BooleanEnum_TRUE
			return nil
		})

		errGroup.Go(func() error {
			// fetching device details
			userDevicePropertiesResp, err := s.userClient.GetUserDeviceProperties(gctx, &usersPb.GetUserDevicePropertiesRequest{
				ActorId:       actorId,
				PropertyTypes: []typesv2.DeviceProperty{typesv2.DeviceProperty_DEVICE_PROP_ALL_APPS_INFO},
			})
			if rpcErr := epifigrpc.RPCError(userDevicePropertiesResp, err); rpcErr != nil {
				logger.Error(gctx, "error fetching device properties", zap.String("actor_id", actorId), zap.Error(rpcErr))
				return nil
			}

			highConfidenceAppsCount := 0
			for _, userDeviceProperty := range userDevicePropertiesResp.GetUserDevicePropertyList() {
				if userDeviceProperty.GetDeviceProperty() == typesv2.DeviceProperty_DEVICE_PROP_ALL_APPS_INFO {
					for _, app := range userDeviceProperty.GetPropertyValue().GetAllAppsInfo().GetAppsList() {
						if lo.Contains(checks.HighConfidenceAppPackages, app.GetPackageName()) {
							highConfidenceAppsCount++
							if highConfidenceAppsCount >= 2 {
								highConfidenceAppsScreenerCheck = true
								break
							}
						}
					}
					break
				}
			}
			return nil
		})
	}

	errGroup.Go(func() error {
		actorLendabilityResp, err := s.lendabilityClient.GetActorLendability(gctx, &lendabilityPb.GetActorLendabilityRequest{
			ActorId: actorId,
			Flow:    lendabilityPb.Flow_FLOW_WEALTH_BUILDER,
		})
		if rpcErr := epifigrpc.RPCError(actorLendabilityResp, err); rpcErr != nil {
			return fmt.Errorf("error fetching actor lendability: %w", rpcErr)
		}
		pdCat = actorLendabilityResp.GetLendabilityDetails().GetPdCategory()
		loanAffinityCat = actorLendabilityResp.GetLendabilityDetails().GetLoanAffinityCategory()
		return nil
	})

	if err := errGroup.Wait(); err != nil {
		return nil, fmt.Errorf("error fetching data with go routine: %w", err)
	}

	finalScreenerCheck = incomeScreenerCheck || lendableUserScreenerCheck || highConfidenceAppsScreenerCheck
	if pdCat == lendabilityPb.PDCategory_PD_CATEGORY_UNSPECIFIED || loanAffinityCat == lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_UNSPECIFIED {
		return nil, fmt.Errorf("pd category or loan affinity category is unspecified")
	}
	productToPitchForActor = productToPitchMap[finalScreenerCheck][loanAffinityCat][pdCat]

	if shouldCheckScreener {
		finalScreenerCheckText = fmt.Sprintf("%v", finalScreenerCheck)
	}

	return &ProductToPitchRow{
		ActorId:              actorId,
		Screener:             finalScreenerCheckText,
		LoanAffinityCategory: strings.TrimPrefix(loanAffinityCat.String(), "LOAN_AFFINITY_CATEGORY_"),
		PdCategory:           strings.TrimPrefix(pdCat.String(), "PD_CATEGORY_"),
		ProductToPitch:       strings.TrimPrefix(productToPitchForActor.String(), "PRODUCT_TYPE_"),
	}, nil
}

func sleep(index int) {
	if index%20 == 0 {
		logger.InfoNoCtx("Completed for 20 actors.........sleeping for 50ms........", zap.Int("index", index))
		time.Sleep(50 * time.Millisecond)
	}
}
