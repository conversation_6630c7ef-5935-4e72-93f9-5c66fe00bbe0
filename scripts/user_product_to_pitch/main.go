package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"sync"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	commsPb "github.com/epifi/gamma/api/comms"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	userPb "github.com/epifi/gamma/api/user"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/scripts/user_product_to_pitch/config"
)

var (
	// input csv file
	inputCsvFile   = flag.String("inputCsvFile", "", "details of a specific reward event seperated by ,")
	inputActorIds  = flag.String("inputActorIds", "", "actor ids for which product to pitch needs to be fetched")
	outputTypeFlag = flag.String("outputType", "BOTH", "type of output: 'PRODUCT_TO_PITCH', 'AFFLUENCE', or 'BOTH' (default: BOTH)")
)

// parseOutputType converts string flag to OutputType enum
func parseOutputType(s string) (OutputType, error) {
	outputType := OutputType(s)
	if !outputType.IsValid() {
		return BOTH, fmt.Errorf("invalid output type: %s. Valid options are: PRODUCT_TO_PITCH, AFFLUENCE, BOTH", s)
	}
	return outputType, nil
}

// nolint:funlen,govet
func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}

	ctx := context.Background()

	// connect to user service
	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)

	// connect to user intel service
	userIntelConn := epifigrpc.NewConnByService(cfg.USER_INTEL_SERVICE)
	defer epifigrpc.CloseConn(userIntelConn)

	// connect to comms service
	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)

	// connect to pl service
	plConn := epifigrpc.NewConnByService(cfg.PRE_APPROVED_LOAN_SERVICE)
	defer epifigrpc.CloseConn(plConn)

	// fetch service specific clients
	userClient := userPb.NewUsersClient(userConn)
	userIntelClient := userIntelPb.NewUserIntelServiceClient(userIntelConn)
	lendabilityClient := lendabilityPb.NewLendabilityClient(plConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	// fetch the s3 client
	awsSession, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic("error creating new aws session", zap.Error(err))
	}
	s3Client := s3.NewClient(awsSession, conf.Aws.S3.BaseBucketName)

	// set the new helper service
	service := NewService(userClient, userIntelClient, lendabilityClient, commsClient, s3Client, conf)

	// Parse and validate output type
	outputType, err := parseOutputType(*outputTypeFlag)
	if err != nil {
		logger.ErrorNoCtx("invalid output type", zap.Error(err))
		return
	}

	var actorIdsList []string

	switch {
	case *inputCsvFile != "":
		// adding /product_to_pitch to filename
		s3InputFilePath := fmt.Sprintf("/%s/input/%s", GetTypeIdentifier(outputType), *inputCsvFile)
		// fetching inputCsvFile from the input csv
		inputCsvRows, err := service.ReadCsvFromS3(s3InputFilePath)
		if err != nil {
			logger.Panic("error reading records from csv", zap.Error(err))
		}

		for _, inputCsvRow := range inputCsvRows {
			actorIdsList = append(actorIdsList, inputCsvRow.ActorId)
		}
	case *inputActorIds != "":
		actorIdsList = strings.Split(*inputActorIds, ",")
	default:
		logger.Panic("either inputCsvFile or inputActorIds should be provided")
	}

	var (
		wg              sync.WaitGroup
		outputCsvRows   []*OutputCsvRow
		lenActorIdsList = len(actorIdsList)
		resChannel      = make(chan *OutputCsvRow, lenActorIdsList)
	)

	for idx := 0; idx < lenActorIdsList; idx += batchSize {
		for j := idx; j < min(idx+batchSize, lenActorIdsList); j++ {
			wg.Add(1)
			actorId_ := actorIdsList[j]
			goroutine.RunWithDefaultTimeout(ctx, func(gctx context.Context) {
				defer wg.Done()

				// Always create a complete OutputCsvRow, but only fetch the data needed
				var outputRow *OutputCsvRow

				switch outputType {
				case PRODUCT_TO_PITCH:
					// Only fetch product to pitch data
					productToPitchForActorRow, err := service.GetProductToPitchForActor(gctx, actorId_)
					if err != nil {
						logger.Error(gctx, "error fetching product to pitch for actor", zap.Int("index", j), zap.String(logger.ACTOR_ID_V2, actorId_), zap.Error(err))
						return
					}
					outputRow = productToPitchForActorRow.ToOutputCsvRow()

				case AFFLUENCE:
					// Only fetch affluence data
					affluenceRow, err := service.GetAffluenceForActor(gctx, actorId_)
					if err != nil {
						logger.Error(gctx, "error fetching affluence for actor", zap.Int("index", j), zap.String(logger.ACTOR_ID_V2, actorId_), zap.Error(err))
						affluenceRow = &AffluenceRow{ActorId: actorId_, Affluence: "NA"} // Set default value if error occurs
					}
					outputRow = affluenceRow.ToOutputCsvRow()

				case BOTH:
					// Fetch both product to pitch and affluence data
					productToPitchForActorRow, err := service.GetProductToPitchForActor(gctx, actorId_)
					if err != nil {
						logger.Error(gctx, "error fetching product to pitch for actor", zap.Int("index", j), zap.String(logger.ACTOR_ID_V2, actorId_), zap.Error(err))
						return
					}

					// fetching the affluence for the actor id
					affluenceRow, err := service.GetAffluenceForActor(gctx, actorId_)
					if err != nil {
						logger.Error(gctx, "error fetching affluence for actor", zap.Int("index", j), zap.String(logger.ACTOR_ID_V2, actorId_), zap.Error(err))
						affluenceRow = &AffluenceRow{ActorId: actorId_, Affluence: "NA"} // Set default value if error occurs
					}

					// Combine both rows into output row
					outputRow = CombineToOutputCsvRow(productToPitchForActorRow, affluenceRow)

				default:
					logger.Error(gctx, "invalid output type", zap.String("outputType", outputType.String()))
					return
				}

				resChannel <- outputRow
			})
		}

		// Wait for the batch to finish
		waitgroup.SafeWaitCtx(ctx, &wg)
		// Sleep after each batch
		sleep(idx)
	}

	// Collect results
	close(resChannel)
	for res := range resChannel {
		outputCsvRows = append(outputCsvRows, res)
	}

	if len(outputCsvRows) == 0 {
		logger.Info(ctx, "no records to write to csv")
		return
	}

	// Generate CSV using the unified function
	csvInBytes, err := generateCsvInBinary(outputCsvRows, outputType)
	if err != nil {
		logger.Panic("error fetching csv in binary", zap.Error(err))
	}

	// Generate output file name and S3 path
	outputFileName := GetOutputFileName(outputType, *inputCsvFile)
	s3OutputFilePath := fmt.Sprintf("/%s/output/%s", GetTypeIdentifier(outputType), outputFileName)

	// Send CSV to mail and write to S3
	err = service.SendCsvToMail(ctx, csvInBytes, outputType, outputFileName)
	if err != nil {
		logger.Error(ctx, "error sending csv to mail", zap.Error(err))
	}

	err = service.WriteCsvToS3(s3OutputFilePath, csvInBytes)
	if err != nil {
		logger.Error(ctx, "error writing csv to s3", zap.Error(err))
	}
}
