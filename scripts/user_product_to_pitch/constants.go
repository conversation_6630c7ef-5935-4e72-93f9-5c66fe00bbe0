package main

import (
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	productPb "github.com/epifi/gamma/api/product"
)

// OutputType represents the type of CSV output to generate
type OutputType string

const (
	PRODUCT_TO_PITCH OutputType = "PRODUCT_TO_PITCH"
	AFFLUENCE        OutputType = "AFFLUENCE"
	BOTH             OutputType = "BOTH"
)

// String returns the string representation of OutputType
func (ot OutputType) String() string {
	return string(ot)
}

// IsValid checks if the OutputType is valid
func (ot OutputType) IsValid() bool {
	switch ot {
	case PRODUCT_TO_PITCH, AFFLUENCE, BOTH:
		return true
	default:
		return false
	}
}

const (
	// batchSize is the number of actor ids to be processed in a single batch in parallel in go routines
	batchSize = 10
	// keeping this false because screener value does not make a difference for current productToPitchMap
	shouldCheckScreener = false
)

var (
	// productToPitchMap is mapping to fetch product to pitch to a user based on screener check, loan affinity category and pd category
	productToPitchMap = map[bool]map[lendabilityPb.LoanAffinityCategory]map[lendabilityPb.PDCategory]productPb.ProductType{
		true: {
			lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH: {
				lendabilityPb.PDCategory_PD_CATEGORY_HIGH:   productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM: productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
				lendabilityPb.PDCategory_PD_CATEGORY_LOW:    productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
			},
			lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_MEDIUM: {
				lendabilityPb.PDCategory_PD_CATEGORY_HIGH:   productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM: productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_LOW:    productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
			},
			lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_LOW: {
				lendabilityPb.PDCategory_PD_CATEGORY_HIGH:   productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM: productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_LOW:    productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
			},
		},
		false: {
			lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_HIGH: {
				lendabilityPb.PDCategory_PD_CATEGORY_HIGH:   productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM: productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
				lendabilityPb.PDCategory_PD_CATEGORY_LOW:    productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
			},
			lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_MEDIUM: {
				lendabilityPb.PDCategory_PD_CATEGORY_HIGH:   productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM: productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_LOW:    productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
			},
			lendabilityPb.LoanAffinityCategory_LOAN_AFFINITY_CATEGORY_LOW: {
				lendabilityPb.PDCategory_PD_CATEGORY_HIGH:   productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM: productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				lendabilityPb.PDCategory_PD_CATEGORY_LOW:    productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
			},
		},
	}
)
