package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"

	"github.com/epifi/gamma/pay/config/server"
)

type Config struct {
	Environment                      string
	Aws                              *cfg.AWS
	Logging                          *cfg.Logging
	PrestoConfigWrapper              *PrestoConfigWrapper
	TemporalSecrets                  *TemporalSecrets
	CurrentAccountStatementGenParams *CurrentAccountStatementGenParams
	RiskyVPABucket                   string
	EpifiDb                          *cfg.DB
	UsecaseDbConfigMap               cfg.UseCaseDbConfigMap
	DataExport                       *DataExportConfig
	PgProgramToAuthSecretMap         map[string]*server.PgProgramToAuthSecret
	OrderTransactionDetailsConfig    *OrderTransactionDetailsConfig
	// SQS publisher config for FetchAndCreateFailedEnachTransactionPublisher job
	FetchAndCreateFailedEnachTransactionPublisher *cfg.SqsPublisher
}

type PrestoConfigWrapper struct {
	PrestoConfig      *cfg.Presto
	PrestoUserName    string `field:"PrestoSecretsPath" jsonPath:"username"`
	PrestoPassword    string `field:"PrestoSecretsPath" jsonPath:"password"`
	PrestoSecretsPath string `iam:"sm-read"`
}

type DataExportConfig struct {
	ExportUseCases map[string]*DataExportUseCase
}

type DataExportUseCase struct {
	Email        *EmailConfig
	Query        string
	CSV          *CSVConfig
	SourceDBType string // Values: "PRESTO" or "SQL"
}

type EmailConfig struct {
	SES *SESConfig
}

type SESConfig struct {
	From       string
	Recipients *Recipients
	Template   *Template
}

type Recipients struct {
	To []string
	Cc []string
}

type Template struct {
	Subject string
	Body    string
}

type CSVConfig struct {
	Filename string
	Headers  []string
}

type TemporalSecrets struct {
	TemporalCodecAesKeySecret string `iam:"sm-read"`
	TemporalCodecAesKey       string `field:"TemporalCodecAesKeySecret"`
}

type CurrentAccountStatementGenParams struct {
	SenderEmail string
	SenderName  string
	// The map stores the use case for which the statement needs to be generated, which can be any arbitrary string
	// using which the given set of receivers can be determined. Please note that the use case in the map keys is different
	// from the USE_CASE enum that we use across the codebase.
	UsecaseToReceiverDetailsMap map[string]*StatementGeneratorMetadata
}

type StatementGeneratorMetadata struct {
	// The account numbers for which to fetch the statements.
	AccountNumbers  []string
	ReceiverDetails []*ReceiverDetails
}

type ReceiverDetails struct {
	ReceiverEmailId string
	ReceiverName    string
}

type OrderTransactionDetailsConfig struct {
	SenderEmail string
	SenderName  string
}

var (
	_, b, _, _ = runtime.Caller(0)

	once    sync.Once
	conf    *Config
	loadErr error
)

func Load() (*Config, error) {
	once.Do(func() {
		conf, loadErr = loadConfig()
	})
	if loadErr != nil {
		return nil, loadErr
	}
	return conf, nil
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "pay-god-script")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Environment, conf.Aws.Region)
	if err != nil {
		return nil, fmt.Errorf("failed to load secrets: %w", err)
	}

	err = cfg.UpdateDbEndpointsInConfigMapV3(conf.UsecaseDbConfigMap)
	if err != nil {
		return nil, fmt.Errorf("error in resolving UseCaseDbConfigMap: %w", err)
	}

	dbList := make([]*cfg.DB, 0)
	for _, db := range conf.UsecaseDbConfigMap {
		dbList = append(dbList, db)
	}
	if _, err := cfg.LoadSecretsAndPrepareDBConfig(nil, conf.Environment, conf.Aws.Region, dbList...); err != nil {
		return nil, fmt.Errorf("failed to load and update secret values in DB Config Map %w", err)
	}

	if _, err := cfg.LoadSecretsAndPrepareDBConfig(nil, conf.Environment, conf.Aws.Region, conf.EpifiDb); err != nil {
		return nil, fmt.Errorf("failed to load and update secret values in DB Config Map %w", err)
	}

	setupPrestoConfig(conf)

	return conf, nil
}

// setupPrestoConfig sets up the Presto secrets in the config by setting the fetched secrets from secrets manager
// and setting them in the PrestoConfig struct.
func setupPrestoConfig(conf *Config) {
	if conf.PrestoConfigWrapper == nil {
		// Skip Presto config setup if not configured
		return
	}
	conf.PrestoConfigWrapper.PrestoConfig.Username = conf.PrestoConfigWrapper.PrestoUserName
	conf.PrestoConfigWrapper.PrestoConfig.Password = conf.PrestoConfigWrapper.PrestoPassword
}

func testEnvConfigDir() string {
	return filepath.Dir(b)
}
