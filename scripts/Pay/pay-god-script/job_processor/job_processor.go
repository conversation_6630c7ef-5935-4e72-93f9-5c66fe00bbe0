package job_processor

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
)

type JobType uint32

const (
	JobUnspecified                       JobType = 0
	JobPoolAccountStatementGenerator     JobType = 1
	JobInvokePgRecon                     JobType = 2
	JobInvokeListAccountProviders        JobType = 3
	JobInvokeValidateAddress             JobType = 4
	JobInvokeGetBeneficiaryNameDetails   JobType = 5
	JobUpdateVpaMerchantInfo             JobType = 6
	JobReconPgMandateStatus              JobType = 7
	JobInvokeBalanceApis                 JobType = 8
	JobGetAverageEODBalance              JobType = 9
	JobInvokeAccountStatementApi         JobType = 10
	JobDataExport                        JobType = 11
	JobUpdateMinRequiredSalaryOfEmployer JobType = 12
	JobGetOrderTransactionDetails        JobType = 13
	JobLinkInternalAccount               JobType = 14
	JobLinkAccount                       JobType = 15
	JobUpdateUpiAccountPinSetState       JobType = 16
	JobIssueInsurancePolicy              JobType = 17
	JobReqCheckTxnStatus                 JobType = 18
	JobFetchEnachFailedTransactions      JobType = 19
)

var (
	JobNames = map[string]JobType{
		"POOL_ACCOUNT_STATEMENT_GENERATOR":       JobPoolAccountStatementGenerator,
		"INVOKE_PG_RECON":                        JobInvokePgRecon,
		"INVOKE_LIST_ACCOUNT_PROVIDERS":          JobInvokeListAccountProviders,
		"INVOKE_VALIDATE_ADDRESS":                JobInvokeValidateAddress,
		"INVOKE_GET_BENEFICIARY_NAME_DETAILS":    JobInvokeGetBeneficiaryNameDetails,
		"UPDATE_VPA_MERCHANT_INFO":               JobUpdateVpaMerchantInfo,
		"RECON_PG_MANDATE_STATUS":                JobReconPgMandateStatus,
		"INVOKE_BALANCE_APIS":                    JobInvokeBalanceApis,
		"GET_AVERAGE_EOD_BALANCE":                JobGetAverageEODBalance,
		"INVOKE_ACCOUNT_STATEMENT_API":           JobInvokeAccountStatementApi,
		"DATA_EXPORT":                            JobDataExport,
		"UPDATE_MIN_REQUIRED_SALARY_OF_EMPLOYER": JobUpdateMinRequiredSalaryOfEmployer,
		"GET_ORDER_TRANSACTION_DETAILS":          JobGetOrderTransactionDetails,
		"UPI_LINK_INTERNAL_ACCOUNT":              JobLinkInternalAccount,
		"UPI_LINK_ACCOUNT":                       JobLinkAccount,
		"UPDATE_UPI_ACCOUNT_PIN_SET_STATUS":      JobUpdateUpiAccountPinSetState,
		"ISSUE_INSURANCE_POLICY":                 JobIssueInsurancePolicy,
		"REQ_CHECK_TXN_STATUS":                   JobReqCheckTxnStatus,
		"FETCH_ENACH_FAILED_TRANSACTIONS":        JobFetchEnachFailedTransactions,
	}
)

type JobProcessor interface {
	// ParseAndStoreArgs method needs to take the input arguments and store them in the job processor.
	ParseAndStoreArgs(input *JobRequest) error
	DoJob(context.Context) error
}

// JobRequest data that is sent to each job for processing
// It has two argument fields so that jobs requiring only two string args can directly use them.
// For jobs requiring more args, take the entire arg in Arg1 and unmarshal them inside the processor.
type JobRequest struct {
	Job   JobType
	Args1 string
	Args2 string
	Args3 string
}

func GetValidJobNames() []string {
	validJobs := make([]string, 0, len(JobNames))
	for name := range JobNames {
		validJobs = append(validJobs, name)
	}
	return validJobs
}

func GetProcessorForJob(jobType JobType, conf *config.Config) (JobProcessor, error, func()) {
	var processor JobProcessor
	var err error
	cleanupFn := func() {}

	switch jobType {
	case JobPoolAccountStatementGenerator:
		processor, err, cleanupFn = NewCurrentAccountStatementGenJob(conf)
	case JobInvokePgRecon:
		processor, err, cleanupFn = NewInvokePgReconJob(conf)
	case JobInvokeListAccountProviders:
		processor, err, cleanupFn = NewInvokeListAccountProvidersRpcJob(conf)
	case JobInvokeValidateAddress:
		processor, err, cleanupFn = NewInvokeValidateAddressRpcJob(conf)
	case JobInvokeGetBeneficiaryNameDetails:
		processor, err, cleanupFn = NewInvokeGetBeneficiaryNameDetailsJob(conf)
	case JobInvokeBalanceApis:
		processor, err, cleanupFn = NewInvokeBalanceApisJob(conf)
	case JobUpdateVpaMerchantInfo:
		processor, err, cleanupFn = NewUpdateVpaMerchantInfoJob(conf)
	case JobReconPgMandateStatus:
		processor, err, cleanupFn = NewReconPgMandateStatusJob(conf)
	case JobGetAverageEODBalance:
		processor, cleanupFn, err = NewGetAverageEODBalanceJob(conf)
	case JobInvokeAccountStatementApi:
		processor, cleanupFn, err = NewInvokeStatementApiJob(conf)
	case JobDataExport:
		processor, cleanupFn, err = NewDataExportJob(conf)
	case JobUpdateMinRequiredSalaryOfEmployer:
		processor, cleanupFn, err = NewUpdateMinRequiredSalaryOfEmployerJob(conf)
	case JobGetOrderTransactionDetails:
		processor, cleanupFn, err = NewGetOrderTransactionDetailsJob(conf)
	case JobLinkInternalAccount:
		processor, err, cleanupFn = NewLinkInternalAccountJob(conf)
	case JobLinkAccount:
		processor, err, cleanupFn = NewLinkAccountJob(conf)
	case JobUpdateUpiAccountPinSetState:
		processor, cleanupFn, err = NewUpdateUpiAccountPinSetStateJob(conf)
	case JobIssueInsurancePolicy:
		processor, err, cleanupFn = NewIssueInsurancePolicyJob(conf)
	case JobReqCheckTxnStatus:
		processor, cleanupFn, err = NewReqCheckTxnStatusJob(conf)
	case JobFetchEnachFailedTransactions:
		processor, cleanupFn, err = NewFetchEnachFailedTransactionsJob(conf)
	default:
		err = fmt.Errorf("invalid job type: %v", jobType)
	}

	if err != nil {
		return nil, err, cleanupFn
	}
	return processor, nil, cleanupFn
}
