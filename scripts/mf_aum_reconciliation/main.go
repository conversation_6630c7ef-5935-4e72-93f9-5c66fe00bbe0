package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	awss3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"bytes"
	"encoding/csv"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	confpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	mfNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/mutualfund"
	"github.com/epifi/be-common/pkg/logger"
	woWorkflowPb "github.com/epifi/gamma/api/wealthonboarding/workflow"
	"github.com/epifi/gamma/scripts/mf_aum_reconciliation/config"
)

var (
	rtaFlag = flag.String("rta", "", "rta")
)

const (
	aumFilePath     = "./aumFilePath.csv"
	cams            = "cams"
	karvy           = "karvy"
	aumFileS3Folder = "capture_mf_aum_reconciliation"
	aumFile         = "aum_file"
	chunkSize       = 10000
)

func main() {
	flag.Parse()
	if rtaFlag == nil || *rtaFlag == "" {
		logger.ErrorNoCtx("rta should not be empty")
		os.Exit(1)
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
		os.Exit(1)
	}

	rta := *rtaFlag
	rtaVendor, err := getVendor(strings.ToLower(rta))
	if err != nil {
		logger.ErrorNoCtx("error in getVendor", zap.Error(err))
		os.Exit(1)
	}

	aumFileContent, err := os.ReadFile(aumFilePath)
	if err != nil {
		logger.Panic("failed to load aum file", zap.Error(err))
		os.Exit(1)
	}

	reader := csv.NewReader(strings.NewReader(string(aumFileContent)))
	allRecords, err := reader.ReadAll()
	if err != nil {
		logger.Panic("failed to parse csv file", zap.Error(err))
		os.Exit(1)
	}
	if len(allRecords) < 2 {
		logger.Panic("csv file does not have enough records", zap.Int("num_records", len(allRecords)))
		os.Exit(1)
	}
	header := allRecords[0]
	records := allRecords[1:]
	totalRecords := len(records)
	chunkCount := (totalRecords + chunkSize - 1) / chunkSize

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)

	ctx := context.Background()
	awsConf, awsErr := confpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	if awsErr != nil {
		logger.ErrorNoCtx("failed to load aws config", zap.Error(awsErr))
		os.Exit(1)
	}
	s3Client := s3.NewClient(awsConf, conf.Aws.S3.BucketName)

	curTime := time.Now()

	for i := 0; i < chunkCount; i++ {
		start := i * chunkSize
		end := start + chunkSize
		if end > totalRecords {
			end = totalRecords
		}
		chunkRecords := records[start:end]

		// Write header + chunkRecords to buffer
		var buf bytes.Buffer
		csvWriter := csv.NewWriter(&buf)
		if err := csvWriter.Write(header); err != nil {
			logger.Panic("failed to write header to chunk", zap.Error(err))
			os.Exit(1)
		}
		if err := csvWriter.WriteAll(chunkRecords); err != nil {
			logger.Panic("failed to write records to chunk", zap.Error(err))
			os.Exit(1)
		}
		csvWriter.Flush()
		if err := csvWriter.Error(); err != nil {
			logger.Panic("csv writer error", zap.Error(err))
			os.Exit(1)
		}

		// S3 path for this chunk
		path := getS3PathForAumFileChunk(rtaVendor, curTime, i+1)
		logger.InfoNoCtx("aum file chunk path", zap.String("path", path), zap.Int("chunk", i+1), zap.Int("records", len(chunkRecords)))
		err1 := s3Client.Write(ctx, path, buf.Bytes(), string(awss3.ObjectCannedACLBucketOwnerFullControl))
		if err1 != nil {
			logger.ErrorNoCtx("failed to write chunk to s3", zap.Error(err1))
			os.Exit(1)
		}

		payloadBytes, _ := protojson.Marshal(&woWorkflowPb.CaptureMfAumFileRequest{
			RtaType:    rtaVendor,
			AumFileUrl: path,
		})

		wfRes, wfErr := celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{Params: &celestialPb.WorkflowCreationRequestParams{
			Version: workflowPb.Version_V0,
			Type:    celestial.GetTypeEnumFromWorkflowType(mfNs.CaptureMfAumFile),
			Payload: payloadBytes,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     uuid.NewString(),
				Client: workflowPb.Client_WEALTH_ONBOARDING,
			},
			Ownership:        commontypes.Ownership_EPIFI_WEALTH,
			QualityOfService: celestialPb.QoS_GUARANTEED,
		}})
		if te := epifigrpc.RPCError(wfRes, wfErr); te != nil {
			logger.Error(ctx, "error in initiating capture mf aum file workflow", zap.Error(te))
			os.Exit(1)
		}
		logger.Info(ctx, "workflow initiated for chunk", zap.String("workflowId", wfRes.GetParams().GetWorkflowRequestId()), zap.Int("chunk", i+1))
	}
	os.Exit(0)
}

func getVendor(rta string) (commonvgpb.Vendor, error) {
	switch rta {
	case cams:
		return commonvgpb.Vendor_CAMS, nil
	case karvy:
		return commonvgpb.Vendor_KARVY, nil

	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, errors.New("vendor is invalid")
	}
}

func getS3PathForAumFile(vendor commonvgpb.Vendor, curTime time.Time) string {
	return fmt.Sprintf("%s/%s/%s/%s",
		aumFileS3Folder,
		strings.ToLower(vendor.String()),
		aumFile,
		fmt.Sprintf("aumFilePath_%s.csv", curTime.Format("20060102_150405")),
	)
}

func getS3PathForAumFileChunk(vendor commonvgpb.Vendor, curTime time.Time, chunkIdx int) string {
	return fmt.Sprintf("%s/%s/%s/%s",
		aumFileS3Folder,
		strings.ToLower(vendor.String()),
		aumFile,
		fmt.Sprintf("aumFilePath_%s_chunk%d.csv", curTime.Format("20060102_150405"), chunkIdx),
	)
}
