Name: "frontend"

ServerPorts:
  GrpcPort: 8082
  GrpcSecurePort: 9509
  HttpPort: 9999
  HttpPProfPort: 9990

QuestSdk:
  Disable: false

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/frontend/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

SecureLogging:
  EnableSecureLog: true
  DefaultFileLoggingParams:
    LogPath: "/var/log/frontend/secure.log"
    MaxSizeInMBs: 5
    MaxBackups: 20

GrpcServerConfig:
  MaxRecvMsgSize:
    # 9 MB
    Size: 9437184
  SuppressZapLogger: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Frontend:
  EnableDeviceIntegrityCheck: true
  EnableLocationInterceptor: true
  MaxGRPCTimeout: "1m"
  MaxGRPCStreamTimeout: "20m"
  DeviceIntegrity:
    EnableWhitelistedTokens: true
    SkipAsyncDeviceIntegrityChecks: false
    WhitelistedTokensList: [ "DUMMY_TOKEN" ]
    DefaultHighRiskDeviceConsentDuration: "24h"
    MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
    AsyncDeviceIntegrityCheck:
      DisableFeature: false
      MinAndroidVersion: 174
      MinIOSVersion: 10000
  IPInterceptorParams:
    EnableIPInterceptor: true

EnableGetPaymentOptionsV1: true

GrpcRatelimiterParams:
  Disable: false

GrpcAttributeRatelimiterParams:
  Namespace: "frontend-rpc"
  Disable: false
  ResourceMap:
    frontend_account_signup_signup_loginwithoauth:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_account_signup_signup_addoauthaccount:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
    frontend_account_signup_signup_fetchaccesstoken:
      RateLimitList:
        "0":
          ArrayElement:
            Position: 0
          Attribute: "ATTRIBUTE_REQ_HEADER_DEVICE_ID"
          AttributeValueRegexp: ".*"
          Rate: 20
          Period: 1m
      EnableLogging: false
