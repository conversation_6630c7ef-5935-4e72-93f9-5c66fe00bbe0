// nolint:unused
package leads

import (
	"github.com/epifi/be-common/tools/servergen/meta"

	leadsPb "github.com/epifi/gamma/api/leads"
	consumerPb "github.com/epifi/gamma/api/leads/consumer"
	leadsDevPb "github.com/epifi/gamma/api/leads/developer"
	"github.com/epifi/gamma/leads/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeUserLeadService,
		GRPCRegisterMethods: []any{leadsPb.RegisterUserLeadSvcServer},
	},
	{
		WireInitializer:     wire.InitializrDevLeadsService,
		GRPCRegisterMethods: []any{leadsDevPb.RegisterDevLeadServer},
	},
	{
		WireInitializer:     wire.InitializeDeleteUserEventConsumerService,
		GRPCRegisterMethods: []any{consumerPb.RegisterConsumerServer},
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  consumerPb.ProcessDeleteUserEventMethod,
				ConfigField: "DeleteUserEventSqsSubscriber",
			},
		},
	},
}
