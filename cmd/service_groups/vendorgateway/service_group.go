package vendorgateway

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/tools/servergen/meta"

	aa "github.com/epifi/gamma/api/vendorgateway/aa"
	igVgPb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	aml "github.com/epifi/gamma/api/vendorgateway/aml"
	seon "github.com/epifi/gamma/api/vendorgateway/appscreener/seon"
	billPaymentsPb "github.com/epifi/gamma/api/vendorgateway/billpayments/setu"
	bouncycastle "github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	ckycpb "github.com/epifi/gamma/api/vendorgateway/ckyc"
	aclVgPb "github.com/epifi/gamma/api/vendorgateway/comms/acl"
	creditreport "github.com/epifi/gamma/api/vendorgateway/credit_report"
	vgCcV2Pb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	crmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	leadsquaredPb "github.com/epifi/gamma/api/vendorgateway/crm/leadsquared"
	currencyInsightsVgPb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	senseforthpb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	nuggetpb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
	"github.com/epifi/gamma/api/vendorgateway/cx/federal"
	freshchatpb "github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	freshdeskpb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	cxinhousepb "github.com/epifi/gamma/api/vendorgateway/cx/inhouse"
	ozonetelpb "github.com/epifi/gamma/api/vendorgateway/cx/ozonetel"
	solutionspb "github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	digilockerPb "github.com/epifi/gamma/api/vendorgateway/digilocker"
	dlvalidatepb "github.com/epifi/gamma/api/vendorgateway/dl"
	docPb "github.com/epifi/gamma/api/vendorgateway/docs"
	ekycpb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	emailPb "github.com/epifi/gamma/api/vendorgateway/email"
	employmentpb "github.com/epifi/gamma/api/vendorgateway/employment"
	esign "github.com/epifi/gamma/api/vendorgateway/esign"
	extvalidatepb "github.com/epifi/gamma/api/vendorgateway/extvalidate"
	fcmServicePb "github.com/epifi/gamma/api/vendorgateway/fcm"
	vgFnPb "github.com/epifi/gamma/api/vendorgateway/fennel"
	fitttpb "github.com/epifi/gamma/api/vendorgateway/fittt"
	gplacepb "github.com/epifi/gamma/api/vendorgateway/gplace"
	riskcovryvgpb "github.com/epifi/gamma/api/vendorgateway/healthinsurance/riskcovry"
	vgIdfcPb "github.com/epifi/gamma/api/vendorgateway/idfc"
	idvalidate "github.com/epifi/gamma/api/vendorgateway/idvalidate"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	p2ppb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	vgiplocpb "github.com/epifi/gamma/api/vendorgateway/iplocation"
	vgItrPb "github.com/epifi/gamma/api/vendorgateway/itr"
	"github.com/epifi/gamma/api/vendorgateway/kyc/uqudo"
	vgIdfcVkycPb "github.com/epifi/gamma/api/vendorgateway/kyc/vkyc/idfc"
	"github.com/epifi/gamma/api/vendorgateway/lending/bre"
	"github.com/epifi/gamma/api/vendorgateway/lending/collateral/mutualfund"
	vgCredgenicsPb "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	creditcard "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	creditline "github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	"github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	vgFinfluxPb "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	preapprovedloan "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	abflPLPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	idfcPLPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lentra"
	liquiloansPLPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	fiftyFinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	setuVgPb "github.com/epifi/gamma/api/vendorgateway/lending/setu"
	vglivenesspb "github.com/epifi/gamma/api/vendorgateway/liveness"
	locationpb "github.com/epifi/gamma/api/vendorgateway/location"
	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	"github.com/epifi/gamma/api/vendorgateway/moengage"
	namecheckpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	employernamecategoriserPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	employernamematchpb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	npspb "github.com/epifi/gamma/api/vendorgateway/nps"
	"github.com/epifi/gamma/api/vendorgateway/ocr"
	dreamfolksVgPb "github.com/epifi/gamma/api/vendorgateway/offers/dreamfolks"
	loyltypb "github.com/epifi/gamma/api/vendorgateway/offers/loylty"
	poshvineVgPb "github.com/epifi/gamma/api/vendorgateway/offers/poshvine"
	qwikcilvervgpb "github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver"
	thriweVgPb "github.com/epifi/gamma/api/vendorgateway/offers/thriwe"
	vistaraVgPb "github.com/epifi/gamma/api/vendorgateway/offers/vistara"
	"github.com/epifi/gamma/api/vendorgateway/onsurity"
	vgaccountspb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vendorauthpb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	partnersdkpb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth/partnersdk"
	bankcustomervgpb "github.com/epifi/gamma/api/vendorgateway/openbanking/bank_customer"
	cardpb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	customerservicepb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	depositpb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	vgDiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/dispute"
	vgEnachPb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	lienPb "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
	paymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	b2cpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	internationalfundtransferpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	savingsservicepb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	shippingprefpb "github.com/epifi/gamma/api/vendorgateway/openbanking/shipping_preference"
	standinginstructionpb "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction"
	upiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	panpb "github.com/epifi/gamma/api/vendorgateway/pan"
	parserpb "github.com/epifi/gamma/api/vendorgateway/parser"
	pgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	phonenetwork "github.com/epifi/gamma/api/vendorgateway/phonenetwork"
	profilevalidationpb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	riskpb "github.com/epifi/gamma/api/vendorgateway/risk"
	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	shipwaypb "github.com/epifi/gamma/api/vendorgateway/shipway"
	slackPb "github.com/epifi/gamma/api/vendorgateway/slack_bot"
	smsservicepb "github.com/epifi/gamma/api/vendorgateway/sms"
	usstockpb "github.com/epifi/gamma/api/vendorgateway/stocks"
	catalog "github.com/epifi/gamma/api/vendorgateway/stocks/catalog"
	"github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	"github.com/epifi/gamma/api/vendorgateway/tiering"
	vgtransactionmonitoringpb "github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay"
	userseg "github.com/epifi/gamma/api/vendorgateway/userseg"
	vkycpb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	vkyccallpb "github.com/epifi/gamma/api/vendorgateway/vkyccall"
	wealthckycpb "github.com/epifi/gamma/api/vendorgateway/wealth/ckyc"
	wealthcvlpb "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	wealthdigilockerpb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	wealthdigiopb "github.com/epifi/gamma/api/vendorgateway/wealth/digio"
	wealthocrpb "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	wealthkarzapb "github.com/epifi/gamma/api/vendorgateway/wealth/karza"
	wealthmanchpb "github.com/epifi/gamma/api/vendorgateway/wealth/manch"
	wealthmutualfundpb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	mfAnalyticsPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	holdingsimporterpb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	wealthnsdlpb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	whatsapppb "github.com/epifi/gamma/api/vendorgateway/whatsapp"
	"github.com/epifi/gamma/api/vendorgateway/zenduty"
	wire "github.com/epifi/gamma/vendorgateway/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeSMSService,
		GRPCRegisterMethods: []any{smsservicepb.RegisterSMSServer},
	},
	{
		WireInitializer:     wire.InitializeGPlaceService,
		GRPCRegisterMethods: []any{gplacepb.RegisterGPlaceServer},
	},
	{
		WireInitializer:     wire.InitializeCustomerService,
		GRPCRegisterMethods: []any{customerservicepb.RegisterCustomerServer},
	},
	{
		WireInitializer:     wire.InitializeSavingsService,
		GRPCRegisterMethods: []any{savingsservicepb.RegisterSavingsServer},
	},
	{
		WireInitializer:     wire.InitializeShippingPreferenceService,
		GRPCRegisterMethods: []any{shippingprefpb.RegisterShippingPreferenceServer},
	},
	{
		WireInitializer:     wire.InitializeVendorAuthService,
		GRPCRegisterMethods: []any{vendorauthpb.RegisterVendorAuthServer},
	},
	{
		WireInitializer:     wire.InitializePartnerSDKService,
		GRPCRegisterMethods: []any{partnersdkpb.RegisterPartnerSDKServer},
	},
	{
		WireInitializer:     wire.InitializePaymentService,
		GRPCRegisterMethods: []any{paymentpb.RegisterPaymentServer},
	},
	{
		WireInitializer:     wire.InitializeCKYCService,
		GRPCRegisterMethods: []any{ckycpb.RegisterCKycServer},
	},
	{
		WireInitializer:     wire.InitializeLivenessService,
		GRPCRegisterMethods: []any{vglivenesspb.RegisterLivenessServer},
	},
	{
		WireInitializer:     wire.InitializeEmploymentService,
		GRPCRegisterMethods: []any{employmentpb.RegisterEmploymentServer},
	},
	{
		WireInitializer:     wire.InitializeTransactionMonitoringService,
		GRPCRegisterMethods: []any{vgtransactionmonitoringpb.RegisterTransactionRiskDronaPayServer},
	},
	{
		WireInitializer:     wire.InitializePANService,
		GRPCRegisterMethods: []any{panpb.RegisterPANServer},
	},
	{
		WireInitializer:     wire.InitializeUNNameCheckService,
		GRPCRegisterMethods: []any{namecheckpb.RegisterUNNameCheckServer},
	},
	{
		WireInitializer:     wire.InitializeEmployerNameMatchService,
		GRPCRegisterMethods: []any{employernamematchpb.RegisterEmployerNameMatchServer},
	},
	{
		WireInitializer:     wire.InitializeSenseforthLiveChatFallbackService,
		GRPCRegisterMethods: []any{senseforthpb.RegisterSenseforthLiveChatFallbackServer},
	},
	{
		WireInitializer:     wire.InitializeFreshdeskService,
		GRPCRegisterMethods: []any{freshdeskpb.RegisterFreshdeskServer},
	},
	{
		WireInitializer:     wire.InitializeFreshChatServer,
		GRPCRegisterMethods: []any{freshchatpb.RegisterFreshchatServer},
	},
	{
		WireInitializer:     wire.InitializeOzonetelService,
		GRPCRegisterMethods: []any{ozonetelpb.RegisterOzonetelServer},
	},
	{
		WireInitializer:     wire.InitializeSolutionsService,
		GRPCRegisterMethods: []any{solutionspb.RegisterSolutionsServer},
	},
	{
		WireInitializer:     wire.InitializeDepositService,
		GRPCRegisterMethods: []any{depositpb.RegisterDepositServer},
	},
	{
		WireInitializer:     wire.InitializeB2CService,
		GRPCRegisterMethods: []any{b2cpaymentpb.RegisterPaymentServer},
	},
	{
		WireInitializer:     wire.InitializeLeadSquaredService,
		GRPCRegisterMethods: []any{leadsquaredPb.RegisterLeadManagementServer},
	},
	{
		WireInitializer:     wire.InitializeStandingInstructionService,
		GRPCRegisterMethods: []any{standinginstructionpb.RegisterStandingInstructionServer},
	},
	{
		WireInitializer:     wire.InitializeLoyltyService,
		GRPCRegisterMethods: []any{loyltypb.RegisterLoyltyServer},
	},
	{
		WireInitializer:     wire.InitializeQwikcilverService,
		GRPCRegisterMethods: []any{qwikcilvervgpb.RegisterQwikcilverServer},
	},
	{
		WireInitializer:     wire.InitializeVkycService,
		GRPCRegisterMethods: []any{vkycpb.RegisterVkycServer},
	},
	{
		WireInitializer:     wire.InitializeAccountsServer,
		GRPCRegisterMethods: []any{vgaccountspb.RegisterAccountsServer},
	},
	{
		WireInitializer:     wire.InitializeEKYCService,
		GRPCRegisterMethods: []any{ekycpb.RegisterEKYCServer},
	},
	{
		WireInitializer:     wire.InitializeDOCSService,
		GRPCRegisterMethods: []any{docPb.RegisterDocsServer},
	},
	{
		WireInitializer:     wire.InitialiseFitttCricketServcie,
		GRPCRegisterMethods: []any{fitttpb.RegisterFitttServer},
	},
	{
		WireInitializer:     wire.InitializeWhatsAppService,
		GRPCRegisterMethods: []any{whatsapppb.RegisterWhatsAppServer},
	},
	{
		WireInitializer:     wire.InitializeIpLocationService,
		GRPCRegisterMethods: []any{vgiplocpb.RegisterIPLocationServer},
	},
	{
		WireInitializer:     wire.InitializeAAService,
		GRPCRegisterMethods: []any{aa.RegisterAccountAggregatorServer},
	},
	{
		WireInitializer:     wire.InitialiseLamfService,
		GRPCRegisterMethods: []any{mutualfund.RegisterLoanAgainstMutualFundServer},
	},
	{
		WireInitializer:     wire.InitializeTieringService,
		GRPCRegisterMethods: []any{tiering.RegisterTieringServer},
	},
	{
		WireInitializer:     wire.InitializeBcService,
		GRPCRegisterMethods: []any{bouncycastle.RegisterBouncyCastleServer},
	},
	{
		WireInitializer:     wire.InitializeCreditReportService,
		GRPCRegisterMethods: []any{creditreport.RegisterCreditReportServer},
	},
	{
		WireInitializer:     wire.InitializeWealthCvlService,
		GRPCRegisterMethods: []any{wealthcvlpb.RegisterCvlServer},
	},
	{
		WireInitializer:     wire.InitializeWealthNsdlService,
		GRPCRegisterMethods: []any{wealthnsdlpb.RegisterNsdlServer},
	},
	{
		WireInitializer:     wire.InitializeWealthMutualFundService,
		GRPCRegisterMethods: []any{wealthmutualfundpb.RegisterMutualFundServer},
	},
	{
		WireInitializer:     wire.InitializeHoldingsImporterService,
		GRPCRegisterMethods: []any{holdingsimporterpb.RegisterHoldingImporterServer},
	},
	{
		WireInitializer:     wire.InitialiseShipwayService,
		GRPCRegisterMethods: []any{shipwaypb.RegisterShipwayServer},
	},
	{
		WireInitializer:     wire.InitializeWealthCkycService,
		GRPCRegisterMethods: []any{wealthckycpb.RegisterCkycServer},
	},
	{
		WireInitializer:     wire.InitializeWealthManchService,
		GRPCRegisterMethods: []any{wealthmanchpb.RegisterManchServer},
	},
	{
		WireInitializer:     wire.InitializeWealthKarzaService,
		GRPCRegisterMethods: []any{wealthkarzapb.RegisterKarzaServer},
	},
	{
		WireInitializer:     wire.InitializeWealthDigioService,
		GRPCRegisterMethods: []any{wealthdigiopb.RegisterDigioServer},
	},
	{
		WireInitializer:     wire.InitializeDlAuthService,
		GRPCRegisterMethods: []any{dlvalidatepb.RegisterDLServer},
	},
	{
		WireInitializer:     wire.InitializeParserService,
		GRPCRegisterMethods: []any{parserpb.RegisterParserServer},
	},
	{
		WireInitializer:     wire.InitializeSeonService,
		GRPCRegisterMethods: []any{seon.RegisterSeonServer},
	},
	{
		WireInitializer:     wire.InitializeCXInHouseService,
		GRPCRegisterMethods: []any{cxinhousepb.RegisterCXInHouseServer},
	},
	{
		WireInitializer:     wire.InitializeCRMService,
		GRPCRegisterMethods: []any{crmPb.RegisterCRMServer},
	},
	{
		WireInitializer:     wire.InitializeIdValidateService,
		GRPCRegisterMethods: []any{idvalidate.RegisterIdValidateServer},
	},
	{
		WireInitializer:     wire.InitializeWealthInhouseOCRService,
		GRPCRegisterMethods: []any{wealthocrpb.RegisterOcrServer},
	},
	{
		WireInitializer:     wire.InitializeWealthDigilockerService,
		GRPCRegisterMethods: []any{wealthdigilockerpb.RegisterDigilockerServer},
	},
	{
		WireInitializer:     wire.InitializeP2pInvestmentService,
		GRPCRegisterMethods: []any{p2ppb.RegisterP2PServer},
	},
	{
		WireInitializer:     wire.InitializeLocationService,
		GRPCRegisterMethods: []any{locationpb.RegisterLocationServer},
	},
	{
		WireInitializer:     wire.InitializeExternalValidateService,
		GRPCRegisterMethods: []any{extvalidatepb.RegisterExternalValidateServer},
	},
	{
		WireInitializer:     wire.InitializeUserSegmentationService,
		GRPCRegisterMethods: []any{userseg.RegisterUserSegmentationServer},
	},
	{
		WireInitializer:     wire.InitialisePhoneNetworkService,
		GRPCRegisterMethods: []any{phonenetwork.RegisterPhoneNetworkServer},
	},
	{
		WireInitializer:     wire.InitialiseCredgenicsService,
		GRPCRegisterMethods: []any{vgCredgenicsPb.RegisterCredgenicsServer},
	},
	{
		WireInitializer:     wire.InitialiseFinfluxService,
		GRPCRegisterMethods: []any{vgFinfluxPb.RegisterFinfluxServer},
	},
	{
		WireInitializer:     wire.InitialiseIdfcService,
		GRPCRegisterMethods: []any{idfcPLPb.RegisterIdfcServer},
	},
	{
		WireInitializer:     wire.InitialiseMoneyviewService,
		GRPCRegisterMethods: []any{moneyviewVgPb.RegisterMoneyviewServer},
	},
	{
		WireInitializer:     wire.InitialiseSetuService,
		GRPCRegisterMethods: []any{setuVgPb.RegisterSetuServer},
	},
	{
		WireInitializer:     wire.InitialiseFiftyFinService,
		GRPCRegisterMethods: []any{fiftyFinPb.RegisterFiftyFinServer},
	},
	{
		WireInitializer:     wire.InitialisePreApprovedLoanService,
		GRPCRegisterMethods: []any{preapprovedloan.RegisterPreApprovedLoanServer},
	},
	{
		WireInitializer:     wire.InitialiseInhouseRiskService,
		GRPCRegisterMethods: []any{riskpb.RegisterRiskServer},
	},
	{
		WireInitializer:     wire.InitialiseEsignService,
		GRPCRegisterMethods: []any{esign.RegisterESignServer},
	},
	{
		WireInitializer:     wire.InitialiseCreditCardService,
		GRPCRegisterMethods: []any{creditcard.RegisterCreditCardServer},
	},
	{
		WireInitializer:     wire.InitialiseUSStockService,
		GRPCRegisterMethods: []any{usstockpb.RegisterStocksServer},
	},
	{
		WireInitializer:     wire.InitialiseProfileValidationService,
		GRPCRegisterMethods: []any{profilevalidationpb.RegisterProfileValidationServer},
	},
	{
		WireInitializer:     wire.InitializeAmlService,
		GRPCRegisterMethods: []any{aml.RegisterAmlServer},
	},
	{
		WireInitializer:     wire.InitializeInternationalfundtransferService,
		GRPCRegisterMethods: []any{internationalfundtransferpb.RegisterInternationalFundTransferServer},
	},
	{
		WireInitializer:     wire.InitializeRiskcovryService,
		GRPCRegisterMethods: []any{riskcovryvgpb.RegisterRiskcovryServer},
	},
	{
		WireInitializer:     wire.InitializeOnsurityService,
		GRPCRegisterMethods: []any{onsurity.RegisterOnSurityServer},
	},
	{
		WireInitializer:     wire.InitalizeUsStockCatalogService,
		GRPCRegisterMethods: []any{catalog.RegisterCatalogServer},
	},
	{
		WireInitializer:     wire.InitialiseVistaraService,
		GRPCRegisterMethods: []any{vistaraVgPb.RegisterVistaraServer},
	},
	{
		WireInitializer:     wire.InitializeLienService,
		GRPCRegisterMethods: []any{lienPb.RegisterLienServer},
	},
	{
		WireInitializer:     wire.InitialiseIncomeEstimatorService,
		GRPCRegisterMethods: []any{incomeestimator.RegisterIncomeEstimatorServer},
	},
	{
		WireInitializer:     wire.InitialiseEnachService,
		GRPCRegisterMethods: []any{vgEnachPb.RegisterEnachServer},
	},
	{
		WireInitializer:     wire.InitialiseLentra,
		GRPCRegisterMethods: []any{lentra.RegisterLentraServer},
	},
	{
		WireInitializer:     wire.InitializeBankCustomerService,
		GRPCRegisterMethods: []any{bankcustomervgpb.RegisterBankCustomerServer},
	},
	{
		WireInitializer:     wire.InitializeSyncWrapperConsumerService,
		GRPCRegisterMethods: []any{commonvgpb.RegisterSyncWrapperConsumerServer},
	},
	{
		WireInitializer:     wire.InitializeCardProvisioningService,
		GRPCRegisterMethods: []any{cardpb.RegisterCardProvisioningServer},
	},
	{
		WireInitializer:     wire.InitialiseCreditLineService,
		GRPCRegisterMethods: []any{creditline.RegisterCreditLineServer},
	},
	{
		WireInitializer:     wire.InitialiseBreService,
		GRPCRegisterMethods: []any{bre.RegisterBusinessRuleEngineServer},
	},
	{
		WireInitializer:     wire.InitializeFennelFeatureStoreService,
		GRPCRegisterMethods: []any{vgFnPb.RegisterFennelFeatureStoreServer},
	},
	{
		WireInitializer:     wire.InitializeThriweService,
		GRPCRegisterMethods: []any{thriweVgPb.RegisterThriweServer},
	},
	{
		WireInitializer:     wire.InitializeDreamfolksService,
		GRPCRegisterMethods: []any{dreamfolksVgPb.RegisterDreamfolksServer},
	},
	{
		WireInitializer:     wire.InitialiseDisputeService,
		GRPCRegisterMethods: []any{vgDiPb.RegisterDisputeServer},
	},
	{
		WireInitializer:     wire.InitialiseEmailService,
		GRPCRegisterMethods: []any{emailPb.RegisterEmailServer},
	},
	{
		WireInitializer:     wire.InitialiseFcmService,
		GRPCRegisterMethods: []any{fcmServicePb.RegisterFCMServer},
	},
	{
		WireInitializer:     wire.InitializeIdfcService,
		GRPCRegisterMethods: []any{vgIdfcPb.RegisterIdfcServer},
	},
	{
		WireInitializer:     wire.InitializeIdfcVkycService,
		GRPCRegisterMethods: []any{vgIdfcVkycPb.RegisterIdfcServer},
	},
	{
		WireInitializer:     wire.InitialiseLiquiloansService,
		GRPCRegisterMethods: []any{liquiloansPLPb.RegisterLiquiloansServer},
	},
	{
		WireInitializer:     wire.InitializeMFAnalyticsService,
		GRPCRegisterMethods: []any{mfAnalyticsPb.RegisterMFAnalyticsServer},
	},
	{
		WireInitializer:     wire.InitializeMerchantResolutionService,
		GRPCRegisterMethods: []any{merchantResolutionPb.RegisterMerchantResolutionServer},
	},
	{
		WireInitializer:     wire.InitialiseUpiService,
		GRPCRegisterMethods: []any{upiPb.RegisterUPIServer},
	},
	{
		WireInitializer:     wire.InitializeSlackBotService,
		GRPCRegisterMethods: []any{slackPb.RegisterSlackBotServer},
		InitCondition: func(conf *config.Config) bool {
			return cfg.IsDevelopmentEnv(conf.Environment) || cfg.IsStagingEnv(conf.Environment) || cfg.IsProdEnv(conf.Environment)
		},
	},
	{
		WireInitializer:     wire.InitialiseAbflService,
		GRPCRegisterMethods: []any{abflPLPb.RegisterAbflServer},
	},
	{
		WireInitializer:     wire.InitializePoshvineService,
		GRPCRegisterMethods: []any{poshvineVgPb.RegisterPoshvineServer},
	},
	{
		WireInitializer:     wire.InitializeITRService,
		GRPCRegisterMethods: []any{vgItrPb.RegisterITRServer},
	},
	{
		WireInitializer:     wire.InitializeEmployerNameCategoriserService,
		GRPCRegisterMethods: []any{employernamecategoriserPb.RegisterEmployerNameCategoriserServer},
	},
	{
		WireInitializer:     wire.InitialiseOCRService,
		GRPCRegisterMethods: []any{ocr.RegisterOCRServer},
	},
	{
		WireInitializer:     wire.InitialiseAclSftpService,
		GRPCRegisterMethods: []any{aclVgPb.RegisterAclSftpServer},
	},
	{
		WireInitializer:     wire.InitializePaymentGatewayService,
		GRPCRegisterMethods: []any{pgPb.RegisterPaymentGatewayServer},
	},
	{
		WireInitializer:     wire.InitializeVkycCallService,
		GRPCRegisterMethods: []any{vkyccallpb.RegisterVkycCallServer},
	},
	{
		WireInitializer:     wire.InitializeUqudoService,
		GRPCRegisterMethods: []any{uqudo.RegisterUqudoServer},
	},
	{
		WireInitializer:     wire.InitialiseDigitapService,
		GRPCRegisterMethods: []any{digitap.RegisterDigitapServiceServer},
	},
	{
		WireInitializer:     wire.InitialiseLendenService,
		GRPCRegisterMethods: []any{lenden.RegisterLendenServer},
	},
	{
		WireInitializer:     wire.InitializeMoEngageService,
		GRPCRegisterMethods: []any{moengage.RegisterMoEngageServer},
	},
	{
		WireInitializer:     wire.InitialiseDigilockerService,
		GRPCRegisterMethods: []any{digilockerPb.RegisterDigilockerServer},
	},
	{
		WireInitializer:     wire.InitializeCurrencyInsightsService,
		GRPCRegisterMethods: []any{currencyInsightsVgPb.RegisterServiceServer},
	},
	{
		WireInitializer:     wire.InitializeScienapticService,
		GRPCRegisterMethods: []any{vgScienapticPb.RegisterScienapticServer},
	},
	{
		WireInitializer:     wire.InitializeBillPaymentsService,
		GRPCRegisterMethods: []any{billPaymentsPb.RegisterSetuBillPaymentsServiceServer},
	},
	{
		WireInitializer:     wire.InitializeAaIgnosisService,
		GRPCRegisterMethods: []any{igVgPb.RegisterIgnosisAaAnalyticsServiceServer},
	},
	{
		WireInitializer:     wire.InitalizeStockCatalogService,
		GRPCRegisterMethods: []any{bridgewise.RegisterCatalogServer},
	},
	{
		WireInitializer:     wire.InitializeNpsService,
		GRPCRegisterMethods: []any{npspb.RegisterNPSServer},
	},
	{
		WireInitializer:     wire.InitializeFederalEscalationService,
		GRPCRegisterMethods: []any{federal.RegisterFederalEscalationServiceServer},
	},
	{
		WireInitializer:     wire.InitializeZendutyService,
		GRPCRegisterMethods: []any{zenduty.RegisterZendutyServer},
	},
	{
		WireInitializer:     wire.InitializeCreditCardV2Service,
		GRPCRegisterMethods: []any{vgCcV2Pb.RegisterCreditCardServer},
	},
	{
		WireInitializer:     wire.InitializeNuggetService,
		GRPCRegisterMethods: []any{nuggetpb.RegisterNuggetChatbotServiceServer},
	},
}
