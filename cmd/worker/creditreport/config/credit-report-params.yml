Application:
  WorkerOptions:
    Options:
      MaxConcurrentActivityExecutionSize: 100
      WorkerActivitiesPerSecond: 200.0
      MaxConcurrentLocalActivityExecutionSize: 100
      WorkerLocalActivitiesPerSecond: 200.0
      TaskQueueActivitiesPerSecond: 600.0
      MaxConcurrentActivityTaskPollers: 5
      MaxConcurrentWorkflowTaskExecutionSize: 300
      MaxConcurrentWorkflowTaskPollers: 5
  Flags:
    SkipDeprecatedCelestialActivitiesRegistration: false

Server:
  GrpcPort: 9000
  HttpPort: 9089

AWS:
  Region: "ap-south-1"

DefaultActivityParamsList:
  - ActivityName: "GetWorkflowProcessingParamsV2"
    ScheduleToCloseTimeout: "3m15s"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 minutes with max cap between retries at 1 minute
      # Retry interval - 5s, 10s, 20s, 40s, 1m, 1m
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "1m"
        BackoffCoefficient: 2.0
        MaxAttempts: 6
  - ActivityName: "InitiateWorkflowStageV2"
    ScheduleToCloseTimeout: "3m15s"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 minutes with max cap between retries at 1 minute
      # Retry interval - 5s, 10s, 20s, 40s, 1m, 1m
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "1m"
        BackoffCoefficient: 2.0
        MaxAttempts: 6
  - ActivityName: "UpdateWorkflowStage"
    ScheduleToCloseTimeout: "3m15s"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 minutes with max cap between retries at 1 minute
      # Retry interval - 5s, 10s, 20s, 40s, 1m, 1m
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "1m"
        BackoffCoefficient: 2.0
        MaxAttempts: 6
  - ActivityName: "PublishWorkflowUpdateEventV2"
    ScheduleToCloseTimeout: "5h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 4 hours with max cap between retries at 1 minute
      # Retry interval - 5s, 10s, 20s, 40s, 1m20s, 2m40s, 5m20s, 10m40s, 21m20s, 30m, 30m, 30m, 30m, 30m, 30m
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 15
  - ActivityName: "SendSignalToWorkflowsV2"
    ScheduleToCloseTimeout: "5h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 4 hours with max cap between retries at 1 minute
      # Retry interval - 5s, 10s, 20s, 40s, 1m20s, 2m40s, 5m20s, 10m40s, 21m20s, 30m, 30m, 30m, 30m, 30m, 30m
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 15
  - ActivityName: "GetWorkflowProcessingParamsByWorkflowTypeV2"
    ScheduleToCloseTimeout: "5h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 4 hours with max cap between retries at 1 minute
      # Retry interval - 5s, 10s, 20s, 40s, 1m20s, 2m40s, 5m20s, 10m40s, 21m20s, 30m, 30m, 30m, 30m, 30m, 30m
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 15

WorkflowParamsList:
  - WorkflowName: "DownloadCreditReport"
    ActivityParamsList:
      - ActivityName: "CibilFulfillOffer"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 15
      - ActivityName: "InitiateCustomerAuthentication"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 10
      - ActivityName: "CheckCustomerAuthenticationStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "30s"
        # Exponential retry strategy that runs for 4 hours with max cap between retries at 1 minute
        # Retry interval - 5s, 10s, 20s, 40s, 1m20s, 2m40s, 5m20s, 10m40s, 21m20s, 30m, 30m, 30m, 30m, 30m, 30m
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
      - ActivityName: "SendProductUrl"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 10
      - ActivityName: "PublishFlattenCibilReportEvent"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 10
      - ActivityName: "InitiateOtpVerification"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 40
      - ActivityName: "CheckAuthFlowProcessStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "30s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "15s"
            BackoffCoefficient: 1.05 # backoff will reach 15s after 20 retries
            MaxAttempts: 50
      - ActivityName: "UpdateCreditReportStatusBasedOnAuthFlowStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 20
      - ActivityName: "InitiateRecordConsentStep"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 30
      - ActivityName: "CheckRecordConsentStepProgress"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "15s"
            BackoffCoefficient: 1.05 # backoff will reach 15s after 20 retries
            MaxAttempts: 50
      - ActivityName: "FetchCreditReportFromVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "20s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5
      - ActivityName: "ExtendCreditReportSubscription"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 40
      - ActivityName: "UpdateCreditReportDownloadProcessStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 40
      - ActivityName: "UpdateCreditReportStatusBasedOnConsentStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 40
      - ActivityName: "PublishCreditReportDownloadEvent"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 40
      - ActivityName: "GetUserPanDobStatus"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "CheckPanDobPresent"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "GetNSDLPanDobDeeplink"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "20s"
            BackoffCoefficient: 2.0
            MaxAttempts: 5



Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

