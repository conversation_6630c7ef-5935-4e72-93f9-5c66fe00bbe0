# This file stores the path patterns using which run_lci_validation github action determines the paths for which
# a given test needs to be triggered.
# The convention to add a path is to prefix the path from repo root with the repo name.
#  For ex, say we want to # trigger a test for `pkg/aws/**` contained in be-common repo. Then add the path as `be-common/pkg/aws/**`.
# The need for prefixing the path with the repo is to remove any conflicts between same package names present in different repo.
# These paths are passed as arguments to the script `be-common/tools/actions/get_required_check/required_check.go`.
# The script matches these paths against the input set of paths.
# Adding a `!` at the start of a path will exclude it from the matching.
service_file_patterns:
  common_paths:
    - 'be-common/pkg/aws/**'
    - 'be-common/pkg/cache/**'
    - 'be-common/pkg/cfg/**'
    - '!be-common/pkg/cfg/config/server-definition-**.yml'
    - '!be-common/pkg/cfg/explorer/**'
    - '!be-common/pkg/cfg/config/endpoints-{demo,deploy,development,prod,staging,uat,qa,teamspace-params}.yml'
    - 'be-common/pkg/idgen/**'
    - '!be-common/pkg/idgen/prefixes.go'
    - 'be-common/pkg/cmd/**'
    - 'be-common/pkg/colors/**'
    - 'be-common/pkg/crypto/**'
    - 'be-common/pkg/data_structs/**'
    - 'be-common/pkg/datetime/**'
    - 'be-common/pkg/discovery/**'
    - 'be-common/pkg/durationapprox/**'
    - 'be-common/pkg/epificontext/**'
    - 'be-common/pkg/epifierrors/**'
    - 'be-common/pkg/epifigrpc/**'
    - 'be-common/pkg/epifinet/**'
    - 'be-common/pkg/epifiserver/**'
    - 'be-common/pkg/epifitemporal/**'
    - '!be-common/pkg/epifitemporal/namespace/**'
    - '!be-common/pkg/epifitemporal/router/route_map.go'
    - '!be-common/pkg/epifitemporal/types.go'
    - 'be-common/pkg/errgroup/**'
    - 'be-common/pkg/events/**'
    - 'be-common/pkg/faas/**'
    - 'be-common/pkg/fieldmask/**'
    - 'be-common/pkg/file/**'
    - 'be-common/pkg/go_utils/**'
    - 'be-common/pkg/hash/**'
    - 'be-common/pkg/healthcheck/**'
    - 'be-common/pkg/hystrix/**'
    - 'be-common/pkg/integer/**'
    - 'be-common/pkg/localstack/**'
    - 'be-common/pkg/location/**'
    - 'be-common/pkg/lock/**'
    - 'be-common/pkg/mask/**'
    - 'be-common/pkg/mock/**'
    - 'be-common/pkg/monitoring/**'
    - 'be-common/pkg/names/**'
    - 'be-common/pkg/nulltypes/**'
    - 'be-common/pkg/opentelemetry/**'
    - 'be-common/pkg/pagination/**'
    - 'be-common/pkg/path/**'
    - 'be-common/pkg/profiling/**'
    - 'be-common/pkg/queue/**'
    - 'be-common/pkg/ratelimiter/**'
    - 'be-common/pkg/redactor/**'
    - 'be-common/pkg/redisprom/**'
    - 'be-common/pkg/retry/**'
    - 'be-common/pkg/secrets/**'
    - 'be-common/pkg/storage/**'
    - 'be-common/pkg/stream/**'
    - 'be-common/pkg/syncmap/**'
    - 'be-common/pkg/syncwrapper/**'
    - 'be-common/pkg/test/**'
    - 'be-common/pkg/timeunit/**'
    - 'be-common/pkg/tracing/**'
    - 'gamma/pkg/airflow/**'
    - 'gamma/pkg/banking/**'
    - 'gamma/pkg/cachedao/**'
    - 'gamma/pkg/dao/**'
    - 'gamma/pkg/deeplink/**'
    - 'gamma/pkg/deeplinkv2/**'
    - 'gamma/pkg/deeplinkv3/**'
    - 'gamma/pkg/developer/**'
    - 'gamma/pkg/device/**'
    - 'gamma/pkg/email/**'
    - 'gamma/pkg/epifigrpc/**'
    - 'gamma/pkg/epifiproxy/**'
    - 'gamma/pkg/epifitemporal/**'
    - 'gamma/pkg/feature/**'
    - 'gamma/pkg/fonts/**'
    - 'gamma/pkg/http/**'
    - 'gamma/pkg/image/**'
    - 'gamma/pkg/incomeslab/**'
    - 'gamma/pkg/kafka/**'
    - 'gamma/pkg/language/**'
    - 'gamma/pkg/legality/**'
    - 'gamma/pkg/map/**'
    - 'gamma/pkg/media/**'
    - 'gamma/pkg/migrations/**'
    - 'gamma/pkg/money/**'
    - 'be-common/pkg/monorail/**'
    - 'gamma/pkg/obfuscator/**'
    - 'gamma/pkg/opensearch/**'
    - 'gamma/pkg/orgs/**'
    - 'gamma/pkg/pinot/**'
    - 'gamma/pkg/qr/**'
    - 'gamma/pkg/rule_evaluator/**'
    - 'gamma/pkg/sftp/**'
    - 'gamma/pkg/string/**'
    - 'gamma/pkg/unsafe/**'
    - 'gamma/pkg/urlshortener/**'
    - 'gamma/pkg/vendors/**'
    - 'gamma/pkg/vendorstore/**'
    - 'gamma/pkg/x/**'
    - '!gamma/pkg/epifitemporal/namespace/**'

  precommit:
    - '**.go'
    - 'gamma/.github/workflows/**.yml'

  build_apps:
    - '**.go'
    - 'gamma/go.mod'
    - 'gamma/.github/workflows/build_apps.yml'

  product:
    - 'gamma/api/product/**'
    - 'gamma/product/**'
    - 'gamma/.github/workflows/product.yml'

  fe_insights:
    - 'gamma/frontend/insights/**'
    - 'gamma/pkg/frontend/**'
    - 'gamma/.github/workflows/fe_insights.yml' # Self reference

  fe_analyser:
    - 'gamma/frontend/analyser/**'
    - 'gamma/pkg/frontend/**'
    - 'gamma/.github/workflows/fe_analyser.yml' # Self reference

  fe_deposit:
    - 'gamma/frontend/deposit/**'
    - 'gamma/pkg/frontend/**'
    - 'gamma/.github/workflows/fe_deposit.yml' # Self reference

  fe_connected_account:
    - 'gamma/frontend/connected_account/**'
    - 'gamma/pkg/frontend/**'
    - 'gamma/.github/workflows/fe_connected_account.yml' # Self reference

  fe_categorizer:
    - 'gamma/frontend/categorizer/**'
    - 'gamma/pkg/frontend/**'
    - 'gamma/.github/workflows/fe_categorizer.yml' # Self reference

  fe_investment:
    - 'gamma/frontend/investment/**'
    - 'gamma/pkg/frontend/**'
    - 'gamma/.github/workflows/fe_investment.yml' # Self reference


  omegle:
    - 'gamma/omegle/**'
    - 'gamma/cmd/servers/test/onboarding-test.yml'
    - 'gamma/cmd/servers/config/onboarding-params.yml'
    - 'gamma/db/kyc_non_resident/fixture.sql'
    - 'gamma/db/kyc_non_resident/latest.sql'
    - 'gamma/.github/workflows/omegle.yml'

  vkyccall:
    - 'gamma/vkyccall/**'
    - 'gamma/cmd/servers/test/config/auror-test.yml'
    - 'gamma/cmd/servers/config/auror-params.yml'
    - 'gamma/cmd/servers/test/config/nebula-test.yml'
    - 'gamma/cmd/servers/config/nebula-params.yml'
    - 'gamma/.github/workflows/vkyccall.yml'

  celestial:
    - 'gamma/celestial/**'
    - 'gamma/cmd/worker/**'
    - 'gamma/db/celestial_pgdb/fixture.sql'
    - 'gamma/db/celestial_pgdb/latest.sql'
    - 'gamma/db/celestial_crdb/latest.sql'
    - 'gamma/db/celestial_crdb/fixture.sql'
    - 'gamma/.github/workflows/celestial.yml'
    - '!gamma/celestial/config/values/celestial-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  accounts:
    - 'gamma/accounts/**'
    - 'gamma/cmd/servers/test/actor/config/actor-test.yml'
    - 'gamma/cmd/servers/config/actor-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/actor.yml'
    - '!gamma/accounts/config/values/accounts-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  accrual:
    - 'gamma/accrual/**'
    - 'gamma/cmd/rewards/config/accrual-test.yml'
    - 'gamma/cmd/rewards/config/accrual-params.yml'
    - 'gamma/db/accrual/fixture.sql'
    - 'gamma/db/accrual/latest.sql'
    - 'gamma/.github/workflows/accrual.yml'
    - '!gamma/accrual/config/values/accrual-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  actor:
    - 'gamma/actor/**'
    - 'gamma/cmd/servers/test/actor/config/actor-test.yml'
    - 'gamma/cmd/servers/config/actor-params.yml'
    - 'gamma/db/actor/fixture.sql'
    - 'gamma/db/actor/latest.sql'
    - 'gamma/.github/workflows/actor.yml'
    - '!gamma/actor/config/values/actor-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  alfred:
    - 'gamma/alfred/**'
    - 'gamma/cmd/user/config/alfred-test.yml'
    - 'gamma/cmd/user/config/alfred-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/alfred.yml'
    - 'gamma/pkg/chequebook'
    - 'be-common/pkg/constants'
    - '!gamma/alfred/config/values/alfred-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  auth:
    - 'gamma/auth/**'
    - 'gamma/cmd/auth/config/auth-test.yml'
    - 'gamma/cmd/auth/config/auth-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/auth.yml'
    - 'gamma/pkg/auth'
    - 'be-common/pkg/counter'
    - 'gamma/pkg/changefeed'
    - 'gamma/pkg/decisionmodel'
    - '!gamma/auth/config/values/auth-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  bankcust:
    - 'gamma/bankcust/**'
    - 'gamma/db/bank_customers/**'
    - 'gamma/cmd/user/config/bankcust-test.yml'
    - 'gamma/cmd/user/config/bankcust-params.yml'
    - 'gamma/.github/workflows/bankcust.yml'
    - 'gamma/api/bankcust/**'
    - 'gamma/pkg/chequebook'
    - '!gamma/bankcust/config/values/bankcust-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  card:
    - 'gamma/card/**'
    - 'gamma/cmd/card/config/card-test.yml'
    - 'gamma/cmd/card/config/card-params.yml'
    - 'gamma/.github/workflows/card.yml'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/pkg/downtime'
    - 'be-common/pkg/counter'
    - '!gamma/card/config/values/card-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  casbin:
    - 'gamma/casbin/**'
    - 'gamma/cmd/casbin/config/values/casbin-params.yml'
    - 'gamma/cmd/casbin/config/values/casbin-test.yml'
    - 'gamma/.github/workflows/casbin.yml'
    - '!gamma/casbin/config/values/casbin-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  casper_cms:
    - 'gamma/casper/**'
    - 'gamma/cms/**'
    - 'gamma/cmd/rewards/config/casper-test.yml'
    - 'gamma/cmd/rewards/config/casper-params.yml'
    - 'gamma/cmd/rewards/config/cms-test.yml'
    - 'gamma/cmd/rewards/config/cms-params.yml'
    - 'gamma/db/casper/fixture.sql'
    - 'gamma/db/casper/latest.sql'
    - 'gamma/db/cms/fixture.sql'
    - 'gamma/db/cms/latest.sql'
    - 'gamma/.github/workflows/casper.yml'
    - 'gamma/.github/workflows/cms.yml'
    - '!gamma/casper/config/values/casper-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    - '!gamma/cms/config/values/cms-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  comms_dynamicelements:
    - 'gamma/comms/**'
    - 'gamma/dynamicelements/**'
    - 'gamma/cmd/comms/config/comms-test.yml'
    - 'gamma/cmd/comms/config/comms-params.yml'
    - 'gamma/cmd/comms/config/dynamicelements-test.yml'
    - 'gamma/cmd/comms/config/dynamicelements-params.yml'
    - 'gamma/cmd/comms/config/whatsappbot-test.yml'
    - 'gamma/cmd/comms/config/whatsappbot-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/comms/fixture.sql'
    - 'gamma/db/comms/latest.sql'
    - 'gamma/.github/workflows/comms.yml'
    - 'gamma/.github/workflows/dynamic_elements.yml'
    - 'gamma/pkg/downtime'
    - '!gamma/comms/config/values/comms-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    - '!gamma/dynamicelements/config/values/dynamicelements-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  connectedaccount:
    - 'gamma/connectedaccount/**'
    - 'gamma/cmd/connectedaccount/config/connectedaccount-test.yml'
    - 'gamma/cmd/connectedaccount/config/connectedaccount-params.yml'
    - 'gamma/db/epifi_wealth/fixture.sql'
    - 'gamma/db/epifi_wealth/latest.sql'
    - 'gamma/db/connected_account/fixture.sql'
    - 'gamma/db/connected_account/latest.sql'
    - 'gamma/.github/workflows/connectedaccount.yml'
    - 'gamma/pkg/connectedaccount'
    - 'gamma/pkg/decisionmodel'
    - '!gamma/connectedaccount/config/values/connectedaccount-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  consent:
    - 'gamma/cmd/user/config/user-test.yml'
    - 'gamma/cmd/user/config/user-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/consent.yml'
    - 'gamma/consent/**'
    - '!gamma/consent/config/values/consent-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  cx:
    - 'gamma/cx/**'
    - 'gamma/cmd/cx/config/values/cx-params.yml'
    - 'gamma/cmd/cx/config/values/cx-test.yml'
    - 'gamma/db/sherlock/fixture.sql'
    - 'gamma/db/sherlock/latest.sql'
    - 'gamma/pkg/accessrevoke/**'
    - 'gamma/.github/workflows/cx.yml'
    - 'gamma/pkg/auth'
    - 'gamma/pkg/connectedaccount'
    - 'gamma/pkg/employment'
    - 'gamma/pkg/firefly'
    - 'be-common/pkg/constants'
    - 'be-common/pkg/counter'
    - '!gamma/cx/config/values/cx-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  docs:
    - 'gamma/docs/**'
    - 'gamma/cmd/servers/test/docs/config/docs-test.yml'
    - 'gamma/cmd/servers/config/docs-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/docs.yml'
    - '!gamma/docs/config/values/docs-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  employment:
    - 'gamma/cmd/user/config/user-test.yml'
    - 'gamma/cmd/user/config/user-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/employment.yml'
    - 'gamma/employment/**'
    - 'gamma/pkg/employment'
    - '!gamma/employment/config/values/employment-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  firefly:
    - 'gamma/firefly/**'
    - 'gamma/cmd/lending/config/firefly-test.yml'
    - 'gamma/cmd/lending/config/firefly-params.yml'
    - 'gamma/cmd/worker/firefly/**'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/credit_card/fixture.sql'
    - 'gamma/db/credit_card/latest.sql'
    - 'gamma/.github/workflows/firefly.yml'
    - 'gamma/pkg/creditreport'
    - 'gamma/pkg/firefly'
    - '!gamma/firefly/config/values/firefly-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  frontend:
    - 'gamma/frontend/**'
    - 'gamma/cmd/frontend/config/*-test.yml'
    - 'gamma/cmd/frontend/config/*-params.yml'
    - 'gamma/pkg/frontend/**'
    - 'gamma/pkg/acquisition/**'
    - 'gamma/.github/workflows/frontend.yml'
    - 'gamma/pkg/auth'
    - 'gamma/pkg/chequebook'
    - 'gamma/pkg/connectedaccount'
    - 'gamma/pkg/creditscore'
    - 'gamma/pkg/deposit'
    - 'gamma/pkg/device_integrity'
    - 'gamma/pkg/dmf'
    - 'gamma/pkg/downtime'
    - 'gamma/pkg/employment'
    - 'be-common/pkg/constants'
    - 'gamma/pkg/firefly'
    - '!gamma/frontend/config/values/frontend-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    - '!gamma/frontend/insights/**'
    - '!gamma/frontend/analyser/**'
    - '!gamma/frontend/deposit/**'
    - '!gamma/frontend/connected_account/**'
    - '!gamma/frontend/categorizer/**'
    - '!gamma/frontend/investment/**'

  health_engine:
    - 'gamma/health_engine/**'
    - 'gamma/cmd/servers/test/docs/config/docs-test.yml'
    - 'gamma/cmd/servers/config/docs-params.yml'
    - 'gamma/.github/workflows/health_engine.yml'
    - '!gamma/health_engine/config/values/health_engine-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  inapphelp:
    - 'gamma/inapphelp/**'
    - 'gamma/cmd/inapphelp/config/values/inapphelp-params.yml'
    - 'gamma/cmd/inapphelp/config/values/inapphelp-test.yml'
    - 'gamma/db/inapphelp/fixture.sql'
    - 'gamma/db/inapphelp/latest.sql'
    - 'gamma/.github/workflows/inapphelp.yml'
    - '!gamma/inapphelp/config/values/inapphelp-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  inappreferral:
    - 'gamma/inappreferral/**'
    - 'gamma/cmd/user/config/inappreferral-test.yml'
    - 'gamma/cmd/user/config/inappreferral-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/inappreferral.yml'
    - '!gamma/inappreferral/config/values/inappreferral-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  investment:
    - 'gamma/investment/**'
    - 'gamma/db/epifi_wealth/fixture.sql'
    - 'gamma/db/epifi_wealth/latest.sql'
    - 'gamma/.github/workflows/investment.yml'
    - 'gamma/pkg/zinc/**'
    - '!gamma/investment/config/values/investment-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  kyc:
    - 'gamma/kyc/**'
    - 'gamma/cmd/kyc/config/kyc-test.yml'
    - 'gamma/cmd/kyc/config/kyc-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/kyc.yml'
    - 'gamma/pkg/changefeed'
    - 'be-common/pkg/constants'
    - 'be-common/pkg/counter'
    - '!gamma/kyc/config/values/kyc-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  merchant:
    - 'gamma/merchant/**'
    - 'gamma/cmd/servers/test/actor/config/actor-test.yml'
    - 'gamma/cmd/servers/config/actor-params.yml'
    - 'gamma/db/merchant/fixture.sql'
    - 'gamma/db/merchant/latest.sql'
    - 'gamma/.github/workflows/merchant.yml'
    - '!gamma/merchant/config/values/merchant-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  order:
    - 'gamma/order/**'
    - 'gamma/cmd/servers/test/order/config/order-test.yml'
    - 'gamma/cmd/servers/config/order-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/pay/fixture.sql'
    - 'gamma/db/pay/latest.sql'
    - 'gamma/.github/workflows/order.yml'
    - '!gamma/order/config/values/order-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  quest:
    - 'gamma/quest/**'
    - 'gamma/cmd/servers/test/growth-infra/config/growth-infra-test.yml'
    - 'gamma/cmd/servers/config/growth-infra-params.yml'
    - 'gamma/db/quest/fixture.sql'
    - 'gamma/db/quest/latest.sql'
    - 'gamma/.github/workflows/quest.yml'
    - '!gamma/quest/config/values/quest-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  nudge:
    - 'gamma/nudge/**'
    - 'gamma/cmd/user/config/nudge-test.yml'
    - 'gamma/cmd/user/config/nudge-params.yml'
    - 'gamma/db/nudge/fixture.sql'
    - 'gamma/db/nudge/latest.sql'
    - 'gamma/.github/workflows/nudge.yml'
    - '!gamma/nudge/config/values/nudge-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  p2pinvestment:
    - 'gamma/p2pinvestment/**'
    - 'gamma/cmd/investment/config/p2pinvestment-test.yml'
    - 'gamma/cmd/investment/config/p2pinvestment-params.yml'
    - 'gamma/db/p2pinvestment_liquiloans/fixture.sql'
    - 'gamma/db/p2pinvestment_liquiloans/latest.sql'
    - 'gamma/db/test_schema_backup/p2pinvestment_liquiloans_test/**'
    - 'gamma/.github/workflows/p2pinvestment.yml'
    - 'gamma/pkg/counter'
    - '!gamma/p2pinvestment/config/values/p2pinvestment-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  pan:
    - 'gamma/pan/**'
    - 'gamma/cmd/pan/config/pan-test.yml'
    - 'gamma/cmd/pan/config/pan-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/pan.yml'
    - '!gamma/pan/config/values/pan-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  pay:
    - 'gamma/pay/**'
    - 'gamma/cmd/servers/test/order/config/order-test.yml'
    - 'gamma/cmd/servers/config/order-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/pay/fixture.sql'
    - 'gamma/db/pay/latest.sql'
    - 'gamma/.github/workflows/pay.yml'
    - '!gamma/pay/config/server/values/pay-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  paymentinstrument:
    - 'gamma/paymentinstrument/**'
    - 'gamma/cmd/servers/test/actor/config/actor-test.yml'
    - 'gamma/cmd/servers/config/actor-params.yml'
    - 'gamma/db/payment_instrument/fixture.sql'
    - 'gamma/db/payment_instrument/latest.sql'
    - 'gamma/.github/workflows/payment_instrument.yml'
    - '!gamma/paymentinstrument/config/values/paymentinstrument-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  pkg:
    - 'gamma/pkg/**'
    - 'gamma/.github/workflows/pkg.yml'
    - 'go.mod'

  preapprovedloan:
    - 'gamma/preapprovedloan/**'
    - 'gamma/cmd/lending/config/preapprovedloan-test.yml'
    - 'gamma/cmd/lending/config/preapprovedloan-params.yml'
    - 'gamma/cmd/worker/preapprovedloan/**'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/loans/fixtures/**'
    - 'gamma/db/loans/latest.sql'
    - 'gamma/db/loans_federal/fixture.sql'
    - 'gamma/db/loans_federal/latest.sql'
    - 'gamma/db/loans_idfc/fixture.sql'
    - 'gamma/db/loans_idfc/latest.sql'
    - 'gamma/db/loans_liquiloans/fixture.sql'
    - 'gamma/db/loans_liquiloans/latest.sql'
    - 'gamma/db/loans_fiftyfin/fixture.sql'
    - 'gamma/db/loans_fiftyfin/latest.sql'
    - 'be-common/pkg/constants'
    - 'be-common/pkg/counter'
    - 'gamma/.github/workflows/preapprovedloan.yml'
    - '!gamma/preapprovedloan/config/values/preapprovedloan-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  recurringpayment:
    - 'gamma/recurringpayment/**'
    - 'gamma/cmd/order/config/recurringpayment-test.yml'
    - 'gamma/cmd/order/config/recurringpayment-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/pay/fixture.sql'
    - 'gamma/db/pay/latest.sql'
    - 'gamma/.github/workflows/recurring_payment.yml'
    - '!gamma/recurringpayment/config/values/recurringpayment-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  rewards:
    - 'gamma/rewards/**'
    - 'gamma/cmd/rewards/config/rewards-test.yml'
    - 'gamma/cmd/rewards/config/rewards-params.yml'
    - 'gamma/cmd/rewards/config/customdelayqueue-test.yml'
    - 'gamma/cmd/rewards/config/customdelayqueue-params.yml'
    - 'gamma/db/rewards/fixture.sql'
    - 'gamma/db/rewards/latest.sql'
    - 'gamma/.github/workflows/rewards.yml'
    - '!gamma/rewards/config/values/rewards-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  risk:
    - 'gamma/risk/**'
    - 'gamma/cmd/worker/risk'
    - 'gamma/.github/workflows/risk.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/db/frm/fixture.sql'
    - 'gamma/db/frm/latest.sql'
    - 'be-common/pkg/constants'
    - 'be-common/pkg/counter'
    - 'gamma/db/frm_pgdb/fixtures.sql'
    - 'gamma/db/frm_pgdb/latest.sql'
    - 'gamma/pkg/accessrevoke/**'
    - '!gamma/risk/config/values/risk-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  salaryprogram:
    - 'gamma/salaryprogram/**'
    - 'gamma/db/salaryprogram/latest.sql'
    - 'gamma/db/salaryprogram/fixture.sql'
    - 'gamma/cmd/savings/config/salaryprogram-test.yml'
    - 'gamma/cmd/savings/config/salaryprogram-params.yml'
    - 'gamma/.github/workflows/salaryprogram.yml'
    - '!gamma/salaryprogram/config/values/salaryprogram-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  savings:
    - 'gamma/savings/**'
    - 'gamma/cmd/savings/config/savings-test.yml'
    - 'gamma/cmd/savings/config/savings-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/savings.yml'
    - 'gamma/pkg/changefeed'
    - '!gamma/savings/config/values/savings-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  screener:
    - 'gamma/screener/**'
    - 'gamma/cmd/user/config/screener-test.yml'
    - 'gamma/cmd/user/config/screener-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/screener.yml'
    - 'gamma/pkg/creditreport'
    - 'gamma/pkg/decisionmodel'
    - '!gamma/screener/config/values/screener-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  securities:
     - 'gamma/securities/**'
     - 'gamma/cmd/securities/config/securities-test.yml'
     - 'gamma/cmd/securities/config/securities-params.yml'
     - 'gamma/db/stocks/fixture.sql'
     - 'gamma/db/stocks/latest.sql'
     - 'gamma/.github/workflows/securities.yml'
     - '!gamma/securities/config/values/securities-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  segment:
    - 'gamma/segment/**'
    - 'gamma/cmd/user/config/segment-test.yml'
    - 'gamma/cmd/user/config/segment-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/segment.yml'
    - '!gamma/segment/config/values/segment-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  tiering:
    - 'gamma/actor/**'
    - 'gamma/tiering/**'
    - 'gamma/cmd/actor/config/tiering-test.yml'
    - 'gamma/cmd/actor/config/tiering-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/tiering.yml'
    - '!gamma/tiering/config/values/tiering-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  timeline:
    - 'gamma/timeline/**'
    - 'gamma/cmd/servers/test/actor/config/actor-test.yml'
    - 'gamma/cmd/servers/config/actor-params.yml'
    - 'gamma/db/timeline/fixture.sql'
    - 'gamma/db/timeline/latest.sql'
    - 'gamma/.github/workflows/timeline.yml'
    - '!gamma/timeline/config/values/timeline-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  upi:
    - 'gamma/upi/**'
    - 'gamma/cmd/servers/test/order/config/order-test.yml'
    - 'gamma/cmd/servers/config/order-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/upi.yml'
    - '!gamma/upi/config/values/upi-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  user:
    - 'gamma/user/**'
    - 'gamma/cmd/user/config/user-test.yml'
    - 'gamma/cmd/user/config/user-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/user.yml'
    - 'gamma/.github/workflows/user_dao.yml'
    - 'gamma/pkg/auth'
    - 'gamma/pkg/changefeed'
    - 'gamma/pkg/decisionmodel'
    - 'gamma/pkg/employment'
    - 'be-common/pkg/constants'
    - 'be-common/pkg/counter'
    - 'gamma/pkg/firefly'
    - '!gamma/user/config/values/user-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  userintel:
    - 'gamma/userintel/**'
    - 'gamma/cmd/user/config/userintel-test.yml'
    - 'gamma/cmd/user/config/userintel-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/userintel.yml'
    - 'gamma/pkg/decisionmodel'
    - 'gamma/pkg/employment'
    - '!gamma/userintel/config/values/userintel-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  usstocks:
    - 'gamma/usstocks/**'
    - 'gamma/cmd/investment/config/usstocks-test.yml'
    - 'gamma/cmd/investment/config/usstocks-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/usstocks.yml'
    - 'gamma/db/usstocks_alpaca/fixture.sql'
    - 'gamma/db/usstocks_alpaca/latest.sql'
    - 'gamma/pkg/zinc/**'
    - 'be-common/pkg/counter'
    - '!gamma/usstocks/config/values/usstocks-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  vendorgateway:
    - 'gamma/vendorgateway/**'
    - 'gamma/cmd/vendorgateway/config/vendorgateway-test.yml'
    - 'gamma/cmd/vendorgateway/config/vendorgateway-params.yml'
    - 'gamma/.github/workflows/vendorgateway.yml'
    - 'gamma/pkg/connectedaccount'
    - 'gamma/pkg/creditreport'
    - 'gamma/pkg/deposit'
    - 'gamma/pkg/firefly'
    - '!gamma/vendorgateway/config/values/vendorgateway-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  vendornotification_vnotificationgw:
    - 'gamma/vendornotification/**'
    - 'gamma/vnotificationgw/**'
    - 'gamma/cmd/vendornotification/config/vendornotification-test.yml'
    - 'gamma/cmd/vendornotification/config/vendornotification-params.yml'
    - 'gamma/cmd/vnotificationgw/config/vnotificationgw-test.yml'
    - 'gamma/cmd/vnotificationgw/config/vnotificationgw-params.yml'
    - 'gamma/.github/workflows/vendornotification.yml'
    - 'gamma/.github/workflows/vnotificationgw.yml'
    - 'gamma/pkg/connectedaccount'
    - 'gamma/pkg/creditreport'
    - 'gamma/pkg/cx'
    - 'gamma/pkg/deposit'
    - 'gamma/pkg/dmf'
    - '!gamma/vendornotification/config/values/vendornotification-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    - '!gamma/vnotificationgw/config/values/vnotificationgw-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  wealthonboarding:
    - 'gamma/wealthonboarding/**'
    - 'gamma/cmd/connectedaccount/config/wealthonboarding-test.yml'
    - 'gamma/cmd/connectedaccount/config/wealthonboarding-params.yml'
    - 'gamma/db/epifi_wealth/fixture.sql'
    - 'gamma/db/epifi_wealth/latest.sql'
    - 'gamma/.github/workflows/wealthonboarding.yml'
    - 'be-common/pkg/counter'
    - '!gamma/wealthonboarding/config/values/wealthonboarding-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  webfe:
    - 'gamma/webfe/**'
    - 'gamma/.github/workflows/webfe.yml'
    - 'gamma/pkg/auth'
    - 'be-common/pkg/constants'
    - '!gamma/webfe/config/values/webfe-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  liveness:
    - 'gamma/liveness/**'
    - 'gamma/cmd/auth/config/auth-test.yml'
    - 'gamma/cmd/auth/config/auth-params.yml'
    - 'gamma/db/epifi/fixture.sql'
    - 'gamma/db/epifi/latest.sql'
    - 'gamma/.github/workflows/liveness.yml'
    - '!gamma/liveness/config/values/liveness-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

  leads:
    - 'gamma/leads/**'
    - 'gamma/db/loans/fixtures/**'
    - 'gamma/db/loans/latest.sql'
    - 'gamma/.github/workflows/leads.yml'
    - '!gamma/leads/config/values/leads-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'

