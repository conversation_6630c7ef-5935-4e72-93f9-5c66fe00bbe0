name: "Run LCI Validation"
description: "Action to run LCI validation for all services. This assumes that the
github.com/epifi/gamma/tools/actions/lci directory has already been checked out and the git note has been fetched."
inputs:
  workflow_token:
    description: "workflow token generated by peter-murray/workflow-application-token-action"
    required: true

runs:
  using: composite
  steps:
    - run: |
        echo "Running LCI validation"
      shell: bash
    - name: Get changed files using GitHub API
      shell: bash
      id: get_changed_files
      run: |
        PR_NUMBER=${{ github.event.pull_request.number }}
        REPO_NAME=${{ github.repository }}

        # Fetch modified files in the PR
        echo "Fetching modified files for PR $PR_NUMBER in repo $REPO_NAME"
        CHANGED_FILES=$(gh api repos/$REPO_NAME/pulls/$PR_NUMBER/files --paginate --jq '.[].filename')
        TRIMMED_CHANGED_FILES=$(echo "$CHANGED_FILES" | tr '\n' ' ')

        # Add repo name prefix to each changed file
        PREFIXED_CHANGED_FILES=$(echo "$TRIMMED_CHANGED_FILES" | sed "s|[^ ]*|gamma/&|g")
        echo "changed_files=$PREFIXED_CHANGED_FILES" >> $GITHUB_OUTPUT

        # Check if any go.* files are changed
        if echo "$TRIMMED_CHANGED_FILES" | grep -q "go\."; then
          echo "Go-related files changed: go.mod or go.sum detected"
          echo "go_mod_changed=true" >> $GITHUB_OUTPUT
        else
          echo "No go-related files changed."
          echo "go_mod_changed=false" >> $GITHUB_OUTPUT
        fi
      env:
        GITHUB_TOKEN: ${{ inputs.workflow_token }}

    # Step 3: Check if `be-common` version is upgraded
    - name: Check if  `be-common` version is upgraded
      if: ${{ steps.get_changed_files.outputs.go_mod_changed == 'true' }}
      shell: bash
      id: version_check
      run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          REPO_NAME=${{ github.repository }}

          echo "go.mod is modified, checking patch details..."
          PATCH=$(gh api repos/$REPO_NAME/pulls/$PR_NUMBER/files --paginate --jq '.[] | select(.filename == "go.mod").patch')
          BE_COMMON_CHANGED=false
          OTHER_DEPS_CHANGED=false
          OLD_COMMIT=""
          NEW_COMMIT=""

          # Loop through the patch to find be-common version upgrade and extract commit hashes
          while IFS= read -r line; do
            if [[ "$line" == -* ]] || [[ "$line" == +* ]]; then
              if echo "$line" | grep -q "be-common"; then
                BE_COMMON_CHANGED=true  # be-common version changed
                # Extract old and new commit hashes for be-common version change
                if [[ "$line" == -* ]]; then
                  OLD_COMMIT=$(echo "$line" | grep -oE '[a-f0-9]+$')
                  echo "Old commit hash for be-common: $OLD_COMMIT"
                elif [[ "$line" == +* ]]; then
                  NEW_COMMIT=$(echo "$line" | grep -oE '[a-f0-9]+$')
                  echo "New commit hash for be-common: $NEW_COMMIT"
                fi
              fi
            fi
          done <<< "$PATCH"

          if [[ "$BE_COMMON_CHANGED" == true  ]]; then
               if [[ -n "$OLD_COMMIT" && -n "$NEW_COMMIT"  ]]; then
                    echo "be-common version was upgraded."
                    echo "old_commit=$OLD_COMMIT" >> $GITHUB_OUTPUT
                    echo "new_commit=$NEW_COMMIT" >> $GITHUB_OUTPUT
                    echo "is_be_common_upgraded=true" >> $GITHUB_OUTPUT
               else
                    echo "be-common version upgrade detected, but commit hashes are missing."
                    exit 1
               fi
          else
            echo "No valid be-common version upgrade or other dependencies modified."
            echo "is_be_common_upgraded=false" >> $GITHUB_OUTPUT
          fi
      env:
        GITHUB_TOKEN: ${{ inputs.workflow_token }}

    # Step 4: Find the changed files between the old and new commit in be-common
    - name: "Get changed files in be-common between commits"
      id: be_common_changed_files
      if: ${{ steps.version_check.outputs.is_be_common_upgraded == 'true' }}
      shell: bash
      run: |
          OLD_COMMIT=${{ steps.version_check.outputs.old_commit }}
          NEW_COMMIT=${{ steps.version_check.outputs.new_commit }}
          REPO_NAME="epiFi/be-common"

          echo "Fetching files changed between commits $OLD_COMMIT and $NEW_COMMIT in be-common repository..."
          CHANGED_FILES=$(gh api repos/$REPO_NAME/compare/$OLD_COMMIT...$NEW_COMMIT --paginate --jq '.files[].filename')
          TRIMMED_CHANGED_FILES=$(echo "$CHANGED_FILES" | tr '\n' ' ')

          # Add repo name prefix to each changed file
          PREFIXED_CHANGED_FILES=$(echo "$TRIMMED_CHANGED_FILES" | sed "s|[^ ]*|be-common/&|g")
          echo "changed_files=$PREFIXED_CHANGED_FILES" >> $GITHUB_OUTPUT
      env:
        GITHUB_TOKEN: ${{ inputs.workflow_token }}

    - name: "Check out be-common repo"
      uses: actions/checkout@24cb9080177205b6e8c946b17badbe402adc938f
      with:
        repository: 'epiFi/be-common'
        path: go/src/github.com/epifi/be-common
        fetch-depth: 1
        ref: "master"
        token: ${{ inputs.workflow_token }}

    - name: "Find required checks"
      id: path_filters
      shell: bash
      run: |
          set -x
          patternJson=$(yq eval '.service_file_patterns' ./go/src/github.com/epifi/gamma/tools/actions/lci/run_lci_validation/path_pattern.yml | yq eval -o=json)
          prchangedFiles="${{ steps.get_changed_files.outputs.changed_files }}"
          be_common_changedFiles="${{ steps.be_common_changed_files.outputs.changed_files }}"

          allChangedFiles="$prchangedFiles $be_common_changedFiles"
          cd "./go/src/github.com/epifi/be-common"
          go build -o ./output/required_check ./tools/actions/get_required_check/required_check.go

          output="$(./output/required_check --inputFile="$allChangedFiles" --globPattern="$patternJson")"
          echo "output $output"
          outputArray=()

          # Use IFS (Internal Field Separator) to split by ',' and read into the array
          clean_output=$(echo "$output" | sed 's/"//g')
          IFS=',' read -r -a outputArray <<< "$clean_output"

          # Iterate through the array and append results to the GitHub output
          for check in "${outputArray[@]}"; do
              echo "${check}=true" >> "$GITHUB_OUTPUT"
          done

    - name: "BatchBaseServiceChecker status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ always() }}
      with:
        # BatchBaseServiceChecker is to ensure that there is no error in batching workflows to run services workflows
        # LCI will publish BatchBaseServiceChecker event even if there is no service workflows to run.
        services: "BatchBaseServiceChecker"
        lci_stamp_key: "BatchBaseServiceChecker"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "other"
        enabled: true
    - name: "Precommit LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.precommit == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "precommit"
        lci_stamp_key: "precommit"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "other"
        enabled: true
    - name: "BuildApps LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.build_apps == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "build_apps"
        lci_stamp_key: "build_apps"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "other"
        enabled: true
    - name: "Celestial LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.celestial == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "celestial"
        lci_stamp_key: "celestial"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Accrual LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.accrual == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "accrual"
        lci_stamp_key: "accrual"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Alfred LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.alfred == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "alfred"
        lci_stamp_key: "alfred"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Auth LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.auth == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "auth"
        lci_stamp_key: "auth"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "BankCustomer LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.bankcust == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "bankcust"
        lci_stamp_key: "bankcust"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Card LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.card == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "card"
        lci_stamp_key: "card"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Casbin LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.casbin == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "casbin"
        lci_stamp_key: "casbin"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Casper LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.casper_cms == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "casper cms"
        lci_stamp_key: "casper_cms"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Comms LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.comms_dynamicelements == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "comms dynamicelements"
        lci_stamp_key: "comms_dynamicelements"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "ConnectedAccount LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.connectedaccount == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "connectedaccount"
        lci_stamp_key: "connectedaccount"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Consent LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.consent == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "consent"
        lci_stamp_key: "consent"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "CX LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.cx == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "cx"
        lci_stamp_key: "cx"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Employment LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.employment == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "employment"
        lci_stamp_key: "employment"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Firefly LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.firefly == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "firefly"
        lci_stamp_key: "firefly"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Frontend LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.frontend == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend"
        lci_stamp_key: "frontend"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Inapphelp LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.inapphelp == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "inapphelp"
        lci_stamp_key: "inapphelp"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "InAppReferral LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.inappreferral == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "inappreferral"
        lci_stamp_key: "inappreferral"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Investment LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.investment == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "investment"
        lci_stamp_key: "investment"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Kyc LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.kyc == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "kyc"
        lci_stamp_key: "kyc"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Quest LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.nebula_quest == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "quest"
        lci_stamp_key: "quest"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Nudge LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.nudge == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "nudge"
        lci_stamp_key: "nudge"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "P2p Investment LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.p2pinvestment == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "p2pinvestment"
        lci_stamp_key: "p2pinvestment"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Pan LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.pan == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "pan"
        lci_stamp_key: "pan"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Pay LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.pay == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "pay"
        lci_stamp_key: "pay"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "PreApprovedLoan LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.preapprovedloan == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "preapprovedloan"
        lci_stamp_key: "preapprovedloan"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "RecurringPayment LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.recurringpayment == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "recurringpayment"
        lci_stamp_key: "recurringpayment"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Rewards LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.rewards == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "rewards"
        lci_stamp_key: "rewards"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Risk LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.risk == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "risk"
        lci_stamp_key: "risk"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "SalaryProgram LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.salaryprogram == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "salaryprogram"
        lci_stamp_key: "salaryprogram"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Savings LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.savings == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "savings"
        lci_stamp_key: "savings"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Screener LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.screener == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "screener"
        lci_stamp_key: "screener"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Segment LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.segment == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "segment"
        lci_stamp_key: "segment"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Tiering LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.tiering == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "tiering"
        lci_stamp_key: "tiering"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "User LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.user == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "user"
        lci_stamp_key: "user"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "UserIntel LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.userintel == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "userintel"
        lci_stamp_key: "userintel"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "USStocks LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.usstocks == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "usstocks"
        lci_stamp_key: "usstocks"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Vendorgateway LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.vendorgateway == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "vendorgateway"
        lci_stamp_key: "vendorgateway"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Vendornotification LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.vendornotification_vnotificationgw == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "vendornotification vnotificationgw"
        lci_stamp_key: "vendornotification_vnotificationgw"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "WealthOnboarding LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.wealthonboarding == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "wealthonboarding"
        lci_stamp_key: "wealthonboarding"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Webfe LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.webfe == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "webfe"
        lci_stamp_key: "webfe"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Omegle LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.omegle == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "omegle"
        lci_stamp_key: "omegle"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Vkyc Call LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.vkyccall == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "vkyccall"
        lci_stamp_key: "vkyccall"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Accounts LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.accounts == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "accounts"
        lci_stamp_key: "accounts"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Actor LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.actor == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "actor"
        lci_stamp_key: "actor"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Docs LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.docs == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "docs"
        lci_stamp_key: "docs"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "HealthEngine LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.health_engine == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "health_engine"
        lci_stamp_key: "health_engine"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Merchant LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.merchant == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "merchant"
        lci_stamp_key: "merchant"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Order LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.order == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "order"
        lci_stamp_key: "order"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "PaymentInstrument LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.paymentinstrument == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "paymentinstrument"
        lci_stamp_key: "paymentinstrument"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Timeline LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.timeline == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "timeline"
        lci_stamp_key: "timeline"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "UPI LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.upi == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "upi"
        lci_stamp_key: "upi"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Liveness LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.liveness == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "liveness"
        lci_stamp_key: "liveness"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "Product LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.product == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "product"
        lci_stamp_key: "product"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true
    - name: "FE Insights LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.fe_insights == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend/insights"
        lci_stamp_key: "fe_insights"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "FE Analyser LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.fe_analyser == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend/analyser"
        lci_stamp_key: "fe_analyser"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "FE Deposit LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.fe_deposit == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend/deposit"
        lci_stamp_key: "fe_deposit"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "FE Connected Account LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.fe_connected_account == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend/connected_account"
        lci_stamp_key: "fe_connected_account"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "FE Categorizer LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.fe_categorizer == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend/categorizer"
        lci_stamp_key: "fe_categorizer"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "FE Investment LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.fe_investment == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "frontend/investment"
        lci_stamp_key: "fe_investment"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Leads LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.outputs.leads == 'true' || steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "leads"
        lci_stamp_key: "leads"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

    - name: "Securities LCI status"
      uses: "./go/src/github.com/epifi/gamma/tools/actions/lci/lci_checker"
      if: ${{ steps.path_filters.output.securities == 'true' ||  steps.path_filters.outputs.common_paths == 'true' }}
      with:
        services: "securities"
        lci_stamp_key: "securities"
        workflow_token: ${{ inputs.workflow_token }}
        checker_type: "service_test"
        enabled: true

