Application:
  Environment: "staging"
  Name: "pay"

# Pay service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Pay service to be running on a
# different port in the order server
Server:
  HealthCheckPort: 9999
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9513
    HttpPort: 9999

# Pay service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Pay
EpifiDb:
  DbType: "CRDB"
  AppName: "pay"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PayDb:
  DbType: "PGDB"
  AppName: "pay"
  StatementTimeout: 5m
  Name: "pay"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "staging/rds/postgres/pay_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "staging-celestial-signal-workflow-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "staging-order-in-payment-order-update-queue"

OrderUpdateEventPublisher:
  TopicName: "staging-order-update-topic"

IFTProcessFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-pay-international-fund-transfer-process-file-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-pay-order-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

InternationalFundTransfer:
  EnableUpdateLrsLimitsWorkflowFlag: false
  EnableFederalSherlock: false
  S3Bucket: "epifi-staging-pay-international-fund-transfer"
  PoolInwardAccountPI: "paymentinstrument-us-stock-inward-account"
  UsStocksVendorPI: "paymentinstrument-alpaca-international-account"
  SkipSofDocumentGenerationStage: false
  SkipOrderFulfilmentStage: false
  SherlockHost: "https://sherlock.staging.pointz.in"
  FederalSherlockHost: "https://federal-sherlock.staging.pointz.in"
  GenerateSwiftReportRetry: 5
  GenerateSwiftReportSleep: 25
  MaxPageSize: 15
  NoDataExistForPANCode: "Data does not exist for given pan"
  DailyAggregateLimit:
    CurrencyCode: "INR"
    Units: 100000
  AnnualAggregateLimit:
    CurrencyCode: "INR"
    Units: 1000000
  # 60 days
  AccountVintageCheckDuration: "1440h"
  AccountVintageTxnCount: 5
  ForexRate:
    UpdateAmountInUse:
      EnableIdempotency: true

PayIncidentMgrOrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-order-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

TransactionDetailedStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-txn-detailed-status-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

MinDurationRequiredForUserVintageCheck: "4320h" #180 days in hours

VersionSupport:
  MinAndroidAppVersionToSupportSofCheck: 216
  MinIOSAppVersionToSupportSofCheck: 1077
  MinAndroidAppVersionToSupportAddCA: 216
  MinIOSAppVersionToSupportAddCA: 999


VelocityRuleThresholdsMap:
  "USER_AMOUNT_LIMIT_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "USER_LEVEL_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "UPI_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_PIN_RESET_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_DEVICE_REGISTRATION_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_LIMIT_WINDOW_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 100000
    ThresholdCount: 20
  "NEFT_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "RTGS_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "INTRA_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "IMPS_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "IMPS_LIMIT_WINDOW_RULE":
    ThresholdCount: 500


VelocityRuleAmountRangeMap:
  "UPI_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1
    MaxAmount: 100000
  "NEFT_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 200001
    MaxAmount: 300000
  "RTGS_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 300001
    MaxAmount: 1500000
  "IMPS_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1001
    MaxAmount: 200000
  "INTRA_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 200
    MaxAmount: 1000

PinotIngestionDelay:
  DelayDuration: 12h

NonTpapPspHandles: ["fede"]

UniqueATMActorId: "AC221129j0a2e5HESKCK7l4zv9QcNw=="

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 14

IFTRemittanceFileProcessingEventPublisher:
  TopicName: "staging-ift-remittance-file-processing-events-topic"

TxnBackfillBucketName: "epifi-staging-pay-txn-backfill"

FeatureReleaseConfig:
  FeatureConstraints:
    - PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_DEBITED_TRANSACTION_THRESHOLD_BREACH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FUND_TRANSFER_V1:
        AppVersionConstraintConfig:
          MinAndroidVersion: 250
          MinIOSVersion: 345
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # most of the Transactions which comes under this either go into DEEMED or go into DEBITED_AND_FAILED_CATCH_ALL within 5 mins (even sooner though).
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # Disabling because we are rarely having any transaction which stays in IN_PROGRESS state for more than threshold (5 mins).
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_QR_SCAN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups: []
    - FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL

IncidentManagerParams:
  DebitedTransactionThresholdBreachDuration: 5m

Tracing:
  Enable: true

GenerateSofLimitStrategiesValuesSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-generate-sof-limit-strategies-values-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "internationalfundtransfer"

GenerateSofLimitStrategiesValuesPublisher:
  QueueName: "staging-generate-sof-limit-strategies-values-queue"

WorkflowToVendorPaymentDetailsMap:
  SECURED_CC_FEDERAL_FD_FUND_TRANSFER:
    ActorId: "actor-creditcard-federal-pool-account"
    PiId: "paymentinstrument-creditcard-federal-pool-account-1"
    BufferDuration: "30s"

PennyDropConfig:
  PennyDropPollRetryThreshold: 20
  PennyDropSourceAccountMapping:
    PENNY_DROP_PROVENANCE_SECURED_CREDIT_CARDS_NTB_FLOW:
      FEDERAL_BANK:
        ActorId: "actor-creditcard-federal-pool-account"
        PaymentInstrumentId: "paymentinstrument-creditcard-federal-pool-account-1"
        Amount:
          CurrencyCode: "INR"
          Units: 1000
    PENNY_DROP_PROVENANCE_STOCKGUARDIAN_TSP:
      # todo(ayushb): update to correct account_details
      FEDERAL_BANK:
        ActorId: "actor-creditcard-federal-pool-account"
        PaymentInstrumentId: "paymentinstrument-creditcard-federal-pool-account-1"
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_LOANS_LSP:
      FEDERAL_BANK:
        ActorId: "actor-loans-federal-penny-drop-pool-account"
        PaymentInstrumentId: "paymentinstrument-loans-federal-penny-drop-pool-account"
        Amount:
          CurrencyCode: "INR"
          Units: 1

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "staging/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "staging/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-creditcard-federal-pool-account-1":
    AuthParam: "staging/vendorgateway/razorpay-stock-guardian-loans-api-key"

PayloadSigningCreds:
  SigningKeyJson: "staging/pay/signing_credentials"

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"

Secrets:
  Ids:
    MT199MessageAttachmentPassword: "staging/ift/mt199-message-attachment-password"

ProcessPaymentGatewayWebhookEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-pg-razorpay-inbound-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"
