Application:
  Environment: "qa"
  Name: "pay"

# Pay service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Pay service to be running on a
# different port in the order server
Server:
  Ports:
    GrpcPort: 8091
    GrpcSecurePort: 9513
    HttpPort: 9853

# Pay service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Pay
EpifiDb:
  DbType: "CRDB"
  AppName: "pay"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

PayDb:
  DbType: "PGDB"
  AppName: "pay"
  StatementTimeout: 5m
  Name: "pay"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "qa/rds/postgres/pay_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "qa-celestial-signal-workflow-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "qa-order-in-payment-order-update-queue"

OrderUpdateEventPublisher:
  TopicName: "qa-order-update-topic"

IFTProcessFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-pay-international-fund-transfer-process-file-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

OrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-pay-order-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 25
      MaxAttempts: 3
      TimeUnit: "Minute"

InternationalFundTransfer:
  EnableLRSCheckFromVendor: false
  EnableFederalSherlock: false
  S3Bucket: "epifi-qa-pay-international-fund-transfer"
  PoolInwardAccountPI: "paymentinstrument-us-stock-inward-account"
  UsStocksVendorPI: "paymentinstrument-alpaca-international-account"
  SkipSofDocumentGenerationStage: false
  SkipOrderFulfilmentStage: false
  SherlockHost: "https://sherlock.qa.pointz.in"
  FederalSherlockHost: "https://federal-sherlock.qa.pointz.in"
  GenerateSwiftReportRetry: 5
  GenerateSwiftReportSleep: 25
  MaxPageSize: 15
  NoDataExistForPANCode: "Data does not exist for given pan"
  DailyAggregateLimit:
    CurrencyCode: "INR"
    Units: 100000
  AnnualAggregateLimit:
    CurrencyCode: "INR"
    Units: 1000000
  # 60 days
  AccountVintageCheckDuration: "1440h"
  AccountVintageTxnCount: 5
  ForexRate:
    UpdateAmountInUse:
      EnableIdempotency: true

PayIncidentMgrOrderUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-order-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "order-consumer"

TransactionDetailedStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-txn-detailed-status-update-payincidentmanager-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "order-consumer"

MinDurationRequiredForUserVintageCheck: "4320h" #180 days in hours
EnableSOFLogForDebugging: true

VersionSupport:
  MinAndroidAppVersionToSupportSofCheck: 216
  MinIOSAppVersionToSupportSofCheck: 1110
  MinAndroidAppVersionToSupportAddCA: 216
  MinIOSAppVersionToSupportAddCA: 1110

VelocityRuleThresholdsMap:
  "USER_AMOUNT_LIMIT_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 1000000
  "USER_LEVEL_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "UPI_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "UPI_PIN_RESET_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_DEVICE_REGISTRATION_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 5000
  "UPI_LIMIT_WINDOW_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 200000
    ThresholdCount: 10
  "NEFT_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "RTGS_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "INTRA_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "IMPS_AFU_COOL_DOWN_RULE":
    ThresholdAmount:
      CurrencyCode: "INR"
      Units: 10000
  "IMPS_LIMIT_WINDOW_RULE":
    ThresholdCount: 500


VelocityRuleAmountRangeMap:
  "UPI_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 1
    MaxAmount: 100000
  "NEFT_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 3000
    MaxAmount: 200000
  "RTGS_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 200000
    MaxAmount: 1000000000
  "IMPS_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 500
    MaxAmount: 3000
  "INTRA_CHECK_AMOUNT_WITHIN_RANGE_RULE":
    MinAmount: 500
    MaxAmount: 1000

NonTpapPspHandles: ["fede"]

FeatureReleaseConfig:
  FeatureConstraints:
    - PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FUND_TRANSFER_V1:
        AppVersionConstraintConfig:
          MinAndroidVersion: 250
          MinIOSVersion: 345
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # most of the Transactions which comes under this either go into DEEMED or go into DEBITED_AND_FAILED_CATCH_ALL within 5 mins (even sooner though).
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP:
        # Disabling because we are rarely having any transaction which stays in IN_PROGRESS state for more than threshold(5 mins)
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL
    - FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_QR_SCAN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 300
          MinIOSVersion: 300
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups: []
    - FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # PAY_EXPERIMENTAL

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 14

IFTRemittanceFileProcessingEventPublisher:
  TopicName: "qa-ift-remittance-file-processing-events-topic"

TxnBackfillBucketName: "epifi-qa-pay-txn-backfill"

Tracing:
  Enable: true


GenerateSofLimitStrategiesValuesSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-generate-sof-limit-strategies-values-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "internationalfundtransfer"

GenerateSofLimitStrategiesValuesPublisher:
  QueueName: "qa-generate-sof-limit-strategies-values-queue"

PennyDropConfig:
  PennyDropPollRetryThreshold: 20
  PennyDropSourceAccountMapping:
    PENNY_DROP_PROVENANCE_SECURED_CREDIT_CARDS_NTB_FLOW:
      # todo(@saurabh): add ids here post pi and actor creation
      FEDERAL_BANK:
        ActorId: "actor-creditcard-federal-pool-account"
        PaymentInstrumentId: "paymentinstrument-creditcard-federal-pool-account-1"
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_STOCKGUARDIAN_TSP:
      FEDERAL_BANK:
        ActorId: "actor-stock-guardian"
        PaymentInstrumentId: "paymentinstrument-stockguardian_MbZRPgHhafW"
        Amount:
          CurrencyCode: "INR"
          Units: 1
    PENNY_DROP_PROVENANCE_LOANS_LSP:
      FEDERAL_BANK:
        ActorId: "actor-loans-federal-penny-drop-pool-account"
        PaymentInstrumentId: "paymentinstrument-loans-federal-penny-drop-pool-account"
        Amount:
          CurrencyCode: "INR"
          Units: 1

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "qa/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "qa/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "qa/vendorgateway/razorpay-stock-guardian-loans-api-key"

PayloadSigningCreds:
  SigningKeyJson: "qa/pay/signing_credentials"

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"

Secrets:
  Ids:
    MT199MessageAttachmentPassword: "qa/ift/mt199-message-attachment-password"

EnableEntitySegregation: true

ProcessPaymentGatewayWebhookEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-pg-razorpay-inbound-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"
