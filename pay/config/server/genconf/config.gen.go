// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	money "google.golang.org/genproto/googleapis/type/money"

	questtypes "github.com/epifi/be-common/api/quest/types"
	common "github.com/epifi/be-common/api/typesv2/common"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	genconfig2 "github.com/epifi/be-common/pkg/rulerover/config/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	payment "github.com/epifi/gamma/api/order/payment"
	server "github.com/epifi/gamma/pay/config/server"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pay "github.com/epifi/gamma/pkg/pay"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_PageSizeToFetchTxnForATMActor       int32
	_PageSizeForChargeRelatedOrderAndTxn uint32
	_MaximumActiveDaysForAutoIdTickets   int32
	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance
	// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
	// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
	// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
	// can be that the data got written with entity segregation but fetched from the default
	// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
	_EnableEntitySegregation uint32
	// Enable logging account creation details for debugging sof status
	_EnableSOFLogForDebugging uint32
	// Defines minimum duration a user needs to be a Fi user to perform purchase
	_MinDurationRequiredForUserVintageCheck int64
	// FeedbackEngineCustomEvaluatorRules stores the custom evaluator rules for feedback engine.
	_FeedbackEngineCustomEvaluatorRules                            *syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule]
	_UniqueATMActorId                                              string
	_UniqueATMActorIdMutex                                         *sync.RWMutex
	_FundTransferParams                                            *FundTransferParams
	_ExecutionReportGenerationParams                               *ExecutionReportGenerationParams
	_InternationalFundTransfer                                     *InternationalFundTransfer
	_PayIncidentMgrOrderUpdateSubscriber                           *gencfg.SqsSubscriber
	_TransactionDetailedStatusUpdateSubscriber                     *gencfg.SqsSubscriber
	_PayIncidentManager                                            *PayIncidentManager
	_PinotIngestionDelay                                           *PinotIngestionDelay
	_IFTProcessFileSubscriber                                      *gencfg.SqsSubscriber
	_FundTransferCelestialParams                                   *FundTransferCelestialParams
	_VersionSupport                                                *VersionSupport
	_FeatureReleaseConfig                                          *genconfig.FeatureReleaseConfig
	_TierConfig                                                    *TierConfig
	_OrderUpdateSubscriber                                         *gencfg.SqsSubscriber
	_TpapEntryPointSwitchConfigForSaUser                           *genconfig.FeatureReleaseConfig
	_TpapEntryPointSwitchConfigForNonSaUser                        *genconfig.FeatureReleaseConfig
	_GenerateSofLimitStrategiesValuesSubscriber                    *gencfg.SqsSubscriber
	_ProcessPaymentGatewayWebhookEventSubscriber                   *gencfg.SqsSubscriber
	_PayOrderCacheConfig                                           *PayOrderCacheConfig
	_PayTransactionCacheConfig                                     *PayTransactionCacheConfig
	_PgParams                                                      *PgParams
	_BaseRuleEngineConfig                                          *genconfig2.RuleEngineConfig
	_EnrichOrderConfig                                             *EnrichOrderConfig
	_Application                                                   *server.Application
	_RazorPayResponseCodesJson                                     string
	_Server                                                        *server.Server
	_Logging                                                       *cfg.Logging
	_EpifiDb                                                       *cfg.DB
	_RedisOptions                                                  *cfg.RedisOptions
	_AWS                                                           *cfg.AWS
	_Secrets                                                       *cfg.Secrets
	_PaymentEnquiryParams                                          *server.PaymentEnquiryParams
	_SignalWorkflowPublisher                                       *cfg.SqsPublisher
	_VelocityRuleThresholdsMap                                     map[string]server.VelocityRuleThresholds
	_VelocityRuleAmountRangeMap                                    map[string]server.VelocityRuleAmountRange
	_RulesToRuleGroupMap                                           map[string][]string
	_InPaymentOrderUpdatePublisher                                 *cfg.SqsPublisher
	_NonTpapPspHandles                                             []string
	_VendorToNameMap                                               *server.VendorToNameMap
	_IFTRemittanceFileProcessingEventPublisher                     *cfg.SnsPublisher
	_PostPaymentBanners                                            []*server.Banner
	_PayLandingScreenBanners                                       []*server.Banner
	_QrScreenElements                                              *server.QrScreenElements
	_TxnBackfillBucketName                                         string
	_IncidentManagerParams                                         *pay.IncidentManagerParams
	_PayDb                                                         *cfg.DB
	_GrpcRatelimiterParams                                         *cfg.GrpcRateLimiterParams
	_Tracing                                                       *cfg.Tracing
	_GenerateSofLimitStrategiesValuesPublisher                     *cfg.SqsPublisher
	_PennyDropConfig                                               *server.PennyDropConfig
	_TransactionStatusDetailsCombinationsForDebitAndFailedIncident []*server.TransactionStatusDetails
	_PgProgramToAuthSecretMap                                      map[string]*server.PgProgramToAuthSecret
	_PayloadSigningCreds                                           *server.PayloadSigningCreds
	_OrderUpdateEventPublisher                                     *cfg.SnsPublisher
}

func (obj *Config) PageSizeToFetchTxnForATMActor() int32 {
	return int32(atomic.LoadInt32(&obj._PageSizeToFetchTxnForATMActor))
}
func (obj *Config) PageSizeForChargeRelatedOrderAndTxn() uint32 {
	return uint32(atomic.LoadUint32(&obj._PageSizeForChargeRelatedOrderAndTxn))
}
func (obj *Config) MaximumActiveDaysForAutoIdTickets() int32 {
	return int32(atomic.LoadInt32(&obj._MaximumActiveDaysForAutoIdTickets))
}

// flag to indicate whether to enable resource provider based db instance provision or whether to
// go with a static db instance
// Assumption : Once the flag is turned on, it will not be turned off again. Since it will go through a
// CUG, any occurring issues will be fixed within the CUG itself. Also, all the systems using this
// have been made backward compatible. So the older prod entities will not see any issues. Possible consequences
// can be that the data got written with entity segregation but fetched from the default
// epifi db but since that scale will be low at the time of CUG, we would be able to fix it before going external
func (obj *Config) EnableEntitySegregation() bool {
	if atomic.LoadUint32(&obj._EnableEntitySegregation) == 0 {
		return false
	} else {
		return true
	}
}

// Enable logging account creation details for debugging sof status
func (obj *Config) EnableSOFLogForDebugging() bool {
	if atomic.LoadUint32(&obj._EnableSOFLogForDebugging) == 0 {
		return false
	} else {
		return true
	}
}

// Defines minimum duration a user needs to be a Fi user to perform purchase
func (obj *Config) MinDurationRequiredForUserVintageCheck() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MinDurationRequiredForUserVintageCheck))
}

// FeedbackEngineCustomEvaluatorRules stores the custom evaluator rules for feedback engine.
func (obj *Config) FeedbackEngineCustomEvaluatorRules() *syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule] {
	return obj._FeedbackEngineCustomEvaluatorRules
}
func (obj *Config) UniqueATMActorId() string {
	obj._UniqueATMActorIdMutex.RLock()
	defer obj._UniqueATMActorIdMutex.RUnlock()
	return obj._UniqueATMActorId
}
func (obj *Config) FundTransferParams() *FundTransferParams {
	return obj._FundTransferParams
}
func (obj *Config) ExecutionReportGenerationParams() *ExecutionReportGenerationParams {
	return obj._ExecutionReportGenerationParams
}
func (obj *Config) InternationalFundTransfer() *InternationalFundTransfer {
	return obj._InternationalFundTransfer
}
func (obj *Config) PayIncidentMgrOrderUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._PayIncidentMgrOrderUpdateSubscriber
}
func (obj *Config) TransactionDetailedStatusUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._TransactionDetailedStatusUpdateSubscriber
}
func (obj *Config) PayIncidentManager() *PayIncidentManager {
	return obj._PayIncidentManager
}
func (obj *Config) PinotIngestionDelay() *PinotIngestionDelay {
	return obj._PinotIngestionDelay
}
func (obj *Config) IFTProcessFileSubscriber() *gencfg.SqsSubscriber {
	return obj._IFTProcessFileSubscriber
}
func (obj *Config) FundTransferCelestialParams() *FundTransferCelestialParams {
	return obj._FundTransferCelestialParams
}
func (obj *Config) VersionSupport() *VersionSupport {
	return obj._VersionSupport
}
func (obj *Config) FeatureReleaseConfig() *genconfig.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) TierConfig() *TierConfig {
	return obj._TierConfig
}
func (obj *Config) OrderUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderUpdateSubscriber
}
func (obj *Config) TpapEntryPointSwitchConfigForSaUser() *genconfig.FeatureReleaseConfig {
	return obj._TpapEntryPointSwitchConfigForSaUser
}
func (obj *Config) TpapEntryPointSwitchConfigForNonSaUser() *genconfig.FeatureReleaseConfig {
	return obj._TpapEntryPointSwitchConfigForNonSaUser
}
func (obj *Config) GenerateSofLimitStrategiesValuesSubscriber() *gencfg.SqsSubscriber {
	return obj._GenerateSofLimitStrategiesValuesSubscriber
}
func (obj *Config) ProcessPaymentGatewayWebhookEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessPaymentGatewayWebhookEventSubscriber
}
func (obj *Config) PayOrderCacheConfig() *PayOrderCacheConfig {
	return obj._PayOrderCacheConfig
}
func (obj *Config) PayTransactionCacheConfig() *PayTransactionCacheConfig {
	return obj._PayTransactionCacheConfig
}
func (obj *Config) PgParams() *PgParams {
	return obj._PgParams
}
func (obj *Config) BaseRuleEngineConfig() *genconfig2.RuleEngineConfig {
	return obj._BaseRuleEngineConfig
}
func (obj *Config) EnrichOrderConfig() *EnrichOrderConfig {
	return obj._EnrichOrderConfig
}
func (obj *Config) Application() *server.Application {
	return obj._Application
}
func (obj *Config) RazorPayResponseCodesJson() string {
	return obj._RazorPayResponseCodesJson
}
func (obj *Config) Server() *server.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) PaymentEnquiryParams() *server.PaymentEnquiryParams {
	return obj._PaymentEnquiryParams
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) VelocityRuleThresholdsMap() map[string]server.VelocityRuleThresholds {
	return obj._VelocityRuleThresholdsMap
}
func (obj *Config) VelocityRuleAmountRangeMap() map[string]server.VelocityRuleAmountRange {
	return obj._VelocityRuleAmountRangeMap
}
func (obj *Config) RulesToRuleGroupMap() map[string][]string {
	return obj._RulesToRuleGroupMap
}
func (obj *Config) InPaymentOrderUpdatePublisher() *cfg.SqsPublisher {
	return obj._InPaymentOrderUpdatePublisher
}
func (obj *Config) NonTpapPspHandles() []string {
	return obj._NonTpapPspHandles
}
func (obj *Config) VendorToNameMap() *server.VendorToNameMap {
	return obj._VendorToNameMap
}
func (obj *Config) IFTRemittanceFileProcessingEventPublisher() *cfg.SnsPublisher {
	return obj._IFTRemittanceFileProcessingEventPublisher
}
func (obj *Config) PostPaymentBanners() []*server.Banner {
	return obj._PostPaymentBanners
}
func (obj *Config) PayLandingScreenBanners() []*server.Banner {
	return obj._PayLandingScreenBanners
}
func (obj *Config) QrScreenElements() *server.QrScreenElements {
	return obj._QrScreenElements
}
func (obj *Config) TxnBackfillBucketName() string {
	return obj._TxnBackfillBucketName
}
func (obj *Config) IncidentManagerParams() *pay.IncidentManagerParams {
	return obj._IncidentManagerParams
}
func (obj *Config) PayDb() *cfg.DB {
	return obj._PayDb
}
func (obj *Config) GrpcRatelimiterParams() *cfg.GrpcRateLimiterParams {
	return obj._GrpcRatelimiterParams
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) GenerateSofLimitStrategiesValuesPublisher() *cfg.SqsPublisher {
	return obj._GenerateSofLimitStrategiesValuesPublisher
}
func (obj *Config) PennyDropConfig() *server.PennyDropConfig {
	return obj._PennyDropConfig
}
func (obj *Config) TransactionStatusDetailsCombinationsForDebitAndFailedIncident() []*server.TransactionStatusDetails {
	return obj._TransactionStatusDetailsCombinationsForDebitAndFailedIncident
}
func (obj *Config) PgProgramToAuthSecretMap() map[string]*server.PgProgramToAuthSecret {
	return obj._PgProgramToAuthSecretMap
}
func (obj *Config) PayloadSigningCreds() *server.PayloadSigningCreds {
	return obj._PayloadSigningCreds
}
func (obj *Config) OrderUpdateEventPublisher() *cfg.SnsPublisher {
	return obj._OrderUpdateEventPublisher
}

type FundTransferParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DefaultFundTransferExpiryDuration int64
	_HardPreferredPaymentProtocol      payment.PaymentProtocol
	_SMSTypeToOptionVersionMap         map[string]*cfg.SMSOptionVersion
	_PaymentNotificationParams         *server.PaymentNotificationParams
}

func (obj *FundTransferParams) DefaultFundTransferExpiryDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DefaultFundTransferExpiryDuration))
}
func (obj *FundTransferParams) HardPreferredPaymentProtocol() payment.PaymentProtocol {
	return obj._HardPreferredPaymentProtocol
}
func (obj *FundTransferParams) SMSTypeToOptionVersionMap() map[string]*cfg.SMSOptionVersion {
	return obj._SMSTypeToOptionVersionMap
}
func (obj *FundTransferParams) PaymentNotificationParams() *server.PaymentNotificationParams {
	return obj._PaymentNotificationParams
}

type ExecutionReportGenerationParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ReportStalenessDuration int64
}

func (obj *ExecutionReportGenerationParams) ReportStalenessDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ReportStalenessDuration))
}

type InternationalFundTransfer struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_GenerateSwiftReportRetry int64
	_GenerateSwiftReportSleep int64
	// max no of records allowed in LRS file
	_MaxLRSPageSize                 int32
	_AccountVintageTxnCount         int32
	_SkipSofDocumentGenerationStage uint32
	// TODO(Brijesh): Remove after no IFT workflow is waiting on stock fulfilment signal
	// Deprecated: No need to wait for placement/fulfilment of stock trades when adding funds to USD wallet
	_SkipOrderFulfilmentStage uint32
	_EnableFederalSherlock    uint32
	_EnableLRSCheckFromVendor uint32
	// skip pre check for swift file generation
	_SkipPreCheckForSwiftFileGen uint32
	// controls if sof document based limit is check during international outward funds transfer
	_IsSofBasedRemittanceLimitCheckEnabled uint32
	// if set to true, cut off time defined for lrs check file generation will be used, else current state of the orders will be considered
	// i.e. all the orders placed before configured cutoff time will only be considered for file generation
	// eg: If cutoff time is 10:00, and file generation is requested at 01 Feb 09:00 am, cutoff timestamp would be 31 Jan 10:00
	// if file generation is requested at 01 Feb 11:00, cutoff timestamp would be 01 Feb 10:00
	_GenerateLrsCheckFileUsingCutOffTime uint32
	// flag to enable 2 new columns (sof_id and sof state) in swift file
	_EnableSofStateColumnInSwiftFileFlag uint32
	// flag to check if an old account is being used enough for transactions
	_EnableMinTxnCountCheckForVintageAccounts uint32
	// flag to enable lrs limits updatation via workflow
	_EnableUpdateLrsLimitsWorkflowFlag uint32
	_AccountVintageCheckDuration       int64
	// duration after which swift file should generated after lrs response file acknowledge
	_DurationWaitForSwiftFileGen int64
	// AuthFactorUpdateCoolOffPeriod determines the time duration till which international funds transfer will be blocked post
	// an auth factor update for the user
	// Note: Update the AuthFactorUpdateCoolOffPeriod in us stocks too when changing this config
	_AuthFactorUpdateCoolOffPeriod int64
	_PoolInwardAccountPI           string
	_PoolInwardAccountPIMutex      *sync.RWMutex
	_UsStocksVendorPI              string
	_UsStocksVendorPIMutex         *sync.RWMutex
	_SherlockHost                  string
	_SherlockHostMutex             *sync.RWMutex
	// This is the domain of the federal sherlock as part of IFT migration
	_FederalSherlockHost      string
	_FederalSherlockHostMutex *sync.RWMutex
	_SherlockPath             string
	_SherlockPathMutex        *sync.RWMutex
	// represent code for no data exist for pan
	_NoDataExistForPANCode                           string
	_NoDataExistForPANCodeMutex                      *sync.RWMutex
	_ForexRate                                       *InternationalFundTransferForexRate
	_TransactionAmountConstraint                     *TransactionAmountConstraint
	_S3Bucket                                        string
	_GstReportingInfo                                *server.GstReportingInfo
	_MaxPageSize                                     uint32
	_DailyAggregateLimit                             *money.Money
	_AnnualAggregateLimit                            *money.Money
	_IgstAccountNumber                               string
	_IgstAccountSolId                                string
	_CgstAccountNumber                               string
	_CgstAccountSolId                                string
	_SgstAccountNumber                               string
	_SgstAccountSolId                                string
	_TCSAccountNumber                                string
	_TCSAccountSolId                                 string
	_OutwardPoolAccountSolId                         string
	_OutwardPoolAccount                              string
	_ForexRateIdPrecision                            int64
	_NostroBankSwiftCode                             string
	_SpecialInstructionForUSStocks                   string
	_ForexRateVendorAPIMaximumConsumptionAmountInUSD int64
}

func (obj *InternationalFundTransfer) GenerateSwiftReportRetry() int {
	return int(atomic.LoadInt64(&obj._GenerateSwiftReportRetry))
}
func (obj *InternationalFundTransfer) GenerateSwiftReportSleep() int {
	return int(atomic.LoadInt64(&obj._GenerateSwiftReportSleep))
}

// max no of records allowed in LRS file
func (obj *InternationalFundTransfer) MaxLRSPageSize() int32 {
	return int32(atomic.LoadInt32(&obj._MaxLRSPageSize))
}
func (obj *InternationalFundTransfer) AccountVintageTxnCount() int64 {
	return int64(atomic.LoadInt32(&obj._AccountVintageTxnCount))
}
func (obj *InternationalFundTransfer) SkipSofDocumentGenerationStage() bool {
	if atomic.LoadUint32(&obj._SkipSofDocumentGenerationStage) == 0 {
		return false
	} else {
		return true
	}
}

// TODO(Brijesh): Remove after no IFT workflow is waiting on stock fulfilment signal
// Deprecated: No need to wait for placement/fulfilment of stock trades when adding funds to USD wallet
func (obj *InternationalFundTransfer) SkipOrderFulfilmentStage() bool {
	if atomic.LoadUint32(&obj._SkipOrderFulfilmentStage) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *InternationalFundTransfer) EnableFederalSherlock() bool {
	if atomic.LoadUint32(&obj._EnableFederalSherlock) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *InternationalFundTransfer) EnableLRSCheckFromVendor() bool {
	if atomic.LoadUint32(&obj._EnableLRSCheckFromVendor) == 0 {
		return false
	} else {
		return true
	}
}

// skip pre check for swift file generation
func (obj *InternationalFundTransfer) SkipPreCheckForSwiftFileGen() bool {
	if atomic.LoadUint32(&obj._SkipPreCheckForSwiftFileGen) == 0 {
		return false
	} else {
		return true
	}
}

// controls if sof document based limit is check during international outward funds transfer
func (obj *InternationalFundTransfer) IsSofBasedRemittanceLimitCheckEnabled() bool {
	if atomic.LoadUint32(&obj._IsSofBasedRemittanceLimitCheckEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// if set to true, cut off time defined for lrs check file generation will be used, else current state of the orders will be considered
// i.e. all the orders placed before configured cutoff time will only be considered for file generation
// eg: If cutoff time is 10:00, and file generation is requested at 01 Feb 09:00 am, cutoff timestamp would be 31 Jan 10:00
// if file generation is requested at 01 Feb 11:00, cutoff timestamp would be 01 Feb 10:00
func (obj *InternationalFundTransfer) GenerateLrsCheckFileUsingCutOffTime() bool {
	if atomic.LoadUint32(&obj._GenerateLrsCheckFileUsingCutOffTime) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable 2 new columns (sof_id and sof state) in swift file
func (obj *InternationalFundTransfer) EnableSofStateColumnInSwiftFileFlag() bool {
	if atomic.LoadUint32(&obj._EnableSofStateColumnInSwiftFileFlag) == 0 {
		return false
	} else {
		return true
	}
}

// flag to check if an old account is being used enough for transactions
func (obj *InternationalFundTransfer) EnableMinTxnCountCheckForVintageAccounts() bool {
	if atomic.LoadUint32(&obj._EnableMinTxnCountCheckForVintageAccounts) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable lrs limits updatation via workflow
func (obj *InternationalFundTransfer) EnableUpdateLrsLimitsWorkflowFlag() bool {
	if atomic.LoadUint32(&obj._EnableUpdateLrsLimitsWorkflowFlag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *InternationalFundTransfer) AccountVintageCheckDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AccountVintageCheckDuration))
}

// duration after which swift file should generated after lrs response file acknowledge
func (obj *InternationalFundTransfer) DurationWaitForSwiftFileGen() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DurationWaitForSwiftFileGen))
}

// AuthFactorUpdateCoolOffPeriod determines the time duration till which international funds transfer will be blocked post
// an auth factor update for the user
// Note: Update the AuthFactorUpdateCoolOffPeriod in us stocks too when changing this config
func (obj *InternationalFundTransfer) AuthFactorUpdateCoolOffPeriod() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AuthFactorUpdateCoolOffPeriod))
}
func (obj *InternationalFundTransfer) PoolInwardAccountPI() string {
	obj._PoolInwardAccountPIMutex.RLock()
	defer obj._PoolInwardAccountPIMutex.RUnlock()
	return obj._PoolInwardAccountPI
}
func (obj *InternationalFundTransfer) UsStocksVendorPI() string {
	obj._UsStocksVendorPIMutex.RLock()
	defer obj._UsStocksVendorPIMutex.RUnlock()
	return obj._UsStocksVendorPI
}
func (obj *InternationalFundTransfer) SherlockHost() string {
	obj._SherlockHostMutex.RLock()
	defer obj._SherlockHostMutex.RUnlock()
	return obj._SherlockHost
}

// This is the domain of the federal sherlock as part of IFT migration
func (obj *InternationalFundTransfer) FederalSherlockHost() string {
	obj._FederalSherlockHostMutex.RLock()
	defer obj._FederalSherlockHostMutex.RUnlock()
	return obj._FederalSherlockHost
}
func (obj *InternationalFundTransfer) SherlockPath() string {
	obj._SherlockPathMutex.RLock()
	defer obj._SherlockPathMutex.RUnlock()
	return obj._SherlockPath
}

// represent code for no data exist for pan
func (obj *InternationalFundTransfer) NoDataExistForPANCode() string {
	obj._NoDataExistForPANCodeMutex.RLock()
	defer obj._NoDataExistForPANCodeMutex.RUnlock()
	return obj._NoDataExistForPANCode
}
func (obj *InternationalFundTransfer) ForexRate() *InternationalFundTransferForexRate {
	return obj._ForexRate
}
func (obj *InternationalFundTransfer) TransactionAmountConstraint() *TransactionAmountConstraint {
	return obj._TransactionAmountConstraint
}
func (obj *InternationalFundTransfer) S3Bucket() string {
	return obj._S3Bucket
}
func (obj *InternationalFundTransfer) GstReportingInfo() *server.GstReportingInfo {
	return obj._GstReportingInfo
}
func (obj *InternationalFundTransfer) MaxPageSize() uint32 {
	return obj._MaxPageSize
}
func (obj *InternationalFundTransfer) DailyAggregateLimit() *money.Money {
	return obj._DailyAggregateLimit
}
func (obj *InternationalFundTransfer) AnnualAggregateLimit() *money.Money {
	return obj._AnnualAggregateLimit
}
func (obj *InternationalFundTransfer) IgstAccountNumber() string {
	return obj._IgstAccountNumber
}
func (obj *InternationalFundTransfer) IgstAccountSolId() string {
	return obj._IgstAccountSolId
}
func (obj *InternationalFundTransfer) CgstAccountNumber() string {
	return obj._CgstAccountNumber
}
func (obj *InternationalFundTransfer) CgstAccountSolId() string {
	return obj._CgstAccountSolId
}
func (obj *InternationalFundTransfer) SgstAccountNumber() string {
	return obj._SgstAccountNumber
}
func (obj *InternationalFundTransfer) SgstAccountSolId() string {
	return obj._SgstAccountSolId
}
func (obj *InternationalFundTransfer) TCSAccountNumber() string {
	return obj._TCSAccountNumber
}
func (obj *InternationalFundTransfer) TCSAccountSolId() string {
	return obj._TCSAccountSolId
}
func (obj *InternationalFundTransfer) OutwardPoolAccountSolId() string {
	return obj._OutwardPoolAccountSolId
}
func (obj *InternationalFundTransfer) OutwardPoolAccount() string {
	return obj._OutwardPoolAccount
}
func (obj *InternationalFundTransfer) ForexRateIdPrecision() int64 {
	return obj._ForexRateIdPrecision
}
func (obj *InternationalFundTransfer) NostroBankSwiftCode() string {
	return obj._NostroBankSwiftCode
}
func (obj *InternationalFundTransfer) SpecialInstructionForUSStocks() string {
	return obj._SpecialInstructionForUSStocks
}
func (obj *InternationalFundTransfer) ForexRateVendorAPIMaximumConsumptionAmountInUSD() int64 {
	return obj._ForexRateVendorAPIMaximumConsumptionAmountInUSD
}

type InternationalFundTransferForexRate struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// if AllowVaryingRatesFromForexAPI value is true then will support multiple forex rate entries for different forex rate in DB for vendor API provenance
	// otherwise will throw error if from vendor getting different forex rate from existing forex rate in DB.
	_AllowVaryingRatesFromForexAPI uint32
	// ForexRateRefreshInterval will be used by MonitorForexRate workflow
	// It is the interval after which fresh value for amount_in_use is fetched from db and evaluated for sending alert
	// amount_in_use is updated on receiving order update signal
	// since there can be cases that forex deal update signal is missed due to platform failure, celestial service downtime etc
	// workflow should handle this by refreshing the forex rate object from db after defined interval
	_ForexRateRefreshInterval int64
	// list of thresholds for sending alerts on forex rate consumption in percentage
	// alert is sent when a threshold is breached for first time
	// eg: [70, 90] would mean that 2 alerts will be sent when 70% of the total deal amount is consumed
	// and 90% of the total deal amount is consumed
	// If a single order bumps the deal consumption from 65% to 95%, then only 1 alert is expected to be sent
	_ForexRateAlertThresholdsInPercent      roarray.ROArray[int64]
	_ForexRateAlertThresholdsInPercentMutex *sync.RWMutex
	_UpdateAmountInUse                      *ForexRateUpdateAmountInUse
	_USD                                    *USDForexRate
}

// if AllowVaryingRatesFromForexAPI value is true then will support multiple forex rate entries for different forex rate in DB for vendor API provenance
// otherwise will throw error if from vendor getting different forex rate from existing forex rate in DB.
func (obj *InternationalFundTransferForexRate) AllowVaryingRatesFromForexAPI() bool {
	if atomic.LoadUint32(&obj._AllowVaryingRatesFromForexAPI) == 0 {
		return false
	} else {
		return true
	}
}

// ForexRateRefreshInterval will be used by MonitorForexRate workflow
// It is the interval after which fresh value for amount_in_use is fetched from db and evaluated for sending alert
// amount_in_use is updated on receiving order update signal
// since there can be cases that forex deal update signal is missed due to platform failure, celestial service downtime etc
// workflow should handle this by refreshing the forex rate object from db after defined interval
func (obj *InternationalFundTransferForexRate) ForexRateRefreshInterval() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ForexRateRefreshInterval))
}

// list of thresholds for sending alerts on forex rate consumption in percentage
// alert is sent when a threshold is breached for first time
// eg: [70, 90] would mean that 2 alerts will be sent when 70% of the total deal amount is consumed
// and 90% of the total deal amount is consumed
// If a single order bumps the deal consumption from 65% to 95%, then only 1 alert is expected to be sent
func (obj *InternationalFundTransferForexRate) ForexRateAlertThresholdsInPercent() roarray.ROArray[int64] {
	obj._ForexRateAlertThresholdsInPercentMutex.RLock()
	defer obj._ForexRateAlertThresholdsInPercentMutex.RUnlock()
	return obj._ForexRateAlertThresholdsInPercent
}
func (obj *InternationalFundTransferForexRate) UpdateAmountInUse() *ForexRateUpdateAmountInUse {
	return obj._UpdateAmountInUse
}
func (obj *InternationalFundTransferForexRate) USD() *USDForexRate {
	return obj._USD
}

type ForexRateUpdateAmountInUse struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// if true, then client req id will be mandatory for UpdateForexRateAmountInUse RPC and maintains the idempotency
	_EnableIdempotency uint32
}

// if true, then client req id will be mandatory for UpdateForexRateAmountInUse RPC and maintains the idempotency
func (obj *ForexRateUpdateAmountInUse) EnableIdempotency() bool {
	if atomic.LoadUint32(&obj._EnableIdempotency) == 0 {
		return false
	} else {
		return true
	}
}

type USDForexRate struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// eg: "82"
	_Units int32
	// e.g: "510000000" for 0.51
	_Nanos int32
	// "INR"
	_CurrencyCode      string
	_CurrencyCodeMutex *sync.RWMutex
}

// eg: "82"
func (obj *USDForexRate) Units() int64 {
	return int64(atomic.LoadInt32(&obj._Units))
}

// e.g: "510000000" for 0.51
func (obj *USDForexRate) Nanos() int32 {
	return int32(atomic.LoadInt32(&obj._Nanos))
}

// "INR"
func (obj *USDForexRate) CurrencyCode() string {
	obj._CurrencyCodeMutex.RLock()
	defer obj._CurrencyCodeMutex.RUnlock()
	return obj._CurrencyCode
}

type TransactionAmountConstraint struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// represents the maximum allow percentage of International Transaction Amount/ [Max (Last 5 Credit transaction Amount)]
	_MaxTransactionAmountToCreditAmountPercentage int32
	// represents the maximum allow percentage of International Transaction Amount/ Account Balance
	_MaxTransactionAmountToCurrentBalancePercentage int32
	// if it is true then only will check suspected amount conditions
	_IsEnabled            uint32
	_MaxPermissibleAmount *money.Money
}

// represents the maximum allow percentage of International Transaction Amount/ [Max (Last 5 Credit transaction Amount)]
func (obj *TransactionAmountConstraint) MaxTransactionAmountToCreditAmountPercentage() int64 {
	return int64(atomic.LoadInt32(&obj._MaxTransactionAmountToCreditAmountPercentage))
}

// represents the maximum allow percentage of International Transaction Amount/ Account Balance
func (obj *TransactionAmountConstraint) MaxTransactionAmountToCurrentBalancePercentage() int64 {
	return int64(atomic.LoadInt32(&obj._MaxTransactionAmountToCurrentBalancePercentage))
}

// if it is true then only will check suspected amount conditions
func (obj *TransactionAmountConstraint) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TransactionAmountConstraint) MaxPermissibleAmount() *money.Money {
	return obj._MaxPermissibleAmount
}

type PayIncidentManager struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DefaultTransactionHandler *DefaultTransactionHandler
	_UpiPinFlowError           *server.UpiPinFlowError
}

func (obj *PayIncidentManager) DefaultTransactionHandler() *DefaultTransactionHandler {
	return obj._DefaultTransactionHandler
}
func (obj *PayIncidentManager) UpiPinFlowError() *server.UpiPinFlowError {
	return obj._UpiPinFlowError
}

type DefaultTransactionHandler struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ErrorCodeIncidentMap            *syncmap.Map[string, string]
	_IncidentProductCategoryMap      *syncmap.Map[string, *IncidentCategory]
	_IncidentTicketConfigMap         *syncmap.Map[string, *IncidentTicketConfig]
	_IncidentNotificationTemplateMap map[string]*server.IncidentNotificationTemplate
}

func (obj *DefaultTransactionHandler) ErrorCodeIncidentMap() *syncmap.Map[string, string] {
	return obj._ErrorCodeIncidentMap
}
func (obj *DefaultTransactionHandler) IncidentProductCategoryMap() *syncmap.Map[string, *IncidentCategory] {
	return obj._IncidentProductCategoryMap
}
func (obj *DefaultTransactionHandler) IncidentTicketConfigMap() *syncmap.Map[string, *IncidentTicketConfig] {
	return obj._IncidentTicketConfigMap
}
func (obj *DefaultTransactionHandler) IncidentNotificationTemplateMap() map[string]*server.IncidentNotificationTemplate {
	return obj._IncidentNotificationTemplateMap
}

type IncidentCategory struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// We Might store different issue category ids for different payment protocols.
	_PaymentProtocolToIssueCategoryIdMap *syncmap.Map[string, string]
	_ProductCategory                     string
	_ProductCategoryMutex                *sync.RWMutex
	_ProductCategoryDetails              string
	_ProductCategoryDetailsMutex         *sync.RWMutex
	_SubCategory                         string
	_SubCategoryMutex                    *sync.RWMutex
}

// We Might store different issue category ids for different payment protocols.
func (obj *IncidentCategory) PaymentProtocolToIssueCategoryIdMap() *syncmap.Map[string, string] {
	return obj._PaymentProtocolToIssueCategoryIdMap
}
func (obj *IncidentCategory) ProductCategory() string {
	obj._ProductCategoryMutex.RLock()
	defer obj._ProductCategoryMutex.RUnlock()
	return obj._ProductCategory
}
func (obj *IncidentCategory) ProductCategoryDetails() string {
	obj._ProductCategoryDetailsMutex.RLock()
	defer obj._ProductCategoryDetailsMutex.RUnlock()
	return obj._ProductCategoryDetails
}
func (obj *IncidentCategory) SubCategory() string {
	obj._SubCategoryMutex.RLock()
	defer obj._SubCategoryMutex.RUnlock()
	return obj._SubCategory
}

type IncidentTicketConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_TicketDescription      string
	_TicketDescriptionMutex *sync.RWMutex
	_TicketPriority         ticket.Priority
	_TicketGroup            ticket.Group
}

func (obj *IncidentTicketConfig) TicketDescription() string {
	obj._TicketDescriptionMutex.RLock()
	defer obj._TicketDescriptionMutex.RUnlock()
	return obj._TicketDescription
}
func (obj *IncidentTicketConfig) TicketPriority() ticket.Priority {
	return obj._TicketPriority
}
func (obj *IncidentTicketConfig) TicketGroup() ticket.Group {
	return obj._TicketGroup
}

type PinotIngestionDelay struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DelayDuration int64
}

func (obj *PinotIngestionDelay) DelayDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DelayDuration))
}

type FundTransferCelestialParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// param to check if fund transfer is enabled via celestial or not
	_IsFundTransferViaCelestialRestricted      uint32
	_AllowedUserGrpForFundTransferViaCelestial []common.UserGroup
	_Version                                   server.WorkflowVersion
}

// param to check if fund transfer is enabled via celestial or not
func (obj *FundTransferCelestialParams) IsFundTransferViaCelestialRestricted() bool {
	if atomic.LoadUint32(&obj._IsFundTransferViaCelestialRestricted) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FundTransferCelestialParams) AllowedUserGrpForFundTransferViaCelestial() []common.UserGroup {
	return obj._AllowedUserGrpForFundTransferViaCelestial
}
func (obj *FundTransferCelestialParams) Version() server.WorkflowVersion {
	return obj._Version
}

type VersionSupport struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// First app version to support SOF flow(connected account flow) in android
	_MinAndroidAppVersionToSupportSofCheck uint32
	// First app version to support SOF flow(connected account flow) in iOS
	_MinIOSAppVersionToSupportSofCheck uint32
	// First app version to support add new connected account flow in android
	_MinAndroidAppVersionToSupportAddCA uint32
	// First app version to support add new connected account flow in iOS
	_MinIOSAppVersionToSupportAddCA uint32
}

// First app version to support SOF flow(connected account flow) in android
func (obj *VersionSupport) MinAndroidAppVersionToSupportSofCheck() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinAndroidAppVersionToSupportSofCheck))
}

// First app version to support SOF flow(connected account flow) in iOS
func (obj *VersionSupport) MinIOSAppVersionToSupportSofCheck() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinIOSAppVersionToSupportSofCheck))
}

// First app version to support add new connected account flow in android
func (obj *VersionSupport) MinAndroidAppVersionToSupportAddCA() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinAndroidAppVersionToSupportAddCA))
}

// First app version to support add new connected account flow in iOS
func (obj *VersionSupport) MinIOSAppVersionToSupportAddCA() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinIOSAppVersionToSupportAddCA))
}

type TierConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Mapping of tiers to their icon URLs
	_TierToIconMap *syncmap.Map[string, string]
}

// Mapping of tiers to their icon URLs
func (obj *TierConfig) TierToIconMap() *syncmap.Map[string, string] {
	return obj._TierToIconMap
}

type PayOrderCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// this boolean will turn on/off cache layer
	_IsCachingEnabled uint32
	// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
	_UseCaseToCacheConfigMap *syncmap.Map[string, *CacheConfig]
	_RedisOptions            *cfg.RedisOptions
}

// this boolean will turn on/off cache layer
func (obj *PayOrderCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
func (obj *PayOrderCacheConfig) UseCaseToCacheConfigMap() *syncmap.Map[string, *CacheConfig] {
	return obj._UseCaseToCacheConfigMap
}
func (obj *PayOrderCacheConfig) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}

type CacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// switch to enable/disable cache
	_IsCachingEnabled uint32
	// duration for which cache is stored
	_CacheTTL int64
}

// switch to enable/disable cache
func (obj *CacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// duration for which cache is stored
func (obj *CacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}

type PayTransactionCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// this boolean will turn on/off cache layer
	_IsCachingEnabled uint32
	// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
	_UseCaseToCacheConfigMap *syncmap.Map[string, *CacheConfig]
	_RedisOptions            *cfg.RedisOptions
}

// this boolean will turn on/off cache layer
func (obj *PayTransactionCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// UseCaseToCacheConfigMap will have use case mapped to cache config (ie, IsCachingEnabled and CacheTTL)
func (obj *PayTransactionCacheConfig) UseCaseToCacheConfigMap() *syncmap.Map[string, *CacheConfig] {
	return obj._UseCaseToCacheConfigMap
}
func (obj *PayTransactionCacheConfig) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}

type PgParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Number of worker goroutines to use for enriching vendor payment/refund details in pgorderprocessor
	// This controls the number of concurrent vendor calls made to fetch the details for enrichment.
	_VendorDetailsEnrichmentNumWorkers int64
	// Controls the numbers of concurrent requests to enrich vendor payment/refund details,
	// that can be made in a given second.
	_VendorDetailsEnrichmentApiRatelimit int64
	// Controls whether PG mandate status need to be synced via webhooks
	_EnableMandateStatusUpdateViaConsumer           uint32
	_OneTimeFundTransferStatusWaitSignalTimeout     int64
	_RpExecutionFundTransferStatusWaitSignalTimeout int64
	// OneTimePaymentOrderExpirationDuration stores the duration till which a payment can be attempted on the order.
	// If no payment attempts are made on the order even after this duration, then we mark the internal order as EXPIRED.
	_OneTimePaymentOrderExpirationDuration         *syncmap.Map[string, time.Duration]
	_PaymentGatewayAdjustmentActorId               string
	_DummyPaymentGatewayAdjustmentActorToName      string
	_PGAdjustmentOwnershipToPiMapping              map[string]string
	_PaymentGatewayRefundGenericActorId            string
	_DummyPaymentGatewayRefundGenericActorFromName string
}

// Number of worker goroutines to use for enriching vendor payment/refund details in pgorderprocessor
// This controls the number of concurrent vendor calls made to fetch the details for enrichment.
func (obj *PgParams) VendorDetailsEnrichmentNumWorkers() int {
	return int(atomic.LoadInt64(&obj._VendorDetailsEnrichmentNumWorkers))
}

// Controls the numbers of concurrent requests to enrich vendor payment/refund details,
// that can be made in a given second.
func (obj *PgParams) VendorDetailsEnrichmentApiRatelimit() int {
	return int(atomic.LoadInt64(&obj._VendorDetailsEnrichmentApiRatelimit))
}

// Controls whether PG mandate status need to be synced via webhooks
func (obj *PgParams) EnableMandateStatusUpdateViaConsumer() bool {
	if atomic.LoadUint32(&obj._EnableMandateStatusUpdateViaConsumer) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *PgParams) OneTimeFundTransferStatusWaitSignalTimeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OneTimeFundTransferStatusWaitSignalTimeout))
}
func (obj *PgParams) RpExecutionFundTransferStatusWaitSignalTimeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RpExecutionFundTransferStatusWaitSignalTimeout))
}

// OneTimePaymentOrderExpirationDuration stores the duration till which a payment can be attempted on the order.
// If no payment attempts are made on the order even after this duration, then we mark the internal order as EXPIRED.
func (obj *PgParams) OneTimePaymentOrderExpirationDuration() *syncmap.Map[string, time.Duration] {
	return obj._OneTimePaymentOrderExpirationDuration
}
func (obj *PgParams) PaymentGatewayAdjustmentActorId() string {
	return obj._PaymentGatewayAdjustmentActorId
}
func (obj *PgParams) DummyPaymentGatewayAdjustmentActorToName() string {
	return obj._DummyPaymentGatewayAdjustmentActorToName
}
func (obj *PgParams) PGAdjustmentOwnershipToPiMapping() map[string]string {
	return obj._PGAdjustmentOwnershipToPiMapping
}
func (obj *PgParams) PaymentGatewayRefundGenericActorId() string {
	return obj._PaymentGatewayRefundGenericActorId
}
func (obj *PgParams) DummyPaymentGatewayRefundGenericActorFromName() string {
	return obj._DummyPaymentGatewayRefundGenericActorFromName
}

type FeedbackEngineCustomEvaluatorRule struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinSuccessCountThreshold int32
	_MinDebitAmount           int32
	_DurationForTxnEvaluation int64
}

func (obj *FeedbackEngineCustomEvaluatorRule) MinSuccessCountThreshold() int32 {
	return int32(atomic.LoadInt32(&obj._MinSuccessCountThreshold))
}
func (obj *FeedbackEngineCustomEvaluatorRule) MinDebitAmount() int32 {
	return int32(atomic.LoadInt32(&obj._MinDebitAmount))
}
func (obj *FeedbackEngineCustomEvaluatorRule) DurationForTxnEvaluation() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DurationForTxnEvaluation))
}

type EnrichOrderConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnrichmentFromDcSwitchConfig *EnrichmentFromDcSwitchConfig
}

func (obj *EnrichOrderConfig) EnrichmentFromDcSwitchConfig() *EnrichmentFromDcSwitchConfig {
	return obj._EnrichmentFromDcSwitchConfig
}

type EnrichmentFromDcSwitchConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_AllowedTxnTimeDeviationBuffer int64
}

func (obj *EnrichmentFromDcSwitchConfig) AllowedTxnTimeDeviationBuffer() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AllowedTxnTimeDeviationBuffer))
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["pagesizetofetchtxnforatmactor"] = _obj.SetPageSizeToFetchTxnForATMActor
	_setters["pagesizeforchargerelatedorderandtxn"] = _obj.SetPageSizeForChargeRelatedOrderAndTxn
	_setters["maximumactivedaysforautoidtickets"] = _obj.SetMaximumActiveDaysForAutoIdTickets
	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["enablesoflogfordebugging"] = _obj.SetEnableSOFLogForDebugging
	_setters["mindurationrequiredforuservintagecheck"] = _obj.SetMinDurationRequiredForUserVintageCheck

	_obj._FeedbackEngineCustomEvaluatorRules = &syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule]{}
	_setters["feedbackenginecustomevaluatorrules"] = _obj.SetFeedbackEngineCustomEvaluatorRules
	_setters["uniqueatmactorid"] = _obj.SetUniqueATMActorId
	_obj._UniqueATMActorIdMutex = &sync.RWMutex{}
	_FundTransferParams, _fieldSetters := NewFundTransferParams()
	_obj._FundTransferParams = _FundTransferParams
	helper.AddFieldSetters("fundtransferparams", _fieldSetters, _setters)
	_ExecutionReportGenerationParams, _fieldSetters := NewExecutionReportGenerationParams()
	_obj._ExecutionReportGenerationParams = _ExecutionReportGenerationParams
	helper.AddFieldSetters("executionreportgenerationparams", _fieldSetters, _setters)
	_InternationalFundTransfer, _fieldSetters := NewInternationalFundTransfer()
	_obj._InternationalFundTransfer = _InternationalFundTransfer
	helper.AddFieldSetters("internationalfundtransfer", _fieldSetters, _setters)
	_PayIncidentMgrOrderUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PayIncidentMgrOrderUpdateSubscriber = _PayIncidentMgrOrderUpdateSubscriber
	helper.AddFieldSetters("payincidentmgrorderupdatesubscriber", _fieldSetters, _setters)
	_TransactionDetailedStatusUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TransactionDetailedStatusUpdateSubscriber = _TransactionDetailedStatusUpdateSubscriber
	helper.AddFieldSetters("transactiondetailedstatusupdatesubscriber", _fieldSetters, _setters)
	_PayIncidentManager, _fieldSetters := NewPayIncidentManager()
	_obj._PayIncidentManager = _PayIncidentManager
	helper.AddFieldSetters("payincidentmanager", _fieldSetters, _setters)
	_PinotIngestionDelay, _fieldSetters := NewPinotIngestionDelay()
	_obj._PinotIngestionDelay = _PinotIngestionDelay
	helper.AddFieldSetters("pinotingestiondelay", _fieldSetters, _setters)
	_IFTProcessFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._IFTProcessFileSubscriber = _IFTProcessFileSubscriber
	helper.AddFieldSetters("iftprocessfilesubscriber", _fieldSetters, _setters)
	_FundTransferCelestialParams, _fieldSetters := NewFundTransferCelestialParams()
	_obj._FundTransferCelestialParams = _FundTransferCelestialParams
	helper.AddFieldSetters("fundtransfercelestialparams", _fieldSetters, _setters)
	_VersionSupport, _fieldSetters := NewVersionSupport()
	_obj._VersionSupport = _VersionSupport
	helper.AddFieldSetters("versionsupport", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_TierConfig, _fieldSetters := NewTierConfig()
	_obj._TierConfig = _TierConfig
	helper.AddFieldSetters("tierconfig", _fieldSetters, _setters)
	_OrderUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateSubscriber = _OrderUpdateSubscriber
	helper.AddFieldSetters("orderupdatesubscriber", _fieldSetters, _setters)
	_TpapEntryPointSwitchConfigForSaUser, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._TpapEntryPointSwitchConfigForSaUser = _TpapEntryPointSwitchConfigForSaUser
	helper.AddFieldSetters("tpapentrypointswitchconfigforsauser", _fieldSetters, _setters)
	_TpapEntryPointSwitchConfigForNonSaUser, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._TpapEntryPointSwitchConfigForNonSaUser = _TpapEntryPointSwitchConfigForNonSaUser
	helper.AddFieldSetters("tpapentrypointswitchconfigfornonsauser", _fieldSetters, _setters)
	_GenerateSofLimitStrategiesValuesSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._GenerateSofLimitStrategiesValuesSubscriber = _GenerateSofLimitStrategiesValuesSubscriber
	helper.AddFieldSetters("generatesoflimitstrategiesvaluessubscriber", _fieldSetters, _setters)
	_ProcessPaymentGatewayWebhookEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessPaymentGatewayWebhookEventSubscriber = _ProcessPaymentGatewayWebhookEventSubscriber
	helper.AddFieldSetters("processpaymentgatewaywebhookeventsubscriber", _fieldSetters, _setters)
	_PayOrderCacheConfig, _fieldSetters := NewPayOrderCacheConfig()
	_obj._PayOrderCacheConfig = _PayOrderCacheConfig
	helper.AddFieldSetters("payordercacheconfig", _fieldSetters, _setters)
	_PayTransactionCacheConfig, _fieldSetters := NewPayTransactionCacheConfig()
	_obj._PayTransactionCacheConfig = _PayTransactionCacheConfig
	helper.AddFieldSetters("paytransactioncacheconfig", _fieldSetters, _setters)
	_PgParams, _fieldSetters := NewPgParams()
	_obj._PgParams = _PgParams
	helper.AddFieldSetters("pgparams", _fieldSetters, _setters)
	_BaseRuleEngineConfig, _fieldSetters := genconfig2.NewRuleEngineConfig()
	_obj._BaseRuleEngineConfig = _BaseRuleEngineConfig
	helper.AddFieldSetters("baseruleengineconfig", _fieldSetters, _setters)
	_EnrichOrderConfig, _fieldSetters := NewEnrichOrderConfig()
	_obj._EnrichOrderConfig = _EnrichOrderConfig
	helper.AddFieldSetters("enrichorderconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["pagesizetofetchtxnforatmactor"] = _obj.SetPageSizeToFetchTxnForATMActor
	_setters["pagesizeforchargerelatedorderandtxn"] = _obj.SetPageSizeForChargeRelatedOrderAndTxn
	_setters["maximumactivedaysforautoidtickets"] = _obj.SetMaximumActiveDaysForAutoIdTickets
	_setters["enableentitysegregation"] = _obj.SetEnableEntitySegregation
	_setters["enablesoflogfordebugging"] = _obj.SetEnableSOFLogForDebugging
	_setters["mindurationrequiredforuservintagecheck"] = _obj.SetMinDurationRequiredForUserVintageCheck

	_obj._FeedbackEngineCustomEvaluatorRules = &syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule]{}
	_setters["feedbackenginecustomevaluatorrules"] = _obj.SetFeedbackEngineCustomEvaluatorRules
	_setters["uniqueatmactorid"] = _obj.SetUniqueATMActorId
	_obj._UniqueATMActorIdMutex = &sync.RWMutex{}
	_FundTransferParams, _fieldSetters := NewFundTransferParams()
	_obj._FundTransferParams = _FundTransferParams
	helper.AddFieldSetters("fundtransferparams", _fieldSetters, _setters)
	_ExecutionReportGenerationParams, _fieldSetters := NewExecutionReportGenerationParams()
	_obj._ExecutionReportGenerationParams = _ExecutionReportGenerationParams
	helper.AddFieldSetters("executionreportgenerationparams", _fieldSetters, _setters)
	_InternationalFundTransfer, _fieldSetters := NewInternationalFundTransfer()
	_obj._InternationalFundTransfer = _InternationalFundTransfer
	helper.AddFieldSetters("internationalfundtransfer", _fieldSetters, _setters)
	_PayIncidentMgrOrderUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PayIncidentMgrOrderUpdateSubscriber = _PayIncidentMgrOrderUpdateSubscriber
	helper.AddFieldSetters("payincidentmgrorderupdatesubscriber", _fieldSetters, _setters)
	_TransactionDetailedStatusUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TransactionDetailedStatusUpdateSubscriber = _TransactionDetailedStatusUpdateSubscriber
	helper.AddFieldSetters("transactiondetailedstatusupdatesubscriber", _fieldSetters, _setters)
	_PayIncidentManager, _fieldSetters := NewPayIncidentManager()
	_obj._PayIncidentManager = _PayIncidentManager
	helper.AddFieldSetters("payincidentmanager", _fieldSetters, _setters)
	_PinotIngestionDelay, _fieldSetters := NewPinotIngestionDelay()
	_obj._PinotIngestionDelay = _PinotIngestionDelay
	helper.AddFieldSetters("pinotingestiondelay", _fieldSetters, _setters)
	_IFTProcessFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._IFTProcessFileSubscriber = _IFTProcessFileSubscriber
	helper.AddFieldSetters("iftprocessfilesubscriber", _fieldSetters, _setters)
	_FundTransferCelestialParams, _fieldSetters := NewFundTransferCelestialParams()
	_obj._FundTransferCelestialParams = _FundTransferCelestialParams
	helper.AddFieldSetters("fundtransfercelestialparams", _fieldSetters, _setters)
	_VersionSupport, _fieldSetters := NewVersionSupport()
	_obj._VersionSupport = _VersionSupport
	helper.AddFieldSetters("versionsupport", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_TierConfig, _fieldSetters := NewTierConfig()
	_obj._TierConfig = _TierConfig
	helper.AddFieldSetters("tierconfig", _fieldSetters, _setters)
	_OrderUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateSubscriber = _OrderUpdateSubscriber
	helper.AddFieldSetters("orderupdatesubscriber", _fieldSetters, _setters)
	_TpapEntryPointSwitchConfigForSaUser, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._TpapEntryPointSwitchConfigForSaUser = _TpapEntryPointSwitchConfigForSaUser
	helper.AddFieldSetters("tpapentrypointswitchconfigforsauser", _fieldSetters, _setters)
	_TpapEntryPointSwitchConfigForNonSaUser, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._TpapEntryPointSwitchConfigForNonSaUser = _TpapEntryPointSwitchConfigForNonSaUser
	helper.AddFieldSetters("tpapentrypointswitchconfigfornonsauser", _fieldSetters, _setters)
	_GenerateSofLimitStrategiesValuesSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._GenerateSofLimitStrategiesValuesSubscriber = _GenerateSofLimitStrategiesValuesSubscriber
	helper.AddFieldSetters("generatesoflimitstrategiesvaluessubscriber", _fieldSetters, _setters)
	_ProcessPaymentGatewayWebhookEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessPaymentGatewayWebhookEventSubscriber = _ProcessPaymentGatewayWebhookEventSubscriber
	helper.AddFieldSetters("processpaymentgatewaywebhookeventsubscriber", _fieldSetters, _setters)
	_PayOrderCacheConfig, _fieldSetters := NewPayOrderCacheConfig()
	_obj._PayOrderCacheConfig = _PayOrderCacheConfig
	helper.AddFieldSetters("payordercacheconfig", _fieldSetters, _setters)
	_PayTransactionCacheConfig, _fieldSetters := NewPayTransactionCacheConfig()
	_obj._PayTransactionCacheConfig = _PayTransactionCacheConfig
	helper.AddFieldSetters("paytransactioncacheconfig", _fieldSetters, _setters)
	_PgParams, _fieldSetters := NewPgParams()
	_obj._PgParams = _PgParams
	helper.AddFieldSetters("pgparams", _fieldSetters, _setters)
	_BaseRuleEngineConfig, _fieldSetters := genconfig2.NewRuleEngineConfig()
	_obj._BaseRuleEngineConfig = _BaseRuleEngineConfig
	helper.AddFieldSetters("baseruleengineconfig", _fieldSetters, _setters)
	_EnrichOrderConfig, _fieldSetters := NewEnrichOrderConfig()
	_obj._EnrichOrderConfig = _EnrichOrderConfig
	helper.AddFieldSetters("enrichorderconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *server.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "pagesizetofetchtxnforatmactor":
		return obj.SetPageSizeToFetchTxnForATMActor(v.PageSizeToFetchTxnForATMActor, true, nil)
	case "pagesizeforchargerelatedorderandtxn":
		return obj.SetPageSizeForChargeRelatedOrderAndTxn(v.PageSizeForChargeRelatedOrderAndTxn, true, nil)
	case "maximumactivedaysforautoidtickets":
		return obj.SetMaximumActiveDaysForAutoIdTickets(v.MaximumActiveDaysForAutoIdTickets, true, nil)
	case "enableentitysegregation":
		return obj.SetEnableEntitySegregation(v.EnableEntitySegregation, true, nil)
	case "enablesoflogfordebugging":
		return obj.SetEnableSOFLogForDebugging(v.EnableSOFLogForDebugging, true, nil)
	case "mindurationrequiredforuservintagecheck":
		return obj.SetMinDurationRequiredForUserVintageCheck(v.MinDurationRequiredForUserVintageCheck, true, nil)
	case "feedbackenginecustomevaluatorrules":
		return obj.SetFeedbackEngineCustomEvaluatorRules(v.FeedbackEngineCustomEvaluatorRules, true, path)
	case "uniqueatmactorid":
		return obj.SetUniqueATMActorId(v.UniqueATMActorId, true, nil)
	case "fundtransferparams":
		return obj._FundTransferParams.Set(v.FundTransferParams, true, path)
	case "executionreportgenerationparams":
		return obj._ExecutionReportGenerationParams.Set(v.ExecutionReportGenerationParams, true, path)
	case "internationalfundtransfer":
		return obj._InternationalFundTransfer.Set(v.InternationalFundTransfer, true, path)
	case "payincidentmgrorderupdatesubscriber":
		return obj._PayIncidentMgrOrderUpdateSubscriber.Set(v.PayIncidentMgrOrderUpdateSubscriber, true, path)
	case "transactiondetailedstatusupdatesubscriber":
		return obj._TransactionDetailedStatusUpdateSubscriber.Set(v.TransactionDetailedStatusUpdateSubscriber, true, path)
	case "payincidentmanager":
		return obj._PayIncidentManager.Set(v.PayIncidentManager, true, path)
	case "pinotingestiondelay":
		return obj._PinotIngestionDelay.Set(v.PinotIngestionDelay, true, path)
	case "iftprocessfilesubscriber":
		return obj._IFTProcessFileSubscriber.Set(v.IFTProcessFileSubscriber, true, path)
	case "fundtransfercelestialparams":
		return obj._FundTransferCelestialParams.Set(v.FundTransferCelestialParams, true, path)
	case "versionsupport":
		return obj._VersionSupport.Set(v.VersionSupport, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "tierconfig":
		return obj._TierConfig.Set(v.TierConfig, true, path)
	case "orderupdatesubscriber":
		return obj._OrderUpdateSubscriber.Set(v.OrderUpdateSubscriber, true, path)
	case "tpapentrypointswitchconfigforsauser":
		return obj._TpapEntryPointSwitchConfigForSaUser.Set(v.TpapEntryPointSwitchConfigForSaUser, true, path)
	case "tpapentrypointswitchconfigfornonsauser":
		return obj._TpapEntryPointSwitchConfigForNonSaUser.Set(v.TpapEntryPointSwitchConfigForNonSaUser, true, path)
	case "generatesoflimitstrategiesvaluessubscriber":
		return obj._GenerateSofLimitStrategiesValuesSubscriber.Set(v.GenerateSofLimitStrategiesValuesSubscriber, true, path)
	case "processpaymentgatewaywebhookeventsubscriber":
		return obj._ProcessPaymentGatewayWebhookEventSubscriber.Set(v.ProcessPaymentGatewayWebhookEventSubscriber, true, path)
	case "payordercacheconfig":
		return obj._PayOrderCacheConfig.Set(v.PayOrderCacheConfig, true, path)
	case "paytransactioncacheconfig":
		return obj._PayTransactionCacheConfig.Set(v.PayTransactionCacheConfig, true, path)
	case "pgparams":
		return obj._PgParams.Set(v.PgParams, true, path)
	case "baseruleengineconfig":
		return obj._BaseRuleEngineConfig.Set(v.BaseRuleEngineConfig, true, path)
	case "enrichorderconfig":
		return obj._EnrichOrderConfig.Set(v.EnrichOrderConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *server.Config, dynamic bool, path []string) (err error) {

	err = obj.SetPageSizeToFetchTxnForATMActor(v.PageSizeToFetchTxnForATMActor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPageSizeForChargeRelatedOrderAndTxn(v.PageSizeForChargeRelatedOrderAndTxn, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaximumActiveDaysForAutoIdTickets(v.MaximumActiveDaysForAutoIdTickets, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableEntitySegregation(v.EnableEntitySegregation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSOFLogForDebugging(v.EnableSOFLogForDebugging, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinDurationRequiredForUserVintageCheck(v.MinDurationRequiredForUserVintageCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFeedbackEngineCustomEvaluatorRules(v.FeedbackEngineCustomEvaluatorRules, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetUniqueATMActorId(v.UniqueATMActorId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._FundTransferParams.Set(v.FundTransferParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ExecutionReportGenerationParams.Set(v.ExecutionReportGenerationParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InternationalFundTransfer.Set(v.InternationalFundTransfer, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayIncidentMgrOrderUpdateSubscriber.Set(v.PayIncidentMgrOrderUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TransactionDetailedStatusUpdateSubscriber.Set(v.TransactionDetailedStatusUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayIncidentManager.Set(v.PayIncidentManager, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PinotIngestionDelay.Set(v.PinotIngestionDelay, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IFTProcessFileSubscriber.Set(v.IFTProcessFileSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FundTransferCelestialParams.Set(v.FundTransferCelestialParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VersionSupport.Set(v.VersionSupport, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TierConfig.Set(v.TierConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderUpdateSubscriber.Set(v.OrderUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TpapEntryPointSwitchConfigForSaUser.Set(v.TpapEntryPointSwitchConfigForSaUser, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TpapEntryPointSwitchConfigForNonSaUser.Set(v.TpapEntryPointSwitchConfigForNonSaUser, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._GenerateSofLimitStrategiesValuesSubscriber.Set(v.GenerateSofLimitStrategiesValuesSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessPaymentGatewayWebhookEventSubscriber.Set(v.ProcessPaymentGatewayWebhookEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayOrderCacheConfig.Set(v.PayOrderCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayTransactionCacheConfig.Set(v.PayTransactionCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PgParams.Set(v.PgParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BaseRuleEngineConfig.Set(v.BaseRuleEngineConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnrichOrderConfig.Set(v.EnrichOrderConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *server.Config) error {

	obj._Application = v.Application
	obj._RazorPayResponseCodesJson = v.RazorPayResponseCodesJson
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._EpifiDb = v.EpifiDb
	obj._RedisOptions = v.RedisOptions
	obj._AWS = v.AWS
	obj._Secrets = v.Secrets
	obj._PaymentEnquiryParams = v.PaymentEnquiryParams
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._VelocityRuleThresholdsMap = v.VelocityRuleThresholdsMap
	obj._VelocityRuleAmountRangeMap = v.VelocityRuleAmountRangeMap
	obj._RulesToRuleGroupMap = v.RulesToRuleGroupMap
	obj._InPaymentOrderUpdatePublisher = v.InPaymentOrderUpdatePublisher
	obj._NonTpapPspHandles = v.NonTpapPspHandles
	obj._VendorToNameMap = v.VendorToNameMap
	obj._IFTRemittanceFileProcessingEventPublisher = v.IFTRemittanceFileProcessingEventPublisher
	obj._PostPaymentBanners = v.PostPaymentBanners
	obj._PayLandingScreenBanners = v.PayLandingScreenBanners
	obj._QrScreenElements = v.QrScreenElements
	obj._TxnBackfillBucketName = v.TxnBackfillBucketName
	obj._IncidentManagerParams = v.IncidentManagerParams
	obj._PayDb = v.PayDb
	obj._GrpcRatelimiterParams = v.GrpcRatelimiterParams
	obj._Tracing = v.Tracing
	obj._GenerateSofLimitStrategiesValuesPublisher = v.GenerateSofLimitStrategiesValuesPublisher
	obj._PennyDropConfig = v.PennyDropConfig
	obj._TransactionStatusDetailsCombinationsForDebitAndFailedIncident = v.TransactionStatusDetailsCombinationsForDebitAndFailedIncident
	obj._PgProgramToAuthSecretMap = v.PgProgramToAuthSecretMap
	obj._PayloadSigningCreds = v.PayloadSigningCreds
	obj._OrderUpdateEventPublisher = v.OrderUpdateEventPublisher
	return nil
}

func (obj *Config) SetPageSizeToFetchTxnForATMActor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.PageSizeToFetchTxnForATMActor", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._PageSizeToFetchTxnForATMActor, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PageSizeToFetchTxnForATMActor")
	}
	return nil
}
func (obj *Config) SetPageSizeForChargeRelatedOrderAndTxn(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.PageSizeForChargeRelatedOrderAndTxn", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._PageSizeForChargeRelatedOrderAndTxn, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PageSizeForChargeRelatedOrderAndTxn")
	}
	return nil
}
func (obj *Config) SetMaximumActiveDaysForAutoIdTickets(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MaximumActiveDaysForAutoIdTickets", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaximumActiveDaysForAutoIdTickets, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaximumActiveDaysForAutoIdTickets")
	}
	return nil
}
func (obj *Config) SetEnableEntitySegregation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableEntitySegregation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEntitySegregation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEntitySegregation")
	}
	return nil
}
func (obj *Config) SetEnableSOFLogForDebugging(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableSOFLogForDebugging", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSOFLogForDebugging, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSOFLogForDebugging, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSOFLogForDebugging")
	}
	return nil
}
func (obj *Config) SetMinDurationRequiredForUserVintageCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MinDurationRequiredForUserVintageCheck", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinDurationRequiredForUserVintageCheck, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinDurationRequiredForUserVintageCheck")
	}
	return nil
}
func (obj *Config) SetFeedbackEngineCustomEvaluatorRules(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*server.FeedbackEngineCustomEvaluatorRule)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.FeedbackEngineCustomEvaluatorRules", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._FeedbackEngineCustomEvaluatorRules, v, dynamic, path)

}
func (obj *Config) SetUniqueATMActorId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.UniqueATMActorId", reflect.TypeOf(val))
	}
	obj._UniqueATMActorIdMutex.Lock()
	defer obj._UniqueATMActorIdMutex.Unlock()
	obj._UniqueATMActorId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "UniqueATMActorId")
	}
	return nil
}

func NewFundTransferParams() (_obj *FundTransferParams, _setters map[string]dynconf.SetFunc) {
	_obj = &FundTransferParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["defaultfundtransferexpiryduration"] = _obj.SetDefaultFundTransferExpiryDuration
	return _obj, _setters
}

func (obj *FundTransferParams) Init() {
	newObj, _ := NewFundTransferParams()
	*obj = *newObj
}

func (obj *FundTransferParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FundTransferParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.FundTransferParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *FundTransferParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FundTransferParams) setDynamicField(v *server.FundTransferParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "defaultfundtransferexpiryduration":
		return obj.SetDefaultFundTransferExpiryDuration(v.DefaultFundTransferExpiryDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FundTransferParams) setDynamicFields(v *server.FundTransferParams, dynamic bool, path []string) (err error) {

	err = obj.SetDefaultFundTransferExpiryDuration(v.DefaultFundTransferExpiryDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FundTransferParams) setStaticFields(v *server.FundTransferParams) error {

	obj._HardPreferredPaymentProtocol = v.HardPreferredPaymentProtocol
	obj._SMSTypeToOptionVersionMap = v.SMSTypeToOptionVersionMap
	obj._PaymentNotificationParams = v.PaymentNotificationParams
	return nil
}

func (obj *FundTransferParams) SetDefaultFundTransferExpiryDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *FundTransferParams.DefaultFundTransferExpiryDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DefaultFundTransferExpiryDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultFundTransferExpiryDuration")
	}
	return nil
}

func NewExecutionReportGenerationParams() (_obj *ExecutionReportGenerationParams, _setters map[string]dynconf.SetFunc) {
	_obj = &ExecutionReportGenerationParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["reportstalenessduration"] = _obj.SetReportStalenessDuration
	return _obj, _setters
}

func (obj *ExecutionReportGenerationParams) Init() {
	newObj, _ := NewExecutionReportGenerationParams()
	*obj = *newObj
}

func (obj *ExecutionReportGenerationParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ExecutionReportGenerationParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.ExecutionReportGenerationParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExecutionReportGenerationParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ExecutionReportGenerationParams) setDynamicField(v *server.ExecutionReportGenerationParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "reportstalenessduration":
		return obj.SetReportStalenessDuration(v.ReportStalenessDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ExecutionReportGenerationParams) setDynamicFields(v *server.ExecutionReportGenerationParams, dynamic bool, path []string) (err error) {

	err = obj.SetReportStalenessDuration(v.ReportStalenessDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ExecutionReportGenerationParams) setStaticFields(v *server.ExecutionReportGenerationParams) error {

	return nil
}

func (obj *ExecutionReportGenerationParams) SetReportStalenessDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExecutionReportGenerationParams.ReportStalenessDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ReportStalenessDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReportStalenessDuration")
	}
	return nil
}

func NewInternationalFundTransfer() (_obj *InternationalFundTransfer, _setters map[string]dynconf.SetFunc) {
	_obj = &InternationalFundTransfer{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["generateswiftreportretry"] = _obj.SetGenerateSwiftReportRetry
	_setters["generateswiftreportsleep"] = _obj.SetGenerateSwiftReportSleep
	_setters["maxlrspagesize"] = _obj.SetMaxLRSPageSize
	_setters["accountvintagetxncount"] = _obj.SetAccountVintageTxnCount
	_setters["skipsofdocumentgenerationstage"] = _obj.SetSkipSofDocumentGenerationStage
	_setters["skiporderfulfilmentstage"] = _obj.SetSkipOrderFulfilmentStage
	_setters["enablefederalsherlock"] = _obj.SetEnableFederalSherlock
	_setters["enablelrscheckfromvendor"] = _obj.SetEnableLRSCheckFromVendor
	_setters["skipprecheckforswiftfilegen"] = _obj.SetSkipPreCheckForSwiftFileGen
	_setters["issofbasedremittancelimitcheckenabled"] = _obj.SetIsSofBasedRemittanceLimitCheckEnabled
	_setters["generatelrscheckfileusingcutofftime"] = _obj.SetGenerateLrsCheckFileUsingCutOffTime
	_setters["enablesofstatecolumninswiftfileflag"] = _obj.SetEnableSofStateColumnInSwiftFileFlag
	_setters["enablemintxncountcheckforvintageaccounts"] = _obj.SetEnableMinTxnCountCheckForVintageAccounts
	_setters["enableupdatelrslimitsworkflowflag"] = _obj.SetEnableUpdateLrsLimitsWorkflowFlag
	_setters["accountvintagecheckduration"] = _obj.SetAccountVintageCheckDuration
	_setters["durationwaitforswiftfilegen"] = _obj.SetDurationWaitForSwiftFileGen
	_setters["authfactorupdatecooloffperiod"] = _obj.SetAuthFactorUpdateCoolOffPeriod
	_setters["poolinwardaccountpi"] = _obj.SetPoolInwardAccountPI
	_obj._PoolInwardAccountPIMutex = &sync.RWMutex{}
	_setters["usstocksvendorpi"] = _obj.SetUsStocksVendorPI
	_obj._UsStocksVendorPIMutex = &sync.RWMutex{}
	_setters["sherlockhost"] = _obj.SetSherlockHost
	_obj._SherlockHostMutex = &sync.RWMutex{}
	_setters["federalsherlockhost"] = _obj.SetFederalSherlockHost
	_obj._FederalSherlockHostMutex = &sync.RWMutex{}
	_setters["sherlockpath"] = _obj.SetSherlockPath
	_obj._SherlockPathMutex = &sync.RWMutex{}
	_setters["nodataexistforpancode"] = _obj.SetNoDataExistForPANCode
	_obj._NoDataExistForPANCodeMutex = &sync.RWMutex{}
	_ForexRate, _fieldSetters := NewInternationalFundTransferForexRate()
	_obj._ForexRate = _ForexRate
	helper.AddFieldSetters("forexrate", _fieldSetters, _setters)
	_TransactionAmountConstraint, _fieldSetters := NewTransactionAmountConstraint()
	_obj._TransactionAmountConstraint = _TransactionAmountConstraint
	helper.AddFieldSetters("transactionamountconstraint", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *InternationalFundTransfer) Init() {
	newObj, _ := NewInternationalFundTransfer()
	*obj = *newObj
}

func (obj *InternationalFundTransfer) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InternationalFundTransfer) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.InternationalFundTransfer)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InternationalFundTransfer) setDynamicField(v *server.InternationalFundTransfer, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "generateswiftreportretry":
		return obj.SetGenerateSwiftReportRetry(v.GenerateSwiftReportRetry, true, nil)
	case "generateswiftreportsleep":
		return obj.SetGenerateSwiftReportSleep(v.GenerateSwiftReportSleep, true, nil)
	case "maxlrspagesize":
		return obj.SetMaxLRSPageSize(v.MaxLRSPageSize, true, nil)
	case "accountvintagetxncount":
		return obj.SetAccountVintageTxnCount(v.AccountVintageTxnCount, true, nil)
	case "skipsofdocumentgenerationstage":
		return obj.SetSkipSofDocumentGenerationStage(v.SkipSofDocumentGenerationStage, true, nil)
	case "skiporderfulfilmentstage":
		return obj.SetSkipOrderFulfilmentStage(v.SkipOrderFulfilmentStage, true, nil)
	case "enablefederalsherlock":
		return obj.SetEnableFederalSherlock(v.EnableFederalSherlock, true, nil)
	case "enablelrscheckfromvendor":
		return obj.SetEnableLRSCheckFromVendor(v.EnableLRSCheckFromVendor, true, nil)
	case "skipprecheckforswiftfilegen":
		return obj.SetSkipPreCheckForSwiftFileGen(v.SkipPreCheckForSwiftFileGen, true, nil)
	case "issofbasedremittancelimitcheckenabled":
		return obj.SetIsSofBasedRemittanceLimitCheckEnabled(v.IsSofBasedRemittanceLimitCheckEnabled, true, nil)
	case "generatelrscheckfileusingcutofftime":
		return obj.SetGenerateLrsCheckFileUsingCutOffTime(v.GenerateLrsCheckFileUsingCutOffTime, true, nil)
	case "enablesofstatecolumninswiftfileflag":
		return obj.SetEnableSofStateColumnInSwiftFileFlag(v.EnableSofStateColumnInSwiftFileFlag, true, nil)
	case "enablemintxncountcheckforvintageaccounts":
		return obj.SetEnableMinTxnCountCheckForVintageAccounts(v.EnableMinTxnCountCheckForVintageAccounts, true, nil)
	case "enableupdatelrslimitsworkflowflag":
		return obj.SetEnableUpdateLrsLimitsWorkflowFlag(v.EnableUpdateLrsLimitsWorkflowFlag, true, nil)
	case "accountvintagecheckduration":
		return obj.SetAccountVintageCheckDuration(v.AccountVintageCheckDuration, true, nil)
	case "durationwaitforswiftfilegen":
		return obj.SetDurationWaitForSwiftFileGen(v.DurationWaitForSwiftFileGen, true, nil)
	case "authfactorupdatecooloffperiod":
		return obj.SetAuthFactorUpdateCoolOffPeriod(v.AuthFactorUpdateCoolOffPeriod, true, nil)
	case "poolinwardaccountpi":
		return obj.SetPoolInwardAccountPI(v.PoolInwardAccountPI, true, nil)
	case "usstocksvendorpi":
		return obj.SetUsStocksVendorPI(v.UsStocksVendorPI, true, nil)
	case "sherlockhost":
		return obj.SetSherlockHost(v.SherlockHost, true, nil)
	case "federalsherlockhost":
		return obj.SetFederalSherlockHost(v.FederalSherlockHost, true, nil)
	case "sherlockpath":
		return obj.SetSherlockPath(v.SherlockPath, true, nil)
	case "nodataexistforpancode":
		return obj.SetNoDataExistForPANCode(v.NoDataExistForPANCode, true, nil)
	case "forexrate":
		return obj._ForexRate.Set(v.ForexRate, true, path)
	case "transactionamountconstraint":
		return obj._TransactionAmountConstraint.Set(v.TransactionAmountConstraint, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InternationalFundTransfer) setDynamicFields(v *server.InternationalFundTransfer, dynamic bool, path []string) (err error) {

	err = obj.SetGenerateSwiftReportRetry(v.GenerateSwiftReportRetry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGenerateSwiftReportSleep(v.GenerateSwiftReportSleep, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxLRSPageSize(v.MaxLRSPageSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccountVintageTxnCount(v.AccountVintageTxnCount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipSofDocumentGenerationStage(v.SkipSofDocumentGenerationStage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipOrderFulfilmentStage(v.SkipOrderFulfilmentStage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableFederalSherlock(v.EnableFederalSherlock, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableLRSCheckFromVendor(v.EnableLRSCheckFromVendor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipPreCheckForSwiftFileGen(v.SkipPreCheckForSwiftFileGen, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsSofBasedRemittanceLimitCheckEnabled(v.IsSofBasedRemittanceLimitCheckEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGenerateLrsCheckFileUsingCutOffTime(v.GenerateLrsCheckFileUsingCutOffTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSofStateColumnInSwiftFileFlag(v.EnableSofStateColumnInSwiftFileFlag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableMinTxnCountCheckForVintageAccounts(v.EnableMinTxnCountCheckForVintageAccounts, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableUpdateLrsLimitsWorkflowFlag(v.EnableUpdateLrsLimitsWorkflowFlag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccountVintageCheckDuration(v.AccountVintageCheckDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDurationWaitForSwiftFileGen(v.DurationWaitForSwiftFileGen, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAuthFactorUpdateCoolOffPeriod(v.AuthFactorUpdateCoolOffPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPoolInwardAccountPI(v.PoolInwardAccountPI, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUsStocksVendorPI(v.UsStocksVendorPI, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSherlockHost(v.SherlockHost, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFederalSherlockHost(v.FederalSherlockHost, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSherlockPath(v.SherlockPath, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNoDataExistForPANCode(v.NoDataExistForPANCode, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ForexRate.Set(v.ForexRate, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TransactionAmountConstraint.Set(v.TransactionAmountConstraint, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InternationalFundTransfer) setStaticFields(v *server.InternationalFundTransfer) error {

	obj._S3Bucket = v.S3Bucket
	obj._GstReportingInfo = v.GstReportingInfo
	obj._MaxPageSize = v.MaxPageSize
	obj._DailyAggregateLimit = v.DailyAggregateLimit
	obj._AnnualAggregateLimit = v.AnnualAggregateLimit
	obj._IgstAccountNumber = v.IgstAccountNumber
	obj._IgstAccountSolId = v.IgstAccountSolId
	obj._CgstAccountNumber = v.CgstAccountNumber
	obj._CgstAccountSolId = v.CgstAccountSolId
	obj._SgstAccountNumber = v.SgstAccountNumber
	obj._SgstAccountSolId = v.SgstAccountSolId
	obj._TCSAccountNumber = v.TCSAccountNumber
	obj._TCSAccountSolId = v.TCSAccountSolId
	obj._OutwardPoolAccountSolId = v.OutwardPoolAccountSolId
	obj._OutwardPoolAccount = v.OutwardPoolAccount
	obj._ForexRateIdPrecision = v.ForexRateIdPrecision
	obj._NostroBankSwiftCode = v.NostroBankSwiftCode
	obj._SpecialInstructionForUSStocks = v.SpecialInstructionForUSStocks
	obj._ForexRateVendorAPIMaximumConsumptionAmountInUSD = v.ForexRateVendorAPIMaximumConsumptionAmountInUSD
	return nil
}

func (obj *InternationalFundTransfer) SetGenerateSwiftReportRetry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.GenerateSwiftReportRetry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._GenerateSwiftReportRetry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "GenerateSwiftReportRetry")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetGenerateSwiftReportSleep(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.GenerateSwiftReportSleep", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._GenerateSwiftReportSleep, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "GenerateSwiftReportSleep")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetMaxLRSPageSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.MaxLRSPageSize", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxLRSPageSize, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxLRSPageSize")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetAccountVintageTxnCount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.AccountVintageTxnCount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AccountVintageTxnCount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountVintageTxnCount")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetSkipSofDocumentGenerationStage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.SkipSofDocumentGenerationStage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipSofDocumentGenerationStage, 1)
	} else {
		atomic.StoreUint32(&obj._SkipSofDocumentGenerationStage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipSofDocumentGenerationStage")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetSkipOrderFulfilmentStage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.SkipOrderFulfilmentStage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipOrderFulfilmentStage, 1)
	} else {
		atomic.StoreUint32(&obj._SkipOrderFulfilmentStage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipOrderFulfilmentStage")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetEnableFederalSherlock(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableFederalSherlock", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableFederalSherlock, 1)
	} else {
		atomic.StoreUint32(&obj._EnableFederalSherlock, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableFederalSherlock")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetEnableLRSCheckFromVendor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableLRSCheckFromVendor", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableLRSCheckFromVendor, 1)
	} else {
		atomic.StoreUint32(&obj._EnableLRSCheckFromVendor, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableLRSCheckFromVendor")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetSkipPreCheckForSwiftFileGen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.SkipPreCheckForSwiftFileGen", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipPreCheckForSwiftFileGen, 1)
	} else {
		atomic.StoreUint32(&obj._SkipPreCheckForSwiftFileGen, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipPreCheckForSwiftFileGen")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetIsSofBasedRemittanceLimitCheckEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.IsSofBasedRemittanceLimitCheckEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsSofBasedRemittanceLimitCheckEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsSofBasedRemittanceLimitCheckEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsSofBasedRemittanceLimitCheckEnabled")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetGenerateLrsCheckFileUsingCutOffTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.GenerateLrsCheckFileUsingCutOffTime", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._GenerateLrsCheckFileUsingCutOffTime, 1)
	} else {
		atomic.StoreUint32(&obj._GenerateLrsCheckFileUsingCutOffTime, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "GenerateLrsCheckFileUsingCutOffTime")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetEnableSofStateColumnInSwiftFileFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableSofStateColumnInSwiftFileFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSofStateColumnInSwiftFileFlag, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSofStateColumnInSwiftFileFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSofStateColumnInSwiftFileFlag")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetEnableMinTxnCountCheckForVintageAccounts(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableMinTxnCountCheckForVintageAccounts", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableMinTxnCountCheckForVintageAccounts, 1)
	} else {
		atomic.StoreUint32(&obj._EnableMinTxnCountCheckForVintageAccounts, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableMinTxnCountCheckForVintageAccounts")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetEnableUpdateLrsLimitsWorkflowFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableUpdateLrsLimitsWorkflowFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableUpdateLrsLimitsWorkflowFlag, 1)
	} else {
		atomic.StoreUint32(&obj._EnableUpdateLrsLimitsWorkflowFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableUpdateLrsLimitsWorkflowFlag")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetAccountVintageCheckDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.AccountVintageCheckDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AccountVintageCheckDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountVintageCheckDuration")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetDurationWaitForSwiftFileGen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.DurationWaitForSwiftFileGen", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DurationWaitForSwiftFileGen, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DurationWaitForSwiftFileGen")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetAuthFactorUpdateCoolOffPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.AuthFactorUpdateCoolOffPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AuthFactorUpdateCoolOffPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AuthFactorUpdateCoolOffPeriod")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetPoolInwardAccountPI(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.PoolInwardAccountPI", reflect.TypeOf(val))
	}
	obj._PoolInwardAccountPIMutex.Lock()
	defer obj._PoolInwardAccountPIMutex.Unlock()
	obj._PoolInwardAccountPI = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PoolInwardAccountPI")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetUsStocksVendorPI(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.UsStocksVendorPI", reflect.TypeOf(val))
	}
	obj._UsStocksVendorPIMutex.Lock()
	defer obj._UsStocksVendorPIMutex.Unlock()
	obj._UsStocksVendorPI = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "UsStocksVendorPI")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetSherlockHost(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.SherlockHost", reflect.TypeOf(val))
	}
	obj._SherlockHostMutex.Lock()
	defer obj._SherlockHostMutex.Unlock()
	obj._SherlockHost = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SherlockHost")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetFederalSherlockHost(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.FederalSherlockHost", reflect.TypeOf(val))
	}
	obj._FederalSherlockHostMutex.Lock()
	defer obj._FederalSherlockHostMutex.Unlock()
	obj._FederalSherlockHost = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FederalSherlockHost")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetSherlockPath(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.SherlockPath", reflect.TypeOf(val))
	}
	obj._SherlockPathMutex.Lock()
	defer obj._SherlockPathMutex.Unlock()
	obj._SherlockPath = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SherlockPath")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetNoDataExistForPANCode(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.NoDataExistForPANCode", reflect.TypeOf(val))
	}
	obj._NoDataExistForPANCodeMutex.Lock()
	defer obj._NoDataExistForPANCodeMutex.Unlock()
	obj._NoDataExistForPANCode = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NoDataExistForPANCode")
	}
	return nil
}

func NewInternationalFundTransferForexRate() (_obj *InternationalFundTransferForexRate, _setters map[string]dynconf.SetFunc) {
	_obj = &InternationalFundTransferForexRate{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["allowvaryingratesfromforexapi"] = _obj.SetAllowVaryingRatesFromForexAPI
	_setters["forexraterefreshinterval"] = _obj.SetForexRateRefreshInterval
	_setters["forexratealertthresholdsinpercent"] = _obj.SetForexRateAlertThresholdsInPercent
	_obj._ForexRateAlertThresholdsInPercentMutex = &sync.RWMutex{}
	_UpdateAmountInUse, _fieldSetters := NewForexRateUpdateAmountInUse()
	_obj._UpdateAmountInUse = _UpdateAmountInUse
	helper.AddFieldSetters("updateamountinuse", _fieldSetters, _setters)
	_USD, _fieldSetters := NewUSDForexRate()
	_obj._USD = _USD
	helper.AddFieldSetters("usd", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *InternationalFundTransferForexRate) Init() {
	newObj, _ := NewInternationalFundTransferForexRate()
	*obj = *newObj
}

func (obj *InternationalFundTransferForexRate) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InternationalFundTransferForexRate) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.InternationalFundTransferForexRate)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransferForexRate", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InternationalFundTransferForexRate) setDynamicField(v *server.InternationalFundTransferForexRate, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "allowvaryingratesfromforexapi":
		return obj.SetAllowVaryingRatesFromForexAPI(v.AllowVaryingRatesFromForexAPI, true, nil)
	case "forexraterefreshinterval":
		return obj.SetForexRateRefreshInterval(v.ForexRateRefreshInterval, true, nil)
	case "forexratealertthresholdsinpercent":
		return obj.SetForexRateAlertThresholdsInPercent(v.ForexRateAlertThresholdsInPercent, true, path)
	case "updateamountinuse":
		return obj._UpdateAmountInUse.Set(v.UpdateAmountInUse, true, path)
	case "usd":
		return obj._USD.Set(v.USD, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InternationalFundTransferForexRate) setDynamicFields(v *server.InternationalFundTransferForexRate, dynamic bool, path []string) (err error) {

	err = obj.SetAllowVaryingRatesFromForexAPI(v.AllowVaryingRatesFromForexAPI, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetForexRateRefreshInterval(v.ForexRateRefreshInterval, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetForexRateAlertThresholdsInPercent(v.ForexRateAlertThresholdsInPercent, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UpdateAmountInUse.Set(v.UpdateAmountInUse, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._USD.Set(v.USD, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InternationalFundTransferForexRate) setStaticFields(v *server.InternationalFundTransferForexRate) error {

	return nil
}

func (obj *InternationalFundTransferForexRate) SetAllowVaryingRatesFromForexAPI(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransferForexRate.AllowVaryingRatesFromForexAPI", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._AllowVaryingRatesFromForexAPI, 1)
	} else {
		atomic.StoreUint32(&obj._AllowVaryingRatesFromForexAPI, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "AllowVaryingRatesFromForexAPI")
	}
	return nil
}
func (obj *InternationalFundTransferForexRate) SetForexRateRefreshInterval(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransferForexRate.ForexRateRefreshInterval", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ForexRateRefreshInterval, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ForexRateRefreshInterval")
	}
	return nil
}
func (obj *InternationalFundTransferForexRate) SetForexRateAlertThresholdsInPercent(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransferForexRate.ForexRateAlertThresholdsInPercent", reflect.TypeOf(val))
	}
	obj._ForexRateAlertThresholdsInPercentMutex.Lock()
	defer obj._ForexRateAlertThresholdsInPercentMutex.Unlock()
	obj._ForexRateAlertThresholdsInPercent = roarray.New[int64](v)
	return nil
}

func NewForexRateUpdateAmountInUse() (_obj *ForexRateUpdateAmountInUse, _setters map[string]dynconf.SetFunc) {
	_obj = &ForexRateUpdateAmountInUse{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableidempotency"] = _obj.SetEnableIdempotency
	return _obj, _setters
}

func (obj *ForexRateUpdateAmountInUse) Init() {
	newObj, _ := NewForexRateUpdateAmountInUse()
	*obj = *newObj
}

func (obj *ForexRateUpdateAmountInUse) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ForexRateUpdateAmountInUse) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.ForexRateUpdateAmountInUse)
	if !ok {
		return fmt.Errorf("invalid data type %v *ForexRateUpdateAmountInUse", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ForexRateUpdateAmountInUse) setDynamicField(v *server.ForexRateUpdateAmountInUse, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableidempotency":
		return obj.SetEnableIdempotency(v.EnableIdempotency, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ForexRateUpdateAmountInUse) setDynamicFields(v *server.ForexRateUpdateAmountInUse, dynamic bool, path []string) (err error) {

	err = obj.SetEnableIdempotency(v.EnableIdempotency, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ForexRateUpdateAmountInUse) setStaticFields(v *server.ForexRateUpdateAmountInUse) error {

	return nil
}

func (obj *ForexRateUpdateAmountInUse) SetEnableIdempotency(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ForexRateUpdateAmountInUse.EnableIdempotency", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableIdempotency, 1)
	} else {
		atomic.StoreUint32(&obj._EnableIdempotency, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableIdempotency")
	}
	return nil
}

func NewUSDForexRate() (_obj *USDForexRate, _setters map[string]dynconf.SetFunc) {
	_obj = &USDForexRate{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["units"] = _obj.SetUnits
	_setters["nanos"] = _obj.SetNanos
	_setters["currencycode"] = _obj.SetCurrencyCode
	_obj._CurrencyCodeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *USDForexRate) Init() {
	newObj, _ := NewUSDForexRate()
	*obj = *newObj
}

func (obj *USDForexRate) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *USDForexRate) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.USDForexRate)
	if !ok {
		return fmt.Errorf("invalid data type %v *USDForexRate", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *USDForexRate) setDynamicField(v *server.USDForexRate, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "units":
		return obj.SetUnits(v.Units, true, nil)
	case "nanos":
		return obj.SetNanos(v.Nanos, true, nil)
	case "currencycode":
		return obj.SetCurrencyCode(v.CurrencyCode, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *USDForexRate) setDynamicFields(v *server.USDForexRate, dynamic bool, path []string) (err error) {

	err = obj.SetUnits(v.Units, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNanos(v.Nanos, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCurrencyCode(v.CurrencyCode, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *USDForexRate) setStaticFields(v *server.USDForexRate) error {

	return nil
}

func (obj *USDForexRate) SetUnits(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *USDForexRate.Units", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Units, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Units")
	}
	return nil
}
func (obj *USDForexRate) SetNanos(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *USDForexRate.Nanos", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Nanos, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Nanos")
	}
	return nil
}
func (obj *USDForexRate) SetCurrencyCode(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *USDForexRate.CurrencyCode", reflect.TypeOf(val))
	}
	obj._CurrencyCodeMutex.Lock()
	defer obj._CurrencyCodeMutex.Unlock()
	obj._CurrencyCode = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CurrencyCode")
	}
	return nil
}

func NewTransactionAmountConstraint() (_obj *TransactionAmountConstraint, _setters map[string]dynconf.SetFunc) {
	_obj = &TransactionAmountConstraint{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxtransactionamounttocreditamountpercentage"] = _obj.SetMaxTransactionAmountToCreditAmountPercentage
	_setters["maxtransactionamounttocurrentbalancepercentage"] = _obj.SetMaxTransactionAmountToCurrentBalancePercentage
	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *TransactionAmountConstraint) Init() {
	newObj, _ := NewTransactionAmountConstraint()
	*obj = *newObj
}

func (obj *TransactionAmountConstraint) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TransactionAmountConstraint) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.TransactionAmountConstraint)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAmountConstraint", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TransactionAmountConstraint) setDynamicField(v *server.TransactionAmountConstraint, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxtransactionamounttocreditamountpercentage":
		return obj.SetMaxTransactionAmountToCreditAmountPercentage(v.MaxTransactionAmountToCreditAmountPercentage, true, nil)
	case "maxtransactionamounttocurrentbalancepercentage":
		return obj.SetMaxTransactionAmountToCurrentBalancePercentage(v.MaxTransactionAmountToCurrentBalancePercentage, true, nil)
	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TransactionAmountConstraint) setDynamicFields(v *server.TransactionAmountConstraint, dynamic bool, path []string) (err error) {

	err = obj.SetMaxTransactionAmountToCreditAmountPercentage(v.MaxTransactionAmountToCreditAmountPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxTransactionAmountToCurrentBalancePercentage(v.MaxTransactionAmountToCurrentBalancePercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TransactionAmountConstraint) setStaticFields(v *server.TransactionAmountConstraint) error {

	obj._MaxPermissibleAmount = v.MaxPermissibleAmount
	return nil
}

func (obj *TransactionAmountConstraint) SetMaxTransactionAmountToCreditAmountPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAmountConstraint.MaxTransactionAmountToCreditAmountPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxTransactionAmountToCreditAmountPercentage, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxTransactionAmountToCreditAmountPercentage")
	}
	return nil
}
func (obj *TransactionAmountConstraint) SetMaxTransactionAmountToCurrentBalancePercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAmountConstraint.MaxTransactionAmountToCurrentBalancePercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxTransactionAmountToCurrentBalancePercentage, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxTransactionAmountToCurrentBalancePercentage")
	}
	return nil
}
func (obj *TransactionAmountConstraint) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAmountConstraint.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewPayIncidentManager() (_obj *PayIncidentManager, _setters map[string]dynconf.SetFunc) {
	_obj = &PayIncidentManager{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_DefaultTransactionHandler, _fieldSetters := NewDefaultTransactionHandler()
	_obj._DefaultTransactionHandler = _DefaultTransactionHandler
	helper.AddFieldSetters("defaulttransactionhandler", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *PayIncidentManager) Init() {
	newObj, _ := NewPayIncidentManager()
	*obj = *newObj
}

func (obj *PayIncidentManager) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PayIncidentManager) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.PayIncidentManager)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayIncidentManager", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PayIncidentManager) setDynamicField(v *server.PayIncidentManager, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "defaulttransactionhandler":
		return obj._DefaultTransactionHandler.Set(v.DefaultTransactionHandler, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PayIncidentManager) setDynamicFields(v *server.PayIncidentManager, dynamic bool, path []string) (err error) {

	err = obj._DefaultTransactionHandler.Set(v.DefaultTransactionHandler, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PayIncidentManager) setStaticFields(v *server.PayIncidentManager) error {

	obj._UpiPinFlowError = v.UpiPinFlowError
	return nil
}

func NewDefaultTransactionHandler() (_obj *DefaultTransactionHandler, _setters map[string]dynconf.SetFunc) {
	_obj = &DefaultTransactionHandler{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._ErrorCodeIncidentMap = &syncmap.Map[string, string]{}
	_setters["errorcodeincidentmap"] = _obj.SetErrorCodeIncidentMap

	_obj._IncidentProductCategoryMap = &syncmap.Map[string, *IncidentCategory]{}
	_setters["incidentproductcategorymap"] = _obj.SetIncidentProductCategoryMap

	_obj._IncidentTicketConfigMap = &syncmap.Map[string, *IncidentTicketConfig]{}
	_setters["incidentticketconfigmap"] = _obj.SetIncidentTicketConfigMap
	return _obj, _setters
}

func (obj *DefaultTransactionHandler) Init() {
	newObj, _ := NewDefaultTransactionHandler()
	*obj = *newObj
}

func (obj *DefaultTransactionHandler) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DefaultTransactionHandler) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.DefaultTransactionHandler)
	if !ok {
		return fmt.Errorf("invalid data type %v *DefaultTransactionHandler", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DefaultTransactionHandler) setDynamicField(v *server.DefaultTransactionHandler, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "errorcodeincidentmap":
		return obj.SetErrorCodeIncidentMap(v.ErrorCodeIncidentMap, true, path)
	case "incidentproductcategorymap":
		return obj.SetIncidentProductCategoryMap(v.IncidentProductCategoryMap, true, path)
	case "incidentticketconfigmap":
		return obj.SetIncidentTicketConfigMap(v.IncidentTicketConfigMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DefaultTransactionHandler) setDynamicFields(v *server.DefaultTransactionHandler, dynamic bool, path []string) (err error) {

	err = obj.SetErrorCodeIncidentMap(v.ErrorCodeIncidentMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetIncidentProductCategoryMap(v.IncidentProductCategoryMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetIncidentTicketConfigMap(v.IncidentTicketConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DefaultTransactionHandler) setStaticFields(v *server.DefaultTransactionHandler) error {

	obj._IncidentNotificationTemplateMap = v.IncidentNotificationTemplateMap
	return nil
}

func (obj *DefaultTransactionHandler) SetErrorCodeIncidentMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DefaultTransactionHandler.ErrorCodeIncidentMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._ErrorCodeIncidentMap, v, path)
}
func (obj *DefaultTransactionHandler) SetIncidentProductCategoryMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*server.IncidentCategory)
	if !ok {
		return fmt.Errorf("invalid data type %v *DefaultTransactionHandler.IncidentProductCategoryMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._IncidentProductCategoryMap, v, dynamic, path)

}
func (obj *DefaultTransactionHandler) SetIncidentTicketConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*server.IncidentTicketConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DefaultTransactionHandler.IncidentTicketConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._IncidentTicketConfigMap, v, dynamic, path)

}

func NewIncidentCategory() (_obj *IncidentCategory, _setters map[string]dynconf.SetFunc) {
	_obj = &IncidentCategory{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._PaymentProtocolToIssueCategoryIdMap = &syncmap.Map[string, string]{}
	_setters["paymentprotocoltoissuecategoryidmap"] = _obj.SetPaymentProtocolToIssueCategoryIdMap
	_setters["productcategory"] = _obj.SetProductCategory
	_obj._ProductCategoryMutex = &sync.RWMutex{}
	_setters["productcategorydetails"] = _obj.SetProductCategoryDetails
	_obj._ProductCategoryDetailsMutex = &sync.RWMutex{}
	_setters["subcategory"] = _obj.SetSubCategory
	_obj._SubCategoryMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *IncidentCategory) Init() {
	newObj, _ := NewIncidentCategory()
	*obj = *newObj
}

func (obj *IncidentCategory) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IncidentCategory) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.IncidentCategory)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentCategory", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IncidentCategory) setDynamicField(v *server.IncidentCategory, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "paymentprotocoltoissuecategoryidmap":
		return obj.SetPaymentProtocolToIssueCategoryIdMap(v.PaymentProtocolToIssueCategoryIdMap, true, path)
	case "productcategory":
		return obj.SetProductCategory(v.ProductCategory, true, nil)
	case "productcategorydetails":
		return obj.SetProductCategoryDetails(v.ProductCategoryDetails, true, nil)
	case "subcategory":
		return obj.SetSubCategory(v.SubCategory, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IncidentCategory) setDynamicFields(v *server.IncidentCategory, dynamic bool, path []string) (err error) {

	err = obj.SetPaymentProtocolToIssueCategoryIdMap(v.PaymentProtocolToIssueCategoryIdMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetProductCategory(v.ProductCategory, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetProductCategoryDetails(v.ProductCategoryDetails, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubCategory(v.SubCategory, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IncidentCategory) setStaticFields(v *server.IncidentCategory) error {

	return nil
}

func (obj *IncidentCategory) SetPaymentProtocolToIssueCategoryIdMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentCategory.PaymentProtocolToIssueCategoryIdMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._PaymentProtocolToIssueCategoryIdMap, v, path)
}
func (obj *IncidentCategory) SetProductCategory(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentCategory.ProductCategory", reflect.TypeOf(val))
	}
	obj._ProductCategoryMutex.Lock()
	defer obj._ProductCategoryMutex.Unlock()
	obj._ProductCategory = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ProductCategory")
	}
	return nil
}
func (obj *IncidentCategory) SetProductCategoryDetails(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentCategory.ProductCategoryDetails", reflect.TypeOf(val))
	}
	obj._ProductCategoryDetailsMutex.Lock()
	defer obj._ProductCategoryDetailsMutex.Unlock()
	obj._ProductCategoryDetails = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ProductCategoryDetails")
	}
	return nil
}
func (obj *IncidentCategory) SetSubCategory(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentCategory.SubCategory", reflect.TypeOf(val))
	}
	obj._SubCategoryMutex.Lock()
	defer obj._SubCategoryMutex.Unlock()
	obj._SubCategory = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubCategory")
	}
	return nil
}

func NewIncidentTicketConfig() (_obj *IncidentTicketConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IncidentTicketConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ticketdescription"] = _obj.SetTicketDescription
	_obj._TicketDescriptionMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *IncidentTicketConfig) Init() {
	newObj, _ := NewIncidentTicketConfig()
	*obj = *newObj
}

func (obj *IncidentTicketConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IncidentTicketConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.IncidentTicketConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentTicketConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IncidentTicketConfig) setDynamicField(v *server.IncidentTicketConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ticketdescription":
		return obj.SetTicketDescription(v.TicketDescription, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IncidentTicketConfig) setDynamicFields(v *server.IncidentTicketConfig, dynamic bool, path []string) (err error) {

	err = obj.SetTicketDescription(v.TicketDescription, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IncidentTicketConfig) setStaticFields(v *server.IncidentTicketConfig) error {

	obj._TicketPriority = v.TicketPriority
	obj._TicketGroup = v.TicketGroup
	return nil
}

func (obj *IncidentTicketConfig) SetTicketDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncidentTicketConfig.TicketDescription", reflect.TypeOf(val))
	}
	obj._TicketDescriptionMutex.Lock()
	defer obj._TicketDescriptionMutex.Unlock()
	obj._TicketDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TicketDescription")
	}
	return nil
}

func NewPinotIngestionDelay() (_obj *PinotIngestionDelay, _setters map[string]dynconf.SetFunc) {
	_obj = &PinotIngestionDelay{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["delayduration"] = _obj.SetDelayDuration
	return _obj, _setters
}

func (obj *PinotIngestionDelay) Init() {
	newObj, _ := NewPinotIngestionDelay()
	*obj = *newObj
}

func (obj *PinotIngestionDelay) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PinotIngestionDelay) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.PinotIngestionDelay)
	if !ok {
		return fmt.Errorf("invalid data type %v *PinotIngestionDelay", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PinotIngestionDelay) setDynamicField(v *server.PinotIngestionDelay, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "delayduration":
		return obj.SetDelayDuration(v.DelayDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PinotIngestionDelay) setDynamicFields(v *server.PinotIngestionDelay, dynamic bool, path []string) (err error) {

	err = obj.SetDelayDuration(v.DelayDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PinotIngestionDelay) setStaticFields(v *server.PinotIngestionDelay) error {

	return nil
}

func (obj *PinotIngestionDelay) SetDelayDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PinotIngestionDelay.DelayDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DelayDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DelayDuration")
	}
	return nil
}

func NewFundTransferCelestialParams() (_obj *FundTransferCelestialParams, _setters map[string]dynconf.SetFunc) {
	_obj = &FundTransferCelestialParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isfundtransferviacelestialrestricted"] = _obj.SetIsFundTransferViaCelestialRestricted
	return _obj, _setters
}

func (obj *FundTransferCelestialParams) Init() {
	newObj, _ := NewFundTransferCelestialParams()
	*obj = *newObj
}

func (obj *FundTransferCelestialParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FundTransferCelestialParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.FundTransferCelestialParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *FundTransferCelestialParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FundTransferCelestialParams) setDynamicField(v *server.FundTransferCelestialParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isfundtransferviacelestialrestricted":
		return obj.SetIsFundTransferViaCelestialRestricted(v.IsFundTransferViaCelestialRestricted, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FundTransferCelestialParams) setDynamicFields(v *server.FundTransferCelestialParams, dynamic bool, path []string) (err error) {

	err = obj.SetIsFundTransferViaCelestialRestricted(v.IsFundTransferViaCelestialRestricted, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FundTransferCelestialParams) setStaticFields(v *server.FundTransferCelestialParams) error {

	obj._AllowedUserGrpForFundTransferViaCelestial = v.AllowedUserGrpForFundTransferViaCelestial
	obj._Version = v.Version
	return nil
}

func (obj *FundTransferCelestialParams) SetIsFundTransferViaCelestialRestricted(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FundTransferCelestialParams.IsFundTransferViaCelestialRestricted", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsFundTransferViaCelestialRestricted, 1)
	} else {
		atomic.StoreUint32(&obj._IsFundTransferViaCelestialRestricted, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsFundTransferViaCelestialRestricted")
	}
	return nil
}

func NewVersionSupport() (_obj *VersionSupport, _setters map[string]dynconf.SetFunc) {
	_obj = &VersionSupport{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minandroidappversiontosupportsofcheck"] = _obj.SetMinAndroidAppVersionToSupportSofCheck
	_setters["miniosappversiontosupportsofcheck"] = _obj.SetMinIOSAppVersionToSupportSofCheck
	_setters["minandroidappversiontosupportaddca"] = _obj.SetMinAndroidAppVersionToSupportAddCA
	_setters["miniosappversiontosupportaddca"] = _obj.SetMinIOSAppVersionToSupportAddCA
	return _obj, _setters
}

func (obj *VersionSupport) Init() {
	newObj, _ := NewVersionSupport()
	*obj = *newObj
}

func (obj *VersionSupport) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VersionSupport) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.VersionSupport)
	if !ok {
		return fmt.Errorf("invalid data type %v *VersionSupport", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VersionSupport) setDynamicField(v *server.VersionSupport, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minandroidappversiontosupportsofcheck":
		return obj.SetMinAndroidAppVersionToSupportSofCheck(v.MinAndroidAppVersionToSupportSofCheck, true, nil)
	case "miniosappversiontosupportsofcheck":
		return obj.SetMinIOSAppVersionToSupportSofCheck(v.MinIOSAppVersionToSupportSofCheck, true, nil)
	case "minandroidappversiontosupportaddca":
		return obj.SetMinAndroidAppVersionToSupportAddCA(v.MinAndroidAppVersionToSupportAddCA, true, nil)
	case "miniosappversiontosupportaddca":
		return obj.SetMinIOSAppVersionToSupportAddCA(v.MinIOSAppVersionToSupportAddCA, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VersionSupport) setDynamicFields(v *server.VersionSupport, dynamic bool, path []string) (err error) {

	err = obj.SetMinAndroidAppVersionToSupportSofCheck(v.MinAndroidAppVersionToSupportSofCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinIOSAppVersionToSupportSofCheck(v.MinIOSAppVersionToSupportSofCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinAndroidAppVersionToSupportAddCA(v.MinAndroidAppVersionToSupportAddCA, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinIOSAppVersionToSupportAddCA(v.MinIOSAppVersionToSupportAddCA, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VersionSupport) setStaticFields(v *server.VersionSupport) error {

	return nil
}

func (obj *VersionSupport) SetMinAndroidAppVersionToSupportSofCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VersionSupport.MinAndroidAppVersionToSupportSofCheck", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinAndroidAppVersionToSupportSofCheck, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAndroidAppVersionToSupportSofCheck")
	}
	return nil
}
func (obj *VersionSupport) SetMinIOSAppVersionToSupportSofCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VersionSupport.MinIOSAppVersionToSupportSofCheck", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinIOSAppVersionToSupportSofCheck, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinIOSAppVersionToSupportSofCheck")
	}
	return nil
}
func (obj *VersionSupport) SetMinAndroidAppVersionToSupportAddCA(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VersionSupport.MinAndroidAppVersionToSupportAddCA", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinAndroidAppVersionToSupportAddCA, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAndroidAppVersionToSupportAddCA")
	}
	return nil
}
func (obj *VersionSupport) SetMinIOSAppVersionToSupportAddCA(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VersionSupport.MinIOSAppVersionToSupportAddCA", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinIOSAppVersionToSupportAddCA, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinIOSAppVersionToSupportAddCA")
	}
	return nil
}

func NewTierConfig() (_obj *TierConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TierConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._TierToIconMap = &syncmap.Map[string, string]{}
	_setters["tiertoiconmap"] = _obj.SetTierToIconMap
	return _obj, _setters
}

func (obj *TierConfig) Init() {
	newObj, _ := NewTierConfig()
	*obj = *newObj
}

func (obj *TierConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TierConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.TierConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TierConfig) setDynamicField(v *server.TierConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "tiertoiconmap":
		return obj.SetTierToIconMap(v.TierToIconMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TierConfig) setDynamicFields(v *server.TierConfig, dynamic bool, path []string) (err error) {

	err = obj.SetTierToIconMap(v.TierToIconMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TierConfig) setStaticFields(v *server.TierConfig) error {

	return nil
}

func (obj *TierConfig) SetTierToIconMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TierConfig.TierToIconMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._TierToIconMap, v, path)
}

func NewPayOrderCacheConfig() (_obj *PayOrderCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PayOrderCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled

	_obj._UseCaseToCacheConfigMap = &syncmap.Map[string, *CacheConfig]{}
	_setters["usecasetocacheconfigmap"] = _obj.SetUseCaseToCacheConfigMap
	return _obj, _setters
}

func (obj *PayOrderCacheConfig) Init() {
	newObj, _ := NewPayOrderCacheConfig()
	*obj = *newObj
}

func (obj *PayOrderCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PayOrderCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.PayOrderCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayOrderCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PayOrderCacheConfig) setDynamicField(v *server.PayOrderCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "usecasetocacheconfigmap":
		return obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PayOrderCacheConfig) setDynamicFields(v *server.PayOrderCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PayOrderCacheConfig) setStaticFields(v *server.PayOrderCacheConfig) error {

	obj._RedisOptions = v.RedisOptions
	return nil
}

func (obj *PayOrderCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayOrderCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *PayOrderCacheConfig) SetUseCaseToCacheConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*server.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayOrderCacheConfig.UseCaseToCacheConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._UseCaseToCacheConfigMap, v, dynamic, path)

}

func NewCacheConfig() (_obj *CacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *CacheConfig) Init() {
	newObj, _ := NewCacheConfig()
	*obj = *newObj
}

func (obj *CacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CacheConfig) setDynamicField(v *server.CacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CacheConfig) setDynamicFields(v *server.CacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CacheConfig) setStaticFields(v *server.CacheConfig) error {

	return nil
}

func (obj *CacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *CacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewPayTransactionCacheConfig() (_obj *PayTransactionCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PayTransactionCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled

	_obj._UseCaseToCacheConfigMap = &syncmap.Map[string, *CacheConfig]{}
	_setters["usecasetocacheconfigmap"] = _obj.SetUseCaseToCacheConfigMap
	return _obj, _setters
}

func (obj *PayTransactionCacheConfig) Init() {
	newObj, _ := NewPayTransactionCacheConfig()
	*obj = *newObj
}

func (obj *PayTransactionCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PayTransactionCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.PayTransactionCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayTransactionCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PayTransactionCacheConfig) setDynamicField(v *server.PayTransactionCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "usecasetocacheconfigmap":
		return obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PayTransactionCacheConfig) setDynamicFields(v *server.PayTransactionCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseCaseToCacheConfigMap(v.UseCaseToCacheConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PayTransactionCacheConfig) setStaticFields(v *server.PayTransactionCacheConfig) error {

	obj._RedisOptions = v.RedisOptions
	return nil
}

func (obj *PayTransactionCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayTransactionCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *PayTransactionCacheConfig) SetUseCaseToCacheConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*server.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PayTransactionCacheConfig.UseCaseToCacheConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._UseCaseToCacheConfigMap, v, dynamic, path)

}

func NewPgParams() (_obj *PgParams, _setters map[string]dynconf.SetFunc) {
	_obj = &PgParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["vendordetailsenrichmentnumworkers"] = _obj.SetVendorDetailsEnrichmentNumWorkers
	_setters["vendordetailsenrichmentapiratelimit"] = _obj.SetVendorDetailsEnrichmentApiRatelimit
	_setters["enablemandatestatusupdateviaconsumer"] = _obj.SetEnableMandateStatusUpdateViaConsumer
	_setters["onetimefundtransferstatuswaitsignaltimeout"] = _obj.SetOneTimeFundTransferStatusWaitSignalTimeout
	_setters["rpexecutionfundtransferstatuswaitsignaltimeout"] = _obj.SetRpExecutionFundTransferStatusWaitSignalTimeout

	_obj._OneTimePaymentOrderExpirationDuration = &syncmap.Map[string, time.Duration]{}
	_setters["onetimepaymentorderexpirationduration"] = _obj.SetOneTimePaymentOrderExpirationDuration
	return _obj, _setters
}

func (obj *PgParams) Init() {
	newObj, _ := NewPgParams()
	*obj = *newObj
}

func (obj *PgParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PgParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.PgParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PgParams) setDynamicField(v *server.PgParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "vendordetailsenrichmentnumworkers":
		return obj.SetVendorDetailsEnrichmentNumWorkers(v.VendorDetailsEnrichmentNumWorkers, true, nil)
	case "vendordetailsenrichmentapiratelimit":
		return obj.SetVendorDetailsEnrichmentApiRatelimit(v.VendorDetailsEnrichmentApiRatelimit, true, nil)
	case "enablemandatestatusupdateviaconsumer":
		return obj.SetEnableMandateStatusUpdateViaConsumer(v.EnableMandateStatusUpdateViaConsumer, true, nil)
	case "onetimefundtransferstatuswaitsignaltimeout":
		return obj.SetOneTimeFundTransferStatusWaitSignalTimeout(v.OneTimeFundTransferStatusWaitSignalTimeout, true, nil)
	case "rpexecutionfundtransferstatuswaitsignaltimeout":
		return obj.SetRpExecutionFundTransferStatusWaitSignalTimeout(v.RpExecutionFundTransferStatusWaitSignalTimeout, true, nil)
	case "onetimepaymentorderexpirationduration":
		return obj.SetOneTimePaymentOrderExpirationDuration(v.OneTimePaymentOrderExpirationDuration, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PgParams) setDynamicFields(v *server.PgParams, dynamic bool, path []string) (err error) {

	err = obj.SetVendorDetailsEnrichmentNumWorkers(v.VendorDetailsEnrichmentNumWorkers, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVendorDetailsEnrichmentApiRatelimit(v.VendorDetailsEnrichmentApiRatelimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableMandateStatusUpdateViaConsumer(v.EnableMandateStatusUpdateViaConsumer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOneTimeFundTransferStatusWaitSignalTimeout(v.OneTimeFundTransferStatusWaitSignalTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRpExecutionFundTransferStatusWaitSignalTimeout(v.RpExecutionFundTransferStatusWaitSignalTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOneTimePaymentOrderExpirationDuration(v.OneTimePaymentOrderExpirationDuration, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PgParams) setStaticFields(v *server.PgParams) error {

	obj._PaymentGatewayAdjustmentActorId = v.PaymentGatewayAdjustmentActorId
	obj._DummyPaymentGatewayAdjustmentActorToName = v.DummyPaymentGatewayAdjustmentActorToName
	obj._PGAdjustmentOwnershipToPiMapping = v.PGAdjustmentOwnershipToPiMapping
	obj._PaymentGatewayRefundGenericActorId = v.PaymentGatewayRefundGenericActorId
	obj._DummyPaymentGatewayRefundGenericActorFromName = v.DummyPaymentGatewayRefundGenericActorFromName
	return nil
}

func (obj *PgParams) SetVendorDetailsEnrichmentNumWorkers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.VendorDetailsEnrichmentNumWorkers", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._VendorDetailsEnrichmentNumWorkers, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VendorDetailsEnrichmentNumWorkers")
	}
	return nil
}
func (obj *PgParams) SetVendorDetailsEnrichmentApiRatelimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.VendorDetailsEnrichmentApiRatelimit", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._VendorDetailsEnrichmentApiRatelimit, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VendorDetailsEnrichmentApiRatelimit")
	}
	return nil
}
func (obj *PgParams) SetEnableMandateStatusUpdateViaConsumer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.EnableMandateStatusUpdateViaConsumer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableMandateStatusUpdateViaConsumer, 1)
	} else {
		atomic.StoreUint32(&obj._EnableMandateStatusUpdateViaConsumer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableMandateStatusUpdateViaConsumer")
	}
	return nil
}
func (obj *PgParams) SetOneTimeFundTransferStatusWaitSignalTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.OneTimeFundTransferStatusWaitSignalTimeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OneTimeFundTransferStatusWaitSignalTimeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OneTimeFundTransferStatusWaitSignalTimeout")
	}
	return nil
}
func (obj *PgParams) SetRpExecutionFundTransferStatusWaitSignalTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.RpExecutionFundTransferStatusWaitSignalTimeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RpExecutionFundTransferStatusWaitSignalTimeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RpExecutionFundTransferStatusWaitSignalTimeout")
	}
	return nil
}
func (obj *PgParams) SetOneTimePaymentOrderExpirationDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PgParams.OneTimePaymentOrderExpirationDuration", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._OneTimePaymentOrderExpirationDuration, v, path)
}

func NewFeedbackEngineCustomEvaluatorRule() (_obj *FeedbackEngineCustomEvaluatorRule, _setters map[string]dynconf.SetFunc) {
	_obj = &FeedbackEngineCustomEvaluatorRule{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minsuccesscountthreshold"] = _obj.SetMinSuccessCountThreshold
	_setters["mindebitamount"] = _obj.SetMinDebitAmount
	_setters["durationfortxnevaluation"] = _obj.SetDurationForTxnEvaluation
	return _obj, _setters
}

func (obj *FeedbackEngineCustomEvaluatorRule) Init() {
	newObj, _ := NewFeedbackEngineCustomEvaluatorRule()
	*obj = *newObj
}

func (obj *FeedbackEngineCustomEvaluatorRule) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeedbackEngineCustomEvaluatorRule) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.FeedbackEngineCustomEvaluatorRule)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeedbackEngineCustomEvaluatorRule) setDynamicField(v *server.FeedbackEngineCustomEvaluatorRule, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minsuccesscountthreshold":
		return obj.SetMinSuccessCountThreshold(v.MinSuccessCountThreshold, true, nil)
	case "mindebitamount":
		return obj.SetMinDebitAmount(v.MinDebitAmount, true, nil)
	case "durationfortxnevaluation":
		return obj.SetDurationForTxnEvaluation(v.DurationForTxnEvaluation, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeedbackEngineCustomEvaluatorRule) setDynamicFields(v *server.FeedbackEngineCustomEvaluatorRule, dynamic bool, path []string) (err error) {

	err = obj.SetMinSuccessCountThreshold(v.MinSuccessCountThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinDebitAmount(v.MinDebitAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDurationForTxnEvaluation(v.DurationForTxnEvaluation, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeedbackEngineCustomEvaluatorRule) setStaticFields(v *server.FeedbackEngineCustomEvaluatorRule) error {

	return nil
}

func (obj *FeedbackEngineCustomEvaluatorRule) SetMinSuccessCountThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.MinSuccessCountThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSuccessCountThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSuccessCountThreshold")
	}
	return nil
}
func (obj *FeedbackEngineCustomEvaluatorRule) SetMinDebitAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.MinDebitAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinDebitAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinDebitAmount")
	}
	return nil
}
func (obj *FeedbackEngineCustomEvaluatorRule) SetDurationForTxnEvaluation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.DurationForTxnEvaluation", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DurationForTxnEvaluation, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DurationForTxnEvaluation")
	}
	return nil
}

func NewEnrichOrderConfig() (_obj *EnrichOrderConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnrichOrderConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_EnrichmentFromDcSwitchConfig, _fieldSetters := NewEnrichmentFromDcSwitchConfig()
	_obj._EnrichmentFromDcSwitchConfig = _EnrichmentFromDcSwitchConfig
	helper.AddFieldSetters("enrichmentfromdcswitchconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *EnrichOrderConfig) Init() {
	newObj, _ := NewEnrichOrderConfig()
	*obj = *newObj
}

func (obj *EnrichOrderConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnrichOrderConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnrichOrderConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnrichOrderConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnrichOrderConfig) setDynamicField(v *server.EnrichOrderConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enrichmentfromdcswitchconfig":
		return obj._EnrichmentFromDcSwitchConfig.Set(v.EnrichmentFromDcSwitchConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnrichOrderConfig) setDynamicFields(v *server.EnrichOrderConfig, dynamic bool, path []string) (err error) {

	err = obj._EnrichmentFromDcSwitchConfig.Set(v.EnrichmentFromDcSwitchConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnrichOrderConfig) setStaticFields(v *server.EnrichOrderConfig) error {

	return nil
}

func NewEnrichmentFromDcSwitchConfig() (_obj *EnrichmentFromDcSwitchConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EnrichmentFromDcSwitchConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["allowedtxntimedeviationbuffer"] = _obj.SetAllowedTxnTimeDeviationBuffer
	return _obj, _setters
}

func (obj *EnrichmentFromDcSwitchConfig) Init() {
	newObj, _ := NewEnrichmentFromDcSwitchConfig()
	*obj = *newObj
}

func (obj *EnrichmentFromDcSwitchConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EnrichmentFromDcSwitchConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*server.EnrichmentFromDcSwitchConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnrichmentFromDcSwitchConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EnrichmentFromDcSwitchConfig) setDynamicField(v *server.EnrichmentFromDcSwitchConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "allowedtxntimedeviationbuffer":
		return obj.SetAllowedTxnTimeDeviationBuffer(v.AllowedTxnTimeDeviationBuffer, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EnrichmentFromDcSwitchConfig) setDynamicFields(v *server.EnrichmentFromDcSwitchConfig, dynamic bool, path []string) (err error) {

	err = obj.SetAllowedTxnTimeDeviationBuffer(v.AllowedTxnTimeDeviationBuffer, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EnrichmentFromDcSwitchConfig) setStaticFields(v *server.EnrichmentFromDcSwitchConfig) error {

	return nil
}

func (obj *EnrichmentFromDcSwitchConfig) SetAllowedTxnTimeDeviationBuffer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EnrichmentFromDcSwitchConfig.AllowedTxnTimeDeviationBuffer", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AllowedTxnTimeDeviationBuffer, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AllowedTxnTimeDeviationBuffer")
	}
	return nil
}
