package pay_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	mocksS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	payTxnBackfillPb "github.com/epifi/gamma/api/pay/transaction_backfill"
	"github.com/epifi/gamma/pay"
)

func TestService_BackupTxnBackfillDataToS3(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBroker := eventMocks.NewMockBroker(ctr)
	mockS3Client := mocksS3.NewMockS3Client(ctr)
	mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()

	payService := pay.NewService(nil, nil, nil, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockS3Client, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	defer ctr.Finish()

	var (
		txnBackfillWorkflowPayload = &payPb.TxnBackfillWorkflowPayloadForS3{
			OrderTxnPayload: []*payTxnBackfillPb.OrderTxnPayData{
				{
					OrderDetail: &payTxnBackfillPb.OrderDetail{
						Id:          fixturesOrder43.GetId(),
						FromActorId: fixturesOrder43.GetFromActorId(),
						ToActorId:   fixturesOrder43.GetToActorId(),
						Provenance:  fixturesOrder43.GetProvenance(),
						Tags:        fixturesOrder43.GetTags(),
					},
					TxnDetail: &payTxnBackfillPb.TxnDetail{
						Id:                     fixturesTransaction8.GetId(),
						PiTo:                   fixturesTransaction8.GetPiTo(),
						PiFrom:                 fixturesTransaction8.GetPiFrom(),
						Utr:                    fixturesTransaction8.GetUtr(),
						PaymentProtocol:        fixturesTransaction8.GetPaymentProtocol(),
						Remarks:                fixturesTransaction8.GetRemarks(),
						RawNotificationDetails: fixturesTransaction8.GetRawNotificationDetails(),
					},
				},
			},
		}

		txnBackfillEnrichedData = &payPb.TxnBackfillEnrichedDataForS3{
			EnrichedPayData: []*payPb.OrderWithTransactions{
				{
					Order: fixturesOrder43,
					Transactions: []*paymentPb.Transaction{
						fixturesTransaction11,
						fixturesTransaction1,
					},
				},
				{
					Order: fixturesOrder39,
					Transactions: []*paymentPb.Transaction{
						fixturesTransaction8,
					},
				},
			},
		}

		txnWorkflowPayloadByte, _  = protoJson.Marshal(txnBackfillWorkflowPayload)
		txnBackfillEnrichedByte, _ = protoJson.Marshal(txnBackfillEnrichedData)
	)

	type mockWrite struct {
		enable  bool
		path    string
		payload []byte
		err     error
	}
	tests := []struct {
		name      string
		req       *payPb.BackupTxnBackfillDataToS3Request
		res       *payPb.BackupTxnBackfillDataToS3Response
		mockWrite mockWrite
		wantErr   bool
		err       error
	}{
		{
			name: "successfully wrote workflow payload to S3",
			req: &payPb.BackupTxnBackfillDataToS3Request{
				ScoopJobId: "scoop-job-id",
				WorkflowId: "workflow-id",
				S3Payload: &payPb.BackupTxnBackfillDataToS3Request_TxnBackfillWorkflowPayload{
					TxnBackfillWorkflowPayload: txnBackfillWorkflowPayload,
				},
			},
			res: &payPb.BackupTxnBackfillDataToS3Response{
				Status: rpc.StatusOk(),
			},
			mockWrite: mockWrite{
				enable:  true,
				path:    "oldEnrichedTxn/scoop-job-id/workflow-id.txt",
				payload: txnWorkflowPayloadByte,
			},
		},
		{
			name: "successfully wrote enriched data to S3",
			req: &payPb.BackupTxnBackfillDataToS3Request{
				ScoopJobId: "scoop-job-id",
				WorkflowId: "workflow-id",
				S3Payload: &payPb.BackupTxnBackfillDataToS3Request_TxnBackfillEnrichedData{
					TxnBackfillEnrichedData: txnBackfillEnrichedData,
				},
			},
			res: &payPb.BackupTxnBackfillDataToS3Response{
				Status: rpc.StatusOk(),
			},
			mockWrite: mockWrite{
				enable:  true,
				path:    "newEnrichedTxn/scoop-job-id/workflow-id.txt",
				payload: txnBackfillEnrichedByte,
			},
		},
		{
			name: "unable to write to S3 because write method threw an error",
			req: &payPb.BackupTxnBackfillDataToS3Request{
				ScoopJobId: "scoop-job-id",
				WorkflowId: "workflow-id",
				S3Payload: &payPb.BackupTxnBackfillDataToS3Request_TxnBackfillEnrichedData{
					TxnBackfillEnrichedData: txnBackfillEnrichedData,
				},
			},
			res: &payPb.BackupTxnBackfillDataToS3Response{
				Status: rpc.StatusInternal(),
			},
			mockWrite: mockWrite{
				enable:  true,
				path:    "newEnrichedTxn/scoop-job-id/workflow-id.txt",
				payload: txnBackfillEnrichedByte,
				err:     epifierrors.ErrPermanent,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockWrite.enable {
				mockS3Client.EXPECT().Write(gomock.Any(), tt.mockWrite.path, tt.mockWrite.payload, "bucket-owner-full-control").
					Return(tt.mockWrite.err)
			}
			got, err := payService.BackupTxnBackfillDataToS3(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BackupTxnBackfillDataToS3() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("BackupTxnBackfillDataToS3() got: %v, want: %v", got, tt.res)
				return
			}
		})
	}
}
