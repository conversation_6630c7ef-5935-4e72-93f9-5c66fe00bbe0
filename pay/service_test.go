package pay_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/pay"
	config "github.com/epifi/gamma/pay/config/server"
	genConf "github.com/epifi/gamma/pay/config/server/genconf"
	daoMocks "github.com/epifi/gamma/pay/dao/mocks"
	mockProcessor "github.com/epifi/gamma/pay/internal/mocks"
)

var (
	conf    *config.Config
	dynConf *genConf.Config
)

func TestService_BatchUpdateOrderWithTransactions(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBroker := eventMocks.NewMockBroker(ctr)
	mockTxnDao := daoMocks.NewMockTransactionDao(ctr)
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)
	mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()

	payService := pay.NewService(mockOrderDao, nil, nil, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		mockTxnDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	defer ctr.Finish()

	type mockBatchUpdateTransactions struct {
		enable bool
		req    []*payPb.TransactionWithFieldMasks
		err    error
	}

	type mockBatchUpdateOrders struct {
		enable bool
		req    []*payPb.OrderWithFieldMasks
		err    error
	}

	tests := []struct {
		name                        string
		req                         *payPb.BatchUpdateOrderWithTransactionsRequest
		mockBatchUpdateTransactions mockBatchUpdateTransactions
		mockBatchUpdateOrders       mockBatchUpdateOrders
		res                         *payPb.BatchUpdateOrderWithTransactionsResponse
		wantErr                     bool
	}{
		{
			name: "Successfully updated multiple orders and transactions",
			req: &payPb.BatchUpdateOrderWithTransactionsRequest{
				UpdateOrderTxnRequests: []*payPb.BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask{
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder39,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_FROM_ACTOR_ID,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction1,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_TO,
									paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
								},
							},
							{
								Transaction: fixturesTransaction8,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
								},
							},
						},
					},
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder42,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_TAGS,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction11,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
									paymentPb.TransactionFieldMask_UTR,
								},
							},
						},
					},
				},
			},
			res: &payPb.BatchUpdateOrderWithTransactionsResponse{
				Status: rpc.StatusOk(),
			},
			mockBatchUpdateTransactions: mockBatchUpdateTransactions{
				enable: true,
				req: []*payPb.TransactionWithFieldMasks{
					{
						Transaction: fixturesTransaction1,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_TO,
							paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
						},
					},
					{
						Transaction: fixturesTransaction8,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_FROM,
						},
					},
					{
						Transaction: fixturesTransaction11,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_FROM,
							paymentPb.TransactionFieldMask_UTR,
						},
					},
				},
				err: nil,
			},
			mockBatchUpdateOrders: mockBatchUpdateOrders{
				enable: true,
				req: []*payPb.OrderWithFieldMasks{
					{
						Order: fixturesOrder39,
						FieldMasks: []orderPb.OrderFieldMask{
							orderPb.OrderFieldMask_PROVENANCE,
							orderPb.OrderFieldMask_FROM_ACTOR_ID,
						},
					},
					{
						Order: fixturesOrder42,
						FieldMasks: []orderPb.OrderFieldMask{
							orderPb.OrderFieldMask_PROVENANCE,
							orderPb.OrderFieldMask_TAGS,
						},
					},
				},
			},
		},
		{
			name: "Updation failed because 1 order field mask list contains non updatable field mask",
			req: &payPb.BatchUpdateOrderWithTransactionsRequest{
				UpdateOrderTxnRequests: []*payPb.BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask{
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder39,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_AMOUNT,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction1,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_TO,
									paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
								},
							},
							{
								Transaction: fixturesTransaction8,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
								},
							},
						},
					},
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder42,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_TAGS,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction11,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
									paymentPb.TransactionFieldMask_UTR,
								},
							},
						},
					},
				},
			},
			res: &payPb.BatchUpdateOrderWithTransactionsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "Updation failed because 1 transaction field mask list contains non updatable field mask",
			req: &payPb.BatchUpdateOrderWithTransactionsRequest{
				UpdateOrderTxnRequests: []*payPb.BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask{
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder39,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_FROM_ACTOR_ID,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction1,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_TO,
									paymentPb.TransactionFieldMask_REMARKS,
								},
							},
							{
								Transaction: fixturesTransaction8,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
								},
							},
						},
					},
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder42,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_TAGS,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction11,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
									paymentPb.TransactionFieldMask_UTR,
								},
							},
						},
					},
				},
			},
			res: &payPb.BatchUpdateOrderWithTransactionsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "Updation failed because order dao returned error",
			req: &payPb.BatchUpdateOrderWithTransactionsRequest{
				UpdateOrderTxnRequests: []*payPb.BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask{
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder39,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_FROM_ACTOR_ID,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction1,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_TO,
									paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
								},
							},
							{
								Transaction: fixturesTransaction8,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
								},
							},
						},
					},
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder42,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_TAGS,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction11,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
									paymentPb.TransactionFieldMask_UTR,
								},
							},
						},
					},
				},
			},
			res: &payPb.BatchUpdateOrderWithTransactionsResponse{
				Status: rpc.StatusInternal(),
			},
			mockBatchUpdateTransactions: mockBatchUpdateTransactions{
				enable: true,
				req: []*payPb.TransactionWithFieldMasks{
					{
						Transaction: fixturesTransaction1,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_TO,
							paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
						},
					},
					{
						Transaction: fixturesTransaction8,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_FROM,
						},
					},
					{
						Transaction: fixturesTransaction11,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_FROM,
							paymentPb.TransactionFieldMask_UTR,
						},
					},
				},
				err: nil,
			},
			mockBatchUpdateOrders: mockBatchUpdateOrders{
				enable: true,
				req: []*payPb.OrderWithFieldMasks{
					{
						Order: fixturesOrder39,
						FieldMasks: []orderPb.OrderFieldMask{
							orderPb.OrderFieldMask_PROVENANCE,
							orderPb.OrderFieldMask_FROM_ACTOR_ID,
						},
					},
					{
						Order: fixturesOrder42,
						FieldMasks: []orderPb.OrderFieldMask{
							orderPb.OrderFieldMask_PROVENANCE,
							orderPb.OrderFieldMask_TAGS,
						},
					},
				},
				err: epifierrors.ErrInvalidArgument,
			},
		},
		{
			name: "Updation failed because transaction dao returned error",
			req: &payPb.BatchUpdateOrderWithTransactionsRequest{
				UpdateOrderTxnRequests: []*payPb.BatchUpdateOrderWithTransactionsRequest_OrderAndTransactionsWithFieldMask{
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder39,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_FROM_ACTOR_ID,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction1,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_TO,
									paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
								},
							},
							{
								Transaction: fixturesTransaction8,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
								},
							},
						},
					},
					{
						UpdateOrderRequest: &payPb.OrderWithFieldMasks{
							Order: fixturesOrder42,
							FieldMasks: []orderPb.OrderFieldMask{
								orderPb.OrderFieldMask_PROVENANCE,
								orderPb.OrderFieldMask_TAGS,
							},
						},
						UpdateTxnRequests: []*payPb.TransactionWithFieldMasks{
							{
								Transaction: fixturesTransaction11,
								FieldMasks: []paymentPb.TransactionFieldMask{
									paymentPb.TransactionFieldMask_PI_FROM,
									paymentPb.TransactionFieldMask_UTR,
								},
							},
						},
					},
				},
			},
			res: &payPb.BatchUpdateOrderWithTransactionsResponse{
				Status: rpc.StatusInternal(),
			},
			mockBatchUpdateTransactions: mockBatchUpdateTransactions{
				enable: true,
				req: []*payPb.TransactionWithFieldMasks{
					{
						Transaction: fixturesTransaction1,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_TO,
							paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL,
						},
					},
					{
						Transaction: fixturesTransaction8,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_FROM,
						},
					},
					{
						Transaction: fixturesTransaction11,
						FieldMasks: []paymentPb.TransactionFieldMask{
							paymentPb.TransactionFieldMask_PI_FROM,
							paymentPb.TransactionFieldMask_UTR,
						},
					},
				},
				err: epifierrors.ErrTransient,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockBatchUpdateTransactions.enable {
				mockTxnDao.EXPECT().BatchUpdate(gomock.Any(), tt.mockBatchUpdateTransactions.req).
					Return(tt.mockBatchUpdateTransactions.err)
			}
			if tt.mockBatchUpdateOrders.enable {
				mockOrderDao.EXPECT().BatchUpdate(gomock.Any(), tt.mockBatchUpdateOrders.req).
					Return(tt.mockBatchUpdateOrders.err)
			}
			got, err := payService.BatchUpdateOrderWithTransactions(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateOrderWithTransactions() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("BatchUpdateOrderWithTransactions() got: %v, want: %v", got, tt.res)
				return
			}
		})
	}
}

func TestService_GetBulkOrdersAndTransactions(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBroker := eventMocks.NewMockBroker(ctr)
	mockOrderWithTxnsProcessor := mockProcessor.NewMockOrderWithTransactionsProcessor(ctr)
	mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()

	payService := pay.NewService(nil, nil, nil, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockOrderWithTxnsProcessor, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	defer ctr.Finish()

	type mockGetBulkOrderWithTransactions struct {
		enable bool
		req    []*payPb.OrderIdWithTransactionsId
		res    []*payPb.OrderWithTransactions
		err    error
	}

	tests := []struct {
		name                             string
		req                              *payPb.GetBulkOrdersAndTransactionsRequest
		mockGetBulkOrderWithTransactions mockGetBulkOrderWithTransactions
		res                              *payPb.GetBulkOrdersAndTransactionsResponse
		wantErr                          bool
	}{
		{
			name: "Fetched multiple orders and transactions successfully",
			req: &payPb.GetBulkOrdersAndTransactionsRequest{
				TransactionWithOrder: []*payPb.OrderIdWithTransactionsId{
					{
						OrderId: fixturesOrder39.GetId(),
						TransactionId: []string{
							fixturesTransaction11.GetId(),
						},
					},
					{
						OrderId: fixturesOrder41.GetId(),
						TransactionId: []string{
							fixturesTransaction1.GetId(),
						},
					},
				},
			},
			mockGetBulkOrderWithTransactions: mockGetBulkOrderWithTransactions{
				enable: true,
				req: []*payPb.OrderIdWithTransactionsId{
					{
						OrderId: fixturesOrder39.GetId(),
						TransactionId: []string{
							fixturesTransaction11.GetId(),
						},
					},
					{
						OrderId: fixturesOrder41.GetId(),
						TransactionId: []string{
							fixturesTransaction1.GetId(),
						},
					},
				},
				res: []*payPb.OrderWithTransactions{
					{
						Order: fixturesOrder39,
						Transactions: []*paymentPb.Transaction{
							fixturesTransaction11,
						},
					},
					{
						Order: fixturesOrder41,
						Transactions: []*paymentPb.Transaction{
							fixturesTransaction1,
						},
					},
				},
			},
			res: &payPb.GetBulkOrdersAndTransactionsResponse{
				OrderWithTransactions: []*payPb.OrderWithTransactions{
					{
						Order: fixturesOrder39,
						Transactions: []*paymentPb.Transaction{
							fixturesTransaction11,
						},
					},
					{
						Order: fixturesOrder41,
						Transactions: []*paymentPb.Transaction{
							fixturesTransaction1,
						},
					},
				},
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "Illegal argument passed to the RPC",
			req: &payPb.GetBulkOrdersAndTransactionsRequest{
				TransactionWithOrder: []*payPb.OrderIdWithTransactionsId{},
			},
			res: &payPb.GetBulkOrdersAndTransactionsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "failed because error returned from the processor",
			req: &payPb.GetBulkOrdersAndTransactionsRequest{
				TransactionWithOrder: []*payPb.OrderIdWithTransactionsId{
					{
						OrderId: fixturesOrder39.GetId(),
						TransactionId: []string{
							fixturesTransaction11.GetId(),
						},
					},
					{
						OrderId: fixturesOrder41.GetId(),
						TransactionId: []string{
							fixturesTransaction1.GetId(),
						},
					},
				},
			},
			res: &payPb.GetBulkOrdersAndTransactionsResponse{
				Status: rpc.StatusInternal(),
			},
			mockGetBulkOrderWithTransactions: mockGetBulkOrderWithTransactions{
				enable: true,
				req: []*payPb.OrderIdWithTransactionsId{
					{
						OrderId: fixturesOrder39.GetId(),
						TransactionId: []string{
							fixturesTransaction11.GetId(),
						},
					},
					{
						OrderId: fixturesOrder41.GetId(),
						TransactionId: []string{
							fixturesTransaction1.GetId(),
						},
					},
				},
				err: epifierrors.ErrPermanent,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetBulkOrderWithTransactions.enable {
				mockOrderWithTxnsProcessor.EXPECT().GetBulkOrderWithTransactions(context.Background(), tt.mockGetBulkOrderWithTransactions.req).
					Return(tt.mockGetBulkOrderWithTransactions.res, tt.mockGetBulkOrderWithTransactions.err)
			}
			got, err := payService.GetBulkOrdersAndTransactions(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrdersAndTransactions() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("GetOrdersAndTransactions() got: %v, want: %v", got, tt.res)
				return
			}
		})
	}
}

func TestService_DataSigningE2E(t *testing.T) {
	tests := []*struct {
		name      string
		plainText []byte
	}{
		{
			name:      "Successful E2E encryption and decryption - simple text",
			plainText: []byte("Hello World"),
		},
		{
			name:      "Successful E2E encryption and decryption - empty string",
			plainText: []byte(""),
		},
		{
			name:      "Successful E2E encryption and decryption - special characters",
			plainText: []byte("!@#$%^&*()_+{}[]|\\:;\"'<>,.?/"),
		},
		{
			name:      "Successful E2E encryption and decryption - long text",
			plainText: []byte("This is a much longer piece of text that we want to encrypt and decrypt. It contains multiple sentences and even some numbers like 12345."),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, _, release := getPayServiceWithMock(t)
			defer release()
			encryptRes, err := s.GetSignedData(context.Background(), &payPb.GetSignedDataRequest{
				Data: tt.plainText,
			})
			require.NoError(t, epifigrpc.RPCError(encryptRes, err))
			decryptRes, err := s.GetPlainData(context.Background(), &payPb.GetPlainDataRequest{
				SignedData: encryptRes.GetSignedData(),
			})
			require.NoError(t, epifigrpc.RPCError(decryptRes, err))
			assert.Equal(t, tt.plainText, decryptRes.GetPlainData())
		})
	}
}
