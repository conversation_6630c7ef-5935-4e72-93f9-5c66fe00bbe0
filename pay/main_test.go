package pay_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	mockUpi "github.com/epifi/gamma/api/upi/mocks"

	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"
	mocksS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	mockevents "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	mockTxnAggregates "github.com/epifi/gamma/api/analyser/txnaggregates/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	mockOrder "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentMocks "github.com/epifi/gamma/api/order/payment/mocks"
	mockAccountPi "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	mockPIClient "github.com/epifi/gamma/api/paymentinstrument/mocks"
	mockRecurPaymentClient "github.com/epifi/gamma/api/recurringpayment/mocks"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	mockUpiOnboarding "github.com/epifi/gamma/api/upi/onboarding/mocks"
	groupMocks "github.com/epifi/gamma/api/user/group/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	mockOnboardingPb "github.com/epifi/gamma/api/user/onboarding/mocks"
	mockVgPay "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/pg/mocks"
	"github.com/epifi/gamma/order/test/mocks"
	"github.com/epifi/gamma/pay"
	payServerConfig "github.com/epifi/gamma/pay/config/server"
	payServerGenConf "github.com/epifi/gamma/pay/config/server/genconf"
	daoMocks "github.com/epifi/gamma/pay/dao/mocks"
	mockProcessor "github.com/epifi/gamma/pay/internal/mocks"
	"github.com/epifi/gamma/pay/internal/paymentauth"
	"github.com/epifi/gamma/pay/test"
	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
	mocks3 "github.com/epifi/gamma/pkg/vendorstore/mocks"
)

var (
	payConf            *payServerConfig.Config
	payServerGenConfig *payServerGenConf.Config

	fixturesOrder39 = &orderPb.Order{
		Id:          "order-39",
		FromActorId: "tt-actor-1",
		ToActorId:   "actor-2",
		Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Status:      orderPb.OrderStatus_PAYMENT_FAILED,
		Provenance:  orderPb.OrderProvenance_USER_APP,
		Amount:      &moneyPb.Money{CurrencyCode: "INR", Units: 1000},
		ExternalId:  "order-ext-id-sample-tt-2",
		ClientReqId: "order-client-req-sample-tt-2",
	}

	fixturesOrder41 = &orderPb.Order{
		Id:          "",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Status:      orderPb.OrderStatus_CREATED,
		Provenance:  orderPb.OrderProvenance_USER_APP,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		ExternalId:    "order-ext-1",
		ClientReqId:   "order-client-req-1",
		WorkflowRefId: "wf-req-1",
	}

	fixturesOrder42 = &orderPb.Order{
		Id:          "order-3",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Status:      orderPb.OrderStatus_IN_PAYMENT,
		Provenance:  orderPb.OrderProvenance_USER_APP,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		ExternalId:           "order-ext-3",
		ToActorLocationToken: "to-actor-location",
		Tags: []orderPb.OrderTag{
			orderPb.OrderTag_BHARAT_QR,
			orderPb.OrderTag_CASH,
		},
	}

	fixturesOrder43 = &orderPb.Order{
		Id:          "order-45",
		FromActorId: "actor-1",
		ToActorId:   "actor-2",
		Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Status:      orderPb.OrderStatus_PAYMENT_FAILED,
		Provenance:  orderPb.OrderProvenance_USER_APP,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		ExternalId: "order-ext-5",
		Tags: []orderPb.OrderTag{
			orderPb.OrderTag_MERCHANT,
			orderPb.OrderTag_FD,
		},
	}

	fixturesTransaction1 = &paymentPb.Transaction{
		Id:          "transaction-1",
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
		Remarks:         "XYZ",
		ReqId:           "req-1",
		OrderId:         "order-1",
	}

	fixturesTransaction11 = &paymentPb.Transaction{
		Id:          "transaction-11",
		PiFrom:      "pi-3",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Remarks:         "upi-txn",
		ReqId:           "req-11",
		OrderId:         "order-11",
	}

	fixturesTransaction8 = &paymentPb.Transaction{
		Id:           "transaction-8",
		PiFrom:       "pi-1",
		PiTo:         "pi-2",
		Utr:          "FED-8",
		PartnerRefId: "FED-8",
		PartnerBank:  commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        2000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_SUCCESS,
		PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
		Remarks:         "XYZ",
		ReqId:           "req-8",
		OrderId:         "order-8",
	}

	fixturesTransaction9 = &paymentPb.Transaction{
		Id:           "",
		PiFrom:       "pi-1",
		PiTo:         "pi-2",
		Utr:          "FED-8",
		PartnerRefId: "FED-8",
		PartnerBank:  commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        2000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_SUCCESS,
		PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
		Remarks:         "XYZ",
		ReqId:           "req-8",
		OrderId:         "order-9",
	}
)

func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, payConf, payServerGenConfig, _, teardown = test.InitTestServer()
	err := pgauthkeys.InitialisePgProgramToAuthParams(payConf.PgProgramToAuthSecretMap)
	if err != nil {
		panic(err)
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockService struct {
	mockCelestialClient                    *mockCelestial.MockCelestialClient
	mockOrderDao                           *daoMocks.MockOrderDao
	mockDecisionEngineClient               *mocks.MockDecisionEngineClient
	mockPaymentClient                      *paymentMocks.MockPaymentClient
	mockOrderServiceClient                 *mockOrder.MockOrderServiceClient
	mockActorClient                        *mockActor.MockActorClient
	mockSavingsClient                      *mockSavings.MockSavingsClient
	mockISavingProcessor                   *mockProcessor.MockISavingsProcessor
	mockVgPayClient                        *mockVgPay.MockPaymentClient
	mockIAuthProcessor                     *mockProcessor.MockIAuthProcessor
	mockIUserProcessor                     *mockProcessor.MockIUserProcessor
	mockIPiProcessor                       *mockProcessor.MockIPiProcessor
	broker                                 *mockevents.MockBroker
	mockTxnDao                             *daoMocks.MockTransactionDao
	mockAccPiClient                        *mockAccountPi.MockAccountPIRelationClient
	mockUpiOnboardingClient                *mockUpiOnboarding.MockUpiOnboardingClient
	mockTxnAggregatesClient                *mockTxnAggregates.MockTxnAggregatesClient
	mockPiClient                           *mockPIClient.MockPiClient
	mockCelestialProcessor                 *mockProcessor.MockCelestialProcessor
	mockTimelineProcessor                  *mockProcessor.MockITimelineProcessor
	mockActorProcessor                     *mockProcessor.MockIActorProcessor
	mockFundTransferCelestialProcessor     *mockProcessor.MockPaymentAuthProcessor
	mockFundTransferOmsProcessor           *mockProcessor.MockPaymentAuthProcessor
	mockInternationalFundTransferProcessor *mockProcessor.MockPaymentAuthProcessor
	mockInPaymentPublisher                 *queueMocks.MockPublisher
	mockReleaseEvaluator                   *mock_release.MockIEvaluator
	mockOnboardingClient                   *mockOnboardingPb.MockOnboardingClient
	mockOrderWithTxnProcessor              *mockProcessor.MockOrderWithTransactionsProcessor
	mockS3Client                           *mocksS3.MockS3Client
	mockUpiProcessor                       *mockProcessor.MockIUpiProcessor
	mockUserClient                         *userMocks.MockUsersClient
	mockUserGroupClient                    *groupMocks.MockGroupClient
	tpapForNonSaUserEvaluator              *mock_release.MockIEvaluator
	tpapForSaUserEvaluator                 *mock_release.MockIEvaluator
	mockOvomDao                            *daoMocks.MockOrderVendorOrderMapDao
	mockVgPgClient                         *mocks2.MockPaymentGatewayClient
	mockRecurringPaymentClient             *mockRecurPaymentClient.MockRecurringPaymentServiceClient
	mockVendorStore                        *mocks3.MockVendorStore
	mockEnrichOrderProcessor               *mockProcessor.MockEnrichOrderAndTxnsProcessor
	mockEnrichOrderProcessorFactory        *mockProcessor.MockEnrichOrderAndTxnFactory
	mockUpiClient                          *mockUpi.MockUPIClient
}

func getPayServiceWithMock(t *testing.T) (*pay.Service, *mockService, func()) {
	ctr := gomock.NewController(t)
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)
	mockCelestialClient := mockCelestial.NewMockCelestialClient(ctr)
	mockOrderClient := mockOrder.NewMockOrderServiceClient(ctr)
	mockPaymentClient := paymentMocks.NewMockPaymentClient(ctr)
	mockActorClient := mockActor.NewMockActorClient(ctr)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctr)
	mockDecisionEngineClient := mocks.NewMockDecisionEngineClient(ctr)
	mockSavingsProcessor := mockProcessor.NewMockISavingsProcessor(ctr)
	mockVgPayClient := mockVgPay.NewMockPaymentClient(ctr)
	mockUserProcessor := mockProcessor.NewMockIUserProcessor(ctr)
	mockAuthProcessor := mockProcessor.NewMockIAuthProcessor(ctr)
	piProcessor := mockProcessor.NewMockIPiProcessor(ctr)
	broker := mockevents.NewMockBroker(ctr)
	mockTxnDao := daoMocks.NewMockTransactionDao(ctr)
	mockAccPiClient := mockAccountPi.NewMockAccountPIRelationClient(ctr)
	mockUpiOnboardingClient := mockUpiOnboarding.NewMockUpiOnboardingClient(ctr)
	mockTxnAggregatesClient := mockTxnAggregates.NewMockTxnAggregatesClient(ctr)
	mockCelestialProcessor := mockProcessor.NewMockCelestialProcessor(ctr)
	mockTimelineProcessor := mockProcessor.NewMockITimelineProcessor(ctr)
	mockActorProcessor := mockProcessor.NewMockIActorProcessor(ctr)
	mockFundTransferCelestialProcessor := mockProcessor.NewMockPaymentAuthProcessor(ctr)
	mockFundTransferOmsProcessor := mockProcessor.NewMockPaymentAuthProcessor(ctr)
	mockInternationalFundTransferProcessor := mockProcessor.NewMockPaymentAuthProcessor(ctr)
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockPiClient := mockPIClient.NewMockPiClient(ctr)
	mockReleaseEvaluator := mock_release.NewMockIEvaluator(ctr)
	mockOnboardingClient := mockOnboardingPb.NewMockOnboardingClient(ctr)
	mockOrderWithTxnProcessor := mockProcessor.NewMockOrderWithTransactionsProcessor(ctr)
	mockS3Client := mocksS3.NewMockS3Client(ctr)
	mockUpiProcessor := mockProcessor.NewMockIUpiProcessor(ctr)
	mockNonSaUserTpapEvaluator := mock_release.NewMockIEvaluator(ctr)
	mockSaUserTpapEvaluator := mock_release.NewMockIEvaluator(ctr)
	mockOvomDao := daoMocks.NewMockOrderVendorOrderMapDao(ctr)
	mockPgVgClient := mocks2.NewMockPaymentGatewayClient(ctr)
	mockRecurringPaymentClient := mockRecurPaymentClient.NewMockRecurringPaymentServiceClient(ctr)
	mockVendorStore := mocks3.NewMockVendorStore(ctr)
	mockEnrichOrderProcessor := mockProcessor.NewMockEnrichOrderAndTxnsProcessor(ctr)
	mockEnrichOrderFactory := mockProcessor.NewMockEnrichOrderAndTxnFactory(ctr)
	mockUpiClient := mockUpi.NewMockUPIClient(ctr)
	mockServices := &mockService{
		mockDecisionEngineClient:               mockDecisionEngineClient,
		mockPaymentClient:                      mockPaymentClient,
		mockOrderServiceClient:                 mockOrderClient,
		mockActorClient:                        mockActorClient,
		mockSavingsClient:                      mockSavingsClient,
		mockISavingProcessor:                   mockSavingsProcessor,
		mockVgPayClient:                        mockVgPayClient,
		mockIAuthProcessor:                     mockAuthProcessor,
		mockIUserProcessor:                     mockUserProcessor,
		mockIPiProcessor:                       piProcessor,
		mockOrderDao:                           mockOrderDao,
		mockCelestialClient:                    mockCelestialClient,
		broker:                                 broker,
		mockTxnDao:                             mockTxnDao,
		mockAccPiClient:                        mockAccPiClient,
		mockUpiOnboardingClient:                mockUpiOnboardingClient,
		mockTxnAggregatesClient:                mockTxnAggregatesClient,
		mockCelestialProcessor:                 mockCelestialProcessor,
		mockActorProcessor:                     mockActorProcessor,
		mockTimelineProcessor:                  mockTimelineProcessor,
		mockFundTransferOmsProcessor:           mockFundTransferOmsProcessor,
		mockFundTransferCelestialProcessor:     mockFundTransferCelestialProcessor,
		mockInternationalFundTransferProcessor: mockInternationalFundTransferProcessor,
		mockInPaymentPublisher:                 mockInPaymentPublisher,
		mockPiClient:                           mockPiClient,
		mockReleaseEvaluator:                   mockReleaseEvaluator,
		mockOnboardingClient:                   mockOnboardingClient,
		mockOrderWithTxnProcessor:              mockOrderWithTxnProcessor,
		mockS3Client:                           mockS3Client,
		mockUpiProcessor:                       mockUpiProcessor,
		tpapForSaUserEvaluator:                 mockSaUserTpapEvaluator,
		tpapForNonSaUserEvaluator:              mockNonSaUserTpapEvaluator,
		mockOvomDao:                            mockOvomDao,
		mockVgPgClient:                         mockPgVgClient,
		mockRecurringPaymentClient:             mockRecurringPaymentClient,
		mockVendorStore:                        mockVendorStore,
		mockEnrichOrderProcessor:               mockEnrichOrderProcessor,
		mockEnrichOrderProcessorFactory:        mockEnrichOrderFactory,
		mockUpiClient:                          mockUpiClient,
	}

	payService := pay.NewService(
		mockOrderDao,
		mockCelestialClient,
		mockOrderClient,
		payConf,
		payServerGenConfig,
		mockDecisionEngineClient,
		mockSavingsProcessor,
		mockPaymentClient,
		mockVgPayClient,
		mockAuthProcessor,
		mockUserProcessor,
		piProcessor,
		broker,
		mockAccPiClient,
		mockTxnDao,
		mockUpiOnboardingClient,
		mockTxnAggregatesClient,
		mockCelestialProcessor,
		payServerGenConfig.FundTransferParams(),
		payServerGenConfig.FundTransferCelestialParams(),
		&paymentauth.AuthFactoryImpl{
			AuthFundTranserCelestial:      mockFundTransferCelestialProcessor,
			AuthInternationalFundTransfer: mockInternationalFundTransferProcessor,
			AuthFundTransferOms:           mockFundTransferOmsProcessor,
		},
		mockTimelineProcessor,
		mockActorProcessor,
		mockInPaymentPublisher,
		mockPiClient,
		mockReleaseEvaluator,
		mockOnboardingClient,
		mockOrderWithTxnProcessor,
		mockS3Client,
		mockUpiProcessor,
		mockSavingsClient,
		mockNonSaUserTpapEvaluator,
		mockSaUserTpapEvaluator,
		mockPgVgClient,
		mockVendorStore,
		mockOvomDao,
		nil,
		nil,
		nil,
		nil,
		mockRecurringPaymentClient,
		nil,
		mockEnrichOrderFactory,
		nil,
		nil,
		nil,
		mockUpiClient,
		nil,
		nil,
	)
	return payService, mockServices, func() {
		ctr.Finish()
	}
}
