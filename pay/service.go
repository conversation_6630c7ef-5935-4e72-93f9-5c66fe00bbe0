package pay

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"

	temporalClient "go.temporal.io/sdk/client"
	"go.uber.org/zap"

	"github.com/samber/lo"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	cryptoAes "github.com/epifi/be-common/pkg/crypto/aes"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	actorPb "github.com/epifi/gamma/api/actor"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	tieringPb "github.com/epifi/gamma/api/tiering"

	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	accountsPb "github.com/epifi/gamma/api/savings"
	savingsPb "github.com/epifi/gamma/api/savings"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgPgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	payServerConfig "github.com/epifi/gamma/pay/config/server"
	genConf "github.com/epifi/gamma/pay/config/server/genconf"
	payServerGenConfig "github.com/epifi/gamma/pay/config/server/genconf"
	"github.com/epifi/gamma/pay/dao"
	"github.com/epifi/gamma/pay/internal"
	enrichOrderProcessor "github.com/epifi/gamma/pay/internal/enrichorder"
	"github.com/epifi/gamma/pay/internal/paymentauth"
	"github.com/epifi/gamma/pay/internal/pgprocessor"
	"github.com/epifi/gamma/pay/internal/signalling"
	"github.com/epifi/gamma/pay/txnaggregatesprovider"
	types2 "github.com/epifi/gamma/pay/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/vendorstore"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"
)

type TxnBackfillS3Client s3.S3Client

type Service struct {
	// UnimplementedPaymentServer is embedded to have forward compatible implementations
	payPb.UnimplementedPayServer
	celestialClient                 celestialPb.CelestialClient
	orderDao                        dao.OrderDao
	orderClient                     orderPb.OrderServiceClient
	conf                            *payServerConfig.Config
	dynConf                         *genConf.Config
	decisionEngineClient            paymentPb.DecisionEngineClient
	savingsProcessor                internal.ISavingsProcessor
	paymentClient                   paymentPb.PaymentClient
	vgPaymentClient                 vgPaymentPb.PaymentClient
	authProcessor                   internal.IAuthProcessor
	userProcessor                   internal.IUserProcessor
	piProcessor                     internal.IPiProcessor
	eventBroker                     events.Broker
	acPiClient                      accPiPb.AccountPIRelationClient
	txnDao                          dao.TransactionDao
	upiOnboardingClient             upiOnboardingPb.UpiOnboardingClient
	txnAggregatesClient             txnAggregatesPb.TxnAggregatesClient
	txnAggregatorPinot              txnaggregatesprovider.TxnAggregatesProvider
	txnAggregatorCrdb               txnaggregatesprovider.TxnAggregatesProvider
	celestialProcessor              internal.CelestialProcessor
	fundTransferParams              *payServerGenConfig.FundTransferParams
	authFactory                     paymentauth.AuthFactory
	timelineProcessor               internal.ITimelineProcessor
	actorProcessor                  internal.IActorProcessor
	inPaymentOrderPublisher         types2.InPaymentOrderUpdatePublisher
	fundTransferCelestialParams     *payServerGenConfig.FundTransferCelestialParams
	piClient                        piPb.PiClient
	releaseEvaluator                release.IEvaluator
	onboardingClient                onboardingPb.OnboardingClient
	orderWithTxnProcessor           internal.OrderWithTransactionsProcessor
	txnBackfillS3Client             TxnBackfillS3Client
	upiProcessor                    internal.IUpiProcessor
	accountClient                   accountsPb.SavingsClient
	savingsClient                   savingsPb.SavingsClient
	constraintFactory               release.ConstraintFactory
	tpapForNonSaUserEvaluator       types2.TpapForNonSaUserEvaluator
	tpapForSaUserEvaluator          types2.TpapForSaUserEvaluator
	pgClient                        vgPgPb.PaymentGatewayClient
	vendorStore                     vendorstore.VendorStore
	orderVendorOrderMapDao          dao.OrderVendorOrderMapDao
	nameCheckClient                 vgtypes.UNNameCheckClientWithInterceptors
	pgOrderProcessorFactory         pgprocessor.Factory
	orderWorkflowToDomainDataMethod map[orderPb.OrderWorkflow]types2.DomainDataMethodProvider
	payTemporalClient               temporalClient.Client
	recurringPaymentClient          rpPb.RecurringPaymentServiceClient
	dbTxnExecutorProvider           *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	enrichOrderProcFactory          enrichOrderProcessor.EnrichOrderAndTxnFactory
	signallingProcessorFactory      *signalling.SignallingProcessorFactory
	tieringClient                   tieringPb.TieringClient
	rewardsAggregatesClient         rewardsPinotPb.RewardsAggregatesClient
	rewardsProjectorClient          projectorPb.ProjectorServiceClient
	upiClient                       upiPb.UPIClient
	actorClient                     actorPb.ActorClient
}

func NewService(
	orderDao dao.OrderDao,
	celestialClient celestialPb.CelestialClient,
	orderClient orderPb.OrderServiceClient,
	conf *payServerConfig.Config,
	dynConf *genConf.Config,
	decisionEngineClient paymentPb.DecisionEngineClient,
	savingsProcessor internal.ISavingsProcessor,
	paymentClient paymentPb.PaymentClient,
	vgPaymentClient vgPaymentPb.PaymentClient,
	authProcessor internal.IAuthProcessor,
	userProcessor internal.IUserProcessor,
	piProcessor internal.IPiProcessor,
	eventBroker events.Broker,
	acPiClient accPiPb.AccountPIRelationClient,
	txnDao dao.TransactionDao,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	txnAggregatesClient txnAggregatesPb.TxnAggregatesClient,
	celestialProcessor internal.CelestialProcessor,
	fundTransferParams *payServerGenConfig.FundTransferParams,
	fundTransferCelestialParams *payServerGenConfig.FundTransferCelestialParams,
	authFactory paymentauth.AuthFactory,
	timelineProcessor internal.ITimelineProcessor,
	actorProcessor internal.IActorProcessor,
	inPaymentOrderPublisher types2.InPaymentOrderUpdatePublisher,
	piClient piPb.PiClient,
	releaseEvaluator release.IEvaluator,
	onboardingClient onboardingPb.OnboardingClient,
	orderWithTxnProcessor internal.OrderWithTransactionsProcessor,
	txnBackfillS3Client TxnBackfillS3Client,
	upiProcessor internal.IUpiProcessor,
	savingsClient savingsPb.SavingsClient,
	tpapForNonSaUserEvaluator types2.TpapForNonSaUserEvaluator,
	tpapForSaUserEvaluator types2.TpapForSaUserEvaluator,
	pgClient vgPgPb.PaymentGatewayClient,
	vendorStore vendorstore.VendorStore,
	orderVendorOrderMapDao dao.OrderVendorOrderMapDao,
	nameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	pgOrderProcessorFactory pgprocessor.Factory,
	orderWorkflowToDomainDataMethod map[orderPb.OrderWorkflow]types2.DomainDataMethodProvider,
	payTemporalClient temporalClient.Client,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	dbTxnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	enrichOrderProcFactory enrichOrderProcessor.EnrichOrderAndTxnFactory,
	signallingProcFactory *signalling.SignallingProcessorFactory,
	tieringClient tieringPb.TieringClient,
	rewardsAggregatesClient rewardsPinotPb.RewardsAggregatesClient,
	upiClient upiPb.UPIClient,
	rewardsProjectorClient projectorPb.ProjectorServiceClient,
	actorClient actorPb.ActorClient,
) *Service {
	txnAggregatorPinot := txnaggregatesprovider.NewPinotTxnAggreagtor(txnAggregatesClient)
	txnAggregatorCrdb := txnaggregatesprovider.NewCrdbTxnAggreagtor(txnDao)

	return &Service{
		celestialClient:                 celestialClient,
		orderDao:                        orderDao,
		orderClient:                     orderClient,
		conf:                            conf,
		dynConf:                         dynConf,
		decisionEngineClient:            decisionEngineClient,
		savingsProcessor:                savingsProcessor,
		paymentClient:                   paymentClient,
		vgPaymentClient:                 vgPaymentClient,
		authProcessor:                   authProcessor,
		userProcessor:                   userProcessor,
		piProcessor:                     piProcessor,
		eventBroker:                     eventBroker,
		acPiClient:                      acPiClient,
		txnDao:                          txnDao,
		upiOnboardingClient:             upiOnboardingClient,
		txnAggregatesClient:             txnAggregatesClient,
		txnAggregatorCrdb:               txnAggregatorCrdb,
		txnAggregatorPinot:              txnAggregatorPinot,
		celestialProcessor:              celestialProcessor,
		fundTransferParams:              fundTransferParams,
		authFactory:                     authFactory,
		timelineProcessor:               timelineProcessor,
		actorProcessor:                  actorProcessor,
		fundTransferCelestialParams:     fundTransferCelestialParams,
		inPaymentOrderPublisher:         inPaymentOrderPublisher,
		piClient:                        piClient,
		releaseEvaluator:                releaseEvaluator,
		onboardingClient:                onboardingClient,
		orderWithTxnProcessor:           orderWithTxnProcessor,
		txnBackfillS3Client:             txnBackfillS3Client,
		upiProcessor:                    upiProcessor,
		savingsClient:                   savingsClient,
		tpapForNonSaUserEvaluator:       tpapForNonSaUserEvaluator,
		tpapForSaUserEvaluator:          tpapForSaUserEvaluator,
		pgClient:                        pgClient,
		vendorStore:                     vendorStore,
		orderVendorOrderMapDao:          orderVendorOrderMapDao,
		nameCheckClient:                 nameCheckClient,
		pgOrderProcessorFactory:         pgOrderProcessorFactory,
		orderWorkflowToDomainDataMethod: orderWorkflowToDomainDataMethod,
		payTemporalClient:               payTemporalClient,
		recurringPaymentClient:          recurringPaymentClient,
		dbTxnExecutorProvider:           dbTxnExecutorProvider,
		enrichOrderProcFactory:          enrichOrderProcFactory,
		signallingProcessorFactory:      signallingProcFactory,
		tieringClient:                   tieringClient,
		rewardsAggregatesClient:         rewardsAggregatesClient,
		rewardsProjectorClient:          rewardsProjectorClient,
		upiClient:                       upiClient,
		actorClient:                     actorClient,
	}
}

var (
	nonUpdatableOrderFields = []orderPb.OrderFieldMask{orderPb.OrderFieldMask_STATUS, orderPb.OrderFieldMask_AMOUNT, orderPb.OrderFieldMask_WORKFLOW,
		orderPb.OrderFieldMask_ID, orderPb.OrderFieldMask_CREATED_AT, orderPb.OrderFieldMask_EXTERNAL_ID, orderPb.OrderFieldMask_ORDER_PAYLOAD,
		orderPb.OrderFieldMask_EXPIRE_AT, orderPb.OrderFieldMask_WORFLOW_REF_ID, orderPb.OrderFieldMask_WORFLOW_REF_ID, orderPb.OrderFieldMask_ORDER_FIELD_MASK_UNSPECIFIED}

	nonUpdatableTxnFields = []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_AMOUNT, paymentPb.TransactionFieldMask_RAW_NOTIFICATION_DETAILS,
		paymentPb.TransactionFieldMask_REMARKS, paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_ID, paymentPb.TransactionFieldMask_ORDER_REF_ID,
		paymentPb.TransactionFieldMask_DEDUPE_ID, paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PARTNER_BANK,
		paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_PAYMENT_REQ_INFO, paymentPb.TransactionFieldMask_CREATED_AT,
		paymentPb.TransactionFieldMask_DISPUTED_AT, paymentPb.TransactionFieldMask_ORDER_REF_ID, paymentPb.TransactionFieldMask_DEDUPE_ID}
)

// IsFundTransferViaCelestialAllowed - fetches userGroup for the current user and checks if fund transfer via celestial is allowed for this user
func (s *Service) isFundTransferViaCelestialAllowed(ctx context.Context, actorId string, uiEntryPoint orderPb.UIEntryPoint, clientRequestId string, orderId string) bool {
	if uiEntryPoint == orderPb.UIEntryPoint_PHYSICAL_DEBIT_CARD_CHARGES {
		return true
	}

	// Context (2nd May, 2025): We had FUND_TRANSFER_V1 Feature flag check here. But the app versions added were old. Thus, removed them completely.
	return true
}

// todo (rohan): do we really need this RPC? Ideally, it shouldn't be a wrapper migration when migrating from Order to Pay.
func (s *Service) RecordOffAppPayment(ctx context.Context, req *payPb.RecordOffAppPaymentRequest) (*payPb.RecordOffAppPaymentResponse, error) {
	var (
		res = &payPb.RecordOffAppPaymentResponse{}
	)

	resp, err := s.orderClient.RecordOffAppPayment(ctx, &orderPb.RecordOffAppPaymentRequest{
		SavingsAccountId:            req.GetSavingsAccountId(),
		InternalActorId:             req.GetInternalActorId(),
		AccountType:                 req.GetAccountType(),
		TxnDetails:                  req.GetTxnDetails(),
		ParsedTxnParticulars:        req.GetParsedTxnParticulars(),
		PartnerBank:                 req.GetPartnerBank(),
		TransactionStatus:           req.GetTransactionStatus(),
		TransactionDetailedStatus:   req.GetTransactionDetailedStatus(),
		IsLastAttempt:               req.GetIsLastAttempt(),
		OrderWorkflow:               req.GetOrderWorkflow(),
		ReceivedFromVendorAt:        req.GetReceivedFromVendorAt(),
		AttemptRemitterInfoBackfill: req.GetAttemptRemitterInfoBackfill(),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error in processing off-app payment", zap.Error(rpcErr), logger.AccountId(req.GetSavingsAccountId()),
			zap.String(logger.ACTOR_ID_V2, req.GetInternalActorId()),
		)

		if orderPb.RecordOffAppPaymentResponse_Status(resp.GetStatus().GetCode()) == orderPb.RecordOffAppPaymentResponse_PERMANENT_FAILURE {
			res.Status = rpc.NewStatus(uint32(payPb.RecordOffAppPaymentResponse_PERMANENT_FAILURE), "", "")
			return res, nil
		}

		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = resp.GetStatus()
	res.OrderWithTransactions = resp.GetOrderWithTransactions()

	return res, nil
}

// BatchUpdateOrderWithTransactions rpc is used to batch update orders and their corresponding transactions for multiple fields at a time
// If any update request has empty field masks then that means there is not change in either of order or transaction data for this request. So, we
// will ignore that request during dao update. Also, ID field mask will not be updated
// If amount or status field masks are passed as argument, RPC will throw an error as they are not updatable
// There is no partial update either all will update or all will fail.
// Maximum allowed batch size for this RPC is set at 50
// NOTE: In dao, we are extracting field values for each field mask using switch cases. We have added support for few frequent fields.
// Check the respective dao method for support of the updatable field and add if its missing
func (s *Service) BatchUpdateOrderWithTransactions(ctx context.Context, req *payPb.BatchUpdateOrderWithTransactionsRequest) (*payPb.BatchUpdateOrderWithTransactionsResponse, error) {
	var (
		res                 = &payPb.BatchUpdateOrderWithTransactionsResponse{}
		txnUpdateRequests   []*payPb.TransactionWithFieldMasks
		orderUpdateRequests []*payPb.OrderWithFieldMasks
	)
	for _, orderTxnRequest := range req.GetUpdateOrderTxnRequests() {
		// If an order has empty field masks we are neglecting that order
		if orderTxnRequest.GetUpdateOrderRequest().GetFieldMasks() != nil {

			if ifOrderMaskListContainsOnlyNonUpdatableFields(orderTxnRequest.GetUpdateOrderRequest().GetFieldMasks()) {
				logger.Error(ctx, "invalid field mask", zap.String(logger.ORDER_ID, orderTxnRequest.GetUpdateOrderRequest().GetOrder().GetId()))
				res.Status = rpc.StatusInvalidArgument()
				return res, nil
			}
			orderUpdateRequests = append(orderUpdateRequests, &payPb.OrderWithFieldMasks{
				Order:      orderTxnRequest.GetUpdateOrderRequest().GetOrder(),
				FieldMasks: orderTxnRequest.GetUpdateOrderRequest().GetFieldMasks(),
			})
		}

		// An order can have multiple transactions
		for _, txnRequest := range orderTxnRequest.GetUpdateTxnRequests() {
			// If a transaction has empty field mask we are neglecting that transaction for updation in db
			if txnRequest.GetFieldMasks() != nil {
				if ifTxnMaskListContainsOnlyNonUpdatableFields(txnRequest.GetFieldMasks()) {
					logger.Error(ctx, "invalid field mask", zap.String(logger.TXN_ID, txnRequest.GetTransaction().GetId()))
					res.Status = rpc.StatusInvalidArgument()
					return res, nil
				}
				txnUpdateRequests = append(txnUpdateRequests, &payPb.TransactionWithFieldMasks{
					Transaction: txnRequest.GetTransaction(),
					FieldMasks:  txnRequest.GetFieldMasks(),
				})
			}
		}
	}

	txnErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		err := s.txnDao.BatchUpdate(txnCtx, txnUpdateRequests)
		if err != nil {
			logger.Error(ctx, "failed to update transactions", zap.Error(err))
			return err
		}

		err = s.orderDao.BatchUpdate(txnCtx, orderUpdateRequests)
		if err != nil {
			logger.Error(ctx, "failed to update orders", zap.Error(err))
			return err
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to update transactions and orders in db", zap.Error(txnErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetBulkOrdersAndTransactions(ctx context.Context, req *payPb.GetBulkOrdersAndTransactionsRequest) (*payPb.GetBulkOrdersAndTransactionsResponse, error) {
	var (
		res              = &payPb.GetBulkOrdersAndTransactionsResponse{}
		err              error
		orderWithTxnList []*payPb.OrderWithTransactions
	)

	if req.GetTransactionWithOrder() == nil || len(req.GetTransactionWithOrder()) == 0 {
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	orderWithTxnList, err = s.orderWithTxnProcessor.GetBulkOrderWithTransactions(ctx, req.GetTransactionWithOrder())
	if err != nil {
		logger.Error(ctx, "failed to fetch orderWithTransactions", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.OrderWithTransactions = orderWithTxnList
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetSignedData encrypts the input payload using AES-CBC encryption and returns its base64 encoded value.
// If an error occurs during cipher creation, it logs the error and returns a response with an internal server error status.
func (s *Service) GetSignedData(ctx context.Context, req *payPb.GetSignedDataRequest) (*payPb.GetSignedDataResponse, error) {
	var (
		res = &payPb.GetSignedDataResponse{}
	)

	if s.conf.PayloadSigningCreds == nil {
		logger.Error(ctx, "payload signing creds not found in service configuration")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Step 1: Retrieve the encryption key and initialization vector (IV) from the service configuration.
	encryptionKey := s.conf.PayloadSigningCreds.SigningKey
	iv := s.conf.PayloadSigningCreds.IV
	if encryptionKey == "" || iv == "" {
		logger.Error(ctx, "encryption key or IV is empty")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	plainText := req.GetData()

	// Step 2: Create a new AES cipher block using the encryption key.
	block, err := aes.NewCipher([]byte(encryptionKey))
	if err != nil {
		logger.Error(ctx, "error in creating a new cipher", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Step 3: Create a new CBC encrypter using the AES block and IV.
	encryptor := cipher.NewCBCEncrypter(block, []byte(iv))

	// Step 4: Apply PKCS5 padding to the input plaintext.
	content := cryptoAes.PKCS5Padding(plainText, block.BlockSize())

	// Step 5: Encrypt the padded plaintext using AES-CBC mode.
	cipherText := make([]byte, len(content))
	encryptor.CryptBlocks(cipherText, content)

	// Step 6: Encode the resulting ciphertext to base64.
	encodedCipherText := []byte(base64.StdEncoding.EncodeToString(cipherText))

	// Step 7: Set the encoded ciphertext in the response and set the status to OK.
	res.SignedData = encodedCipherText
	res.Status = rpc.StatusOk()

	return res, nil
}

// GetPlainData decrypts the input signed data using AES-CBC decryption and returns the original plaintext.
// If an error occurs during cipher creation or decoding, it logs the error and returns a response with an internal server error status.
func (s *Service) GetPlainData(ctx context.Context, req *payPb.GetPlainDataRequest) (*payPb.GetPlainDataResponse, error) {
	var (
		res = &payPb.GetPlainDataResponse{}
	)

	if s.conf.PayloadSigningCreds == nil {
		logger.Error(ctx, "payload signing creds not found in service configuration")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Step 1: Retrieve the encryption key and initialization vector (IV) from the service configuration.
	encryptionKey := s.conf.PayloadSigningCreds.SigningKey
	iv := s.conf.PayloadSigningCreds.IV
	if encryptionKey == "" || iv == "" {
		logger.Error(ctx, "encryption key or IV is empty")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	cipherText := req.GetSignedData()

	// Step 2: Create a new AES cipher block using the encryption key.
	block, err := aes.NewCipher([]byte(encryptionKey))
	if err != nil {
		logger.Error(ctx, "error in creating a new cipher", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Step 3: Decode the base64 encoded ciphertext.
	decodedData, err := base64.StdEncoding.DecodeString(string(cipherText))
	if err != nil {
		logger.Error(ctx, "error in decoding the base64 encoded cipher text", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Step 4: Create a new CBC decrypter using the AES block and IV.
	decryptor := cipher.NewCBCDecrypter(block, []byte(iv))

	// Step 5: Decrypt the ciphertext using AES-CBC mode.
	decryptedData := make([]byte, len(decodedData))
	decryptor.CryptBlocks(decryptedData, decodedData)

	// Step 6: Remove PKCS5 padding from the decrypted data.
	plainData := cryptoAes.PKCS5UnPadding(decryptedData)

	// Step 7: Set the plain data in the response and set the status to OK.
	res.PlainData = plainData
	res.Status = rpc.StatusOk()

	return res, nil
}

// ifOrderMaskListContainsOnlyNonUpdatableFields returns true if order mask list contains atleast 1 non updatable field masks
func ifOrderMaskListContainsOnlyNonUpdatableFields(maskList []orderPb.OrderFieldMask) bool {

	for _, mask := range maskList {
		if lo.Contains(nonUpdatableOrderFields, mask) {
			return true
		}
	}

	return false
}

// ifTxnMaskListContainsOnlyNonUpdatableFields returns true if transaction mask list contains atleast 1 non updatable field masks
func ifTxnMaskListContainsOnlyNonUpdatableFields(maskList []paymentPb.TransactionFieldMask) bool {

	for _, mask := range maskList {
		if lo.Contains(nonUpdatableTxnFields, mask) {
			return true
		}
	}

	return false
}
