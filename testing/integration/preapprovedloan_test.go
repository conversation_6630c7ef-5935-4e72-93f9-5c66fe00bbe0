package integration

import (
	"context"
	"testing"

	"google.golang.org/protobuf/proto"

	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/owners"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/auth/dao"
	daoImpl "github.com/epifi/gamma/preapprovedloan/dao/impl"
	"github.com/epifi/gamma/testing/integration/app/preapprovedloan"
)

func LoanApplicationFlows(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()

	// get user from pool
	pooledUser, release := GetPooledUser(ctx, a)
	reqH := proto.Clone(pooledUser.RequestHeader).(*header.RequestHeader)
	userData := pooledUser.Data
	defer release()

	loanOfferDao := daoImpl.NewCrdbLoanOfferDao(loansDbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), lendingRedisCacheStorage)
	loanAccountDao := daoImpl.NewCrdbLoanAccountsDao(loansDbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), lendingRedisCacheStorage)
	loanApplicantDao := daoImpl.NewCrdbLoanApplicantDao(loansDbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()))
	lseDao := daoImpl.NewCrdbLoanStepExecutionsDao(loansDbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()))
	loecDao := daoImpl.NewCrdbLoanOfferEligibilityCriteriaDao(loansDbResourceProvider, idgen.NewDomainIdGenerator(idgen.NewClock()), lendingRedisCacheStorage)
	authDao := dao.NewAuthDao(epifiDbV2)
	palTs := preapprovedloan.NewPlTestSuite(loanOfferDao, loanAccountDao, loanApplicantDao, lseDao, loecDao, *authDao, fePalClient, feConsentClient, bePalClient, authClient, authOrchClient, feSignupClient, feCreditReportClient, loansDbResourceProvider)

	t.Run("loan application flow tests", func(t *testing.T) {
		t.Run("moneyview loan application happy flow", func(t *testing.T) {
			defer Recover(t)
			palTs.TestMoneyviewLoanApplication_HappyFlow(t, reqH)
		})
		t.Run("federal loan application happy flow", func(t *testing.T) {
			defer Recover(t)
			palTs.TestFederalLoanApplication_HappyFlow(t, reqH, a, userData)
		})

		// commenting this because continuous changes are happening in lenden loan program.
		// t.Run("lenden loan application happy flow", func(t *testing.T) {
		//	defer Recover(t)
		//	palTs.TestLendenLoanApplicationHappyFlow(t, reqH)
		// })

		// commenting this as IDFC loan program is currently paused in prod
		// t.Run("IDFC loan application happy flow", func(t *testing.T) {
		//	 defer Recover(t)
		//	 palTs.TestIDFCLoanApplicationPlHappyFlow(t, reqH, a, userData)
		// })
	})
}

func LoansTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is the test for Loans flows",
				BU:          owners.BUSINESS_UNIT_LENDING,
				Scope:       []Scope{Smoke},
			},
			Test: LoanApplicationFlows,
		},
	}
}
