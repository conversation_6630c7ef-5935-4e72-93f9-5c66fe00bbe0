Application:
  Environment: "development"
  Name: "dynamicelements"

Server:
  Ports:
    GrpcPort: 8085
    GrpcSecurePort: 9506
    HttpPort: 9999

Aws:
  Region: "ap-south-1"

ScreenToServiceMapping:
  Default: [ "COMMS_SERVICE" ]
  HOME_SECTION_TOP_BAR: [ "COMMS_SERVICE", "RISK_SERVICE", "KYC_SERVICE","KYC_COMPLIANCE_SERVICE", "BANK_CUSTOMER_SERVICE", "EMPLOYMENT_SERVICE","TIERING_SERVICE","INVESTMENT_SERVICE", "PRE_APPROVED_LOAN_SERVICE", "SAVINGS_SERVICE" ]
  HOME_SECTION_BODY: [ "COMMS_SERVICE", "CASPER_SERVICE" ]
  HOME_SECTION_BODY2: [ "US_STOCKS_SERVICE", "CASPER_SERVICE" ]
  HOME_SECTION_GTM_POPUP: [ "COMMS_SERVICE", "KYC_SERVICE","KYC_COMPLIANCE_SERVICE","EMPLOYMENT_SERVICE","FIREFLY_SERVICE", "SALARY_PROGRAM_SERVICE", "CARD_SERVICE" ]
  HOME_SECTION_FEATURE_PRIMARY: [ "PRE_APPROVED_LOAN_SERVICE", "FIREFLY_SERVICE", "TIERING_SERVICE", "SALARY_PROGRAM_SERVICE", "US_STOCKS_SERVICE" ]
  HOME_SECTION_FEATURE_SECONDARY: [ "PRE_APPROVED_LOAN_SERVICE", "FIREFLY_SERVICE", "TIERING_SERVICE", "SALARY_PROGRAM_SERVICE", "US_STOCKS_SERVICE" ]
  HOME: [ "COMMS_SERVICE","KYC_COMPLIANCE_SERVICE", "KYC_SERVICE","EMPLOYMENT_SERVICE","PRE_APPROVED_LOAN_SERVICE","FIREFLY_SERVICE", "TIERING_SERVICE", "SALARY_PROGRAM_SERVICE", "US_STOCKS_SERVICE" ]
  HELP_MAIN: [ "COMMS_SERVICE" ]
  MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN: [ "COMMS_SERVICE", "INVESTMENT_SERVICE" ]
  INVESTMENT_LANDING_SCREEN: [ "COMMS_SERVICE", "INVESTMENT_SERVICE" ]
  SAVE_PROMOTIONAL_BANNER_HOME: [ "COMMS_SERVICE", "INVESTMENT_SERVICE" ]
  PRIMARY_SAVINGS_SUMMARY_SCREEN: ["COMMS_SERVICE"]
  USSTOCKS_LANDING_SCREEN: ["US_STOCKS_SERVICE"]
  USSTOCKS_WALLET_PAGE: ["US_STOCKS_SERVICE"]
  POST_PAYMENT_SCREEN: ["PAY_SERVICE"]
  HOME_SECTION_TABBED_CARD: ["US_STOCKS_SERVICE"]
  NET_WORTH_HUB_SCREEN: [ "NET_WORTH_SERVICE" ]
  ANALYSER_SCREEN: [ "ANALYSER_SERVICE" ]
  SAVINGS_ACCOUNTS_HOME_SUMMARY_SCREEN: [ "CONNECTED_ACC_SERVICE" ]
  LOANS_DASHBOARD_SCREEN: [ "PRE_APPROVED_LOAN_SERVICE" ]
  DC_DASHBOARD_V2_SCREEN: [ "CARD_SERVICE" ]
  PAY_QR_SCREEN: ["PAY_SERVICE"]
  PAY_ML_KIT_QR_SCREEN: ["PAY_SERVICE"]
  AMB_DETAILS_SCREEN: ["COMMS_SERVICE"]
  PAY_LANDING_SCREEN: [ "COMMS_SERVICE", "PAY_SERVICE" ]

# We use priority order cascaded first on EntityContentType and then on ServiceName
# Each list is in decreasing order of priority
# can be defined per screen
PriorityOrder:
  Default: #this is the default priority order -- overridden by screen specific order
    ElementUtilityType: [ "ELEMENT_UTILITY_TYPE_ALERT", "ELEMENT_UTILITY_TYPE_MARKETING", "ELEMENT_UTILITY_TYPE_INSIGHT" ]
    ServiceName: [ "COMMS_SERVICE", "RISK_SERVICE", "BANK_CUSTOMER_SERVICE", "KYC_SERVICE", "KYC_COMPLIANCE_SERVICE", "EMPLOYMENT_SERVICE", "US_STOCKS_SERVICE", "FIREFLY_SERVICE", "PRE_APPROVED_LOAN_SERVICE", "SALARY_PROGRAM_SERVICE", "TIERING_SERVICE", "INVESTMENT_SERVICE", "CARD_SERVICE" ]

# maximum number of dynamic elements to be returned. first matching config will be read in following order of priority
# 1. screen + section
# 2. screen
# 3. default
MaximumElements:
  Default: 1 #this is the default value -- overridden by screen specific value
  USSTOCKS_LANDING_SCREEN: 2
  PAY_LANDING_SCREEN: 5 # multiple banners can be set for pay landing screen. Allowing upto 5 for now

ReleaseConfig:
  IsRestrictedReleaseEnabled: false
  # if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
  IsEnabledForPlatform:
    android: true
    ios: true
  IsEnabledForUserGroup:
    internal: true

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

QuestSdk:
  Disable: true

QuestRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: dynamic-elements-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

QuestVariables:
  GTMPopUp: false
  HomeBanner: false
