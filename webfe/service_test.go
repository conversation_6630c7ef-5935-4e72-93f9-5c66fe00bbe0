package webfe

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"sync"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/constants"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/employment"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/screener"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/webfe"
	header2 "github.com/epifi/gamma/pkg/frontend/header"
)

var (
	device = &commontypes.Device{
		Model:        "TestModel",
		Manufacturer: "TestManufacturer",
		HwVersion:    "TestHwVersion",
		SwVersion:    "TestSwVersion",
		OsApiVersion: "TestOsApiVersion",
		DeviceId:     "TestDeviceId",
	}

	phnNumber1 = &commontypes.PhoneNumber{
		CountryCode:    91,
		NationalNumber: **********,
	}

	profile1 = &user.Profile{
		PhoneNumber: phnNumber1,
	}

	acquisitionInfo1 = &user.AcquisitionInfo{
		Platform:             commontypes.Platform_WEB,
		AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
		AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
		AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
		AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
	}

	user1 = &user.User{
		Id:              "user1Id",
		Profile:         profile1,
		AcquisitionInfo: acquisitionInfo1,
	}

	actor1 = &types.Actor{
		Id:       "actor1Id",
		EntityId: user1.GetId(),
		Type:     types.Actor_USER,
	}

	token               = "token"
	otp                 = "1234"
	email               = "<EMAIL>"
	internalErrorStatus = rpc.StatusInternal()

	accessToken = "accessToken"
)

func TestService_GeneratePhoneOtp(t *testing.T) {
	type args struct {
		req *webfe.GeneratePhoneOtpRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *webfe.GeneratePhoneOtpResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies)
	}{
		{
			name: "generate phone otp successful",
			args: args{
				req: &webfe.GeneratePhoneOtpRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					PhoneNumber: phnNumber1,
					Token:       "",
				},
			},
			want: &webfe.GeneratePhoneOtpResponse{
				RespHeader:        header2.SuccessRespHeader(),
				Token:             token,
				RetryTimerSeconds: 60,
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.mockAuthClient.EXPECT().GenerateOtp(gomock.Any(), &auth.GenerateOtpRequest{
					Token:       args.req.GetToken(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.GenerateOtpResponse{
					Status:            rpc.StatusOk(),
					Token:             token,
					RetryTimerSeconds: 60,
				}, nil)
			},
		},
		{
			name: "generate phone otp failure",
			args: args{
				req: &webfe.GeneratePhoneOtpRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					PhoneNumber: phnNumber1,
					Token:       "",
				},
			},
			want: &webfe.GeneratePhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), internalErrorStatus.GetShortMessage()),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.mockAuthClient.EXPECT().GenerateOtp(gomock.Any(), &auth.GenerateOtpRequest{
					Token:       args.req.GetToken(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.GenerateOtpResponse{
					Status: internalErrorStatus,
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.GeneratePhoneOtp(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("GeneratePhoneOtp() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_VerifyPhoneOtp(t *testing.T) {
	type args struct {
		req *webfe.VerifyPhoneOtpRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *webfe.VerifyPhoneOtpResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies) *sync.WaitGroup
	}{
		{
			name: "verify phone otp failed due to an error in invoking auth server",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), internalErrorStatus.GetShortMessage()),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: internalErrorStatus,
				}, nil)

				return nil
			},
		},
		{
			name: "verify phone otp failed due to an error in getting user by phone number",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(nil, errors.New("error in getting user by phone number"))

				return nil
			},
		},
		{
			name: "verify phone otp failed due to an error in creating new user",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(nil, errors.New("error creating new user"))

				return nil
			},
		},
		{
			name: "verify phone otp failed due to an error in creating new actor",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(nil, errors.New(" error in creating new actor"))

				return nil
			},
		},
		{
			name: "verify phone otp failed due to an error in creating new token",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(nil, errors.New("internal error"))

				return nil
			},
		},
		{
			name: "verify phone success : onboarding completed user",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.SuccessRespHeader(),
				NextScreen: &dlPb.Deeplink{
					Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
				},
				AccessToken: accessToken,
				ActorId:     actor1.GetId(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
					Token:  accessToken,
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						StageDetails: &onboarding.StageDetails{
							StageMapping: map[string]*onboarding.StageInfo{
								onboarding.OnboardingStage_ONBOARDING_COMPLETE.String(): {
									State: onboarding.OnboardingState_SUCCESS,
								},
							},
						},
						CompletedAt: timestampPb.Now(),
					},
				}, nil)

				return nil
			},
		},
		{
			name: "verify phone otp failed due to error in checking consent",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						StageDetails: &onboarding.StageDetails{
							StageMapping: map[string]*onboarding.StageInfo{
								onboarding.OnboardingStage_APP_SCREENING.String(): {
									State: onboarding.OnboardingState_FAILURE,
								},
							},
						},
						CompletedAt: timestampPb.Now(),
					},
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(nil, errors.New("error in checking consent requirement"))

				return &wg
			},
		},
		{
			name: "verify phone otp failed , mandatory consents not received",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_PRIVACY_POLICY,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp failed , error in recording one of mandatory consents",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: internalErrorStatus,
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp failed , error in getting employment Info",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusInternal(),
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp failed , error in getting processing employment data",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().ProcessEmploymentData(gomock.Any(), &employment.ProcessEmploymentDataRequest{
					ActorId:        actor1.GetId(),
					EmploymentType: employment.EmploymentType_SALARIED,
					UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_B2B_ONBOARDING,
				}).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusInternal(),
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp failed , error in getting screener attempts by actor ID",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().ProcessEmploymentData(gomock.Any(), &employment.ProcessEmploymentDataRequest{
					ActorId:        actor1.GetId(),
					EmploymentType: employment.EmploymentType_SALARIED,
					UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_B2B_ONBOARDING,
				}).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId: actor1.GetId(),
				}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusInternal(),
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp failed , error in run check",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().ProcessEmploymentData(gomock.Any(), &employment.ProcessEmploymentDataRequest{
					ActorId:        actor1.GetId(),
					EmploymentType: employment.EmploymentType_SALARIED,
					UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_B2B_ONBOARDING,
				}).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId: actor1.GetId(),
				}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockScreenerClient.EXPECT().RunCheck(gomock.Any(), &screener.RunCheckRequest{
					ActorId:   actor1.GetId(),
					CheckType: screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION,
				}).Return(&screener.RunCheckResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				return &wg
			},
		},
		{
			name: "verify phone otp success for new employee",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.SuccessRespHeader(),
				NextScreen: &dlPb.Deeplink{
					Screen: dlPb.Screen_SEND_WORK_EMAIL_OTP,
				},
				ActorId: actor1.GetId(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Profile: profile1,
					},
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockUserClient.EXPECT().CreateUser(gomock.Any(), &user.CreateUserRequest{
					User: &user.User{
						Profile: profile1,
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform:             commontypes.Platform_WEB,
							AcquisitionSource:    user.AcquisitionChannel_B2B_SALARY_PROGRAM.String(),
							AcquisitionChannel:   user.AcquisitionChannel_B2B_SALARY_PROGRAM,
							AcquisitionIntent:    user.AcquisitionIntent_ACQUISITION_INTENT_BANKING,
							AcquisitionIntentRaw: user.AcquisitionIntent_ACQUISITION_INTENT_BANKING.String(),
						},
					},
				}).Return(&user.CreateUserResponse{
					User: user1,
				}, nil)

				mock.mockActorClient.EXPECT().CreateActor(gomock.Any(), &actor.CreateActorRequest{
					Type:     types.Actor_USER,
					EntityId: user1.GetId(),
				}).Return(&actor.CreateActorResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().ProcessEmploymentData(gomock.Any(), &employment.ProcessEmploymentDataRequest{
					ActorId:        actor1.GetId(),
					EmploymentType: employment.EmploymentType_SALARIED,
					UpdateSource:   employment.UpdateSource_UPDATE_SOURCE_B2B_ONBOARDING,
				}).Return(&employment.ProcessEmploymentDataResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId: actor1.GetId(),
				}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				mock.mockScreenerClient.EXPECT().RunCheck(gomock.Any(), &screener.RunCheckRequest{
					ActorId:   actor1.GetId(),
					CheckType: screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION,
				}).Return(&screener.RunCheckResponse{
					Status: rpc.StatusOk(),
					NextAction: &dlPb.Deeplink{
						Screen: dlPb.Screen_SEND_WORK_EMAIL_OTP,
					},
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp success for existing employee whose verification is failed",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.SuccessRespHeader(),
				NextScreen: &dlPb.Deeplink{
					Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
				},
				ActorId: actor1.GetId(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id:      user1.GetId(),
						Profile: profile1,
					},
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actor.GetActorByEntityIdRequest{
					EntityId: user1.GetId(),
					Type:     types.Actor_USER,
				}).Return(&actor.GetActorByEntityIdResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId: actor1.GetId(),
				}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusOk(),
					ChecksMap: []*screener.CheckDetails{
						{
							CheckType:   screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION,
							CheckResult: screener.CheckResult_CHECK_RESULT_FAILED,
						},
					},
				}, nil)

				return &wg
			},
		},
		{
			name: "verify phone otp success for already verified employee",
			args: args{
				req: &webfe.VerifyPhoneOtpRequest{
					Otp:         otp,
					PhoneNumber: phnNumber1,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
						},
					},
					HasWhatsappConsent: commontypes.BooleanEnum_TRUE,
					Token:              token,
					ConsentTypes: []webfe.ConsentType{
						webfe.ConsentType_CONSENT_TYPE_FI_TNC,
					},
				},
			},
			want: &webfe.VerifyPhoneOtpResponse{
				RespHeader: header2.SuccessRespHeader(),
				NextScreen: &dlPb.Deeplink{
					Screen: dlPb.Screen_WEB_APP_DOWNLOAD,
				},
				ActorId: actor1.GetId(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockAuthClient.EXPECT().VerifyOtp(gomock.Any(), &auth.VerifyOtpRequest{
					Token:       args.req.GetToken(),
					PhoneNumber: args.req.GetPhoneNumber(),
					Otp:         args.req.GetOtp(),
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					Mediums:     []comms.Medium{comms.Medium_SMS},
				}).Return(&auth.VerifyOtpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_PhoneNumber{
						PhoneNumber: args.req.GetPhoneNumber(),
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id:      user1.GetId(),
						Profile: profile1,
					},
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actor.GetActorByEntityIdRequest{
					EntityId: user1.GetId(),
					Type:     types.Actor_USER,
				}).Return(&actor.GetActorByEntityIdResponse{
					Actor:  actor1,
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &auth.CreateTokenRequest{
					TokenType:   auth.TokenType_WEB_LITE_ACCESS_TOKEN,
					Device:      args.req.GetReq().GetAuth().GetDevice(),
					ActorId:     actor1.GetId(),
					PhoneNumber: args.req.GetPhoneNumber(),
				}).Return(&auth.CreateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockOnbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1.GetId(),
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_PROMOTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).Return(&user_preference.CreatePreferenceResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockUserCommsPrefClient.EXPECT().CreatePreference(gomock.Any(), &user_preference.CreatePreferenceRequest{
					ActorId:    actor1.GetId(),
					Medium:     comms.Medium_WHATSAPP,
					Category:   comms.Category_CATEGORY_TRANSACTIONAL,
					Preference: getPreference(args.req.GetHasWhatsappConsent()),
				}).DoAndReturn(func(context context.Context, args1 *user_preference.CreatePreferenceRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &user_preference.CreatePreferenceResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				mock.mockConsentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consent.CheckConsentRequirementRequest{
					ActorId: actor1.GetId(),
					ConsentTypes: []consent.ConsentType{
						consent.ConsentType_FED_TNC,
						consent.ConsentType_FI_PRIVACY_POLICY,
						consent.ConsentType_FI_TNC,
						consent.ConsentType_FI_WEALTH_TNC,
					},
					Owner: commontypes.Owner_OWNER_EPIFI_TECH,
				}).Return(&consent.CheckConsentRequirementResponse{
					IsConsentRequired: true,
					ConsentTypeUrls: []*consent.ConsentTypeUrl{
						{
							ConsentUrl:  "",
							ConsentType: consent.ConsentType_FI_TNC,
						},
					},
				}, nil)

				mock.mockConsentClient.EXPECT().RecordConsent(gomock.Any(), &consent.RecordConsentRequest{
					Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
					ActorId:     actor1.GetId(),
					ConsentType: consent.ConsentType_FI_TNC,
				}).Return(&consent.RecordConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockEmploymentClient.EXPECT().GetEmploymentInfo(gomock.Any(), &employment.GetEmploymentInfoRequest{
					ActorId:      actor1.GetId(),
					ProcessNames: []employment.ProcessName{employment.ProcessName_PROCESS_NAME_WORK_EMAIL_VERIFICATION},
				}).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mock.mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId: actor1.GetId(),
				}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
					Status: rpc.StatusOk(),
					ChecksMap: []*screener.CheckDetails{
						{
							CheckType:   screener.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION,
							CheckResult: screener.CheckResult_CHECK_RESULT_PASSED,
						},
					},
				}, nil)

				return &wg
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			wg := tt.wantMocks(tt.args, md)
			got, err := s.VerifyPhoneOtp(context.Background(), tt.args.req)
			if wg != nil {
				wg.Wait()
			}
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("VerifyPhoneOtp() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestService_GenerateEmailOtp(t *testing.T) {
	type args struct {
		req *webfe.GenerateEmailOtpRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *webfe.GenerateEmailOtpResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies)
	}{
		{
			name: "generate email otp failure due to error in sending work email otp",
			args: args{
				req: &webfe.GenerateEmailOtpRequest{
					Email: email,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
						},
					},
					Token: "",
				},
			},
			want: &webfe.GenerateEmailOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.SendOtpErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {

				mock.mockEmploymentClient.EXPECT().SendWorkEmailOTP(gomock.Any(), &employment.SendWorkEmailOTPRequest{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					Email:       args.req.GetEmail(),
					Token:       args.req.Token,
					ClientReqId: args.req.ClientReqId,
				}).Return(&employment.SendWorkEmailOTPResponse{
					Status: internalErrorStatus,
				}, nil)
			},
		},
		{
			name: "generate email otp success",
			args: args{
				req: &webfe.GenerateEmailOtpRequest{
					Email: email,
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
						},
					},
					Token: "",
				},
			},
			want: &webfe.GenerateEmailOtpResponse{
				RespHeader:        header2.SuccessRespHeader(),
				Token:             token,
				RetryTimerSeconds: 60,
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.mockEmploymentClient.EXPECT().SendWorkEmailOTP(gomock.Any(), &employment.SendWorkEmailOTPRequest{
					ActorId:     args.req.GetReq().GetAuth().GetActorId(),
					Email:       args.req.GetEmail(),
					Token:       args.req.Token,
					ClientReqId: args.req.ClientReqId,
				}).Return(&employment.SendWorkEmailOTPResponse{
					Status:     rpc.StatusOk(),
					Token:      token,
					RetryAfter: 60,
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.GenerateEmailOtp(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("GenerateEmailOtp() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_VerifyEmailOtp(t *testing.T) {
	type args struct {
		req *webfe.VerifyEmailOtpRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *webfe.VerifyEmailOtpResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies)
	}{
		{
			name: "verify email otp failure, error in verifyingWorkEmailOtp auth server",
			args: args{
				req: &webfe.VerifyEmailOtpRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
							Device:  device,
						},
					},
					Email: email,
					Otp:   otp,
					Token: token,
				},
			},
			want: &webfe.VerifyEmailOtpResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.VerifyOtpErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.mockEmploymentClient.EXPECT().VerifyWorkEmailOTP(gomock.Any(), &employment.VerifyWorkEmailOTPRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					Email:   args.req.GetEmail(),
					Token:   args.req.GetToken(),
					Otp:     args.req.GetOtp(),
				}).Return(&employment.VerifyWorkEmailOTPResponse{
					Status: internalErrorStatus,
				}, nil)
			},
		},
		{
			name: "verify email otp success",
			args: args{
				req: &webfe.VerifyEmailOtpRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
							Device:  device,
						},
					},
					Email: email,
					Otp:   otp,
					Token: token,
				},
			},
			want: &webfe.VerifyEmailOtpResponse{
				RespHeader: header2.SuccessRespHeader(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.mockEmploymentClient.EXPECT().VerifyWorkEmailOTP(gomock.Any(), &employment.VerifyWorkEmailOTPRequest{
					ActorId: args.req.GetReq().GetAuth().GetActorId(),
					Email:   args.req.GetEmail(),
					Token:   args.req.GetToken(),
					Otp:     args.req.GetOtp(),
				}).Return(&employment.VerifyWorkEmailOTPResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.VerifyEmailOtp(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("VerifyEmailOtp() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_SendAppLinkToUser(t *testing.T) {
	type args struct {
		req *webfe.SendAppLinkToUserRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *webfe.SendAppLinkToUserResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies) *sync.WaitGroup
	}{
		{
			name: "send app link to user failed, error in getting user from actor Id",
			args: args{
				req: &webfe.SendAppLinkToUserRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
							Device:  device,
						},
					},
				},
			},
			want: &webfe.SendAppLinkToUserResponse{
				RespHeader: header2.InlineErrResp(internalErrorStatus, fmt.Sprint(internalErrorStatus.GetCode()), constants.InternalErrTitle),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: args.req.GetReq().GetAuth().GetActorId(),
					},
				}).Return(&user.GetUserResponse{
					Status: internalErrorStatus,
				}, nil)
				return nil
			},
		},
		{
			name: "send app link to user success, error in sending whatsapp message",
			args: args{
				req: &webfe.SendAppLinkToUserRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
							Device:  device,
						},
					},
				},
			},
			want: &webfe.SendAppLinkToUserResponse{
				RespHeader: header2.SuccessRespHeader(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: args.req.GetReq().GetAuth().GetActorId(),
					},
				}).Return(&user.GetUserResponse{
					User:   user1,
					Status: rpc.StatusOk(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockCommsClient.EXPECT().SendMessage(gomock.Any(), &comms.SendMessageRequest{
					Type:   comms.QoS_GUARANTEED,
					Medium: comms.Medium_WHATSAPP,
					UserIdentifier: &comms.SendMessageRequest_UserId{
						UserId: user1.GetId(),
					},
					Message: getWebOnbWhatsappMessage(),
				}).DoAndReturn(func(context context.Context, args1 *comms.SendMessageRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &comms.SendMessageResponse{
						Status: internalErrorStatus,
					}, nil
				})

				return &wg
			},
		},
		{
			name: "send app link to user success",
			args: args{
				req: &webfe.SendAppLinkToUserRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actor1.GetId(),
							Device:  device,
						},
					},
				},
			},
			want: &webfe.SendAppLinkToUserResponse{
				RespHeader: header2.SuccessRespHeader(),
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) *sync.WaitGroup {
				mock.mockUserClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: args.req.GetReq().GetAuth().GetActorId(),
					},
				}).Return(&user.GetUserResponse{
					User:   user1,
					Status: rpc.StatusOk(),
				}, nil)

				wg := sync.WaitGroup{}
				wg.Add(1)
				mock.mockCommsClient.EXPECT().SendMessage(gomock.Any(), &comms.SendMessageRequest{
					Type:   comms.QoS_GUARANTEED,
					Medium: comms.Medium_WHATSAPP,
					UserIdentifier: &comms.SendMessageRequest_UserId{
						UserId: user1.GetId(),
					},
					Message: getWebOnbWhatsappMessage(),
				}).DoAndReturn(func(context context.Context, args1 *comms.SendMessageRequest, opts ...grpc.CallOption) (interface{}, interface{}) {
					wg.Done()
					return &comms.SendMessageResponse{
						Status: rpc.StatusOk(),
					}, nil
				})

				return &wg
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			wg := tt.wantMocks(tt.args, md)
			got, err := s.SendAppLinkToUser(context.Background(), tt.args.req)
			if wg != nil {
				wg.Wait()
			}
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("SendAppLinkToUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func getPreference(hasWhatsappConsent commontypes.BooleanEnum) user_preference.Preference {
	if hasWhatsappConsent == commontypes.BooleanEnum_TRUE {
		return user_preference.Preference_ON
	} else {
		return user_preference.Preference_OFF
	}
}

func Test_deriveFlowName(t *testing.T) {
	type args struct {
		flowName webfe.FlowName
		webFlow  webfe.WebFlow
	}
	tests := []struct {
		name    string
		args    args
		want    webfe.FlowName
		wantErr string
	}{
		{
			name: "flowName is specified and not unspecified - should return the provided flowName",
			args: args{
				flowName: webfe.FlowName_FLOW_NAME_CC_ELIGIBILITY_CHECK,
				webFlow:  webfe.WebFlow_WEB_FLOW_UNSPECIFIED,
			},
			want:    webfe.FlowName_FLOW_NAME_CC_ELIGIBILITY_CHECK,
			wantErr: "",
		},
		{
			name: "flowName is unspecified but webFlow has valid mapping - should return mapped flowName",
			args: args{
				flowName: webfe.FlowName_FLOW_NAME_UNSPECIFIED,
				webFlow:  webfe.WebFlow_WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK,
			},
			want:    webfe.FlowName_FLOW_NAME_CC_ELIGIBILITY_CHECK,
			wantErr: "",
		},
		{
			name: "both flowName and webFlow are unspecified - should return unspecified flowName",
			args: args{
				flowName: webfe.FlowName_FLOW_NAME_UNSPECIFIED,
				webFlow:  webfe.WebFlow_WEB_FLOW_UNSPECIFIED,
			},
			want:    webfe.FlowName_FLOW_NAME_UNSPECIFIED,
			wantErr: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := deriveFlowName(context.Background(), tt.args.flowName, tt.args.webFlow)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf(" got error: %v\nwant error: %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf("got error: %v, want error: %v", err, tt.wantErr)
				}
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}
