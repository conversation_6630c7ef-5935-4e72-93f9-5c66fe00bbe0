package usstocks

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	usStocksDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks"
	metadataPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/metadata"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	ussAccPb "github.com/epifi/gamma/api/usstocks/account"
	usstocksBeFePb "github.com/epifi/gamma/api/usstocks/frontend"
	"github.com/epifi/gamma/api/usstocks/order"
	portfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/api/usstocks/rewards"
	usstocksEvents "github.com/epifi/gamma/frontend/events/usstocks"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	addFundsDeeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_WALLET_ADD_FUNDS_SCREEN,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(
			&usStocksDlOptions.USStocksAddFundsScreenOptions{
				UsStocksScreenMetadata: &metadataPb.USStocksScreenMetadata{
					DropOffBottomSheetId: usstocksBeFePb.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_MISSED_STOCK_OPPORTUNITIES_ADD_FUNDS.String(),
				},
			}),
	}

	defaultSelectedTab = usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_EXPLORE
	pageTitle          = &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/Union.png",
			Width:     22,
			Height:    22,
		},
		Texts: []*commontypes.Text{
			usstocksUi.GetText("US stocks", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_M),
		},
		LeftImgTxtPadding: 8,
	}
	proposals = []*usstocksFePb.TextWithImage{
		{
			DisplayText: usstocksUi.GetText("Invest in any stock starting at ₹1,000", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_L),
			ImgUrl:      "https://epifi-icons.pointz.in/usstocks_images/dollar-coin-icon.png",
		},
		{
			DisplayText: usstocksUi.GetText("Zero brokerage & withdrawal fees", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_L),
			ImgUrl:      "https://epifi-icons.pointz.in/usstocks_images/zero-withdrawal.png",
		},
		{
			DisplayText: usstocksUi.GetText("Invest in 3000+ stocks and ETFs", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_L),
			ImgUrl:      "https://epifi-icons.pointz.in/usstocks_images/stock_logos_stacked_2x2.png",
		},
		{
			DisplayText: usstocksUi.GetText("Best-in-class forex rates", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_L),
			ImgUrl:      "https://epifi-icons.pointz.in/usstocks_images/rupee-dollar-coins-3d.png",
		},
		{
			DisplayText: usstocksUi.GetText("Powered by Alpaca Securities - FINRA & SIPC certified broker", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_L),
			ImgUrl:      "https://epifi-icons.pointz.in/usstocks_images/shield.png",
		},
		{
			DisplayText: usstocksUi.GetText("No TCS up to ₹10 Lakhs", usstocksUi.Snow, commontypes.FontStyle_HEADLINE_L),
			ImgUrl:      "https://epifi-icons.pointz.in/usstocks_images/note.png",
		},
	}
	preLaunchRecordInterestBtn = &usstocksFePb.Button{
		BgColor: "#00B899",
		Text: &commontypes.Text{
			FontColor:    usstocksUi.Snow,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "I’m interested!"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
		},
		Status: usstocksFePb.Button_BUTTON_STATUS_ENABLED,
	}
	preLaunchSuccessBtn = &usstocksFePb.Button{
		BgColor: "#F7F9FC",
		ImgUrl:  "https://epifi-icons.pointz.in/usstocks_images/check.png",
		Text: &commontypes.Text{
			FontColor:    "#525355",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Thanks for expressing your interest"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
		},
		Status: usstocksFePb.Button_BUTTON_STATUS_DISABLED,
	}
	preLaunchSuccessMsg = &commontypes.Text{
		FontColor:    "#878A8D",
		DisplayValue: &commontypes.Text_PlainString{PlainString: "We'll be in touch. We're super excited for you to try this feature."},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
	}
	preLaunchDisclaimerMsg = &commontypes.Text{
		FontColor:    "#878A8D",
		DisplayValue: &commontypes.Text_PlainString{PlainString: "By proceeding, I consent to Federal Bank checking my LRS limit & eligibility to purchase international stocks."},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
	}
	claimRewardITC = &ui.IconTextComponent{
		LeftIcon: usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.GiftIcon, 24, 24),
		Texts: []*commontypes.Text{
			commontypes.GetPlainStringText("Claim your free reward").
				WithFontColor("#F8EA99").
				WithFontStyle(commontypes.FontStyle_SUBTITLE_S),
		},
		RightIcon: usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.ChevronYellowRightArrow, 24, 24),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_MY_REWARDS_SCREEN,
		},
		LeftImgTxtPadding:  5,
		RightImgTxtPadding: 5,
	}
	rewardSuccessFooterCTATime = 48 * time.Hour
)

func getCompleteOnboardingCta(nextActionDeeplink *deeplink.Deeplink) *ui.IconTextComponent {
	return ui.NewITC().
		WithTexts(commontypes.GetPlainStringText("Finish setting up your account").
			WithFontStyle(commontypes.FontStyle_BUTTON_M).
			WithFontColor(usstocksUi.Snow)).
		WithDeeplink(nextActionDeeplink).
		WithRightImageUrlHeightAndWidth(usstocksUi.WhiteRightArrow, 12, 12).
		WithRightImagePadding(4).
		WithContainerProperties(
			&ui.IconTextComponent_ContainerProperties{
				BgColor:       usstocksUi.Forest,
				CornerRadius:  20,
				LeftPadding:   16,
				RightPadding:  16,
				TopPadding:    12,
				BottomPadding: 12,
			})
}

func getOnboardingInProcessCta(nextActionDeeplink *deeplink.Deeplink) *ui.IconTextComponent {
	return ui.NewITC().
		WithTexts(commontypes.GetPlainStringText("Reviewing your account").
			WithFontStyle(commontypes.FontStyle_BUTTON_M).
			WithFontColor(usstocksUi.Snow)).
		WithDeeplink(nextActionDeeplink).
		WithLeftImageUrlHeightAndWidth(usstocksUi.HourGlassImage, 24, 24).
		WithLeftImagePadding(8).WithContainerProperties(
		&ui.IconTextComponent_ContainerProperties{
			BgColor:       "#38393B",
			CornerRadius:  20,
			LeftPadding:   16,
			RightPadding:  16,
			TopPadding:    12,
			BottomPadding: 12,
		})
}

// GetLandingPage sends data to client for loading landing page
// response includes the data for rendering
// 1. pitch component for non-invested users
// 2. brief view of existing investments for invested users
// 3. list of tabs with deeplink (eg: Explore, Your Stocks, Watch listed etc)
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A14857&t=ddD12jDMquT0n8CN-4
func (s *Service) GetLandingPage(ctx context.Context, req *usstocksFePb.GetLandingPageRequest) (*usstocksFePb.GetLandingPageResponse, error) {
	logger.Info(ctx, "GetLandingPage request received", zap.Any(logger.REQUEST_TYPE, req.GetEntryPoint()))
	if s.config.USStocks().BrokerDowntime().IsEnabled() &&
		time.Now().After(s.config.USStocks().BrokerDowntime().StartTs()) &&
		time.Now().Before(s.config.USStocks().BrokerDowntime().EndTs()) {
		return &usstocksFePb.GetLandingPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			Page: &usstocksFePb.GetLandingPageResponse_LandingPage{
				LandingPage: &usstocksFePb.LandingPage{
					TopComponent: &usstocksFePb.LandingPage_WalletPromoHeader{
						WalletPromoHeader: &usstocksFePb.WalletPromotionHeaderComponent{
							Title: commontypes.GetPlainStringText("Maintenance in progress. Rest assured, your investments are safe.").
								WithFontColor("#F6F9FD").
								WithFontStyle(commontypes.FontStyle_HEADLINE_L),
							FlipComponent: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/usstocks_images/closed-icon.png"},
										Properties: &commontypes.VisualElementProperties{Width: 80, Height: 80},
										ImageType:  commontypes.ImageType_PNG,
									},
								},
							},
						},
					},
					PageTitle: pageTitle,
				},
			},
		}, nil
	}

	investmentSummary, beInvestmentSummaryResp, err := s.getInvestmentSummary(ctx, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting investment summary from be", zap.Error(err))
		return &usstocksFePb.GetLandingPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusFromErrorWithDefaultInternal(err)}}, nil
	}
	landingPage, preLaunchPage, te := s._getLandingPage(ctx, req.GetReq().GetAuth(), investmentSummary, beInvestmentSummaryResp, req.GetEntryPoint())
	if te != nil {
		logger.Error(ctx, "error in getting landing page", zap.Error(te))
		return &usstocksFePb.GetLandingPageResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusFromErrorWithDefaultInternal(te)}}, nil
	}
	if landingPage != nil {
		return &usstocksFePb.GetLandingPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk(), FeedbackEngineInfo: s.getUSStockLandingPageFeedbackEngineInfo(beInvestmentSummaryResp)},
			Page:       landingPage,
		}, nil
	}
	return &usstocksFePb.GetLandingPageResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk(), FeedbackEngineInfo: s.getUSStockLandingPageFeedbackEngineInfo(beInvestmentSummaryResp)},
		Page:       preLaunchPage,
	}, nil
}

func (s *Service) getUSStockLandingPageFeedbackEngineInfo(investmentSummary *portfolioPb.GetInvestmentSummaryInfoResponse) *header.FeedbackEngineInfo {
	// if user is not onboarded
	if s.config.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInUSSGetLandingPageForNewUserEnabled() && investmentSummary.GetOnboardingSummary().GetOnboardingStatus() != portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_SUCCESSFUL {
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_USER.String(),
			},
		}
	}
	// if user never added fund
	if s.config.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInUSSGetLandingPageForNewWalletUserEnabled() && investmentSummary.GetWalletSummaryInfo().GetSuccessOrdersCount() == 0 {
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_WALLET_USER.String(),
			},
		}
	}
	// if user has done add funds at least once
	if s.config.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInUSSGetLandingPageForExistingWalletUserEnabled() && investmentSummary.GetWalletSummaryInfo().GetSuccessOrdersCount() > 0 {
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_EXISTING_WALLET_USER.String(),
			},
		}
	}
	return nil
}

// nolint:funlen
func (s *Service) getInvestmentSummary(ctx context.Context, actorId string) (
	*usstocksFePb.InvestmentSummary, *portfolioPb.GetInvestmentSummaryInfoResponse, error) {
	investmentSummaryResp, err := s.portfolioManagerClient.GetInvestmentSummaryInfo(ctx, &portfolioPb.GetInvestmentSummaryInfoRequest{
		ActorId: actorId,
		FieldMasks: []portfolioPb.GetInvestmentSummaryInfoRequest_FieldMask{
			portfolioPb.GetInvestmentSummaryInfoRequest_FIELD_MASK_INVESTMENT_SUMMARY,
			portfolioPb.GetInvestmentSummaryInfoRequest_FIELD_MASK_WALLET_SUMMARY,
			portfolioPb.GetInvestmentSummaryInfoRequest_FIELD_MASK_ONBOARDING_SUMMARY,
		},
	})
	if te := epifigrpc.RPCError(investmentSummaryResp, err); te != nil {
		return nil, nil, te
	}

	// returning investment summary as nil, if user has never invested in us stocks
	if !investmentSummaryResp.GetInvestmentSummary().GetHasEverInvested() {
		return nil, investmentSummaryResp, nil
	}

	currentInvestmentAmount := investmentSummaryResp.GetInvestmentSummary().GetCurrentAmount()
	// If current investment amount is zero, return a USD currency code to clients, so that clients don't default to showing INR symbol
	if currentInvestmentAmount == nil {
		currentInvestmentAmount = &moneyPb.Money{CurrencyCode: money.USDCurrencyCode}
	}

	exchangeStatusRes, err := s.orderManagerClient.ExchangeStatus(ctx, &order.ExchangeStatusRequest{})
	if te := epifigrpc.RPCError(exchangeStatusRes, err); te != nil {
		return nil, nil, errors.Wrap(te, "error getting exchange status")
	}

	footerCta, err := s.getInvestmentSummaryFooterCta(ctx, actorId, investmentSummaryResp)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error getting footer CTA for investment summary")
	}

	return &usstocksFePb.InvestmentSummary{
		CurrentValue: types.GetFromBeMoney(currentInvestmentAmount),
		InvestmentStats: []*usstocksFePb.InvestmentStats{
			{
				LeftStat: &usstocksFePb.TitleWithSubTitle{
					Title: usstocksUi.GetText(
						money.ToDisplayStringWithPrecisionV2(investmentSummaryResp.GetInvestmentSummary().GetInvestedAmount(), 2),
						usstocksUi.Snow, commontypes.FontStyle_NUMBER_M),
					SubTitle: usstocksUi.GetText("INVESTED", usstocksUi.MonochromeAsh, commontypes.FontStyle_SUBTITLE_XS),
				},
				RightStat: &usstocksFePb.TitleWithSubTitle{
					Title: usstocksUi.GetText(money.ToDisplayStringWithPrecisionV2(investmentSummaryResp.GetInvestmentSummary().GetGrowthAmount(), 2),
						usstocksUi.Snow, commontypes.FontStyle_NUMBER_M),
					SubTitle: usstocksUi.GetText("RETURNS", usstocksUi.MonochromeAsh, commontypes.FontStyle_SUBTITLE_XS),
				},
			},
			{
				LeftStat: &usstocksFePb.TitleWithSubTitle{
					Title:    usstocksUi.GetText(fmt.Sprintf("%.2f%%", investmentSummaryResp.GetInvestmentSummary().GetOneDayGrowth()), getColorByGrowth(investmentSummaryResp.GetInvestmentSummary().GetOneDayGrowth()), commontypes.FontStyle_NUMBER_M),
					SubTitle: usstocksUi.GetText("1 DAY %", usstocksUi.MonochromeAsh, commontypes.FontStyle_SUBTITLE_XS),
				},
				RightStat: &usstocksFePb.TitleWithSubTitle{
					Title:    usstocksUi.GetText(fmt.Sprintf("%.2f%%", investmentSummaryResp.GetInvestmentSummary().GetGrowthPercent()), getColorByGrowth(investmentSummaryResp.GetInvestmentSummary().GetGrowthPercent()), commontypes.FontStyle_NUMBER_M),
					SubTitle: usstocksUi.GetText("OVERALL %", usstocksUi.MonochromeAsh, commontypes.FontStyle_SUBTITLE_XS),
				},
			},
		},
		FooterCta:        footerCta,
		CurrentValueInfo: getPortfolioInconsistencyInfo(exchangeStatusRes.GetExtendedTradingHours(), investmentSummaryResp.GetInvestmentSummary().GetPositionsLastRefreshedAt()),
	}, investmentSummaryResp, nil
}

// nolint:funlen
func (s *Service) _getLandingPage(ctx context.Context, authHeader *header.AuthHeader,
	feInvestmentSummary *usstocksFePb.InvestmentSummary, beInvestmentSummaryResp *portfolioPb.GetInvestmentSummaryInfoResponse, entryPoint string) (*usstocksFePb.GetLandingPageResponse_LandingPage,
	*usstocksFePb.GetLandingPageResponse_PreLaunch, error) {
	actorId := authHeader.GetActorId()
	platform := authHeader.GetDevice().GetPlatform()
	appVersion := authHeader.GetDevice().GetAppVersion()

	allowed, err := utils.IsUSStocksEnabled(ctx, actorId, s.usersClient, s.userGrpClient, s.releaseEvaluator)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error in checking us stocks enabled for user")
	}
	// if landing page is not allowed returning pre-launch page to user
	if !allowed {
		preLaunch, err := s.getPreLaunchPage(ctx, actorId)
		if err != nil {
			return nil, nil, err
		}
		return nil, preLaunch, nil
	}
	landingPageTabs, defaultSelectedTabIndex, err := s.getLandingPageTabs(ctx, entryPoint)
	if err != nil {
		logger.Error(ctx, "Error in getLandingPageTabs", zap.Error(err))
		return nil, nil, errors.Wrap(err, "error in getting landing page tabs")
	}
	if feInvestmentSummary == nil {
		footerCta, err2 := s.getPitchComponentFooterCta(ctx, actorId, beInvestmentSummaryResp)
		if err2 != nil {
			return nil, nil, errors.Wrap(err2, "error in getting account onboarding cta for landing page footer")
		}

		if (platform == commontypes.Platform_IOS && appVersion < s.config.USStocks().VersionSupport().MinIOSAppVersionToSupportWalletHeaderPromo()) ||
			(platform == commontypes.Platform_ANDROID && appVersion < s.config.USStocks().VersionSupport().MinAndroidAppVersionToSupportWalletHeaderPromo()) {
			return &usstocksFePb.GetLandingPageResponse_LandingPage{
				LandingPage: &usstocksFePb.LandingPage{
					TopComponent: &usstocksFePb.LandingPage_Pitch{
						Pitch: &usstocksFePb.PitchComponent{
							TextWithImage: proposals,
							FooterCta:     footerCta,
						},
					},
					Tabs:                    landingPageTabs,
					DefaultSelectedTab:      defaultSelectedTab,
					DefaultSelectedTabIndex: defaultSelectedTabIndex,
					PageTitle:               pageTitle,
				},
			}, nil, nil
		}

		promoHeader, err2 := s.getWalletPromoHeaderComponent(ctx, footerCta, actorId, beInvestmentSummaryResp, authHeader)
		if err2 != nil {
			return nil, nil, errors.Wrap(err2, "error in getting wallet promo header")
		}
		return &usstocksFePb.GetLandingPageResponse_LandingPage{
			LandingPage: &usstocksFePb.LandingPage{
				TopComponent:            &usstocksFePb.LandingPage_WalletPromoHeader{WalletPromoHeader: promoHeader},
				Tabs:                    landingPageTabs,
				DefaultSelectedTab:      defaultSelectedTab,
				DefaultSelectedTabIndex: defaultSelectedTabIndex,
				PageTitle:               pageTitle,
			},
		}, nil, nil
	}

	return &usstocksFePb.GetLandingPageResponse_LandingPage{
		LandingPage: &usstocksFePb.LandingPage{
			TopComponent:            &usstocksFePb.LandingPage_InvestmentSummary{InvestmentSummary: feInvestmentSummary},
			Tabs:                    landingPageTabs,
			DefaultSelectedTab:      defaultSelectedTab,
			DefaultSelectedTabIndex: defaultSelectedTabIndex,
			PageTitle:               pageTitle,
		},
	}, nil, nil
}

// getLandingPageTabs returns the tabs to be shown on landing page along with index of default selected tab
func (s *Service) getLandingPageTabs(ctx context.Context, entryPoint string) ([]*usstocksFePb.LandingPageTab, int32, error) {
	landingPageTabs := []*usstocksFePb.LandingPageTab{
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.USStocksTabHeader},
				},
				ImgUrl: usstocksUi.TargetIcon,
			},
			Tab: usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_EXPLORE,
		},
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.YourStocksTabHeader},
				},
				ImgUrl: usstocksUi.MarketIcon,
			},
			Tab: usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_YOUR_STOCKS,
		},
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.WatchlistTabHeader},
				},
				ImgUrl: usstocksUi.WatchlistIcon,
			},
			Tab: usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_WATCHLIST,
		},
	}
	// To handle backward compatibility on client side
	if entryPoint == "" {
		return landingPageTabs, 0, nil
	}
	stocksOpts := &GetExplorePagePayload{
		StockType: usstocksCatalogPb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY,
	}
	fundsOpts := &GetExplorePagePayload{
		StockType: usstocksCatalogPb.StockType_STOCK_TYPE_ETF,
	}
	stockPayloadBytes, marshalErr := json.Marshal(stocksOpts)
	if marshalErr != nil {
		logger.Error(ctx, "error in marshalling explore payload", zap.Error(marshalErr))
		return nil, 0, errors.Wrap(marshalErr, "error in marshalling explore payload")
	}
	fundsPayloadBytes, marshalErr := json.Marshal(fundsOpts)
	if marshalErr != nil {
		logger.Error(ctx, "error in marshalling explore payload", zap.Error(marshalErr))
		return nil, 0, errors.Wrap(marshalErr, "error in marshalling explore payload")
	}
	landingPageTabs = []*usstocksFePb.LandingPageTab{
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.USFundsTabHeader},
				},
				ImgUrl: usstocksUi.USFundsIcon,
			},
			Tab:     usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_EXPLORE,
			Payload: fundsPayloadBytes,
		},
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.USStocksTabHeader},
				},
				ImgUrl: usstocksUi.TargetIcon,
			},
			Tab:     usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_EXPLORE,
			Payload: stockPayloadBytes,
		},
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.YourStocksTabHeader},
				},
				ImgUrl: usstocksUi.MarketIcon,
			},
			Tab: usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_YOUR_STOCKS,
		},
		{
			TextWithImage: &usstocksFePb.TextWithImage{
				DisplayText: &commontypes.Text{
					FontColor:    usstocksUi.MonochromeNight,
					DisplayValue: &commontypes.Text_PlainString{PlainString: usstocksUi.WatchlistTabHeader},
				},
				ImgUrl: usstocksUi.WatchlistIcon,
			},
			Tab: usstocksFePb.LandingScreenTabType_LANDING_SCREEN_TAB_WATCHLIST,
		},
	}
	defaultSelectedTabIndex := int32(1) // default to US Stocks tab
	if entryPoint == usstocksFePb.LandingScreenEntryPoint_LANDING_SCREEN_ENTRY_POINT_FUNDS.String() {
		defaultSelectedTabIndex = 0 // todo: figure out a way to map these to array
	}

	return landingPageTabs, defaultSelectedTabIndex, nil
}

func (s *Service) getWalletPromoHeaderComponent(
	ctx context.Context,
	footerCta *ui.IconTextComponent,
	actorId string,
	beInvestmentSummaryResp *portfolioPb.GetInvestmentSummaryInfoResponse,
	authHeader *header.AuthHeader,
) (*usstocksFePb.WalletPromotionHeaderComponent, error) {
	rewardDetailsResp, err := s.ussRewardsClient.GetRewardDetails(ctx, &rewards.GetRewardDetailsRequest{ActorId: actorId})
	if err2 := epifigrpc.RPCError(rewardDetailsResp, err); err2 != nil {
		return nil, errors.Wrap(err2, "error in getting reward details")
	}

	eligibleForRewards := false
	if rewardDetailsResp.GetRewardDetails().GetStatus() != rewards.RewardStatus_REWARD_STATUS_INELIGIBLE_FOR_REWARD {
		// showing normal landing page due to following issue
		// ref: https://epifi.slack.com/archives/C063D6AHV1N/p1727106111217719
		eligibleForRewards = false
		goroutine.RunWithDefaultTimeout(epificontext.WithEventAttributesV2(ctx), func(ctx context.Context) {
			s.eventBroker.AddToBatch(ctx, usstocksEvents.NewRewardPromoScreenEvent(actorId, authHeader.GetDevice().GetPlatform().String(), authHeader.GetDevice().GetAppVersion()))
		})
	}

	return &usstocksFePb.WalletPromotionHeaderComponent{
		Title:           getPromotionHeaderTitleText(eligibleForRewards, rewardDetailsResp.GetDisplayDetails()),
		FlipComponent:   getFlipComponent(eligibleForRewards, rewardDetailsResp.GetDisplayDetails()),
		BottomComponent: s.getBottomComponent(eligibleForRewards, beInvestmentSummaryResp),
		FooterCta:       footerCta,
		ComplianceCta: ui.NewITC().
			WithTexts(commontypes.GetPlainStringText("Co-powered by Alpaca Securities LLC, US registered broker").
				WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6A6D70")).
			WithLeftImageUrlHeightAndWidth(GreyShieldIconUrl, 20, 20).
			WithContainerPadding(8, 0, 8, 0),
		RadialGradient: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_RadialGradient{
				RadialGradient: &ui.RadialGradient{
					Center: &ui.CenterCoordinates{
						CenterX: 0,
						CenterY: 0,
					},
					OuterRadius: 120,
					Colours:     []string{"#619887DB", "#009887DB"},
				},
			},
		},
		BottomComponentDeeplink: footerCta.GetDeeplink(),
	}, nil
}

func getFlipComponent(eligibleForRewards bool, displayDetails *rewards.StockRewardDisplayDetails) *commontypes.VisualElement {
	if eligibleForRewards {
		return commontypes.GetVisualElementLottieFromUrl(displayDetails.GetBgLottieUrl()).WithProperties(
			&commontypes.VisualElementProperties{
				Height: 238,
				Width:  372,
			})
	}
	return commontypes.GetVisualElementLottieFromUrl("https://epifi-icons.pointz.in/usstocks_images/uss-landing-one-dollar.json").WithProperties(
		&commontypes.VisualElementProperties{
			Height: 238,
			Width:  372,
		})
}

func (s *Service) getBottomComponent(eligibleForRewards bool, beInvestmentSummaryResp *portfolioPb.GetInvestmentSummaryInfoResponse) *commontypes.VisualElement {
	imgUrl := "https://epifi-icons.pointz.in/usstocks_images/benefits-card.png"

	if !eligibleForRewards {
		return commontypes.GetVisualElementImageFromUrl(imgUrl).
			WithProperties(
				&commontypes.VisualElementProperties{
					Height: 118,
				})
	}

	switch {
	case beInvestmentSummaryResp.GetInvestmentSummary().GetHasEverInvested():
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-buy-stocks-completed.png"
	case beInvestmentSummaryResp.GetInvestmentSummary().GetPendingOrdersCount() > 0:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-buy-stock-in-progress.png"
	case beInvestmentSummaryResp.GetWalletSummaryInfo().GetSuccessOrdersCount() > 0:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-add-funds-completed.png"
	case beInvestmentSummaryResp.GetWalletSummaryInfo().GetPendingOrdersCount() > 0:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-add-funds-in-progress.png"
	case beInvestmentSummaryResp.GetOnboardingSummary().GetOnboardingStatus() == portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_SUCCESSFUL:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-onb-completed.png"
	case beInvestmentSummaryResp.GetOnboardingSummary().GetOnboardingStatus() == portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_FAILED:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-onb-failed.png"
	case beInvestmentSummaryResp.GetOnboardingSummary().GetOnboardingStatus() == portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_IN_PROGRESS:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-onb-in-progress.png"
	default:
		imgUrl = "https://epifi-icons.pointz.in/usstocks_images/uss-rewards-zero-state.png"
	}
	return commontypes.GetVisualElementImageFromUrl(imgUrl).
		WithProperties(
			&commontypes.VisualElementProperties{
				Height: 118,
			})
}

func getPromotionHeaderTitleText(eligibleForRewards bool, displayDetails *rewards.StockRewardDisplayDetails) *commontypes.Text {
	titleText := "Build your wealth with \nthe world's best brands"
	if eligibleForRewards && len(displayDetails.GetOptions()) > 0 {
		if len(displayDetails.GetOptions()) == 1 {
			titleText = fmt.Sprintf("Invest & get free %s stock worth %s",
				displayDetails.GetOptions()[0].GetStockName(),
				money.ToDisplayStringWithSuffixAndPrecisionV2(displayDetails.GetAmount(), true, true, 0, false, money.IndianNumberSystem),
			)
		} else {
			titleText = fmt.Sprintf("Invest & get free %s or %s stock worth %s",
				displayDetails.GetOptions()[0].GetStockName(),
				displayDetails.GetOptions()[1].GetStockName(),
				money.ToDisplayStringWithSuffixAndPrecisionV2(displayDetails.GetAmount(), true, true, 0, false, money.IndianNumberSystem),
			)
		}
	}
	return commontypes.GetPlainStringText(titleText).
		WithFontColor("#F6F9FD").
		WithFontStyle(commontypes.FontStyle_HEADLINE_XL)
}

// returns cta based on user's onboarding status
// i.e.
// 1. If user has not started onboarding, return `Create account CTA`
// 2. If onboarding has been started, but user did not complete all the steps, return `Complete onboarding CTA`
// 3. If onboarding has been completed by user, and account creation is in process, return `Account creation in process` CTA
// 4. If onboarding is completed and account is ready, return `Add funds to wallet` CTA
func (s *Service) getPitchComponentFooterCta(ctx context.Context, actorId string, beInvestmentSummaryResp *portfolioPb.GetInvestmentSummaryInfoResponse) (*ui.IconTextComponent, error) {
	switch beInvestmentSummaryResp.GetOnboardingSummary().GetOnboardingStatus() {
	case portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_SUCCESSFUL:
		break
	case portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_NOT_STARTED:
		accOnbCta, err := s.getNewUserDeeplink(ctx, actorId)
		if err != nil {
			return nil, errors.Wrapf(err, "error in getting deeplink for new user for actor %s", actorId)
		}
		return ui.NewITC().
			WithTexts(commontypes.GetPlainStringText("Setup your free account").
				WithFontStyle(commontypes.FontStyle_BUTTON_M).
				WithFontColor(usstocksUi.Snow)).
			WithDeeplink(accOnbCta.GetDeeplink()).
			WithRightImageUrlHeightAndWidth(usstocksUi.WhiteRightArrow, 12, 12).
			WithRightImagePadding(4).WithContainerProperties(
			&ui.IconTextComponent_ContainerProperties{
				BgColor:       usstocksUi.Forest,
				CornerRadius:  20,
				LeftPadding:   24,
				RightPadding:  16,
				TopPadding:    12,
				BottomPadding: 12,
			}), nil
	case portfolioPb.OnboardingStatus_ONBOARDING_STATUS_ONBOARDING_IN_PROGRESS:
		nextOnbStepResp, err2 := s.accountManagerClient.GetNextOnboardingStep(ctx, &ussAccPb.GetNextOnboardingStepRequest{
			ActorId: actorId,
		})
		if te := epifigrpc.RPCError(nextOnbStepResp, err2); te != nil {
			return nil, errors.Wrapf(te, "error in getting next onboarding step")
		}

		if nextOnbStepResp.GetNextAction().GetScreen() == deeplink.Screen_USSTOCKS_ONBOARDING_POLLING_SCREEN_V2 {
			return getOnboardingInProcessCta(nextOnbStepResp.GetNextAction()), nil
		}
		return getCompleteOnboardingCta(nextOnbStepResp.GetNextAction()), nil
	default:
		logger.Error(ctx, "onboarding status not handled for footer CTA",
			zap.String(logger.STATUS, beInvestmentSummaryResp.GetOnboardingSummary().GetOnboardingStatus().String()))
		return nil, nil
	}

	if beInvestmentSummaryResp.GetWalletSummaryInfo().GetSuccessOrdersCount() == 0 {
		if beInvestmentSummaryResp.GetWalletSummaryInfo().GetPendingOrdersCount() == 0 {
			return s.getWalletAddFundsCta(ctx, actorId)
		}
		return getWalletInOrderProgressCta(beInvestmentSummaryResp.GetWalletSummaryInfo().GetProcessingAmount()), nil
	}

	if !beInvestmentSummaryResp.GetInvestmentSummary().GetHasEverInvested() {
		if beInvestmentSummaryResp.GetInvestmentSummary().GetPendingOrdersCount() == 0 {
			return ui.NewITC().
				WithTexts(commontypes.GetPlainStringText("All set — you can invest now!").
					WithFontStyle(commontypes.FontStyle_BUTTON_M).
					WithFontColor(usstocksUi.MonochromeAsh)), nil
		}
		return getInProgressActivitiesCta(beInvestmentSummaryResp.GetWalletSummaryInfo().GetPendingOrdersCount(), ""), nil
	}
	return nil, nil
}

func getWalletInOrderProgressCta(amount *moneyPb.Money) *typesUiPb.IconTextComponent {
	return &ui.IconTextComponent{
		LeftIcon: usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.HourGlassImage, 24, 24),
		Texts: []*commontypes.Text{
			commontypes.GetPlainStringText(fmt.Sprintf("Wallet : Processing %s", money.ToDisplayString(amount))).
				WithFontColor("#B9B9B9").
				WithFontStyle(commontypes.FontStyle_SUBTITLE_S)},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_WALLET_PAGE,
		},
		LeftImgTxtPadding:  5,
		RightImgTxtPadding: 5,
	}
}

func getInProgressActivitiesCta(openOrdersCount int64, arrowURL string) *typesUiPb.IconTextComponent {
	msg := commontypes.GetPlainStringText(fmt.Sprintf("You have %d open order", openOrdersCount)).
		WithFontColor("#B9B9B9").
		WithFontStyle(commontypes.FontStyle_SUBTITLE_S)

	if openOrdersCount == 1 {
		msg = commontypes.GetPlainStringText("You have 1 open order").
			WithFontColor("#B9B9B9").
			WithFontStyle(commontypes.FontStyle_SUBTITLE_S)
	}

	var rightIcon *commontypes.Image
	if arrowURL != "" {
		rightIcon = usstocksUi.GetImage(commontypes.ImageType_PNG, arrowURL, 24, 24)
	}

	return &ui.IconTextComponent{
		LeftIcon:  usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.HourGlass, 24, 24),
		Texts:     []*commontypes.Text{msg},
		RightIcon: rightIcon,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_WALLET_PAGE,
		},
		LeftImgTxtPadding:  5,
		RightImgTxtPadding: 5,
	}
}

func (s *Service) getInvestmentSummaryFooterCta(ctx context.Context, actorId string,
	investmentSummaryResp *portfolioPb.GetInvestmentSummaryInfoResponse) (*ui.IconTextComponent, error) {
	rewardFooter, rErr := s.getRewardsFooterIfApplicable(ctx, actorId, investmentSummaryResp)
	if rErr != nil {
		logger.Error(ctx, "error in checking for reward footer", zap.Error(rErr))
		// Not erring out for failures in fetching rewards footer
	}
	if rewardFooter != nil {
		return rewardFooter, nil
	}

	totalPendingOrder := investmentSummaryResp.GetInvestmentSummary().GetPendingOrdersCount() + investmentSummaryResp.GetWalletSummaryInfo().GetPendingOrdersCount()
	if totalPendingOrder == 0 {
		return s.getWalletAddFundsCta(ctx, actorId)
	}
	return getInProgressActivitiesCta(totalPendingOrder, usstocksUi.ChevronRightGreyArrow), nil
}

func (s *Service) getRewardsFooterIfApplicable(ctx context.Context, actorId string, _ *portfolioPb.GetInvestmentSummaryInfoResponse) (*ui.IconTextComponent, error) {
	rewardsDetailsResp, err := s.ussRewardsClient.GetRewardDetails(ctx, &rewards.GetRewardDetailsRequest{ActorId: actorId})
	if err2 := epifigrpc.RPCError(rewardsDetailsResp, err); err2 != nil {
		return nil, errors.Wrap(err2, "error in getting reward details")
	}
	// showing reward success CTA for 2 days after reward processing is success
	if rewardsDetailsResp.GetRewardDetails().GetRewardRequest().GetStatus() == rewards.RewardRequestProcessingStatus_RRPS_SUCCESS {
		if time.Since(rewardsDetailsResp.GetRewardDetails().GetRewardRequest().GetUpdatedAt().AsTime()) < rewardSuccessFooterCTATime {
			stockId := rewardsDetailsResp.GetRewardDetails().GetRewardRequest().GetRewardFulfillmentDetails().GetStockId()
			stock, sErr := utils.GetStockById(ctx, s.catalogManagerClient, stockId)
			if sErr != nil {
				return nil, errors.Wrap(sErr, fmt.Sprintf("error in fetching stock %s", stockId))
			}
			return &ui.IconTextComponent{
				LeftIcon: usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.CelebrationIcon, 24, 24),
				Texts: []*commontypes.Text{
					commontypes.GetPlainStringText(fmt.Sprintf("Your %s stock reward has arrived", stock.GetStockBasicDetails().GetName().GetShortName())).
						WithFontColor("#AFD2A2").
						WithFontStyle(commontypes.FontStyle_SUBTITLE_S),
				},
				RightIcon:          usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.ChevronGreenRightArrow, 24, 24),
				Deeplink:           s.getRewardOrderDetailsDeeplink(rewardsDetailsResp.GetRewardDetails().GetRewardRequest().GetRewardFulfillmentDetails().GetStockBuyOrderId()),
				LeftImgTxtPadding:  5,
				RightImgTxtPadding: 5,
			}, nil
		}
		// 	If reward is in a processing state we show, reward is in processing state and lead the user to reward order receipt
	} else if rewardsDetailsResp.GetRewardDetails().GetStatus() == rewards.RewardStatus_REWARD_STATUS_TO_BE_CLAIM ||
		rewardsDetailsResp.GetRewardDetails().GetStatus() == rewards.RewardStatus_REWARD_STATUS_PROCESSING {
		stockBuyOrderId := rewardsDetailsResp.GetRewardDetails().GetRewardRequest().GetRewardFulfillmentDetails().GetStockBuyOrderId()
		if stockBuyOrderId != "" {
			stockId := rewardsDetailsResp.GetRewardDetails().GetRewardRequest().GetRewardFulfillmentDetails().GetStockId()
			stock, sErr := utils.GetStockById(ctx, s.catalogManagerClient, stockId)
			if sErr != nil {
				return nil, errors.Wrap(sErr, fmt.Sprintf("error in fetching stock %s", stockId))
			}
			return &ui.IconTextComponent{
				LeftIcon: usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.CelebrationIcon, 24, 24),
				Texts: []*commontypes.Text{
					commontypes.GetPlainStringText(fmt.Sprintf("Your %s reward is on its way", stock.GetStockBasicDetails().GetName().GetShortName())).
						WithFontColor("#AFD2A2").
						WithFontStyle(commontypes.FontStyle_SUBTITLE_S),
				},
				RightIcon:          usstocksUi.GetImage(commontypes.ImageType_PNG, usstocksUi.ChevronGreenRightArrow, 24, 24),
				Deeplink:           s.getRewardOrderDetailsDeeplink(stockBuyOrderId),
				LeftImgTxtPadding:  5,
				RightImgTxtPadding: 5,
			}, nil
		}
	}
	return nil, nil
}

func (s *Service) getRewardOrderDetailsDeeplink(orderId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
			UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
				OrderId:    orderId,
				Identifier: &deeplink.USStocksOrderReceiptScreenOptions_StockOrderId{StockOrderId: orderId},
			},
		},
	}
}

// nolint:funlen
func (s *Service) getPreLaunchPage(ctx context.Context, actorId string) (*usstocksFePb.GetLandingPageResponse_PreLaunch, error) {
	var recordInterestBtn *usstocksFePb.Button
	var disclaimerMsg *commontypes.Text

	res, err := s.internationalFundTransferClient.GetPreReqChecksDataForUser(ctx, &internationalfundtransfer.GetPreReqChecksDataForUserRequest{ActorId: actorId})
	if err2 := epifigrpc.RPCError(res, err); err2 != nil {
		// setting record interest and corresponding disclaimer msg only if pre-req check record is not present
		if internationalfundtransfer.GetPreReqChecksDataForUserResponse_Status(res.GetStatus().GetCode()) ==
			internationalfundtransfer.GetPreReqChecksDataForUserResponse_RECORD_NOT_FOUND {
			logger.Info(ctx, "pre-req record not found for user", zap.Error(err2))
			recordInterestBtn = preLaunchRecordInterestBtn
			disclaimerMsg = preLaunchDisclaimerMsg
		} else {
			logger.Error(ctx, "error in getting pre-req checks data for user", zap.Error(err2))
			return nil, err2
		}
	}

	return &usstocksFePb.GetLandingPageResponse_PreLaunch{PreLaunch: &usstocksFePb.PreLaunchPage{
		BgImage: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/pre-launch-bg.png",
		},
		BgColor: usstocksUi.Snow,
		Title: &commontypes.Text{
			FontColor:    "#37383A",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Introducing"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#4F71AB",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "US Stocks"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_XL},
		},
		Benefits: []*ui.IconTextComponent{
			{
				LeftIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/globe.png",
					Width:     32,
					Height:    32,
				},
				LeftImgTxtPadding: 12,
				Texts: []*commontypes.Text{
					{
						FontColor:    "#37383A",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Invest in global brands & ETFs"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					}},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#F7F9FC",
					CornerRadius:  16,
					LeftPadding:   16,
					TopPadding:    8,
					RightPadding:  16,
					BottomPadding: 8,
					Height:        48,
				},
			},
			{
				LeftIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/doc.png",
					Width:     32,
					Height:    32,
				},
				LeftImgTxtPadding: 12,
				Texts: []*commontypes.Text{
					{
						FontColor:    "#37383A",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Free & instant account set-up"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					}},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#F7F9FC",
					CornerRadius:  16,
					LeftPadding:   16,
					TopPadding:    8,
					RightPadding:  16,
					BottomPadding: 8,
					Height:        48,
				},
			},
			{
				LeftIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/dollar-cash.png",
					Width:     32,
					Height:    32,
				},
				LeftImgTxtPadding: 12,
				Texts: []*commontypes.Text{
					{
						FontColor:    "#37383A",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Start investing at just Rs. 1000"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					}},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#F7F9FC",
					CornerRadius:  16,
					LeftPadding:   16,
					TopPadding:    8,
					RightPadding:  16,
					BottomPadding: 8,
					Height:        48,
				},
			},
		},
		Bonus: &ui.IconTextComponent{
			LeftIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/stars-icon.png",
				Width:     20,
				Height:    20,
			},
			Texts: []*commontypes.Text{
				{
					FontColor:    "#D3B250",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Be the first to invest in US stocks with us"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				},
			},
			RightIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/usstocks_images/stars-icon.png",
				Width:     20,
				Height:    20,
			},
		},
		RecordInterest: recordInterestBtn,
		Success:        preLaunchSuccessBtn,
		TryAgain: &usstocksFePb.Button{
			BgColor: "#00B899",
			Text: &commontypes.Text{
				FontColor:    usstocksUi.Snow,
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Could you try once again...please?"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
			},
			Status: usstocksFePb.Button_BUTTON_STATUS_ENABLED,
		},
		DisclaimerMsg: disclaimerMsg,
		SuccessMsg:    preLaunchSuccessMsg,
		TryAgainMsg: &commontypes.Text{
			FontColor:    usstocksUi.DarkPeach,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Our servers are busy sorting submissions! Do retry."},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
	}}, nil
}

func getColorByGrowth(growth float64) string {
	if growth < 0 {
		return usstocksUi.DarkPeach
	} else {
		return usstocksUi.DarkMint
	}
}

func getPortfolioInconsistencyInfo(eth *order.ExtendedTradingHours, lastRefreshedAt *timestamp.Timestamp) *deeplink.InformationPopupOptions {
	if !eth.GetExtendedHoursTradingOpen() {
		return nil
	}
	if eth.GetPreMarketTradingTimePeriod().GetOpeningTs().IsValid() && eth.GetPreMarketTradingTimePeriod().GetClosingTs().IsValid() {
		return getPortfolioInconsistencyInfoDeeplinkOptions(lastRefreshedAt, eth.GetPreMarketTradingTimePeriod().GetOpeningTs(), eth.GetPreMarketTradingTimePeriod().GetClosingTs())
	}
	return getPortfolioInconsistencyInfoDeeplinkOptions(lastRefreshedAt, eth.GetAfterMarketTradingTimePeriod().GetOpeningTs(), eth.GetAfterMarketTradingTimePeriod().GetClosingTs())
}

func getPortfolioInconsistencyInfoDeeplinkOptions(lastRefreshedAt, ethStartAt, ethEndAt *timestamp.Timestamp) *deeplink.InformationPopupOptions {
	var positionLastSyncedAtInfo, ethDisclaimerInfo *commontypes.Text
	if lastRefreshedAt.IsValid() {
		positionLastSyncedAtInfo = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Last synced on %s", lastRefreshedAt.AsTime().In(datetime.IST).Format("2 Jan 3:04 PM"))},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
			BgColor:      "#EAD8A3",
		}
	}
	if ethStartAt.IsValid() && ethEndAt.IsValid() {
		ethTimePeriodInfo := fmt.Sprintf("from %s to %s (IST)", ethStartAt.AsTime().In(datetime.IST).Format("3:04 PM"), ethEndAt.AsTime().In(datetime.IST).Format("3:04 PM"))
		ethDisclaimerInfoStr := fmt.Sprintf("The US market will remain open %s. Currently, Fi doesn't support extended-hours trading. Your portfolio value may differ slightly from the regular market closing price.", ethTimePeriodInfo)
		ethDisclaimerInfo = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: ethDisclaimerInfoStr},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
		}
	} else {
		ethDisclaimerInfoStr := "Your portfolio value may vary because of extended market hours trading. Fi doesn’t support extended hours trading, hence your portfolio value may vary slightly as compared to prevailing market price."
		ethDisclaimerInfo = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: ethDisclaimerInfoStr},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
		}
	}
	return &deeplink.InformationPopupOptions{
		IconUrl: YellowInfoIconUrl,
		Ctas:    []*deeplink.Cta{{Type: deeplink.Cta_DONE, Text: "Ok, got it"}},
		TextTitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Extended market hours open: Your portfolio value may vary"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
			FontColor:    usstocksUi.Night,
		},
		TextSubTitle: positionLastSyncedAtInfo,
		BodyTexts:    []*commontypes.Text{ethDisclaimerInfo},
		BgColor:      "#F4E7BF",
	}
}
