package deeplink

import "github.com/epifi/be-common/api/typesv2/common/ui/widget"

var (
	FiPlusLaunchAnimationBadgeUrl       = "https://epifi-icons.pointz.in/tiering/plus_badge_3d_fill.png"
	FiInfiniteLaunchAnimationBadgeUrl   = "https://epifi-icons.pointz.in/tiering/infinite_badge_3d_fill.png"
	FiSalaryLaunchAnimationBadgeUrl     = "https://epifi-icons.pointz.in/tiering/salary_badge_3d_fill.png"
	FiPrimeLaunchAnimationBadgeUrl      = "https://epifi-icons.s3.ap-south-1.amazonaws.com/tiering/prime_badge_3d_fill.png"
	FiRegularIconUrl                    = "https://epifi-icons.pointz.in/tiering/regular.png"
	FiStandardIconUrl                   = "https://epifi-icons.pointz.in/tiering/standard_3d_icon.png"
	FiPlusLaunchAnimationBadgeHeight    = 140
	FiPlusLaunchAnimationBadgeWidth     = 140
	FiPlusLaunchAnimationText1          = "INTRODUCING"
	FiPrimeLaunchAnimationText1         = "Exclusive"
	FiPlusLaunchAnimationText1FontColor = "#A4A4A4" // Gray/Steel
	FiLaunchAnimationText2              = "%s"
	FiPlusLaunchAnimationText2FontColor = "#FFFFFF" // Gray/Snow
	FiLaunchAnimationText3              = "On the lookout for big rewards? Try %s, a new account tier on Fi."
	FiPrimeLaunchAnimationText3         = "plan for Salaried users"
	FiPlusLaunchAnimationText3FontColor = "#A4A4A4" // Gray/Steel

	BenefitsBgColor = "#383838" // Gray/Charcoal

	ImageTextOneText            = "Enjoy savings & benefits every month!"
	LaunchInfoFontColorPlus     = "#A44B38"
	LaunchInfoFontColorInfinite = "#478295"
	LaunchInfoFontColorSalary   = "#AC7C44"
	LaunchInfoFontColorRegular  = "#9D9D9D"
	ImageTextTwoText            = "UP TO"
	ImageTextTwoFontColor       = "#FFFFFF"
	ImageTextThreeFontColor     = "#FFFFFF" // snow
	// TODO(sainath): move benefits per year and display strings for each tier to new package
	ImageTextThreePlus     = "₹12,000"
	ImageTextThreeInfinite = "₹25,000"
	ImageTextThreeSalary   = "₹35,000"
	HeroBgColorPlus        = "#FADED0"
	HeroBgColorInfinite    = "#DEEEF2"
	HeroBgColorSalary      = "#F4E7BF"

	CtaShowMeText        = "Show me"
	CtaShowMeTextPrime   = "Upgrade Now"
	CtaRemindMeLaterText = "REMIND ME LATER"

	UpgradeSuccessScreenBgColor        = "#383838" // Gray/Charcoal
	UpgradeSuccessScreenBgColorCtaText = "Done"
	FiPlusUpgradeSuccessText1          = "Congrats!"
	FiPlusUpgradeSuccessText1FontColor = "#FFFFFF" // Gray/Snow
	FiUpgradeSuccessText2              = "You are now on %s"
	FiPlusUpgradeSuccessText2FontColor = "#FFFFFF" // Gray/Snow
	FiUpgradeSuccessText3              = "To keep enjoying %s' benefits, maintain %s as the minimum balance"
	FiSalaryUpgradeSuccessText3        = "To keep Salary’s benefits, continue to keep Fi as your salary account."
	FiPlusUpgradeSuccessText3FontColor = "#A4A4A4" // Gray/Steel

	PlusGradientBackgroundColor = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
		{
			Color:          "#8C4E38",
			StopPercentage: -33,
		},
		{
			Color:          "#AC775E",
			StopPercentage: 33,
		},
		{
			Color:          "#BE8D73",
			StopPercentage: 100,
		},
	})

	InfiniteGradientBackgroundColor = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
		{
			Color:          "#C9D3DF",
			StopPercentage: 0,
		},
		{
			Color:          "#7E8B97",
			StopPercentage: 100,
		},
	})

	RegularGradientBackgroundColor = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
		{
			Color:          "#D8D8D8",
			StopPercentage: 0,
		},
		{
			Color:          "#9D9D9D",
			StopPercentage: 100,
		},
	})

	PrimeGradientBackgroundColor = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
		{
			Color:          "#87C0E1",
			StopPercentage: 0,
		},
		{
			Color:          "#2672C7",
			StopPercentage: 100,
		},
	})

	StandardGradientBackgroundColor = widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
		{
			Color:          "#C9D3DF",
			StopPercentage: 0,
		},
		{
			Color:          "#7E8B97",
			StopPercentage: 100,
		},
	})
)
