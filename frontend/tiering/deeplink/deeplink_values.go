package deeplink

import (
	"fmt"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	moneyPkg "github.com/epifi/be-common/pkg/money"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/tiering/deeplink/animations"
	"github.com/epifi/gamma/frontend/tiering/display_names"
)

func GetLaunchAnimationLottieJsonString(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return animations.FiPlusLaunchAnimationLottieJsonString
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return animations.FiInfiniteLaunchAnimationLottieJsonString
	default:
		if feTier.IsAaSalaryTier() {
			return animations.FiPrimeLaunchAnimationLottieJsonString
		}
		if feTier.IsSalaryRelatedTier() {
			return animations.FiSalaryLaunchAnimationLottieJsonString
		}
		return ""
	}
}

func GetUpgradeSuccessLottieJsonString(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return animations.FiPlusUpgradeSuccessLottiesJsonString
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return animations.FiInfiniteUpgradeSuccessLottiesJsonString
	default:
		if feTier.IsSalaryRelatedTier() {
			return animations.FiSalaryUpgradeSuccessLottiesJsonString
		}
		return ""
	}
}

func GetLaunchAnimationText3(feTier beTieringExtPb.Tier) string {
	if feTier.IsAaSalaryTier() {
		return FiPrimeLaunchAnimationText3
	}
	displayString, _ := display_names.GetTitleCaseDisplayString(feTier)
	return fmt.Sprintf(FiLaunchAnimationText3, displayString)
}

func GetImageTextThreeText(feTier beTieringExtPb.Tier, cashEquivalent uint32) string {
	if cashEquivalent != 0 {
		return fmt.Sprintf("%s/yr", moneyPkg.ToDisplayStringInIndianFormat(&moneyPb.Money{
			CurrencyCode: "INR",
			Units:        int64(cashEquivalent),
		}, 0, true))
	}
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return ImageTextThreePlus
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return ImageTextThreeInfinite
	default:
		if feTier.IsSalaryRelatedTier() {
			return ImageTextThreeSalary
		}
		return ""
	}
}

func GetLaunchAnimationText2(feTier beTieringExtPb.Tier) string {
	displayString, _ := display_names.GetTitleCaseDisplayString(feTier)
	if feTier.IsAaSalaryTier() {
		displayString += " Plan"
	}
	return fmt.Sprintf(FiLaunchAnimationText2, displayString)
}

func GetLaunchAnimationBadgeUrl(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return FiPlusLaunchAnimationBadgeUrl
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return FiInfiniteLaunchAnimationBadgeUrl
	default:
		if feTier.IsAaSalaryTier() {
			return FiPrimeLaunchAnimationBadgeUrl
		}
		if feTier.IsSalaryRelatedTier() {
			return FiSalaryLaunchAnimationBadgeUrl
		}
		return ""
	}
}

func GetLaunchAnimationBadgeUrlV2(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return FiPlusLaunchAnimationBadgeUrl
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return FiInfiniteLaunchAnimationBadgeUrl
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return FiRegularIconUrl
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return FiStandardIconUrl
	default:
		if feTier.IsAaSalaryTier() {
			return FiPrimeLaunchAnimationBadgeUrl
		}
		if feTier.IsSalaryRelatedTier() {
			return FiSalaryLaunchAnimationBadgeUrl
		}
		return ""
	}
}

func GetUpgradeSuccessText2(feTier beTieringExtPb.Tier) string {
	displayString, _ := display_names.GetTitleCaseDisplayString(feTier)
	return fmt.Sprintf(FiUpgradeSuccessText2, displayString)
}

func GetUpgradeSuccessText3(feTier beTieringExtPb.Tier, minBalanceDisplayString string) string {
	if feTier.IsSalaryRelatedTier() {
		return FiSalaryUpgradeSuccessText3
	}
	displayString, _ := display_names.GetTitleCaseDisplayString(feTier)
	return fmt.Sprintf(FiUpgradeSuccessText3, displayString, minBalanceDisplayString)
}

func GetImageTextOneFontColor(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return LaunchInfoFontColorPlus
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return LaunchInfoFontColorInfinite
	default:
		if feTier.IsSalaryRelatedTier() {
			return LaunchInfoFontColorSalary
		}
		return ""
	}
}

func GetGradientTierColorV2(feTier beTieringExtPb.Tier) *widget.BackgroundColour {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return PlusGradientBackgroundColor
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return InfiniteGradientBackgroundColor
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return RegularGradientBackgroundColor
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return PrimeGradientBackgroundColor
	default:
		return StandardGradientBackgroundColor
	}
}

func GetFadingGradientTierColor(feTier beTieringExtPb.Tier, appPlatform commontypes.Platform) *widget.BackgroundColour {
	degree := lo.Ternary(appPlatform == commontypes.Platform_ANDROID, int32(180), int32(90))
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return widget.GetLinearGradientBackgroundColour(degree, []*widget.ColorStop{
			{
				Color:          "#995E47",
				StopPercentage: 0,
			},
			{
				Color:          "#E7EBED",
				StopPercentage: 100,
			},
		})
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return widget.GetLinearGradientBackgroundColour(degree, []*widget.ColorStop{
			{
				Color:          "#BEC9D5",
				StopPercentage: 0,
			},
			{
				Color:          "#E7EBED",
				StopPercentage: 100,
			},
		})
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return widget.GetLinearGradientBackgroundColour(degree, []*widget.ColorStop{
			{
				Color:          "#7BB7DD",
				StopPercentage: 0,
			},
			{
				Color:          "#E7EBED",
				StopPercentage: 100,
			},
		})
	default:
		return StandardGradientBackgroundColor
	}
}

func GetHeroBgColor(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return HeroBgColorPlus
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return HeroBgColorInfinite
	default:
		if feTier.IsSalaryRelatedTier() {
			return HeroBgColorSalary
		}
		return ""
	}
}

func GetHeroCircleColor(feTier beTieringExtPb.Tier) string {
	switch feTier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return LaunchInfoFontColorPlus
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return LaunchInfoFontColorInfinite
	default:
		if feTier.IsSalaryRelatedTier() {
			return LaunchInfoFontColorSalary
		}
		return ""
	}
}
