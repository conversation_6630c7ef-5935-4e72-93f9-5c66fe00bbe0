// nolint: dupl
package aggregator

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/investment/aggregator"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	types "github.com/epifi/gamma/api/typesv2"
	caDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	usStocksOpts "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/screen_options_v2"
	userPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/frontend/investment/aggregator/components"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	MFCardTitle                   = "Mutual Funds"
	MFCardSubTitle                = "Curated funds"
	JumpCardTitle                 = "Jump"
	JumpCardSubTitle              = "Upto 10% returns"
	SDCardTitle                   = "Smart Deposits"
	SDCardSubTitle                = "Park extra cash"
	FDCardTitle                   = "Fixed Deposits"
	FDCardSubTitle                = "Safe place to save"
	USStocksCardTitle             = "US Stocks"
	USStocksCardSubTitle          = "0 Commissions"
	IndianStocksCardTitle         = "Indian Stocks"
	IndianStocksCardSubtitle      = "Track your stocks"
	JumpInstrumentIconURL         = "https://epifi-icons.pointz.in/investments/landing/p2p_color.png"
	MFInstrumentIconURL           = "https://epifi-icons.pointz.in/investments/landing/mutual_funds_color.png"
	SDInstrumentIconURL           = "https://epifi-icons.pointz.in/investments/landing/sd_color.png"
	USSInstrumentIconURL          = "https://epifi-icons.pointz.in/investments/landing/us_stocks_color.png"
	FDInstrumentIconURL           = "https://epifi-icons.pointz.in/investments/landing/fd_color.png"
	IndianStocksInstrumentIconURL = "https://epifi-icons.pointz.in/investments/landing/indian_stocks_colors.png"
)

func (s *Service) GetInvestmentInstrumentNavigationToggle(ctx context.Context, req *fePb.GetInvestmentInstrumentNavigationToggleRequest) (*fePb.GetInvestmentInstrumentNavigationToggleResponse, error) {

	actorId := req.GetReq().GetAuth().GetActorId()

	var instrumentCards []*fePb.InstrumentCard

	MFInstrumentCardFlag := s.config.Flags().EnableMFInstrumentCardFlag()
	USStocksInstrumentCardFlag := s.config.Flags().EnableUSStocksInstrumentCardFlag()
	JumpInstrumentCardFlag := s.config.Flags().EnableJumpInstrumentCardFlag()
	SDInstrumentCardFlag := s.config.Flags().EnableSDInstrumentCardFlag()
	FDInstrumentCardFlag := s.config.Flags().EnableFDInstrumentCardFlag()

	if MFInstrumentCardFlag {
		instrumentCards = append(instrumentCards, getMutualFundCardForNavigationToggle())
	}
	isUSFundsEnabled, err := s.isUSFundsEnabled(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in isUSFundsEnabled", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}
	if USStocksInstrumentCardFlag {
		if isUSFundsEnabled {
			usFundsCard, er := s.getUSFundsToggle(ctx, actorId)
			if er != nil {
				logger.Error(ctx, "error in getUSFundsToggle", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(er))
			} else {
				instrumentCards = append(instrumentCards, usFundsCard)
			}
		}
		usStocksCard, err := s.getUSStocksCardForNavigationToggle(ctx, actorId, isUSFundsEnabled)
		if err != nil {
			logger.Error(ctx, "error in getUSStocksCardForNavigationToggle", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		} else {
			instrumentCards = append(instrumentCards, usStocksCard)
		}
	}
	if JumpInstrumentCardFlag {
		instrumentCards = append(instrumentCards, getJumpCardForNavigationToggle())
	}
	if SDInstrumentCardFlag {
		instrumentCards = append(instrumentCards, getSDCardForNavigationToggle())
	}
	if FDInstrumentCardFlag {
		instrumentCards = append(instrumentCards, getFDCardForNavigationToggle())
	}

	isIndianStocksEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_NETWORTH_IND_SECURITIES_WIDGET).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluated if indian stocks is released for actor", zap.Error(err))
	}
	if isIndianStocksEnabled {
		instrumentCards = append(instrumentCards, s.getIndianStocksCardForNavigationToggle(ctx, actorId))
	}

	return &fePb.GetInvestmentInstrumentNavigationToggleResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		InvestmentInstruments: &fePb.InvestmentInstruments{
			Title: &commontypes.Text{
				FontColor:    "#606265",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Switch to"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
			},
			Cards: instrumentCards,
		},
	}, nil

}

func getJumpCardForNavigationToggle() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl:       JumpInstrumentIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(JumpInstrumentIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: JumpCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: JumpCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		BgColor:        "#FFFFFF",
		Deeplink:       &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN},
		InstrumentType: fePb.InstrumentType_P2P_JUMP,
	}
}

func getMutualFundCardForNavigationToggle() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl:       MFInstrumentIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(MFInstrumentIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: MFCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: MFCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Tag:            nil,
		Deeplink:       &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN},
		BgColor:        "#FFFFFF",
		InstrumentType: fePb.InstrumentType_MUTUAL_FUNDS,
	}
}

func getSDCardForNavigationToggle() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl:       SDInstrumentIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(SDInstrumentIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: SDCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: SDCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		},
		BgColor:        "#FFFFFF",
		InstrumentType: fePb.InstrumentType_SMART_DEPOSIT,
	}
}

func (s *Service) getUSStocksCardForNavigationToggle(ctx context.Context, actorId string, isUsFundsEnabled bool) (*fePb.InstrumentCard, error) {

	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})

	err = epifigrpc.RPCError(actorRes, err)
	if err != nil {
		return nil, err
	}

	userRes, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: actorRes.Actor.GetEntityId(),
		}})

	err = epifigrpc.RPCError(userRes, err)
	if err != nil {
		return nil, err
	}

	groupResp, err := s.userGroupClient.GetGroupsMappedToEmail(ctx, &usergroupPb.GetGroupsMappedToEmailRequest{
		Email: userRes.GetUser().GetProfile().GetEmail(),
	})
	err = epifigrpc.RPCError(groupResp, err)
	if err != nil {
		return nil, err
	}

	isUSStocksEnabled, err := s.isUSStocksInstrumentEnabled(ctx, actorId, groupResp.GetGroups())
	if err != nil {
		return nil, err
	}

	if !isUSStocksEnabled {
		return nil, nil
	}
	usStocksCardSubtitle := components.GetUSStocksCardSubtitle(isUsFundsEnabled)
	landingScreenDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_USSTOCKS_LANDING_SCREEN,
	}
	if isUsFundsEnabled {
		landingScreenDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_USSTOCKS_LANDING_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&usStocksOpts.LandingPageScreenOptions{
				EntryPoint: usstocksFePb.LandingScreenEntryPoint_LANDING_SCREEN_ENTRY_POINT_STOCKS.String(),
			}),
		}
	}

	return &fePb.InstrumentCard{
		IconUrl:       USSInstrumentIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(USSInstrumentIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: USStocksCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: usStocksCardSubtitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: landingScreenDeeplink,

		BgColor:        "#FFFFFF",
		StoryDeeplink:  nil,
		InstrumentType: fePb.InstrumentType_US_STOCKS,
	}, nil

}

// nolint
func (i *Service) getUSFundsToggle(ctx context.Context, actorId string) (*fePb.InstrumentCard, error) {
	return &fePb.InstrumentCard{
		IconUrl:       components.USFundsIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(components.USFundsIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: components.USFundsCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: components.USFundsCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_USSTOCKS_LANDING_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&usStocksOpts.LandingPageScreenOptions{
				EntryPoint: usstocksFePb.LandingScreenEntryPoint_LANDING_SCREEN_ENTRY_POINT_FUNDS.String(),
			}),
		},

		BgColor:        "#FFFFFF",
		StoryDeeplink:  nil,
		InstrumentType: fePb.InstrumentType_US_FUNDS,
	}, nil
}
func (s *Service) isUSFundsEnabled(ctx context.Context, actorId string) (bool, error) {
	enableUSFunds, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE).WithActorId(actorId))
	if err != nil {
		return false, err
	}
	return enableUSFunds, nil
}

func (s *Service) isUSStocksInstrumentEnabled(ctx context.Context, actorId string,
	userGroups []commontypes.UserGroup) (bool, error) {
	enableUSStocks, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_US_STOCK_UI).WithActorId(actorId).WithUserGroup(userGroups))
	if err != nil {
		return false, err
	}
	return enableUSStocks, nil
}

func getFDCardForNavigationToggle() *fePb.InstrumentCard {
	return &fePb.InstrumentCard{
		IconUrl:       FDInstrumentIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(FDInstrumentIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: FDCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: FDCardSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		},
		BgColor:        "#FFFFFF",
		InstrumentType: fePb.InstrumentType_FIXED_DEPOSIT,
	}
}

func (s *Service) getIndianStocksCardForNavigationToggle(ctx context.Context, actorId string) *fePb.InstrumentCard {
	var redirectionDl *deeplinkPb.Deeplink
	accountRes, err := s.connectedAccountClient.GetAccounts(ctx, &connectedAccountPb.GetAccountsRequest{
		ActorId: actorId,
		AccountFilterList: []connectedAccountExternalPb.AccountFilter{
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
		},
		AccInstrumentTypeList: []connectedAccountEnumsPb.AccInstrumentType{
			connectedAccountEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
			connectedAccountEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
			connectedAccountEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT,
			connectedAccountEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT,
		},
	})
	if rpcErr := epifigrpc.RPCError(accountRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to get aa equity accounts for actor", zap.Error(rpcErr))
	}

	if accountRes.GetStatus().IsRecordNotFound() || (accountRes.GetStatus().IsSuccess() && len(accountRes.GetAccountDetailsList()) == 0) {
		redirectionDl = connectIndianStockAccountsDeeplink()
	} else {
		redirectionDl = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_INDIAN_STOCKS_DASHBOARD_SCREEN,
		}
	}

	return &fePb.InstrumentCard{
		IconUrl:       IndianStocksInstrumentIconURL,
		VisualElement: commontypes.GetVisualElementImageFromUrl(IndianStocksInstrumentIconURL),
		Title: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: IndianStocksCardTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: IndianStocksCardSubtitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		Deeplink:       redirectionDl,
		BgColor:        "#FFFFFF",
		InstrumentType: fePb.InstrumentType_INDIAN_STOCKS,
	}
}

func connectIndianStockAccountsDeeplink() *deeplinkPb.Deeplink {
	connectAccDlScreenOptions := deeplinkv3.GetScreenOptionV2WithoutError(&caDlOptions.ConnectAccountsFlowScreenOptions{
		CaFlowName: connectedAccountEnumsPb.CAFlowName_CA_FLOW_NAME_NET_WORTH_IND_STOCKS.String(),
	})

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
		ScreenOptionsV2: connectAccDlScreenOptions,
	}
}
