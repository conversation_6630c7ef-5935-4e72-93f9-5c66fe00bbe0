package rewards

import (
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"

	beCasperPb "github.com/epifi/gamma/api/casper"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
)

type rewardTypeSpecificDisplayDetails struct {
	summaryTitle                         string
	pendingRewardsBottomSheetTitle       string
	summarySectionProcessedRewardsIcon   string
	pendingRewardsBottomSheetRewardsIcon string
}

const (
	// name used for reward's SD, for now all reward SDs would have same name
	rewardsSDName = "Reward Smart Deposit"

	// time durations
	// assuming 30 days in a month
	oneMonthDuration                = 24 * 30 * 1 * time.Hour
	defaultCardOfferCatalogCacheTtl = 10 * time.Minute

	// header text used for delivery address field
	deliveryAddressHeader                         = "Delivery Address"
	FirstAndroidAppVersionToSupportFitCollections = 202

	// types of offers on rewards and offers widget
	offerTypeCbr       = "CBR"
	offerTypeNonCbr    = "NON_CBR"
	offerTypeDebitCard = "DEBIT_CARD"

	UNREDEEMABLE_OFFER_LABEL_COMING_SOON = "COMING_SOON"

	// colors
	colorMonochromeLead              = "#606265"
	colorPastelLemon                 = "#80F4E7BF"
	colorMonochromeIvory             = "#F7F9FA"
	colorMonochromeChalk             = "#F0F3F7"
	colorMonochromeSmoke             = "#E3E7EC"
	colorFiGreen                     = "#00B899"
	colorFiSnow                      = "#FFFFFF"
	colorGrayLead                    = "#646464"
	colorGraySlate                   = "#8D8D8D"
	colorDarkOcean                   = "#7FBECE"
	colorDarkBerry                   = "#9287BD"
	colorBlack                       = "#000000"
	colorCharcoalBlack               = "#040404"
	colorContentOnDarkLowEmphasis    = "#6A6D70"
	colorContentOnDarkHighEmphasis   = "#F6F9FD"
	colorContentOnDarkMediumEmphasis = "#929599"
	colorInk                         = "#282828"
	colorContentOnLightHighEmphasis  = "#313234"
	colorGrayNight                   = "#333333"
	colorIvory                       = "#F7F9FA"
	colorSupportingAmber100          = "#F6E1C1"
	colorSupportingAmber700          = "#D48647"
	colorSupportingAmber900          = "#C0723D"
	colorSupportingAmber50           = "#FBF3E6"
	colorGraySteel                   = "#A4A4A4"
	colorShadow                      = "#D9DEE3"
	colorGreenShadow                 = "#00866F"
	colorGunmetal                    = "#18191B"
	colorDeepGreen                   = "#007A56"
	colorJadeLightGreen              = "#A8E0D3"
	colorCloudGray                   = "#E6E9ED"

	// external links
	clubVistaraSignUpPageUrl = "https://go.fi.money/Vistara"

	// cache keys
	cardOfferCatalogCacheKeyTemplate = "rewards:card_offer_catalog:%s"

	// icon URLs
	rewardsSummaryLockedRewardsIcon                = "https://epifi-icons.pointz.in/rewards/rewards_summary_locked_reward_icon.png"
	rewardsSummaryProcessingRewardsIcon            = "https://epifi-icons.pointz.in/rewards/rewards_summary_processing_rewards_icon.png"
	pendingRewardsBottomSheetLockedRewardsIcon     = "https://epifi-icons.pointz.in/rewards/locked_reward_pending_rewards_icon.png"
	pendingRewardsBottomSheetProcessingRewardsIcon = "https://epifi-icons.pointz.in/rewards/processing_rewards_pending_rewards_icon.png"
	chevronDownGray                                = "https://epifi-icons.pointz.in/rewards/chevron_down_grey.png"
	chevronRightWithCircleBgGray                   = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/chevron_right_with_circle_bg_gray"
	rightChevronWhiteUrl                           = "https://epifi-icons.pointz.in/chevron_right_white.png"
	rightChevronGreyUrl                            = "https://epifi-icons.pointz.in/rewards/chevron-right-lead.png"
	rightChevronGreenUrl                           = "https://epifi-icons.pointz.in/home-v2/green-chevron-icon-2.png"
	debitCardOffersPageTabLeftIconUrl              = "https://epifi-icons.pointz.in/rewards/debit-card-offers-page-tab-icon.png"
	amplifiCreditCardOffersPageTabLeftIconUrl      = "https://epifi-icons.pointz.in/rewards/amplifi-credit-card-offers-page-tab-icon.png"  // nolint:gosec
	simplifiCreditCardOffersPageTabLeftIconUrl     = "https://epifi-icons.pointz.in/rewards/simplifi-credit-card-offers-page-tab-icon.png" // nolint:gosec
	magnifiCreditCardOffersPageTabLeftIconUrl      = "https://epifi-icons.pointz.in/rewards/magnifi-credit-card-offers-page-tab-icon.png"  // nolint:gosec
	pencilIconUrl                                  = "https://epifi-icons.pointz.in/vkyc/pencil-icon.png"
	fiCoinsGiftUrl                                 = "https://epifi-icons.pointz.in/rewards/fi-coins-gift.png"
	goldFiCoinsUrl                                 = "https://epifi-icons.pointz.in/rewards/gold-fi-coins.png"
	rupeeSymbolIconUrl                             = "https://epifi-icons.pointz.in/rewards/rupee-symbol-icon.png"
	cashIconUrl                                    = "https://epifi-icons.pointz.in/rewards/cash-icon.png"

	// card offers page tab title
	debitCardOffersPageTabTitle          = "Debit Card"
	amplifiCreditCardOffersPageTabTitle  = "Credit Card"
	simplifiCreditCardOffersPageTabTitle = "Credit Card"
	magnifiCreditCardOffersPageTabTitle  = "Credit Card"

	// constants for dynamic url for webpage screen
	vendorForDynamicUrlForWebpageScreen    = "vendor"
	targetUrlForDynamicUrlForWebpageScreen = "target_url"

	// Offers catalog page constants
	// * Top bar Details
	questionMarkIconUrl    = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/question_mark"
	tncIconUrl             = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/tnc"
	rewardsTnCWebpageTitle = "Rewards T&Cs"
	rewardsTnCWebpageUrl   = "https://fi.money/rewards/TnC"
	// * Sections
	//   - My Earnings Section
	collectedOffersTagUrl  = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/collected_offers_tag"
	collectedOffersCtaText = "Collected\nOffers"
	fiCoinsIconUrl         = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/ficoin.png"
	lockIconUrl            = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/lock.png"
	//   - My Rewards Section V2
	fiCoinBalanceTitleText = "Your Fi Coins"
	allRewardsCtaText      = "All Rewards"
	//   - Generic Server Driven Section
	genericSduiSectionTitle = "Use your Fi coins"
	//   - Banners Section
	// 	 - Offer Filters Section
	allOffersFilterText               = "all"
	ActiveOffersFilterTextColor       = colorContentOnDarkHighEmphasis
	ActiveOffersFilterIconBgColor     = colorFiSnow
	ActiveOffersFilterIconBorderColor = colorFiGreen
	ActiveOffersFilterIconBorderWidth = 2
	//   - Offers Section
	//	   - Offer
	offerRedemptionButton                = "offerRedemptionButton"
	viewOfferDetailsRedemptionButton     = "viewOfferDetailsRedemptionButton"
	offerRedemptionBottomSheet           = "offerRedemptionBottomSheet"
	catalogOfferCardHeight               = 242
	catalogOfferCardShadowHeight         = 4
	finalCatalogOfferCardHeight          = catalogOfferCardHeight + catalogOfferCardShadowHeight
	catalogOfferCardWidth                = 178
	horizontalPaddingBetweenCatalogCards = 10
	verticalPaddingBetweenCatalogCards   = 12
	//		 - Offer Details Bottom Sheet
	offerDetailsBottomSheetTitle      = "Offer Details"
	offerDetailsBottomSheetTitleColor = colorGrayNight
	offerDetailsTopSectionTitleColor  = colorContentOnDarkHighEmphasis

	// screen names
	catalogOfferRedemptionDefaultBottomSheetScreenName         = "DEFAULT"
	catalogOfferRedemptionInoperableInfoBottomSheetScreenName  = "INOPERABLE_INFO"
	catalogOfferRedemptionCustomActionBottomSheetScreenName    = "CUSTOM_ACTION"
	catalogOfferRedemptionLoanEmiReminderBottomSheetScreenName = "LOAN_EMI_REMINDER"

	// reward claim success screen constants
	rewardClaimSuccessScreenTopBgImageUrl        = "https://epifi-icons.pointz.in/rewards/reward-claim-success-screen/top-bg.png"
	rewardClaimSuccessScreenFiCoinsIconUrl       = "https://epifi-icons.pointz.in/rewards/reward-claim-success-screen/fi-coins-icon.png"
	rewardClaimSuccessScreenFiCoinRewardTitle    = "Total Fi-Coins"
	rewardClaimSuccessScreenBottomSectionCtaText = "Go to Gift Card Store"
	rewardClaimSuccessScreenPromoSectionTitle    = "Handpicked Gift Cards for You!"
)

var (
	FitAllCollectionsDeeplink = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_FIT_ALL_COLLECTIONS_PAGE,
		ScreenOptions: &deeplinkPb.Deeplink_FitAllCollectionsPageScreenOptions{
			FitAllCollectionsPageScreenOptions: &deeplinkPb.FitAllCollectionsPageScreenOptions{CollectionType: "AUTO_SAVE"}},
	}

	// display details specific to reward types
	rewardTypeDisplayDetailsMapping = map[beRewardsPb.RewardType]rewardTypeSpecificDisplayDetails{
		beRewardsPb.RewardType_FI_COINS: {
			summaryTitle:                         "FI-COINS",
			pendingRewardsBottomSheetTitle:       "Pending Fi-Coins",
			summarySectionProcessedRewardsIcon:   "https://epifi-icons.pointz.in/rewards/rewards_summary_fi_coins_icon.png",
			pendingRewardsBottomSheetRewardsIcon: "https://epifi-icons.pointz.in/rewards/pending_rewards_fi_coins_symbol.png",
		},
		beRewardsPb.RewardType_CASH: {
			summaryTitle:                         "CASHBACK",
			pendingRewardsBottomSheetTitle:       "Pending Cashback",
			summarySectionProcessedRewardsIcon:   "https://epifi-icons.pointz.in/rewards/rewards_summary_rupee_icon.png",
			pendingRewardsBottomSheetRewardsIcon: "https://epifi-icons.pointz.in/rewards/pending_rewards_rupee_symbol.png",
		},
	}

	offerNotFoundErrorView = &errorsPb.ErrorView{
		Type: errorsPb.ErrorViewType_FULL_SCREEN_V2,
		Options: &errorsPb.ErrorView_FullScreenErrorViewV2{
			FullScreenErrorViewV2: &errorsPb.FullScreenErrorViewV2{
				Image:    commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/offer-not-found-thunder-cloud-transparent.png", 124, 123),
				Title:    commontypes.GetTextFromStringFontColourFontStyle("Offer Not Found", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_XL),
				Subtitle: commontypes.GetTextFromHtmlStringFontColourFontStyle("This might be because the offer has expired or you're no longer eligible. Please check back later for more offers.", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S),
				Ctas: []*deeplinkPb.Cta{
					{
						Type:         deeplinkPb.Cta_CUSTOM,
						Text:         "Okay",
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
						Deeplink:     &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME},
					},
				},
				BackgroundColor: colors.ColorDarkBase},
		},
	}

	// Offers Catalog Page Related Constants
	customBottomSheetOfferTypes = []beCasperPb.OfferType{
		beCasperPb.OfferType_VISTARA_AIR_MILES,
		beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS,
		beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
		beCasperPb.OfferType_EXTERNAL_VENDOR,
	}

	// Button Shadows
	greenShadow = &ui.Shadow{
		Height: 4,
		Colour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: colorGreenShadow,
			},
		},
	}

	greyShadow = &ui.Shadow{
		Height: 4,
		Colour: &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: colors.ColorMonochromeAsh,
			},
		},
	}
)
