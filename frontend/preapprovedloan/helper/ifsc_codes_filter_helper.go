package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	searchPb "github.com/epifi/gamma/api/search"

	"github.com/samber/lo"
)

// TODO(@Shivansh) Add the correct list of IFSCs here
var filterMap = map[pal_enums.SearchIfscType][]string{
	pal_enums.SearchIfscType_SEARCH_IFSC_TYPE_SUBVENTION_MIN_AND_RE_KYC_USERS: []string{"FDRL0005555", "FDRL0005556", "FDRL0007777"},
	pal_enums.SearchIfscType_SEARCH_IFSC_TYPE_FI_ACCOUNT_NOT_OPERATIONAL:      []string{"FDRL0005555", "FDRL0005556"},
}

func FilterIfscSuggestions(searchType pal_enums.SearchIfscType, res *searchPb.AutocompleteResponse) []*palFePb.GetIfscCodeSuggestionsResponse_Suggestion {
	var feSuggestions []*palFePb.GetIfscCodeSuggestionsResponse_Suggestion
	if len(res.GetResult().GetSuggestions()) == 0 {
		return feSuggestions
	}

	for _, suggestion := range res.GetResult().GetSuggestions() {
		feSuggestions = append(feSuggestions, &palFePb.GetIfscCodeSuggestionsResponse_Suggestion{
			Ifsc:         suggestion.GetIfsc(),
			BankName:     suggestion.GetBankName(),
			BankBranch:   suggestion.GetBankBranch(),
			BankDistrict: suggestion.GetBankDistrict(),
		})
	}

	blacklistedIfsc, ok := filterMap[searchType]
	// if search type is not present, return the suggestions as such
	if !ok {
		return feSuggestions
	}

	// apply the filter to edit the blacklisted IFSCs, and add the error msg that we need to show in those suggestions
	for _, suggestion := range feSuggestions {
		if lo.Contains(blacklistedIfsc, suggestion.GetIfsc()) {
			suggestion.ReasonForUnavailability = commontypes.GetTextFromStringFontColourFontStyle("This bank account is not supported", "#A93D5B", commontypes.FontStyle_BODY_XS)
		}
	}
	return feSuggestions
}
