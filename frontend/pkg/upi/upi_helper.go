//nolint:depguard
package upi

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"

	"github.com/epifi/be-common/pkg/mask"
	actorPb "github.com/epifi/gamma/api/actor"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/accounts"
	feAccUpiPb "github.com/epifi/gamma/api/frontend/account/upi"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	pb "github.com/epifi/gamma/api/frontend/user"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	upiScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	beUPIPb "github.com/epifi/gamma/api/upi"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	payPkg "github.com/epifi/gamma/pkg/pay"
	upiPkg "github.com/epifi/gamma/pkg/upi"
)

// IsUpiTpapEnabledForActor - checks whether upi TPAP is enabled for actor or not
func IsUpiTpapEnabledForActor(ctx context.Context, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsTpapEnabledForActor(ctx, &upiOnbPb.IsTpapEnabledForActorRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if tpap is enabled for user",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// IsUpiLiteEnabledForActor - checks whether upi lite is enabled for actor or not
func IsUpiLiteEnabledForActor(ctx context.Context, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsUpiLiteEnabledForActor(ctx, &upiOnbPb.IsUpiLiteEnabledForActorRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if upi lite is enabled for user",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// IsMlKitQrEnabled checks if the ML Kit Qr is enabled for the actor
func IsMlKitQrEnabled(ctx context.Context, actorId string, evaluator release.IEvaluator) bool {
	constraints := release.NewCommonConstraintData(types.Feature_ML_KIT_QR).WithActorId(actorId)
	isAllowed, err := evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if new qr sdk is enabled for the user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed
}

// IsUpiMapperEnabledForActor - checks whether upi Mapper is enabled for actor or not
func IsUpiMapperEnabledForActor(ctx context.Context, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsMapperEnabledForActor(ctx, &upiOnbPb.IsMapperEnabledForActorRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if mapper is enabled for user",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// IsUpiPinSetUsingAadhaarEnabledForActor - checks if user is allowed to set/reset upi pin using aadhaar number
func IsUpiPinSetUsingAadhaarEnabledForActor(ctx context.Context, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsUpiPinSetUsingAadhaarEnabledForActor(ctx, &upiOnbPb.IsUpiPinSetUsingAadhaarEnabledForActorRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if upi pin set using aadhaar number is enabled for the user",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// GetDerivedAccountIdByPiId - creates and returns derived account id for given pi id and actor id
func GetDerivedAccountIdByPiId(ctx context.Context, piId string, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient, accountPiClient accountPiPb.AccountPIRelationClient) (string, error) {
	accPiRes, err := accountPiClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{
		PiId: piId,
	})
	if rpcErr := epifigrpc.RPCError(accPiRes, err); rpcErr != nil {
		return "", fmt.Errorf("error fetching account pi for given pi id %s", piId)
	}

	tpapAccRes, err := upiOnbClient.GetAccounts(ctx, &upiOnbPb.GetAccountsRequest{
		ActorId:       actorId,
		AccountStatus: []upiOnbEnumsPb.UpiAccountStatus{upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
	})
	if rpcErr := epifigrpc.RPCError(tpapAccRes, err); rpcErr != nil {
		if tpapAccRes.GetStatus().IsRecordNotFound() {
			return payPkg.GetEncodedDerivedAccountId(accPiRes.GetAccountId(), "", "")
		}
		return "", fmt.Errorf("error fetching tpap accounts for given actor id %s", actorId)
	}

	for _, tpapAccount := range tpapAccRes.GetAccounts() {
		if tpapAccount.GetId() == accPiRes.GetAccountId() || tpapAccount.GetAccountRefId() == accPiRes.GetAccountId() {
			return payPkg.GetEncodedDerivedAccountId(tpapAccount.GetAccountRefId(), tpapAccount.GetId(), "")
		}
	}

	return "", fmt.Errorf("failed to get derived account id for given pi id %s and actor id %s", piId, actorId)
}

// GetTpapAccountIdFromDerivedId - decodes tpap account id from derived account id
func GetTpapAccountIdFromDerivedId(derivedAccountId string) (string, error) {
	derivedAccountIdProto := &accounts.DerivedAccountId{}

	err := idgen.DecodeProtoFromStdBase64(derivedAccountId, derivedAccountIdProto)
	if err != nil {
		return "", fmt.Errorf("error decoding derived accountId %s %w", derivedAccountId, err)
	}
	return derivedAccountIdProto.GetTpapAccountId(), nil
}

// GetDerivedAccountIdFromTpapAccountId - encodes tpap account id to generate derived account id
func GetDerivedAccountIdFromTpapAccountId(tpapAccountId string) (string, error) {
	derivedAccountId, err := idgen.EncodeProtoToStdBase64(&accounts.DerivedAccountId{
		TpapAccountId: tpapAccountId,
	})
	if err != nil {
		return "", fmt.Errorf("error getting encoded-derived-account-id for the actor %w", err)
	}
	return derivedAccountId, nil
}

// GetDerivedAccountIdFromInternalAccountId - encodes internal account id to generate derived account id
func GetDerivedAccountIdFromInternalAccountId(internalAccountId string) (string, error) {
	derivedAccountId, err := idgen.EncodeProtoToStdBase64(&accounts.DerivedAccountId{
		InternalAccountId: internalAccountId,
	})
	if err != nil {
		return "", fmt.Errorf("error getting encoded-derived-account-id for the actor %w", err)
	}
	return derivedAccountId, nil
}

// GetDerivedAccountIdFromInternalAndTpapAccId - encodes internal account id and tpap accId to generate derived account id
func GetDerivedAccountIdFromInternalAndTpapAccId(internalAccountId string, tpapAccountId string) (string, error) {
	derivedAccountId, err := idgen.EncodeProtoToStdBase64(&accounts.DerivedAccountId{
		InternalAccountId: internalAccountId,
		TpapAccountId:     tpapAccountId,
	})
	if err != nil {
		return "", fmt.Errorf("error getting encoded-derived-account-id for the actor %w", err)
	}
	return derivedAccountId, nil
}

// IsUpiInternationalPaymentEnabledForActor - checks if international payment activation/deactivation is enabled for the user
func IsUpiInternationalPaymentEnabledForActor(ctx context.Context, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsUpiInternationalPaymentEnabledForActor(ctx, &upiOnbPb.IsUpiInternationalPaymentEnabledForActorRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if international payment is enabled for the user",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// IsCcLinkingEnabledForActor checks if user is allowed to link credit cards for upi payments
func IsCcLinkingEnabledForActor(ctx context.Context, actorId string, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsCcLinkingEnabledForActor(ctx, &upiOnbPb.IsCcLinkingEnabledForActorRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if cc linking is enabled for the user",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// IsFeatureEnabledForActor - checks if the passed feature is enabled for given actor or not
func IsFeatureEnabledForActor(ctx context.Context, actorId string, feature types.Feature, upiOnbClient upiOnbPb.UpiOnboardingClient) bool {
	resp, err := upiOnbClient.IsFeatureEnabledForActor(ctx, &upiOnbPb.IsFeatureEnabledForActorRequest{
		ActorId: actorId,
		Feature: feature,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in checking if given feature is enabled for the user",
			zap.String(logger.FEATURE, feature.String()),
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return resp.GetIsEnabled()
}

// GetCredJsonForUpiLiteLRNActivation - used to get credJsonString required by CL
// for upi lite LRN activation flow
func GetCredJsonForUpiLiteLRNActivation() (string, error) {
	controlJson := &beUPIPb.ControlJson{
		CredAlloweds: []*beUPIPb.CredAllowed{
			{
				Type:    feAccUpiPb.CredBlockType_DEVICE.String(),
				SubType: feAccUpiPb.CredBlockSubType_IDENTITY.String(),
				DType:   "ALPH",
				DLength: "2048",
			},
		},
	}

	marshaller := jsonpb.Marshaler{}
	credJsonString, err := marshaller.MarshalToString(controlJson)
	if err != nil {
		return "", fmt.Errorf("error in marshalling credsJson for upi lite payments: err = %w", err)
	}
	return credJsonString, nil
}

// GetTpapEntryPointDeeplinkForAddFundsTpapFlow : add funds flow expectations of deeplink is different than
// normal pay flow, so creating this separate pkg method that provides appropriate deeplink for add funds TPAP flow
func GetTpapEntryPointDeeplinkForAddFundsTpapFlow(ctx context.Context, actorId, vpa string, feDynamicConf *genconf.Config, upiOnbClient upiOnbPb.UpiOnboardingClient) (*deeplinkPb.Deeplink, error) {
	if IsUpiTpapEnabledForActor(ctx, actorId, upiOnbClient) {
		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
	}
	if IsFeatureEnabledForActor(ctx, actorId, types.Feature_NEW_VPA_HANDLE, upiOnbClient) &&
		IsFeatureEnabledForActor(ctx, actorId, types.Feature_VPA_MIGRATION_INTRO_SCREEN, upiOnbClient) {

		return createVpaMigrationIntroScreenDeeplink(vpa, feDynamicConf.VpaMigrationScreenParams().NewVpaHandle, feDynamicConf.VpaMigrationScreenParams().VpaMigrationIntroScreenParams)
	}

	return nil, nil
}

func GetTpapEntryPointDeeplink(ctx context.Context, actorId, vpa string, feDynamicConf *genconf.Config, upiOnbClient upiOnbPb.UpiOnboardingClient) (*deeplinkPb.Deeplink, error) {
	if IsUpiTpapEnabledForActor(ctx, actorId, upiOnbClient) {
		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
	}
	if IsFeatureEnabledForActor(ctx, actorId, types.Feature_NEW_VPA_HANDLE, upiOnbClient) &&
		IsFeatureEnabledForActor(ctx, actorId, types.Feature_VPA_MIGRATION_INTRO_SCREEN, upiOnbClient) {

		if isUnifiedTpapFlowEnabledForUser(ctx, feDynamicConf.TpapUnifiedFlowReleaseVersions()) {
			return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
				UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
			})
		}
		return createVpaMigrationIntroScreenDeeplink(vpa, feDynamicConf.VpaMigrationScreenParams().NewVpaHandle, feDynamicConf.VpaMigrationScreenParams().VpaMigrationIntroScreenParams)
	}

	return nil, nil
}

func isUnifiedTpapFlowEnabledForUser(ctx context.Context, unifiedFlowReleaseVersions *genconf.TpapUnifiedFlowReleaseVersions) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)

	if platform == commontypes.Platform_ANDROID {
		return version >= unifiedFlowReleaseVersions.MinAndroidVersion()
	}
	return version >= unifiedFlowReleaseVersions.MinIOSVersion()
}

// createVpaMigrationIntroScreenDeeplink - generates the deeplink for the vpa migration introduction screen
func createVpaMigrationIntroScreenDeeplink(vpa, newVpaHandle string, vpaMigrationIntroConf *config.VpaMigrationIntroScreenParams) (*deeplinkPb.Deeplink, error) {
	newVpa, err := GetVpaWithHandle(vpa, newVpaHandle)
	if err != nil {
		return nil, fmt.Errorf("error while generating new vpa from old vpa : %w", err)
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_VPA_MIGRATION_INTRO_SCREEN, &upiScreenOptions.VpaMigrationIntroScreenOptions{
		Icon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: vpaMigrationIntroConf.Icon.Url,
					},
					Properties: &commontypes.VisualElementProperties{
						Height: vpaMigrationIntroConf.Icon.Height,
						Width:  vpaMigrationIntroConf.Icon.Width,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			vpaMigrationIntroConf.Title.DisplayValue,
			vpaMigrationIntroConf.Title.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[vpaMigrationIntroConf.Title.FontStyle]),
		),
		Description: commontypes.GetTextFromStringFontColourFontStyle(
			fmt.Sprintf(vpaMigrationIntroConf.Description.DisplayValue, newVpa),
			vpaMigrationIntroConf.Description.FontColour,
			(commontypes.FontStyle)(commontypes.FontStyle_value[vpaMigrationIntroConf.Description.FontStyle]),
		),
		Ctas: []*deeplinkPb.Cta{
			{
				Type:         (deeplinkPb.Cta_Type)(deeplinkPb.Cta_Type_value[vpaMigrationIntroConf.Ctas[0].Type]),
				Text:         vpaMigrationIntroConf.Ctas[0].Text,
				DisplayTheme: (deeplinkPb.Cta_DisplayTheme)(deeplinkPb.Cta_DisplayTheme_value[vpaMigrationIntroConf.Ctas[0].DisplayTheme]),
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_VPA_MIGRATION,
				},
			},
		},
	})
}

// GetVpaWithHandle: generates the vpa with the given vpa handle
func GetVpaWithHandle(vpa, vpaHandle string) (string, error) {
	vpaName, err := upiPkg.GetVpaNameFromVpa(vpa)
	if err != nil {
		return "", fmt.Errorf("error while fetching vpa name from vpa = %v, err = %w", vpa, err)
	}

	return strings.Join([]string{vpaName, vpaHandle}, upiPkg.VPADelimiter), nil
}

// GetVpaAndDerivedAccIdForPrimaryAccount : fetches the vpa for the primary account of the actor
// returns vpa, derivedAccId, vpaState, err
func GetVpaAndDerivedAccIdForPrimaryAccount(
	ctx context.Context,
	actorId string,
	upiOnboardingClient upiOnbPb.UpiOnboardingClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	savingsClient savingsPb.SavingsClient) (string, string, pb.VpaState, error) {
	var (
		vpa          string
		derivedAccId string
		vpaState     pb.VpaState
		err          error
	)

	getAccountsRes, err := upiOnboardingClient.GetAccounts(ctx, &upiOnbPb.GetAccountsRequest{
		ActorId: actorId,
		AccountStatus: []upiOnbEnumsPb.UpiAccountStatus{
			upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
			upiOnbEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
		},
	})

	if err = epifigrpc.RPCError(getAccountsRes, err); err != nil && !getAccountsRes.GetStatus().IsRecordNotFound() {
		return "", "", pb.VpaState_VPA_STATE_UNSPECIFIED, fmt.Errorf("error whle fetching upi accounts for the actor-id = %v, err = %w", actorId, err)
	}

	for _, upiAccount := range getAccountsRes.GetAccounts() {
		if upiAccount.GetAccountPreference() == upiOnbEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY {
			// if account is internal Fi account, account ref id needs to be passed
			if upiAccount.IsInternal() {
				vpa, vpaState, _ = GetVPA(ctx, upiAccount.GetAccountRefId(), upiAccount.GetMaskedAccountNumber(), upiAccount.GetAccountType(), accountPiClient)
				derivedAccId, err = GetDerivedAccountIdFromInternalAndTpapAccId(upiAccount.GetAccountRefId(), upiAccount.GetId())
			} else {
				vpa, vpaState, _ = GetVPA(ctx, upiAccount.GetId(), upiAccount.GetMaskedAccountNumber(), upiAccount.GetAccountType(), accountPiClient)
				derivedAccId, err = GetDerivedAccountIdFromTpapAccountId(upiAccount.GetId())
			}
			if err != nil {
				logger.Error(ctx, "error in getting derived account Id for AccountId: ", zap.String(logger.ACCOUNT_ID, upiAccount.GetId()))
			}
			return vpa, derivedAccId, vpaState, err
		}
	}

	// No primary tpap account found for the actor
	// so, we'll send savings account vpa
	getSavingsAccountEssentialsRes, err := savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return "", "", pb.VpaState_VPA_STATE_UNSPECIFIED, err
	case getSavingsAccountEssentialsRes.GetStatus().IsRecordNotFound():
		return "", "", pb.VpaState_VPA_STATE_UNSPECIFIED, nil

	case !getSavingsAccountEssentialsRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success code while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return "", "", pb.VpaState_VPA_STATE_UNSPECIFIED, rpcPb.StatusAsError(getSavingsAccountEssentialsRes.GetStatus())
	}
	vpa, vpaState, _ = GetVPA(ctx, getSavingsAccountEssentialsRes.GetAccount().GetId(), getSavingsAccountEssentialsRes.GetAccount().GetAccountNo(), accounts.Type_SAVINGS, accountPiClient)
	derivedAccId, err = GetDerivedAccountIdFromInternalAccountId(getSavingsAccountEssentialsRes.GetAccount().GetId())
	return vpa, derivedAccId, vpaState, err
}

// GetVPAForActor fetches the Virtual Payment Address (VPA) for an actor.
// It prioritizes the retrieval of VPA for different types of accounts in the following order:
// Savings Account
// For Non SA (savings accounts) user, it will fetch the primary account of the user.
func GetVPAForActor(
	ctx context.Context,
	actorId string,
	upiOnboardingClient upiOnbPb.UpiOnboardingClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	savingsClient savingsPb.SavingsClient) (string, pb.VpaState, error) {
	var (
		vpa      string
		vpaState pb.VpaState
		err      error
	)

	getSavingsAccountEssentialsRes, err := savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return "", pb.VpaState_VPA_STATE_UNSPECIFIED, err
	case getSavingsAccountEssentialsRes.GetStatus().IsRecordNotFound():
		vpa, _, vpaState, err = GetVpaAndDerivedAccIdForPrimaryAccount(ctx, actorId, upiOnboardingClient, accountPiClient, savingsClient)
		return vpa, vpaState, err

	case !getSavingsAccountEssentialsRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success code while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return "", pb.VpaState_VPA_STATE_UNSPECIFIED, rpcPb.StatusAsError(getSavingsAccountEssentialsRes.GetStatus())
	}
	return GetVPA(ctx, getSavingsAccountEssentialsRes.GetAccount().GetId(), getSavingsAccountEssentialsRes.GetAccount().GetAccountNo(), accounts.Type_SAVINGS, accountPiClient)
}

// GetVPA fetches UPI ID from payment instruments
func GetVPA(ctx context.Context, accountId, accountNo string, accountType accounts.Type, accountPiClient accountPiPb.AccountPIRelationClient) (string, pb.VpaState, error) {
	// Get PIs for the Account
	pis, err := accountPiClient.GetPiByAccountId(ctx, &accountPiPb.GetPiByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
		PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
		PiStates:    []piPb.PaymentInstrumentState{piPb.PaymentInstrumentState_CREATED, piPb.PaymentInstrumentState_VERIFIED, piPb.PaymentInstrumentState_SUSPENDED},
	})
	if err = epifigrpc.RPCError(pis, err); err != nil {
		return "", pb.VpaState_VPA_STATE_UNSPECIFIED, err
	}

	if len(pis.GetPaymentInstruments()) == 0 {
		logger.Info(ctx, "VPA does not exist for the account", zap.String(logger.ACCOUNT_ID, accountId))
		return "", pb.VpaState_UNCREATED, nil
	}

	var upiPI *piPb.PaymentInstrument
	for _, pi := range pis.GetPaymentInstruments() {
		if !pi.IsMandateVPA() {
			upiPI = pi
			break
		}
	}

	upiVpa, err := upiPI.GetVPA()
	if err != nil {
		logger.Error(ctx, "error in pi.GetVPA", zap.Error(err),
			zap.String(logger.ACCOUNT_ID, accountId),
			zap.String(logger.ACCOUNT_NUMBER, accountNo),
			zap.String("GetPi Response", pis.String()))
	}

	vpaState := pb.VpaState_DISABLED
	if upiPI.IsActive() {
		vpaState = pb.VpaState_ENABLED
	}

	return upiVpa, vpaState, err
}

// GetDeeplinkToConnectTpapAccounts returns connect Tpap accounts deeplink
func GetDeeplinkToConnectTpapAccounts(ctx context.Context, actorId string, conf *config.ConnectTpapBankAccountIntroScreenParams, actorClient actorPb.ActorClient) (*deeplinkPb.Deeplink, error) {
	phoneNumber, err := getMaskedPhoneNumberForActor(ctx, actorId, actorClient)
	if err != nil {
		return nil, fmt.Errorf("error while fetching phone number for actor-id = %v, err = %w", actorId, err)
	}

	deeplinkToConnectBankAccounts, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
		UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
	})

	connectBankAccountsScreenOptions, _ := deeplinkv3.GetScreenOptionV2(&upiScreenOptions.ConnectBankAccountsIntroScreenOptions{
		Icon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: conf.Icon.Url,
					},
					Properties: &commontypes.VisualElementProperties{
						Height: conf.Icon.Height,
					},
				},
			},
		},
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			conf.Title.DisplayValue,
			conf.Title.FontColour,
			commontypes.FontStyle(commontypes.FontStyle_value[conf.Title.FontStyle])),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle(
			fmt.Sprintf(conf.Subtitle.DisplayValue, phoneNumber),
			conf.Subtitle.FontColour,
			commontypes.FontStyle(commontypes.FontStyle_value[conf.Subtitle.FontStyle])),

		Cta: &deeplinkPb.Cta{
			Type:         (deeplinkPb.Cta_Type)(deeplinkPb.Cta_Type_value[conf.Cta.Type]),
			Text:         conf.Cta.Text,
			DisplayTheme: (deeplinkPb.Cta_DisplayTheme)(deeplinkPb.Cta_DisplayTheme_value[conf.Cta.DisplayTheme]),
			Deeplink:     deeplinkToConnectBankAccounts,
		},
		TermsAndConditions: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: fmt.Sprintf(conf.Tnc, conf.TncContentFontColor, conf.TncHyperlinkFontColor, conf.TncUrl),
			},
			FontColor: conf.TncContentFontColor,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_4_PARA,
			},
		},
	})
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_CONNECT_BANK_ACCOUNTS_INTRO_SCREEN,
		ScreenOptionsV2: connectBankAccountsScreenOptions,
	}, nil
}

// getMaskedPhoneNumberForActor returns actors masked phone number
func getMaskedPhoneNumberForActor(ctx context.Context, actorId string, actorClient actorPb.ActorClient) (string, error) {
	entityRes, err := actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		return "", fmt.Errorf("failed to get actor entity details %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	return mask.GetMaskedPhoneNumber(entityRes.GetMobileNumber(), "•"), nil
}

// GetDeeplinkToConnectRupayAccount returns connect credit card on Upi [RupayCC] deeplink
func GetDeeplinkToConnectRupayAccount(ctx context.Context, actorId string) (*deeplinkPb.Deeplink, error) {
	logger.Debug(ctx, "getting deeplink to connect rupay account for actorId: ", zap.String(logger.ACTOR_ID_V2, actorId))
	deeplinkToConnectRupayAccounts, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
		UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_CREDIT_CARD,
	})
	return deeplinkToConnectRupayAccounts, nil
}

// IsEligibleForMapperQuickLinkBanner - Helper method to check if UPI Mapper Quick Link banner should be shown or not.
func IsEligibleForMapperQuickLinkBanner(ctx context.Context, upiOnboardingClient upiOnbPb.UpiOnboardingClient, actorClient actorPb.ActorClient, actorId string) bool {

	phoneNumber, err := getPhoneNumberForActor(ctx, actorId, actorClient)
	if err != nil {
		logger.Error(ctx, "failed to fetch phone number for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	upiNumberPiMapping, err := upiOnboardingClient.GetUpiNumberPiMapping(ctx, &upiOnbPb.GetUpiNumberPiMappingRequest{
		UpiNumber: phoneNumber.ToStringNationalNumber(),
	})
	te := epifigrpc.RPCError(upiNumberPiMapping, err)
	if te != nil {
		if upiNumberPiMapping.GetStatus().IsRecordNotFound() {
			// Handle record not found case
			return true
		}
		logger.Error(ctx, "error while fetching upi number pi mapping", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		return false
	}
	// If the mapping exists, then the banner should not be shown
	return false
}

// getPhoneNumberForActor returns actors phone number
func getPhoneNumberForActor(ctx context.Context, actorId string, actorClient actorPb.ActorClient) (*commontypes.PhoneNumber, error) {
	entityRes, err := actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		return nil, fmt.Errorf("failed to get actor entity details %v %w", err, rpcPb.StatusAsError(rpcPb.StatusInternal()))
	}
	return entityRes.GetMobileNumber(), nil
}
