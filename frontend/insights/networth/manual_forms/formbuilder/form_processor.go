package formbuilder

import (
	"context"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2"
)

type FormProcessor interface {
	// BuildForm creates a form with the input components. The form can be an empty form or a pre-filled form.
	BuildForm(ctx context.Context, req *BuildFormRequest) (*networthFePb.NetWorthManualForm, error)
	// SubmitForm validates and stores the form data
	SubmitForm(ctx context.Context, req *SubmitFormRequest) error
	// BuildEmptyForm creates an empty form with the input components for the given form identifier
	BuildEmptyForm(ctx context.Context, req *BuildFormRequest) (*networthFePb.NetWorthManualForm, error)
}

type BuildFormRequest struct {
	ActorId        string
	FormIdentifier *typesv2.ManualAssetFormIdentifier
	// This field will be used by MagicImport for BuildForm
	InvestmentDeclaration *model.InvestmentDeclaration
}

type BuildConsentCheckboxItemsRequest struct {
	ActorId        string
	FormIdentifier *typesv2.ManualAssetFormIdentifier
}

type SubmitFormRequest struct {
	ActorId         string
	FormIdentifier  *typesv2.ManualAssetFormIdentifier
	InputComponents []*networthFePb.NetWorthManualFormInputComponent
	FormData        []*networthFePb.NetWorthManualInputData
}
