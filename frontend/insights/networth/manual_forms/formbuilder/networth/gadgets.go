package networth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/money"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	networthBeModelPb "github.com/epifi/gamma/api/insights/networth/model"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputbuilder"
)

type Gadgets struct {
}

func NewGadgets() *Gadgets {
	return &Gadgets{}
}

// nolint: dupl
func (a *Gadgets) BuildForm(ctx context.Context, req *networthBeFePb.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	decl := req.GetInvestmentDeclaration().GetDeclarationDetails().GetGadgets()
	deviceType := inputbuilder.NewStringBuilder("DEVICE TYPE", "Device type", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_DEVICE_TYPE.String())
	name := inputbuilder.NewStringBuilder("INVESTMENT NAME", "Investment name", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME.String())
	yearOfPurchase := inputbuilder.NewDateBuilder("YEAR OF PURCHASE", "Year of Purchase", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE.String())
	condition := inputbuilder.NewStringBuilder("CONDITION", "Condition", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_CONDITION.String())
	currentValue := inputbuilder.NewInt64Builder("CURRENT VALUE", "Current value", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE.String())
	if decl != nil {
		name.WithValue(decl.GetInvestmentName())
		deviceType.WithValue(decl.GetDeviceType())
		yearOfPurchase.WithValue(decl.GetYearOfPurchase())
		condition.WithValue(decl.GetCondition())
		currentValue.WithValue(decl.GetCurrentValue().GetUnits())
	}

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("Investment Details").
		WithInputComponent(name.Build()).
		WithInputComponent(deviceType.Build()).
		WithInputComponent(yearOfPurchase.Build()).
		WithInputComponent(condition.Build()).
		WithInputComponent(currentValue.Build())

	form := networthFePb.NewNetWorthManualForm("Gadgets", "Add Investment").
		WithComponentsSection(inputSection)
	// If the data has already been entered by the user, display the option to delete it.
	if decl != nil {
		form = form.WithActionCta(networthFePb.NewAssetActionButton().
			WithActionType(networthFePb.AssetActionType_ASSET_ACTION_TYPE_DELETE).
			WithDisplayText(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Remove", "#AA301F", commontypes.FontStyle_BUTTON_S)).
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/networth/delete_icon_manual_asset.png", 13, 13)).
				WithLeftImagePadding(8)))
	}
	return form, nil
}

func (a *Gadgets) ConvertFormInputToInvestmentDeclaration(ctx context.Context, inputComponents []*networthFePb.NetWorthManualFormInputComponent) (*networthBeModelPb.InvestmentDeclaration, error) {
	details := &networthBeModelPb.Gadgets{}
	decl := &networthBeModelPb.InvestmentDeclaration{
		InstrumentType: typesPb.InvestmentInstrumentType_GADGETS,
		DeclarationDetails: &networthBeModelPb.OtherDeclarationDetails{
			Details: &networthBeModelPb.OtherDeclarationDetails_Gadgets{
				Gadgets: details,
			},
		},
	}

	for _, component := range inputComponents {
		data := component.GetInputData()
		inputValue := data.GetInputValueFromSingleOption()
		fieldName := goUtils.Enum(data.GetFieldName(), networthFePb.NetworthManualFormFieldName_value, networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED)
		switch fieldName {
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_DEVICE_TYPE:
			details.DeviceType = inputValue.GetStringData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME:
			details.InvestmentName = inputValue.GetStringData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE:
			investmentDate := inputValue.GetDateData().GetData()
			investmentTime := timestampPb.New(time.Date(int(investmentDate.GetYear()), time.Month(investmentDate.GetMonth()), int(investmentDate.GetDay()), 0, 0, 0, 0, datetime.IST))
			details.YearOfPurchase = investmentDate
			decl.InvestedAt = investmentTime
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_CONDITION:
			details.Condition = inputValue.GetStringData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE:
			details.CurrentValue = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		}
	}

	return decl, nil
}
