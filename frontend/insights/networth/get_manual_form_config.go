package networth

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
)

func (s *Service) GetManualFormConfig(ctx context.Context, req *networthFePb.GetManualFormConfigRequest) (*networthFePb.GetManualFormConfigResponse, error) {
	form, err := s.formProcessor.BuildForm(ctx, &formbuilder.BuildFormRequest{
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		FormIdentifier: req.GetFormIdentifier(),
	})
	if err != nil {
		logger.Error(ctx, "failed to build form", zap.Error(err))
		return &networthFePb.GetManualFormConfigResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}

	return &networthFePb.GetManualFormConfigResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		FormResponse: form,
	}, nil
}
