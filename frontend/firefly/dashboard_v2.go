package firefly

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	cmap "github.com/orcaman/concurrent-map"
	"github.com/pkg/errors"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"go.uber.org/zap"

	beCasperPb "github.com/epifi/gamma/api/casper"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	feFireflyPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	uitypes "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
)

const (
	// figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5808&t=0CR8KdcKOUOtlRGq-4
	dashboardV2MiddleSectionContactSection = "ContactSection"
	// figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5860&t=0CR8KdcKOUOtlRGq-4
	dashboardV2MiddleSectionOffersSection = "OffersSection"
	// fimga: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5976&t=0CR8KdcKOUOtlRGq-4
	dashboardV2MiddleSectionFiCoinsOffersSection = "FiCoinsOffersSection"
	// figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5931&t=xRfPAm2Wy5hbGh1K-4
	dashboardV2MiddleSectionRewardsSection = "RewardsSection"
)

var (
	dashboardV2MiddleSectionsOrderedList = []string{
		dashboardV2MiddleSectionContactSection,
		dashboardV2MiddleSectionOffersSection,
		dashboardV2MiddleSectionFiCoinsOffersSection,
		dashboardV2MiddleSectionRewardsSection,
	}
)

// RewardCardDataObject holds the data for creating a RewardCard
type RewardCardDataObject struct {
	TopeLeftNudge          string
	CardBackgroundImageUrl string
	CardIconUrl            string
	Description            string
	Deeplink               *dlPb.Deeplink
}

// RewardHistoryItemData holds the data for creating a RewardHistoryItemInput
type RewardHistoryItemData struct {
	LeftIconUrl string
	Title       string
	Subtitle    string
	RightLabel  string
	Deeplink    *dlPb.Deeplink
}

func (s *Service) GetDashboardV2(ctx context.Context, req *feFireflyPb.GetDashboardV2Request) (*feFireflyPb.GetDashboardV2Response, error) {
	var (
		res = &feFireflyPb.GetDashboardV2Response{}
	)
	getCCResp, err := s.fireflyV2Client.GetCreditCards(ctx, &ffBeV2Pb.GetCreditCardsRequest{
		Identifier: &ffBeV2Pb.GetCreditCardsRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
	})
	if te := epifigrpc.RPCError(getCCResp, err); te != nil {
		logger.Error(ctx, "error while getting credit cards", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(te))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusFromErrorWithDefaultInternal(te),
		}
	}

	// 1. build top section UI component
	// figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5788&t=0CR8KdcKOUOtlRGq-4
	res.CardSection = s.buildV2CardSection(getCCResp.GetCreditCards()[0])

	// 2. build middle section UI components
	grp, grpCtx := errgroup.WithContext(ctx)
	dashboardV2MiddleSectionsMap := cmap.New()

	for _, middleSectionName := range dashboardV2MiddleSectionsOrderedList {
		middleSectionName := middleSectionName
		grp.Go(func() error {
			section, buildErr := s.buildDashboardV2MiddleSection(grpCtx, getCCResp.GetCreditCards()[0], middleSectionName)
			if buildErr != nil {
				logger.Error(grpCtx, "error while building dashboardV2MiddleSection",
					zap.String("CCDashboardV2MiddleSection", middleSectionName), zap.Error(buildErr))
				return nil
			}
			if section != nil {
				dashboardV2MiddleSectionsMap.Set(middleSectionName, section)
			}
			return nil
		})
	}

	if waitErr := grp.Wait(); waitErr != nil {
		logger.Error(ctx, "error while building credit card dashboard v2 middle sections", zap.Error(waitErr),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.CARD_ID, getCCResp.GetCreditCards()[0].GetId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}

	for _, dashboardV2MiddleSectionName := range dashboardV2MiddleSectionsOrderedList {
		middleSectionUiComponent, found := dashboardV2MiddleSectionsMap.Get(dashboardV2MiddleSectionName)
		if !found {
			continue
		}
		middleSectionUiComponentProto, ok := middleSectionUiComponent.(*feFireflyPb.GetDashboardV2Response_DashboardSection)
		if !ok {
			logger.Error(ctx, "error converting CC dashboardV2 section interface to proto", zap.String("CCDashboardV2MiddleSection", dashboardV2MiddleSectionName))
			continue
		}
		res.DashboardSections = append(res.DashboardSections, middleSectionUiComponentProto)
	}

	// 3. build bottom section UI components
	// fimga: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-6015&t=0CR8KdcKOUOtlRGq-4
	res.BottomInfoSection = s.buildV2BottomSection(getCCResp.GetCreditCards()[0])
	res.PageBackground = commontypes.GetVisualElementFromUrlHeightAndWidth(
		maginifiBgImage,
		572,
		412,
	)

	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) buildDashboardV2MiddleSection(ctx context.Context, savedCard *ffBeV2Pb.CreditCard, sectionName string) (*feFireflyPb.GetDashboardV2Response_DashboardSection, error) {
	switch sectionName {
	case dashboardV2MiddleSectionContactSection:
		return s.buildV2ContactSection(ctx, savedCard)
	case dashboardV2MiddleSectionOffersSection:
		return s.buildV2OffersSection(ctx, savedCard)
	case dashboardV2MiddleSectionFiCoinsOffersSection:
		return s.buildV2FiCoinsOffersSection(ctx, savedCard)
	case dashboardV2MiddleSectionRewardsSection:
		return s.buildV2RewardsSection(ctx, savedCard)
	default:
		return nil, fmt.Errorf("dashboard section name %s not supported", sectionName)
	}
}

func (s *Service) buildV2RewardsSection(ctx context.Context, savedCard *ffBeV2Pb.CreditCard) (*feFireflyPb.GetDashboardV2Response_DashboardSection, error) {

	totalFiCoinsAggregates, monthlyFiCoinsAggregates, err := s.getCreditCardRewardsAggregates(ctx, savedCard)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting credit card reward aggregates")
	}

	// TODO(BE): replace all dummy data with actual data once ready.
	logger.Info(ctx, "cards reward aggregates", zap.Float64("totalFiCoinsAggregates", totalFiCoinsAggregates), zap.Float64("monthlyFiCoinsAggregates", monthlyFiCoinsAggregates))
	return &feFireflyPb.GetDashboardV2Response_DashboardSection{}, nil

	/*// Dummy data for testing
	bannerTitle := "EARNINGS ON MAGNIFI"
	bannerValue := "12000"

	// Dummy data for demonstration
	rewardCardsData := []RewardCardDataObject{
		{
			TopeLeftNudge:          "BENEFITS",
			CardBackgroundImageUrl: "https://epifi-icons.pointz.in/reward_cc_card_sky",
			CardIconUrl:            "https://epifi-icons.pointz.in/reward_cc_airoplane_icon",
			Description:            "Unlock Lounge benefits on MagniFi",
		},
		{
			TopeLeftNudge:          "REWARDS",
			CardBackgroundImageUrl: "https://epifi-icons.pointz.in/reward_cc_card_sky",
			CardIconUrl:            "https://epifi-icons.pointz.in/reward_cc_airoplane_icon",
			Description:            "Earn rewards on every spend",
		},
		{
			TopeLeftNudge:          "OFFERS",
			CardBackgroundImageUrl: "https://epifi-icons.pointz.in/reward_cc_card_sky",
			CardIconUrl:            "https://epifi-icons.pointz.in/reward_cc_airoplane_icon",
			Description:            "Exclusive offers just for you",
		},
	}

	// Dummy reward history items
	historyItemsData := []RewardHistoryItemData{
		{
			LeftIconUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/ficoin-2.svg",
			Title:       "Dummy Title 1",
			Subtitle:    "100",
			RightLabel:  "View",
		},
		{
			LeftIconUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/ficoin-2.svg",
			Title:       "Dummy Title 2",
			Subtitle:    "200",
		},
		{
			LeftIconUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/ficoin-2.svg",
			Title:       "Dummy Title 3",
			Subtitle:    "300",
		},
	}

	return &feFireflyPb.GetDashboardV2Response_DashboardSection{
		Section: &feFireflyPb.GetDashboardV2Response_DashboardSection_RewardsSection{
			RewardsSection: &feFireflyPb.RewardsSection{
				Section: getRewardsSection(getTotalRewardsEarningBanner(bannerTitle, bannerValue),
					getRewardCards(rewardCardsData),
					getRewardHistoryListInput(true, historyItemsData),
				),
			},
		},
	}, nil*/
}

// nolint
func getRewardCards(cards []RewardCardDataObject) []*RewardCard {
	rewardCards := make([]*RewardCard, 0, 3)
	for _, data := range cards {
		rewardCards = append(rewardCards, getRewardCardUIObject(data))
	}
	return rewardCards
}

// nolint
func getRewardCardUIObject(data RewardCardDataObject) *RewardCard {
	return &RewardCard{
		BackgroundImage: commontypes.GetVisualElementImageFromUrl(data.CardBackgroundImageUrl).WithProperties(&commontypes.VisualElementProperties{
			Width:  184,
			Height: 152,
		}),
		TopLeftTag: &uitypes.IconTextComponent{
			ContainerProperties: &uitypes.IconTextComponent_ContainerProperties{
				CornerRadius:     19,
				LeftPadding:      6,
				RightPadding:     6,
				TopPadding:       2,
				BottomPadding:    2,
				BackgroundColour: widget.GetBlockBackgroundColour("#FFFFFF"),
			},
			Texts: []*commontypes.Text{{
				FontColor:    "#6A6D70",
				DisplayValue: &commontypes.Text_PlainString{PlainString: data.TopeLeftNudge},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_3XS_CAPS},
				Alignment:    commontypes.Text_ALIGNMENT_CENTER,
			}},
		},
		CardIcon: commontypes.GetVisualElementImageFromUrl(data.CardIconUrl).WithProperties(&commontypes.VisualElementProperties{
			Width:  45,
			Height: 45,
		}),
		DescriptionLabel: commontypes.GetTextFromStringFontColourFontStyle(data.Description, "#313234", commontypes.FontStyle_SUBTITLE_S).WithAlignment(commontypes.Text_ALIGNMENT_LEFT).WithMaxLines(2),
	}
}

// nolint
func getTotalRewardsEarningBanner(title string, value string) *TotalRewardsEarningBanner {
	return &TotalRewardsEarningBanner{
		Title: &uitypes.VerticalKeyValuePair{
			VerticalPaddingBtwTitleValue: 6,
			Title: &uitypes.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_PlainString{PlainString: title},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_XS},
						Alignment:    commontypes.Text_ALIGNMENT_LEFT,
					},
				},
			},
			Value: &uitypes.IconTextComponent{
				LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/ficoin-2.svg").WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
				LeftImgTxtPadding: 6,
				Texts: []*commontypes.Text{
					{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_PlainString{PlainString: value},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_XS},
						Alignment:    commontypes.Text_ALIGNMENT_LEFT,
					},
				},
			},
			HAlignment: uitypes.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
		},
	}
}

// nolint
func getRewardHistoryListInput(showActionButton bool, items []RewardHistoryItemData) *RewardsHistoryListInput {
	return &RewardsHistoryListInput{
		ShowActionButton: showActionButton,
		Items:            getRewardHistoryItems(items),
	}
}

// nolint
func getRewardHistoryItemView(data RewardHistoryItemData) *RewardHistoryItemInput {
	item := &RewardHistoryItemInput{
		VisualElement: commontypes.GetVisualElementImageFromUrl(data.LeftIconUrl).WithProperties(&commontypes.VisualElementProperties{
			Width:  32,
			Height: 32,
		}),
		VerticalKeyValue: &uitypes.VerticalKeyValuePair{
			HAlignment:                   uitypes.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
			VerticalPaddingBtwTitleValue: 2,
			Title: &uitypes.IconTextComponent{
				Texts: []*commontypes.Text{{
					FontColor:    "#6A6D70",
					DisplayValue: &commontypes.Text_PlainString{PlainString: data.Title},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					Alignment:    commontypes.Text_ALIGNMENT_LEFT,
				}},
			},
			Value: &uitypes.IconTextComponent{
				Texts: []*commontypes.Text{{
					FontColor:    "#313234",
					DisplayValue: &commontypes.Text_PlainString{PlainString: data.Subtitle},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_M},
					Alignment:    commontypes.Text_ALIGNMENT_LEFT,
				}},
			},
		},
	}
	if data.RightLabel != "" {
		item.ActionIconTextComp = &uitypes.IconTextComponent{
			ContainerProperties: &uitypes.IconTextComponent_ContainerProperties{
				CornerRadius:     40,
				LeftPadding:      12,
				RightPadding:     12,
				TopPadding:       10,
				BottomPadding:    10,
				BackgroundColour: widget.GetBlockBackgroundColour("#F6F9FD"),
			},
			Texts: []*commontypes.Text{{
				FontColor:    "#00B899",
				DisplayValue: &commontypes.Text_PlainString{PlainString: data.RightLabel},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_XS},
			}},
			RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/investment/right_chevron_sip_renew.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  20,
				Height: 20,
			}),
		}
	}
	return item
}

// nolint
func getRewardHistoryItems(items []RewardHistoryItemData) []*RewardHistoryItemInput {
	result := make([]*RewardHistoryItemInput, 0, len(items))
	for _, data := range items {
		result = append(result, getRewardHistoryItemView(data))
	}
	return result
}

func (s *Service) getCreditCardRewardsAggregates(ctx context.Context, savedCard *ffBeV2Pb.CreditCard) (float64, float64, error) {
	totalAggregatesResp, err := s.rewardAggrClient.GetRewardsAggregates(ctx, &rewardsPinotPb.GetRewardsAggregatesRequest{
		ActorId: savedCard.GetActorId(),
		Filters: &rewardsPinotPb.Filters{
			RewardType: rewardsPb.RewardType_FI_COINS,
			IncludeActionTypes: []rewardsPb.CollectedDataType{
				rewardsPb.CollectedDataType_VENDOR_REWARD_FULFILLMENT_EVENT,
			},
			RewardStatuses: []rewardsPb.RewardStatus{
				rewardsPb.RewardStatus_PROCESSED,
			},
		},
		TimeRange: &rewardsPinotPb.TimeRangeFilter{
			Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
			Range: &rewardsPinotPb.TimeRange{
				From: savedCard.GetCreatedAt(),
				To:   timestamp.Now(),
			},
		},
	})
	if te := epifigrpc.RPCError(totalAggregatesResp, err); te != nil {
		return 0, 0, errors.Wrap(te, "error while fetching total CC reward aggregates")
	}

	monthlyAggregatesResp, err := s.rewardAggrClient.GetRewardsAggregates(ctx, &rewardsPinotPb.GetRewardsAggregatesRequest{
		ActorId: savedCard.GetActorId(),
		Filters: &rewardsPinotPb.Filters{
			RewardType: rewardsPb.RewardType_FI_COINS,
			IncludeActionTypes: []rewardsPb.CollectedDataType{
				rewardsPb.CollectedDataType_VENDOR_REWARD_FULFILLMENT_EVENT,
			},
			RewardStatuses: []rewardsPb.RewardStatus{
				rewardsPb.RewardStatus_PROCESSED,
			},
		},
		TimeRange: &rewardsPinotPb.TimeRangeFilter{
			Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
			Range: &rewardsPinotPb.TimeRange{
				From: timestamp.New(dateTimePkg.StartOfMonth(time.Now())),
				To:   timestamp.Now(),
			},
		},
	})
	if te := epifigrpc.RPCError(monthlyAggregatesResp, err); te != nil {
		return 0, 0, errors.Wrap(te, "error while fetching monthly CC reward aggregates")
	}

	var (
		totalEarnedFiCoins, monthlyEarnedFiCoins float64
	)

	for _, rewardsData := range totalAggregatesResp.GetRewardOptionAggregates() {
		if rewardsData.GetRewardType() == rewardsPb.RewardType_FI_COINS {
			totalEarnedFiCoins = rewardsData.GetRewardUnits()
			break
		}
	}

	for _, rewardsData := range monthlyAggregatesResp.GetRewardOptionAggregates() {
		if rewardsData.GetRewardType() == rewardsPb.RewardType_FI_COINS {
			monthlyEarnedFiCoins = rewardsData.GetRewardUnits()
			break
		}
	}

	return totalEarnedFiCoins, monthlyEarnedFiCoins, nil
}

func (s *Service) buildV2CardSection(savedCard *ffBeV2Pb.CreditCard) *feFireflyPb.CardSection {
	return &feFireflyPb.CardSection{
		CardId: savedCard.GetId(),
		CardImage: commontypes.GetVisualElementFromUrlHeightAndWidth(
			internal.DashboardV2CardImageUrl, 180, 320),
		ViewCard: uitypes.NewITC().WithLeftVisualElement(
			commontypes.GetVisualElementFromUrlHeightAndWidth(internal.DashboardV2SmallCardWhiteIconUrl, 24, 24),
		).
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
				internal.DashboardV2ViewManageCreditCardText,
				colors.ColorSnow,
				commontypes.FontStyle_BUTTON_M,
			)).
			WithContainerProperties(&uitypes.IconTextComponent_ContainerProperties{
				CornerRadius:  internal.DashboardV2CardCornerRadius,
				LeftPadding:   internal.DashboardV2CardLeftPadding,
				RightPadding:  internal.DashboardV2CardRightPadding,
				TopPadding:    internal.DashboardV2CardTopPadding,
				BottomPadding: internal.DashboardV2CardBottomPadding,
				BorderWidth:   internal.DashboardV2CardBorderWidth,
				BackgroundColour: widget.GetRadialGradientBackgroundColor(&widget.CenterCoordinates{
					CenterX: 50,
					CenterY: -120,
				}, 100, []string{"#4DDCF3EE", "#0DDCF3EE"}),
				BgBorderColour: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
					{Color: "#4DDCF3EE", StopPercentage: 50},
					{Color: "#0DDCF3EE", StopPercentage: 100},
				}),
			}).
			WithLeftImagePadding(8).
			WithRightImagePadding(8).
			WithRightVisualElementUrlHeightAndWidth(internal.DashboardV2ChevronRightWhiteIconUrl,
				internal.DashboardV2ArrowIconSize, internal.DashboardV2ArrowIconSize).
			WithDeeplink(&dlPb.Deeplink{Screen: dlPb.Screen_CREDIT_CARD_SDK_SCREEN}),
		ViewCardInfo: commontypes.GetTextFromStringFontColourFontStyle(
			internal.DashboardV2CardSettingsInfoText,
			colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS,
		),
	}
}

func (s *Service) buildV2OffersSection(ctx context.Context, savedCard *ffBeV2Pb.CreditCard) (*feFireflyPb.GetDashboardV2Response_DashboardSection, error) {
	offersResp, err := s.rewardsListingClient.GetCardOffers(ctx, &beCasperPb.GetCardOffersRequest{
		RedemptionMode: beCasperPb.OfferRedemptionMode_FI_CREDIT_CARD,
		FiltersV2: &beCasperPb.CatalogFiltersV2{
			AndTags: []beCasperPb.TagName{
				cardProgramTypeToExclusiveTagNameMap[savedCard.GetCardProgram().GetCardProgramType()],
			},
		},
	})
	if te := epifigrpc.RPCError(offersResp, err); te != nil {
		return nil, errors.Wrap(te, "error while getting offers")
	}

	if len(offersResp.GetOffers()) == 0 {
		logger.Info(ctx, "no offers found for credit card program", zap.String(logger.CARD_PROGRAM, savedCard.GetCardProgram().GetCardProgramType().String()))
		return nil, nil
	}

	var (
		offersTabsData        []*components.Component
		totalValidOffersCount = 0
	)

	// Leading spacer for the first card
	offersTabsData = append(offersTabsData, &components.Component{
		Content: helper.GetAnyWithoutError(&components.Spacer{
			SpacingValue: components.Spacing_SPACING_S,
		}),
	})

	// build catalog offers widgets
	for _, offer := range offersResp.GetOffers() {
		leftMargin := offerWidgetCardDefaultLeftMargin
		// leftMargin is zero for the first card
		if len(offersTabsData) == 0 {
			leftMargin = 0
		}
		offersDetails := getGenericOfferCard(
			offer,
			colors.ColorSnow,
			nil,
			leftMargin)
		offersComponent := &components.Component{
			Content: helper.GetAnyWithoutError(offersDetails),
		}
		totalValidOffersCount++
		offersTabsData = append(offersTabsData, offersComponent)
	}

	// Trailing spacer for the last card
	offersTabsData = append(offersTabsData, &components.Component{
		Content: helper.GetAnyWithoutError(&components.Spacer{
			SpacingValue: components.Spacing_SPACING_S,
		}),
	})

	if totalValidOffersCount == 0 {
		logger.Info(ctx, "no valid offers found for credit card program", zap.String(logger.CARD_PROGRAM, savedCard.GetCardProgram().GetCardProgramType().String()))
		return nil, nil
	}
	// Use the helper function to create the offers section
	return &feFireflyPb.GetDashboardV2Response_DashboardSection{
		Section: &feFireflyPb.GetDashboardV2Response_DashboardSection_OffersSection{
			OffersSection: &feFireflyPb.OffersSection{
				Section: getGenericOffersSection(
					internal.DashboardV2ExclusiveMagniFiOffersText,
					colors.ColorDarkLayer2,
					internal.DashboardV2ViewAllText,
					colors.ColorOnDarkLowEmphasis,
					offersTabsData,
					&dlPb.Deeplink{
						Screen: dlPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
						ScreenOptions: &dlPb.Deeplink_CardOffersCatalogScreenOptions{
							CardOffersCatalogScreenOptions: &dlPb.CardOffersCatalogScreenOptions{
								CardType:   dlPb.CardOffersCatalogScreenOptions_CREDIT_CARD,
								CardTypeId: cardProgramTypeToCardTypeIdMap[savedCard.GetCardProgram().GetCardProgramType()].String(),
							},
						},
					},
				),
			},
		},
	}, nil
}

func (s *Service) buildV2ContactSection(ctx context.Context, savedCard *ffBeV2Pb.CreditCard) (*feFireflyPb.GetDashboardV2Response_DashboardSection, error) {
	trackingRequests, err := s.fireflyV2Client.GetCreditCardTrackingDetails(ctx, &ffBeV2Pb.GetCreditCardTrackingDetailsRequest{
		Identifier: &ffBeV2Pb.GetCreditCardTrackingDetailsRequest_ActorId{
			ActorId: savedCard.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(trackingRequests, err); te != nil && !rpc.StatusFromError(te).IsRecordNotFound() {
		return nil, errors.Wrap(te, "error while getting tracking details")
	}
	var carousels []*dlPb.InfoItemWithCta
	if !trackingRequests.GetStatus().IsRecordNotFound() && trackingRequests.GetTrackingDetails().GetDeliveryState() != ccEnumsV2Pb.CardTrackingDeliveryState_DELIVERED {
		carousels = append(carousels, &dlPb.InfoItemWithCta{
			Info: &dlPb.InfoItem{
				Icon: internal.DashboardV2DeliveryTruckIconUrl,
				Desc: internal.DashboardV2CardTrackingText,
			},
		})
	}
	return &feFireflyPb.GetDashboardV2Response_DashboardSection{
		Section: &feFireflyPb.GetDashboardV2Response_DashboardSection_ContactSection{
			ContactSection: &feFireflyPb.ContactSection{
				ContactFederalInfo: &uitypes.VisualElementTitleSubtitleItcElement{
					VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(
						internal.DashboardV2FederalHelpIconUrl, internal.DashboardV2FederalIconHeight, internal.DashboardV2FederalIconWidth),
					TitleText: uitypes.NewITC().WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle(
							internal.DashboardV2NeedHelpText, colors.ColorDarkLayer2, commontypes.FontStyle_SUBTITLE_S,
						),
					),
					SubtitleText: uitypes.NewITC().WithTexts(
						commontypes.GetTextFromStringFontColourFontStyle(
							internal.DashboardV2FederalHelpDescText, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS,
						),
						commontypes.GetTextFromStringFontColourFontStyle(
							internal.DashboardV2FederalBankText, colors.ColorDarkLayer2, commontypes.FontStyle_HEADLINE_XS,
						),
					),
					BackgroundColor: colors.ColorSnow,
				},
				Contacts: []*uitypes.IconTextComponent{
					uitypes.NewITC().
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
							internal.DashboardV2PhoneNumberText, internal.DarkGray, commontypes.FontStyle_SUBTITLE_S,
						)).
						WithLeftVisualElementUrlHeightAndWidth(
							internal.DashboardV2CallIconUrl, internal.DashboardV2ContactIconSize, internal.DashboardV2ContactIconSize,
						).WithLeftImagePadding(internal.DashboardV2ContactIconPadding).
						WithRightVisualElementUrlHeightAndWidth(
							internal.DashboardV2RightArrowIconUrl, internal.DashboardV2ArrowIconSize, internal.DashboardV2ArrowIconSize,
						).WithRightImagePadding(internal.DashboardV2ContactIconPadding).
						WithDeeplink(&dlPb.Deeplink{
							Screen: dlPb.Screen_EXTERNAL_REDIRECTION,
							ScreenOptions: &dlPb.Deeplink_ExternalRedirectionScreenOptions{
								ExternalRedirectionScreenOptions: &dlPb.ExternalRedirectionScreenOptions{
									ExternalUrl: internal.DashboardV2PhoneNumberUrl,
								},
							},
						}),
					uitypes.NewITC().
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
							internal.DashboardV2EmailAddressText, internal.DarkGray, commontypes.FontStyle_SUBTITLE_S,
						)).
						WithLeftVisualElementUrlHeightAndWidth(
							internal.DashboardV2EmailIconUrl, internal.DashboardV2ContactIconSize, internal.DashboardV2ContactIconSize,
						).WithLeftImagePadding(internal.DashboardV2ContactIconPadding).
						WithRightVisualElementUrlHeightAndWidth(
							internal.DashboardV2RightArrowIconUrl, internal.DashboardV2ArrowIconSize, internal.DashboardV2ArrowIconSize,
						).WithRightImagePadding(internal.DashboardV2ContactIconPadding).
						WithDeeplink(&dlPb.Deeplink{
							Screen: dlPb.Screen_EXTERNAL_REDIRECTION,
							ScreenOptions: &dlPb.Deeplink_ExternalRedirectionScreenOptions{
								ExternalRedirectionScreenOptions: &dlPb.ExternalRedirectionScreenOptions{
									ExternalUrl: internal.DashboardV2EmailAddressUrl,
								},
							},
						}),
				},
				Carousel: carousels,
			},
		},
	}, nil
}

func (s *Service) buildV2FiCoinsOffersSection(ctx context.Context, savedCard *ffBeV2Pb.CreditCard) (*feFireflyPb.GetDashboardV2Response_DashboardSection, error) {
	offersResp, err := s.rewardsListingClient.GetCardOffers(ctx, &beCasperPb.GetCardOffersRequest{
		RedemptionMode: beCasperPb.OfferRedemptionMode_FI_COINS,
	})
	if te := epifigrpc.RPCError(offersResp, err); te != nil {
		return nil, errors.Wrap(te, "error getting fi coins offers")
	}

	if len(offersResp.GetOffers()) == 0 {
		logger.Info(ctx, "no fi coins offers found for credit card program", zap.String(logger.CARD_PROGRAM, savedCard.GetCardProgram().GetCardProgramType().String()))
		return nil, nil
	}

	var (
		offersTabsData        []*components.Component
		totalValidOffersCount = 0
	)

	// Leading spacer for the first card
	offersTabsData = append(offersTabsData, &components.Component{
		Content: helper.GetAnyWithoutError(&components.Spacer{
			SpacingValue: components.Spacing_SPACING_S,
		}),
	})

	// build catalog offers widgets
	for _, offer := range offersResp.GetOffers() {
		leftMargin := offerWidgetCardDefaultLeftMargin
		// leftMargin is zero for the first card
		if len(offersTabsData) == 0 {
			leftMargin = 0
		}
		offersDetails := getGenericFiCoinsExchangeOfferCard(
			offer,
			colors.ColorSnow,
			nil,
			leftMargin)
		offersComponent := &components.Component{
			Content: helper.GetAnyWithoutError(offersDetails),
		}
		totalValidOffersCount++
		offersTabsData = append(offersTabsData, offersComponent)
	}

	offersTabsData = append(offersTabsData,
		&components.Component{
			Content: helper.GetAnyWithoutError(getSduiViewAllOffersCard()),
		},
		// Trailing spacer for the last card
		&components.Component{
			Content: helper.GetAnyWithoutError(&components.Spacer{
				SpacingValue: components.Spacing_SPACING_S,
			}),
		},
	)

	if totalValidOffersCount == 0 {
		logger.Info(ctx, "no valid fi coins offers found for credit card program", zap.String(logger.CARD_PROGRAM, savedCard.GetCardProgram().GetCardProgramType().String()))
		return nil, nil
	}
	// Use the helper function to create the FiCoins offers section
	return &feFireflyPb.GetDashboardV2Response_DashboardSection{
		Section: &feFireflyPb.GetDashboardV2Response_DashboardSection_OffersSection{
			OffersSection: &feFireflyPb.OffersSection{
				Section: getGenericOffersSection(
					internal.DashboardV2FiCoinsRewardsText,
					colors.ColorCharcoal,
					internal.DashboardV2ViewAllText,
					colors.ColorOnDarkLowEmphasis,
					offersTabsData,
					&dlPb.Deeplink{Screen: dlPb.Screen_OFFERS_LANDING_SCREEN},
				),
			},
		},
	}, nil
}

func (s *Service) buildV2BottomSection(_ *ffBeV2Pb.CreditCard) *feFireflyPb.BottomInfoSection {
	return &feFireflyPb.BottomInfoSection{
		BottomInfoList: []*dlPb.InfoItemWithCta{
			{
				Info: &dlPb.InfoItem{
					Icon:  internal.DashboardV2CardControlsIconUrl,
					Title: internal.DashboardV2CardControlsTitle,
					Desc:  internal.DashboardV2CardControlsDesc,
				},
				Cta: &dlPb.Cta{
					Type:     dlPb.Cta_CONTINUE,
					Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CREDIT_CARD_SDK_SCREEN},
				},
			},
			{
				Info: &dlPb.InfoItem{
					Icon:  internal.DashboardV2CollectedOffersIconUrl,
					Title: internal.DashboardV2CollectedOffersTitle,
					Desc:  internal.DashboardV2CollectedOffersDesc,
				},
				Cta: &dlPb.Cta{
					Type:     dlPb.Cta_CONTINUE,
					Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_REDEEMED_OFFERS_SCREEN},
				},
			},
			/* TODO (chandres/team): add card details screen deeplink here once we have it and uncomment this section.
			{
				Info: &dlPb.InfoItem{
					Icon:  internal.DashboardV2SmallCardBlackIconUrl,
					Title: internal.DashboardV2CardDetailsTitle,
					Desc:  internal.DashboardV2CardDetailsDesc,
				},
			},
			*/
		},
	}
}
